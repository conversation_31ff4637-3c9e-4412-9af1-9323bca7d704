﻿using Xylia.BnsHelper.Common.Helpers;
using Xylia.Preview.Data.Engine.DatData;

namespace Xylia.BnsHelper.Models;
/// <summary>
/// 服务器信息模型
/// </summary>
internal class BnsWorld(short id, string name, EPublisher publisher)
{
    #region Properties
    public short Id { get; } = id;
    public string Name { get; } = name;
    public EPublisher Publisher { get; } = publisher;
    #endregion

    #region Methods
    private static BnsWorld[] Worlds { get; } =
    [
        new(1001, "大漠孤烟", EPublisher.ZTX),
        new(1002, "长河剑气灵光", EPublisher.ZTX),
        new(1006, "武林浑天白雾", EPublisher.ZTX),
        new(1101, "永恒续章", EPublisher.ZTX),
        new(1201, "重逢无限", EPublisher.ZTX),
        new(1301, "北方雪原", EPublisher.ZTX),
        new(1302, "有你才灵", EPublisher.ZTX),

        new(2001, "绝代风华", EPublisher.ZNCG),
        new(2003, "爱欧妮娅", EPublisher.ZNCG),
        new(2004, "巅峰无界", EPublisher.ZNCG),
        new(2006, "登峰极镜", EPublisher.ZNCG),
        new(2007, "新无日峰", EPublisher.ZNCG),
        new(2009, "一剑霜华", EPublisher.ZNCG),
        new(2013, "归去来兮", EPublisher.ZNCG),

        new(1911, "无日峰", EPublisher.Tencent),
        new(1919, "铁傀王", EPublisher.Tencent),
        new(1925, "飞扇堂", EPublisher.Tencent),
        new(1219, "御剑出鞘", EPublisher.Tencent),
        new(1711, "似水华年", EPublisher.Tencent),
        new(6111, "仙衣舞绝色", EPublisher.Tencent),
        new(2021, "花样年华", EPublisher.Tencent),
        new(4115, "南道拳豪", EPublisher.Tencent),
        new(5711, "直上云霄", EPublisher.Tencent),
        new(5911, "涅槃重生", EPublisher.Tencent),
        new(5913, "疾剑无形", EPublisher.Tencent),
        new(5915, "华舞天青", EPublisher.Tencent),
        new(6011, "无限火力", EPublisher.Tencent),
        new(3023, "月华神", EPublisher.Tencent),
        new(3211, "极致时代", EPublisher.Tencent),
    ];

    /// <summary>
    /// 获取服务器列表
    /// </summary>
    /// <returns></returns>
    public static IEnumerable<BnsWorld> GetWorlds() => Worlds.Where(x => x.Publisher == SettingHelper.Default.Publisher);

    /// <summary>
    /// 获取当前服务器的名称
    /// </summary>
    /// <param name="world"></param>
    /// <returns></returns>
    public static string GetName(int world) => Worlds.FirstOrDefault(x => x.Id == world)?.Name ?? world.ToString();

    /// <summary>
    /// 获取当前服务器的发布编号
    /// </summary>
    public static EPublisher GetPublisher(int world) => world >= 1001 && world <= 1999 || world == 9001 ? EPublisher.ZTX : EPublisher.ZNCG;
    #endregion
}
