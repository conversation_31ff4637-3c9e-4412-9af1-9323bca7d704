package handler

import (
	"encoding/json"
	"net/http"
	"strconv"

	gatewayService "udp-server/server/internal/gateway/service"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"

	"github.com/gorilla/mux"
)

// AdminRiskHandler 管理后台风控处理器
type AdminRiskHandler struct {
	riskControlAdminService *gatewayService.RiskControlAdminService
	authService             *service.AuthService
}

// NewAdminRiskHandler 创建管理后台风控处理器
func NewAdminRiskHandler(
	riskControlAdminService *gatewayService.RiskControlAdminService,
	authService *service.AuthService,
) *AdminRiskHandler {
	return &AdminRiskHandler{
		riskControlAdminService: riskControlAdminService,
		authService:             authService,
	}
}

// 处理获取风控事件列表请求
func (h *AdminRiskHandler) HandleGetRiskEvents(w http.ResponseWriter, r *http.Request) {
	// 获取查询参数
	page := r.URL.Query().Get("page")
	pageSize := r.URL.Query().Get("page_size")
	eventType := r.URL.Query().Get("event_type")
	endDate := r.URL.Query().Get("end_date")

	// 设置默认值
	if page == "" {
		page = "1"
	}
	if pageSize == "" {
		pageSize = "20"
	}

	pageInt, err := strconv.Atoi(page)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的页码", nil)
		return
	}

	pageSizeInt, err := strconv.Atoi(pageSize)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的页面大小", nil)
		return
	}

	riskEvents, err := h.riskControlAdminService.GetRiskEvents(pageInt, pageSizeInt, eventType, endDate)
	if err != nil {
		logger.Error("获取风控事件列表失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取风控事件列表失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取风控事件列表成功", riskEvents)
}

// 处理获取风控配置请求
func (h *AdminRiskHandler) HandleGetRiskConfig(w http.ResponseWriter, r *http.Request) {
	riskConfig, err := h.riskControlAdminService.GetRiskConfig()
	if err != nil {
		logger.Error("获取风控配置失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取风控配置失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取风控配置成功", riskConfig)
}

// 处理更新风控配置请求
func (h *AdminRiskHandler) HandleUpdateRiskConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPut && r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	// 解析请求体
	var req map[string]interface{}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析更新风控配置请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 更新风控配置
	err := h.riskControlAdminService.UpdateRiskConfig(req)
	if err != nil {
		logger.Error("更新风控配置失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "更新风控配置失败", nil)
		return
	}

	logger.Info("风控配置更新成功")
	SendJSONResponse(w, http.StatusOK, "风控配置更新成功", nil)
}

// 处理处理风控事件请求
func (h *AdminRiskHandler) HandleProcessRiskEvent(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	vars := mux.Vars(r)
	eventID := vars["id"]

	id, err := strconv.ParseUint(eventID, 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的事件ID", nil)
		return
	}

	// 解析请求体
	var req struct {
		Action string `json:"action"` // approve, reject, ignore
		Reason string `json:"reason"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析处理风控事件请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 验证必填字段
	if req.Action == "" {
		SendJSONResponse(w, http.StatusBadRequest, "处理动作不能为空", nil)
		return
	}

	// 处理风控事件
	// TODO: 获取当前管理员信息
	adminID := uint64(0)     // 临时使用固定值
	adminUsername := "admin" // 临时使用固定值
	err = h.riskControlAdminService.ProcessRiskEvent(uint(id), req.Action, req.Reason, adminID, adminUsername)
	if err != nil {
		logger.Error("处理风控事件失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "处理风控事件失败", nil)
		return
	}

	logger.Info("风控事件处理成功: ID=%d, 动作=%s", id, req.Action)
	SendJSONResponse(w, http.StatusOK, "风控事件处理成功", nil)
}

// 处理恢复用户请求
func (h *AdminRiskHandler) HandleRestoreUsers(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	// 解析请求体
	var req struct {
		QQNumbers string `json:"qq_numbers"` // 逗号分隔的QQ号列表
		Reason    string `json:"reason"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析恢复用户请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 验证必填字段
	if req.QQNumbers == "" {
		SendJSONResponse(w, http.StatusBadRequest, "QQ号列表不能为空", nil)
		return
	}

	// 恢复用户
	err := h.riskControlAdminService.RestoreUsersFromAudit(req.QQNumbers, req.Reason)
	if err != nil {
		logger.Error("恢复用户失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "恢复用户失败", nil)
		return
	}

	logger.Info("用户恢复成功: QQ号=%s, 原因=%s", req.QQNumbers, req.Reason)
	SendJSONResponse(w, http.StatusOK, "用户恢复成功", nil)
}
