using CommunityToolkit.Mvvm.ComponentModel;
using System.ComponentModel;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;

namespace Xylia.BnsHelper.Models.Triggers;

/// <summary>
/// 触发器动作基类
/// </summary>
public abstract partial class TriggerAction : ObservableObject
{
    #region Fields
    [ObservableProperty] bool _isEnabled = true;        // 是否启用
    [ObservableProperty] int _delay = 0;                // 延时执行（毫秒）
    #endregion

    #region Methods
    /// <summary>
    /// 动作详情文本
    /// </summary>
    [JsonIgnore] public virtual string Describe => GetType().Name;

    /// <summary>
    /// 属性发生变化时通知文本刷新
    /// </summary>
    protected override void OnPropertyChanged(PropertyChangedEventArgs e)
    {
        base.OnPropertyChanged(e);

        if (e.PropertyName != nameof(Describe)) OnPropertyChanged(nameof(Describe));
    }

    /// <summary>
    /// 执行动作
    /// </summary>
    /// <param name="context">执行上下文</param>
    /// <returns>执行任务</returns>
    public async Task ExecuteAsync(TriggerExecutionContext context)
    {
        if (!IsEnabled) return;

        await Task.Delay(Delay);
        await ExecuteActionAsync(context);
    }

    /// <summary>
    /// 执行具体动作
    /// </summary>
    protected abstract Task ExecuteActionAsync(TriggerExecutionContext context);

    /// <summary>
    /// 替换文本中的变量
    /// </summary>
    protected string ReplaceVariables(string text, Dictionary<string, object> variables)
    {
        if (string.IsNullOrEmpty(text) || variables.Count == 0) return text;

        var result = text;
        foreach (var variable in variables)
        {
            var pattern = $@"\$\{{{variable.Key}\}}";
            result = Regex.Replace(result, pattern, variable.Value?.ToString() ?? string.Empty);
        }

        return result;
    }

    /// <summary>
    /// 克隆动作
    /// </summary>
    public virtual TriggerAction Clone()
    {
        var newAction = (TriggerAction)MemberwiseClone();
        return newAction;
    }
    #endregion
}

/// <summary>
/// 触发器动作类型枚举
/// </summary>
public enum TriggerActionType
{
    TTS,
    TextDisplay,
    Notification,
    AudioPlay,
    KeyboardInput,
    Mouse,
    Counter,
    Log,
    SendGameMessage
}