using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HandyControl.Tools.Extension;
using Xylia.Preview.UI.Views.Dialogs;

namespace Xylia.BnsHelper.ViewModels;
internal partial class CDKeyDialogViewModel : ObservableObject, IDialogResultable<bool>
{
    #region Fields
    [ObservableProperty] private string _cdKey = string.Empty;
    [ObservableProperty] private string _errorMessage = string.Empty;
    [ObservableProperty] private bool _hasError = false;

    public bool Result { get; set; } = false;
    public Action? CloseAction { get; set; }
    #endregion

    #region Commands
    [RelayCommand]
    async Task ActivateAsync()
    {
        if (string.IsNullOrWhiteSpace(CdKey))
        {
            ShowError("请输入口令码");
            return;
        }

        var user = MainWindowViewModel.Instance.User;
        if (user == null)
        {
            ShowError("请先登录");
            return;
        }

        ClearError();

        var (success, message) = await user.ActivateCDKeyAsync(CdKey);
        if (success)
        {
            // 先关闭当前对话框，避免两个对话框叠在一起
            Result = true;
            CloseAction?.Invoke();
            await MessageDialog.ShowDialog(message, autoCloseMilliseconds: 0);
        }
        else
        {
            ShowError(message);
        }
    }

    [RelayCommand]
    void Close()
    {
        CloseAction?.Invoke();
        Result = false;
    }
    #endregion

    #region Methods
    private void ShowError(string message)
    {
        ErrorMessage = message;
        HasError = true;
    }

    private void ClearError()
    {
        ErrorMessage = string.Empty;
        HasError = false;
    }

    partial void OnCdKeyChanged(string value)
    {
        ClearError();
        ActivateCommand.NotifyCanExecuteChanged();
    }
    #endregion
}
