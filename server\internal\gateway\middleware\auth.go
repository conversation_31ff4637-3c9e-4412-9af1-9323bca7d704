package middleware

import (
	"context"
	"net/http"
	"strconv"
	"strings"

	gatewayService "udp-server/server/internal/gateway/service"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"
)

// AdminAuthMiddleware 管理员认证中间件
type AdminAuthMiddleware struct {
	authService       *service.AuthService
	permissionService *gatewayService.AdminPermissionService
	adminService      *gatewayService.AdminService
}

// NewAdminAuthMiddleware 创建管理员认证中间件
func NewAdminAuthMiddleware(authService *service.AuthService, permissionService *gatewayService.AdminPermissionService, adminService *gatewayService.AdminService) *AdminAuthMiddleware {
	return &AdminAuthMiddleware{
		authService:       authService,
		permissionService: permissionService,
		adminService:      adminService,
	}
}

// RequireAuth 需要认证的中间件
func (m *AdminAuthMiddleware) RequireAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// 跳过登录页面和登录API
		if strings.HasSuffix(r.URL.Path, "/login") || r.URL.Path == "/admin/api/login" {
			next.ServeHTTP(w, r)
			return
		}

		// 检查cookie中的token
		cookie, err := r.Cookie("admin_token")
		if err != nil {
			logger.Warn("管理员访问未认证页面: %s", r.URL.Path)
			if strings.HasPrefix(r.URL.Path, "/admin/api/") {
				// API请求返回JSON错误
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusUnauthorized)
				w.Write([]byte(`{"success":false,"message":"未登录","code":401}`))
			} else {
				// 页面请求重定向到登录页
				http.Redirect(w, r, "/admin/login", http.StatusFound)
			}
			return
		}

		// 验证token
		adminID, err := m.adminService.ValidateAdminToken(cookie.Value)
		if err == nil {
			// 获取管理员信息
			adminInfo := map[string]interface{}{
				"uid": adminID,
			}
			// 将管理员信息存储到上下文中
			ctx := context.WithValue(r.Context(), "admin", adminInfo)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}
		logger.Warn("管理员token验证失败: %v", err)
		if strings.HasPrefix(r.URL.Path, "/admin/api/") {
			// API请求返回JSON错误
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusUnauthorized)
			w.Write([]byte(`{"success":false,"message":"登录已过期","code":401}`))
		} else {
			// 页面请求重定向到登录页
			http.Redirect(w, r, "/admin/login", http.StatusFound)
		}
	})
}

// RequirePermission 需要特定权限的中间件
func (m *AdminAuthMiddleware) RequirePermission(permission string) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// 先检查基本认证
			adminInfo := r.Context().Value("admin_info")
			if adminInfo == nil {
				// 如果上下文中没有管理员信息，尝试重新验证
				cookie, err := r.Cookie("admin_token")
				if err != nil {
					m.sendUnauthorizedResponse(w, r, "未登录")
					return
				}

				adminID, err := m.adminService.ValidateAdminToken(cookie.Value)
				if err == nil {
					adminInfo = map[string]interface{}{"uid": adminID}
				}
				if err != nil {
					m.sendUnauthorizedResponse(w, r, "登录已过期")
					return
				}
			}

			// 获取管理员UID
			adminInfoMap, ok := adminInfo.(map[string]interface{})
			if !ok {
				m.sendUnauthorizedResponse(w, r, "认证信息格式错误")
				return
			}

			var adminUID uint64
			if uid, ok := adminInfoMap["uid"].(uint64); ok {
				adminUID = uid
			} else if uid, ok := adminInfoMap["uid"].(float64); ok {
				adminUID = uint64(uid)
			} else if uidStr, ok := adminInfoMap["uid"].(string); ok {
				if parsed, err := strconv.ParseUint(uidStr, 10, 64); err == nil {
					adminUID = parsed
				}
			}

			if adminUID == 0 {
				m.sendUnauthorizedResponse(w, r, "无效的管理员ID")
				return
			}

			// 检查权限
			hasPermission, err := m.permissionService.HasPermission(adminUID, permission)
			if err != nil {
				logger.Error("检查权限失败: %v", err)
				m.sendForbiddenResponse(w, r, "权限检查失败")
				return
			}

			if !hasPermission {
				logger.Warn("管理员权限不足: UID=%d, 需要权限=%s", adminUID, permission)
				m.sendForbiddenResponse(w, r, "权限不足")
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}

// sendUnauthorizedResponse 发送未认证响应
func (m *AdminAuthMiddleware) sendUnauthorizedResponse(w http.ResponseWriter, r *http.Request, message string) {
	if r.Method == http.MethodPost {
		// API请求返回JSON错误
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusUnauthorized)
		w.Write([]byte(`{"success":false,"message":"` + message + `","code":401}`))
	} else {
		// 页面请求重定向到登录页
		http.Redirect(w, r, "/admin/login", http.StatusFound)
	}
}

// sendForbiddenResponse 发送权限不足响应
func (m *AdminAuthMiddleware) sendForbiddenResponse(w http.ResponseWriter, r *http.Request, message string) {
	if r.Method == http.MethodPost {
		// API请求返回JSON错误
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusForbidden)
		w.Write([]byte(`{"success":false,"message":"` + message + `","code":403}`))
	} else {
		// 页面请求显示错误页面
		w.WriteHeader(http.StatusForbidden)
		w.Write([]byte(`
			<!DOCTYPE html>
			<html>
			<head>
				<title>权限不足</title>
				<meta charset="utf-8">
			</head>
			<body>
				<h1>权限不足</h1>
				<p>` + message + `</p>
				<a href="/admin">返回管理后台</a>
			</body>
			</html>
		`))
	}
}
