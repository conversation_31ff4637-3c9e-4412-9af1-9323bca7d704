﻿using RestSharp;
using System.IO;

namespace Xylia.BnsHelper.Services.ApiEndpoints;
internal class GithubEndpoint(RestClient _client)
{
	public async Task<Stream?> Download(string url)
	{
		ArgumentNullException.ThrowIfNull(url);

		var request = new RestRequest(url);
		var steam = await _client.DownloadStreamAsync(request).ConfigureAwait(false);
		if (steam is null) return null;

		var memory = new MemoryStream();
		steam.CopyTo(memory);
		return memory;
	}
}
