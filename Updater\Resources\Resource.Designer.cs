﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Xylia.Updater.Resources {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resource {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resource() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Xylia.Updater.Resources.Resource", typeof(Resource).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找类似 Extracting {0} 的本地化字符串。
        /// </summary>
        internal static string CurrentFileExtracting {
            get {
                return ResourceManager.GetString("CurrentFileExtracting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Downloading at {0}/s 的本地化字符串。
        /// </summary>
        internal static string DownloadSpeedMessage {
            get {
                return ResourceManager.GetString("DownloadSpeedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Software Update 的本地化字符串。
        /// </summary>
        internal static string DownloadUpdater {
            get {
                return ResourceManager.GetString("DownloadUpdater", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Unable to update the file! 的本地化字符串。
        /// </summary>
        internal static string FileStillInUseCaption {
            get {
                return ResourceManager.GetString("FileStillInUseCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 {0} is still open and it is using &quot;{1}&quot;. Please close the process manually and press Retry. 的本地化字符串。
        /// </summary>
        internal static string FileStillInUseMessage {
            get {
                return ResourceManager.GetString("FileStillInUseMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Removing {0} 的本地化字符串。
        /// </summary>
        internal static string Removing {
            get {
                return ResourceManager.GetString("Removing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Unable to determine file name. 的本地化字符串。
        /// </summary>
        internal static string UnableToDetermineFilenameMessage {
            get {
                return ResourceManager.GetString("UnableToDetermineFilenameMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Update Check Failed 的本地化字符串。
        /// </summary>
        internal static string UpdateCheckFailedCaption {
            get {
                return ResourceManager.GetString("UpdateCheckFailedCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 There is a problem reaching update server. Please check your internet connection and try again later. 的本地化字符串。
        /// </summary>
        internal static string UpdateCheckFailedMessage {
            get {
                return ResourceManager.GetString("UpdateCheckFailedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 Update Unavailable 的本地化字符串。
        /// </summary>
        internal static string UpdateUnavailableCaption {
            get {
                return ResourceManager.GetString("UpdateUnavailableCaption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 There is no update available. Please try again later. 的本地化字符串。
        /// </summary>
        internal static string UpdateUnavailableMessage {
            get {
                return ResourceManager.GetString("UpdateUnavailableMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Byte[] 类型的本地化资源。
        /// </summary>
        internal static byte[] ZipExtractor {
            get {
                object obj = ResourceManager.GetObject("ZipExtractor", resourceCulture);
                return ((byte[])(obj));
            }
        }
    }
}
