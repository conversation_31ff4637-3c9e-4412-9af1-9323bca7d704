using System.Windows;
using Xylia.BnsHelper.ViewModels;

namespace Xylia.BnsHelper.Views;
public partial class AgreementWindow : Window
{
    #region Properties
    /// <summary>
    /// 用户是否同意协议
    /// </summary>
    public bool Result => _viewModel.Result;
    #endregion

    #region Constructor
    readonly AgreementWindowViewModel _viewModel;

    public AgreementWindow()
    {
        InitializeComponent();

        DataContext = _viewModel = new AgreementWindowViewModel();

        // 设置关闭回调
        _viewModel.CloseAction = () =>
        {
            DialogResult = _viewModel.Result;
            Close();
        };

        // 处理窗口关闭事件
        Closing += OnClosing;

        // 窗口加载完成后启动倒计时
        Loaded += OnWindowLoaded;
    }
    #endregion

    #region Event Handlers
    private void OnWindowLoaded(object sender, RoutedEventArgs e)
    {
        // 窗口加载完成后启动倒计时
        Dispatcher.BeginInvoke(() =>
        {
            Activate(); // 确保窗口获得焦点
            _viewModel.StartCountdown();
        });
    }

    private void OnClosing(object sender, System.ComponentModel.CancelEventArgs e)
    {
        // 停止倒计时
        _viewModel.StopCountdown();
    }
    #endregion

    #region Static Methods
    /// <summary>
    /// 显示用户协议窗口
    /// </summary>
    /// <returns>用户是否同意协议</returns>
    public static bool ShowAgreement()
    {
        var window = new AgreementWindow();
        var result = window.ShowDialog();
        return result == true && window.Result;
    }
    #endregion
}
