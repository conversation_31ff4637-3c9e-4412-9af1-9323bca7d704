package service

import (
	"fmt"
	"time"
	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"
	"udp-server/server/pkg/utils"

	"github.com/spf13/viper"
	"gorm.io/gorm"
)

// 更新配置服务
type UpdateService struct {
	db      *gorm.DB
	cache   cache.Cache
	metrics *UpdateMetrics
}

func NewUpdateService(db *gorm.DB, cache cache.Cache) *UpdateService {
	service := &UpdateService{
		db:      db,
		cache:   cache,
		metrics: NewUpdateMetrics(),
	}

	// 启动定期统计日志
	go service.startMetricsLogger()
	return service
}

// 生成缓存键，根据测试服配置区分
func (s *UpdateService) generateCacheKey(appName string) string {
	if viper.GetBool("test_server.enabled") {
		return fmt.Sprintf("update_config:%s:test", appName)
	}
	return fmt.Sprintf("update_config:%s:prod", appName)
}

// 获取最新的更新配置
func (s *UpdateService) GetLatestUpdateConfig(appName string) *model.UpdateConfig {
	startTime := time.Now()
	cacheKey := s.generateCacheKey(appName)
	var config model.UpdateConfig

	// 先检查缓存
	if s.cache != nil {
		var cachedConfig model.UpdateConfig
		err := s.cache.Get(cacheKey, &cachedConfig)
		if err == nil {
			s.metrics.RecordCacheHit()
			config = cachedConfig
		} else {
			s.metrics.RecordCacheMiss()
			s.metrics.RecordCacheGetError()
		}
	}

	// 缓存未命中，从数据库获取配置
	if config.ID == 0 {
		s.metrics.RecordCacheMiss()

		// 根据测试服配置决定查询条件
		var query *gorm.DB
		if viper.GetBool("test_server.enabled") {
			// 测试服模式：优先查找beta=1的配置，如果没有则查找beta=0的配置
			query = s.db.Where("name = ? AND is_active = ?", appName, true).Order("beta DESC")
		} else {
			// 正式服模式：只查找beta=0的配置
			query = s.db.Where("name = ? AND is_active = ? AND beta = ?", appName, true, false)
		}

		err := query.First(&config).Error
		if err != nil {
			s.metrics.RecordDatabaseError()
			s.metrics.RecordError()
			s.metrics.RecordResponseTime(time.Since(startTime))
			return nil
		}

		// 将配置存入缓存（缓存60分钟）
		if s.cache != nil {
			err = s.cache.Set(cacheKey, config, 60*time.Minute)
			if err != nil {
				s.metrics.RecordCacheSetError()
				logger.Warn("Failed to cache update config for %s: %v", appName, err)
			} else {
				logger.Debug("Update config cached for app: %s", appName)
			}
		}
	}

	// 记录成功和响应时间
	s.metrics.RecordSuccess()
	s.metrics.RecordResponseTime(time.Since(startTime))
	return &config
}

// 获取更新配置
func (s *UpdateService) GetUpdateConfig(appName, clientVersion string) (*model.UpdateConfig, bool) {
	// 记录请求指标
	s.metrics.RecordRequest(appName)

	// 增加今日更新请求统计
	s.incrementUpdateRequestCount()

	// 获取配置信息
	config := s.GetLatestUpdateConfig(appName)
	if config == nil {
		return nil, false
	}

	// 比较版本，判断是否需要更新
	needsUpdate, err := utils.IsVersionOutdated(clientVersion, config.Version)
	if err != nil {
		s.metrics.RecordVersionCompareError()
		logger.Warn("Version comparison failed for %s: client=%s, server=%s, error=%v", appName, clientVersion, config.Version, err)
		// 版本比较失败时，假设需要更新以确保安全
		needsUpdate = true
	} else {
		s.metrics.RecordVersionCompareOK()
	}

	// 只有在需要更新时才包含下载信息
	if needsUpdate {
		s.metrics.RecordUpdateNeeded()
	} else {
		s.metrics.RecordNoUpdateNeeded()
	}

	return config, needsUpdate
}

// 检查客户端版本是否过时
func (s *UpdateService) CheckUpdate(appName, clientVersion string) bool {
	// 获取版本配置并检查强制更新标志
	config := s.GetLatestUpdateConfig(appName)
	if config == nil {
		return false
	}

	if !config.Force {
		return false
	}

	// 使用 Version 类进行版本比较
	isOutdated, err := utils.IsVersionOutdated(clientVersion, config.Version)
	if err != nil {
		logger.Error("Failed to compare versions for app %s: client=%s, latest=%s, error=%v", appName, clientVersion, config.Version, err)
		// 版本比较失败时，为了安全起见假设不需要更新
		return false
	}

	return isOutdated
}

// 增加今日更新请求统计
func (s *UpdateService) incrementUpdateRequestCount() {
	today := time.Now().Format("2006-01-02")
	cacheKey := fmt.Sprintf("update_request_count:%s", today)

	// 使用Redis INCR命令增加计数
	_, err := s.cache.Increment(cacheKey, 1)
	if err != nil {
		logger.Error("Failed to increment update request count: %v", err)
	}

	// 设置过期时间为7天
	s.cache.Expire(cacheKey, 15*24*time.Hour)
}

// 初始化缓存
func (s *UpdateService) InitializeCache() error {
	// 预加载常用应用的配置到缓存
	apps := []string{"bns-helper", "bns-preview-tools"}

	for _, app := range apps {
		config, _ := s.GetUpdateConfig(app, "")
		if config == nil {
			logger.Error("[Gateway] Failed to initialize cache for app %s", app)
			continue
		}
	}

	logger.Info("[Gateway] Update config cache initialization completed")
	return nil
}

// 启动定期指标日志
func (s *UpdateService) startMetricsLogger() {
	ticker := time.NewTicker(30 * time.Minute) // 每30分钟记录一次
	defer ticker.Stop()

	for range ticker.C {
		s.metrics.LogStats()
	}
}
