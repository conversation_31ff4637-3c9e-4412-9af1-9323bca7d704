using Xylia.BnsHelper.Models;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Service;

/// <summary>
/// 团队信息包，用于获取团队成员信息
/// </summary>
internal class TeamPacket : BasePacket
{
    #region Response
    /// <summary>
    /// 团队信息
    /// </summary>
    public Team TeamInfo { get; set; }
    #endregion

    #region Methods
    protected override void ReadResponse(DataArchive reader)
    {
        var members = new TeamMember[reader.Read<ushort>()];

        for (int i = 0; i < members.Length; i++)
        {
            members[i] = new TeamMember(reader);
        }

        TeamInfo = new Team(members);
    }
    #endregion
}
