using CommunityToolkit.Mvvm.ComponentModel;
using System.Text.Json.Serialization;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Models;
public partial class Announcement : ObservableObject
{
    #region Properties
    /// <summary>
    /// 公告ID
    /// </summary>
    public uint Id { get; set; }

    /// <summary>
    /// 公告标题
    /// </summary>
    [ObservableProperty] private string _title = string.Empty;

    /// <summary>
    /// 公告内容
    /// </summary>
    [ObservableProperty] private string _content = string.Empty;

    /// <summary>
    /// 公告优先级
    /// </summary>
    [ObservableProperty] private byte _priority;

    /// <summary>
    /// 公告类型
    /// </summary>
    [ObservableProperty] private AnnouncementType _type;

    /// <summary>
    /// 发布时间
    /// </summary>
    [ObservableProperty] private DateTime _publishTime;

    /// <summary>
    /// 是否已读
    /// </summary>
    [ObservableProperty] private bool _isRead;

    /// <summary>
    /// 公告版本号
    /// </summary>
    public ushort Version { get; set; }

    /// <summary>
    /// 开始时间（可选，用于定时显示）
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间（可选，用于定时显示）
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 格式化的发布时间
    /// </summary>
    public string FormattedPublishTime => PublishTime.ToString("yyyy-MM-dd HH:mm");

    /// <summary>
    /// 类型显示文本
    /// </summary>
    [JsonIgnore]
    public string TypeText => Type switch
    {
        AnnouncementType.Info => "信息",
        AnnouncementType.Warning => "警告",
        AnnouncementType.Update => "更新",
        AnnouncementType.Maintenance => "维护",
        _ => "未知"
    };

    /// <summary>
    /// 检查是否在有效时间范围内
    /// </summary>
    [JsonIgnore]
    public bool IsInValidTimeRange
    {
        get
        {
            var now = DateTime.Now;
            return (StartTime == null || now >= StartTime) && (EndTime == null || now <= EndTime);
        }
    }
    #endregion

    #region Constructor
    internal Announcement(DataArchive reader)
    {
        Id = reader.Read<uint>();
        Title = reader.ReadString();
        Content = reader.ReadString();
        Type = (AnnouncementType)reader.Read<byte>();
        Priority = reader.Read<byte>(); 
        PublishTime = DateTimeOffset.FromUnixTimeSeconds(reader.Read<long>()).DateTime.ToLocalTime();
        Version = reader.Read<ushort>();

        // 读取开始时间（0表示无限制）
        var startTime = reader.Read<long>();
        StartTime = startTime > 0 ? DateTimeOffset.FromUnixTimeSeconds(startTime).DateTime : null;

        // 读取结束时间（0表示无限制）
        var endTime = reader.Read<long>();
        EndTime = endTime > 0 ? DateTimeOffset.FromUnixTimeSeconds(endTime).DateTime : null;
    }

    public Announcement()
    {

    }
    #endregion
}

/// <summary>
/// 公告类型
/// </summary>
public enum AnnouncementType
{
    /// <summary>
    /// 信息公告
    /// </summary>
    Info,

    /// <summary>
    /// 警告公告
    /// </summary>
    Warning,

    /// <summary>
    /// 更新公告
    /// </summary>
    Update,

    /// <summary>
    /// 维护公告
    /// </summary>
    Maintenance
}
