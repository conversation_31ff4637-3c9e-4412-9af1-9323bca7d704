package handler

import (
	"encoding/json"
	"net/http"
	"strconv"

	gatewayService "udp-server/server/internal/gateway/service"
	"udp-server/server/internal/model"

	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"

	"github.com/gorilla/mux"
)

// 管理后台更新配置处理器
type AdminUpdateHandler struct {
	updateAdminService *gatewayService.UpdateAdminService
	authService        *service.AuthService
}

// 创建管理后台更新配置处理器
func NewAdminUpdateHandler(updateAdminService *gatewayService.UpdateAdminService, authService *service.AuthService) *AdminUpdateHandler {
	return &AdminUpdateHandler{
		updateAdminService: updateAdminService,
		authService:        authService,
	}
}

// 处理获取更新配置列表请求
func (h *AdminUpdateHandler) HandleGetUpdateConfigs(w http.ResponseWriter, r *http.Request) {
	updateConfigs, err := h.updateAdminService.GetConfigs()
	if err != nil {
		logger.Error("获取更新配置列表失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取更新配置列表失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取更新配置列表成功", updateConfigs)
}

// 处理获取更新配置详情请求
func (h *AdminUpdateHandler) HandleGetUpdateConfig(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id, err := strconv.ParseUint(vars["id"], 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的配置ID", nil)
		return
	}

	updateConfig, err := h.updateAdminService.GetConfig(id)
	if err != nil {
		logger.Error("获取更新配置详情失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取更新配置详情失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取更新配置详情成功", updateConfig)
}

// 处理创建更新配置请求
func (h *AdminUpdateHandler) HandleCreateUpdateConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	// 解析请求体
	var req model.UpdateConfig
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析创建更新配置请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 验证必填字段
	if req.Version == "" || req.URL == "" {
		SendJSONResponse(w, http.StatusBadRequest, "版本号和下载地址不能为空", nil)
		return
	}

	// 创建更新配置
	configID, err := h.updateAdminService.CreateConfig(req)
	if err != nil {
		logger.Error("创建更新配置失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "创建更新配置失败", nil)
		return
	}

	logger.Info("更新配置创建成功: ID=%d, 版本=%s", configID, req.Version)
	SendJSONResponse(w, http.StatusOK, "更新配置创建成功", map[string]interface{}{
		"config_id": configID,
	})
}

// 处理更新配置请求
func (h *AdminUpdateHandler) HandleUpdateConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPut && r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	vars := mux.Vars(r)
	configID := vars["id"]

	id, err := strconv.ParseUint(configID, 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的配置ID", nil)
		return
	}

	// 解析请求体
	var req model.UpdateConfig
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析更新配置请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 验证必填字段
	if req.Version == "" || req.URL == "" {
		SendJSONResponse(w, http.StatusBadRequest, "版本号和下载地址不能为空", nil)
		return
	}

	// 更新配置
	err = h.updateAdminService.UpdateConfig(req)
	if err != nil {
		logger.Error("更新配置失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "更新配置失败", nil)
		return
	}

	logger.Info("更新配置更新成功: ID=%d, 版本=%s", id, req.Version)
	SendJSONResponse(w, http.StatusOK, "更新配置更新成功", nil)
}

// 处理删除更新配置请求
func (h *AdminUpdateHandler) HandleDeleteUpdateConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	vars := mux.Vars(r)
	id, err := strconv.ParseUint(vars["id"], 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的配置ID", nil)
		return
	}

	// 删除更新配置
	err = h.updateAdminService.DeleteConfig(id)
	if err != nil {
		logger.Error("删除更新配置失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "删除更新配置失败", nil)
		return
	}

	logger.Info("更新配置删除成功: ID=%d", id)
	SendJSONResponse(w, http.StatusOK, "更新配置删除成功", nil)
}

// 处理激活更新配置请求
func (h *AdminUpdateHandler) HandleActivateUpdateConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	vars := mux.Vars(r)
	id, err := strconv.ParseUint(vars["id"], 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的配置ID", nil)
		return
	}

	// 激活更新配置
	err = h.updateAdminService.ToggleConfig(id)
	if err != nil {
		logger.Error("激活更新配置失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "激活更新配置失败", nil)
		return
	}

	logger.Info("更新配置激活成功: ID=%d", id)
	SendJSONResponse(w, http.StatusOK, "更新配置激活成功", nil)
}
