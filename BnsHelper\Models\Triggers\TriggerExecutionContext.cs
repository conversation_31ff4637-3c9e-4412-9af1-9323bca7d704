namespace Xylia.BnsHelper.Models.Triggers;

/// <summary>
/// 触发器执行上下文
/// </summary>
public class TriggerExecutionContext
{
    /// <summary>
    /// 触发器名称
    /// </summary>
    public string TriggerName { get; set; } = string.Empty;
    
    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime ExecutionTime { get; set; } = DateTime.Now;
    
    /// <summary>
    /// 变量字典
    /// </summary>
    public Dictionary<string, object> Variables { get; set; } = new();
    
    /// <summary>
    /// 原始消息
    /// </summary>
    public string OriginalMessage { get; set; } = string.Empty;

    /// <summary>
    /// 触发消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 触发器实例
    /// </summary>
    public Trigger? Trigger { get; set; }

    /// <summary>
    /// 触发事件
    /// </summary>
    public ITriggerEvent? TriggerEvent { get; set; }
    
    /// <summary>
    /// 获取变量值
    /// </summary>
    public T? GetVariable<T>(string key, T? defaultValue = default)
    {
        if (Variables.TryGetValue(key, out var value))
        {
            try
            {
                return (T?)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
        return defaultValue;
    }
    
    /// <summary>
    /// 设置变量值
    /// </summary>
    public void SetVariable(string key, object? value)
    {
        if (value != null)
        {
            Variables[key] = value;
        }
        else
        {
            Variables.Remove(key);
        }
    }
}
