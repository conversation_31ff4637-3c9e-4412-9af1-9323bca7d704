<hc:Window x:Class="BnsHelper.Views.Dialogs.FlowSelectionDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             xmlns:converters="clr-namespace:BnsHelper.Converters" >

    <hc:Window.Resources>
        <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter"/>
        <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
    </hc:Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="{DynamicResource ApplicationBackgroundBrush}" 
                BorderBrush="{DynamicResource ControlElevationBorderBrush}" 
                BorderThickness="0,0,0,1" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="{Binding Title}" 
                          FontSize="16" FontWeight="SemiBold"
                          VerticalAlignment="Center"/>
                
                <ProgressBar Grid.Column="1" Width="20" Height="20"
                           IsIndeterminate="True"
                           Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}"/>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="20"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：分组列表 -->
            <Border Grid.Column="0" Background="{DynamicResource ControlFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource ControlElevationBorderBrush}"
                    BorderThickness="1" CornerRadius="6">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 分组标题 -->
                    <Border Grid.Row="0" Background="{DynamicResource AccentFillColorDefaultBrush}"
                            CornerRadius="6,6,0,0" Padding="12,8">
                        <TextBlock Text="流程分组" FontWeight="SemiBold" 
                                  Foreground="{DynamicResource TextOnAccentFillColorPrimaryBrush}"/>
                    </Border>

                    <!-- 分组列表 -->
                    <ListBox Grid.Row="1" ItemsSource="{Binding FlowGroups}"
                             SelectedItem="{Binding SelectedGroup}"
                             Background="Transparent" BorderThickness="0"
                             ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Padding="12,8" Margin="4,2">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" Text="{Binding GroupName}" 
                                                  TextWrapping="Wrap" VerticalAlignment="Center"/>
                                        
                                        <Border Grid.Column="1" Background="{DynamicResource AccentFillColorSecondaryBrush}"
                                               CornerRadius="10" Padding="6,2" Margin="4,0,0,0">
                                            <TextBlock Text="{Binding Flows.Count}" FontSize="11"
                                                      Foreground="{DynamicResource TextOnAccentFillColorSecondaryBrush}"/>
                                        </Border>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                        <ListBox.ItemContainerStyle>
                            <Style TargetType="ListBoxItem" BasedOn="{StaticResource {x:Type ListBoxItem}}">
                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                <Setter Property="Margin" Value="0"/>
                                <Setter Property="Padding" Value="0"/>
                            </Style>
                        </ListBox.ItemContainerStyle>
                    </ListBox>
                </Grid>
            </Border>

            <!-- 右侧：流程列表 -->
            <Border Grid.Column="2" Background="{DynamicResource ControlFillColorDefaultBrush}"
                    BorderBrush="{DynamicResource ControlElevationBorderBrush}"
                    BorderThickness="1" CornerRadius="6">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 流程标题 -->
                    <Border Grid.Row="0" Background="{DynamicResource AccentFillColorDefaultBrush}"
                            CornerRadius="6,6,0,0" Padding="12,8">
                        <TextBlock FontWeight="SemiBold" 
                                  Foreground="{DynamicResource TextOnAccentFillColorPrimaryBrush}">
                            <TextBlock.Text>
                                <MultiBinding StringFormat="流程列表 ({0})">
                                    <Binding Path="SelectedGroup.Flows.Count" FallbackValue="0"/>
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                    </Border>

                    <!-- 流程列表 -->
                    <ListBox Grid.Row="1" ItemsSource="{Binding SelectedGroup.Flows}"
                             SelectedItem="{Binding SelectedFlow}"
                             Background="Transparent" BorderThickness="0"
                             ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Border Padding="12,10" Margin="4,2">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        
                                        <TextBlock Grid.Row="0" Text="{Binding Name}" 
                                                  FontWeight="Medium" TextWrapping="Wrap"/>
                                        
                                        <TextBlock Grid.Row="1" FontSize="11" Margin="0,2,0,0"
                                                  Foreground="{DynamicResource TextFillColorSecondaryBrush}">
                                            <TextBlock.Text>
                                                <MultiBinding StringFormat="ID: {0} | 类型: {1}">
                                                    <Binding Path="FlowId"/>
                                                    <Binding Path="FlowType"/>
                                                </MultiBinding>
                                            </TextBlock.Text>
                                        </TextBlock>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                        <ListBox.ItemContainerStyle>
                            <Style TargetType="ListBoxItem" BasedOn="{StaticResource {x:Type ListBoxItem}}">
                                <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                <Setter Property="Margin" Value="0"/>
                                <Setter Property="Padding" Value="0"/>
                            </Style>
                        </ListBox.ItemContainerStyle>
                    </ListBox>

                    <!-- 空状态提示 -->
                    <StackPanel Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Center"
                               Visibility="{Binding SelectedGroup.Flows.Count, Converter={StaticResource CountToVisibilityConverter}, ConverterParameter=0}">
                        <TextBlock Text="📄" FontSize="48" HorizontalAlignment="Center" Foreground="Gray"/>
                        <TextBlock Text="该分组暂无可用流程" Margin="0,8,0,0" Foreground="Gray" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>

        <!-- 底部按钮栏 -->
        <Border Grid.Row="2" Background="{DynamicResource ApplicationBackgroundBrush}" 
                BorderBrush="{DynamicResource ControlElevationBorderBrush}" 
                BorderThickness="0,1,0,0" Padding="20,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="取消" Command="{Binding CancelCommand}"
                        Margin="0,0,10,0" Padding="12,6" MinWidth="80"/>
                <Button Content="确定执行" Command="{Binding ConfirmCommand}"
                        IsEnabled="{Binding CanConfirm}" Padding="12,6" MinWidth="80"
                        Background="DodgerBlue" Foreground="White"/>
            </StackPanel>
        </Border>
    </Grid>
</hc:Window>
