﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Win32;
using Newtonsoft.Json;
using System.IO;
using System.Windows;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Resources;
using Xylia.Preview.UI.Views.Dialogs;

namespace Xylia.BnsHelper.ViewModels.Pages;
internal partial class HUDPageViewModel : ObservableObject
{
	#region Properties
	[ObservableProperty] HUDCollection data = HUDCollection.Load();
	#endregion

	#region Methods
	[RelayCommand]
	void Add() => Data.Add(new HUDConfig(StringHelper.Get("HUDPage_Default") ?? "新HUD界面"));

	[RelayCommand]
	void Reload() => MainWindowViewModel.ReloadConfig();

	[RelayCommand]
	async Task Import()
	{
		var dialog = new OpenFileDialog()
		{
			Filter = "JavaScript Object Notation|*.json",
		};
		if (dialog.ShowDialog() != true) return;

		try
		{
			var importedData = JsonConvert.DeserializeObject<HUDCollection>(File.ReadAllText(dialog.FileName));
			if (importedData == null)
			{
				await MessageDialog.ShowDialog(StringHelper.Get("HUDPage_Import_Error") ?? "导入文件格式错误");
				return;
			}

			foreach (var item in importedData)
			{
				// 检查是否已存在
				if (Data.Contains(item))
				{
					if (MessageBox.Show(
						StringHelper.Get("HUDPage_Import_Ask", item.Name) ?? $"HUD配置 '{item.Name}' 已存在，是否覆盖？",
						StringHelper.Get("ApplicationName") ?? "应用程序",
						MessageBoxButton.YesNo,
						MessageBoxImage.Question) != MessageBoxResult.Yes)
						continue;

					Data.Remove(item);
				}

				Data.Add(item);
			}

			await MessageDialog.ShowDialog(StringHelper.Get("HUDPage_Import_Message") ?? "导入完成");
		}
		catch (Exception ex)
		{
			await MessageDialog.ShowDialog($"导入失败: {ex.Message}");
		}
	}

	[RelayCommand]
	async Task Export()
	{
		var dialog = new SaveFileDialog()
		{
			Filter = "JavaScript Object Notation|*.json",
			FileName = "hud_config"
		};
		if (dialog.ShowDialog() != true) return;

		try
		{
			var json = JsonConvert.SerializeObject(Data, Formatting.Indented);
			File.WriteAllText(dialog.FileName, json);
			await MessageDialog.ShowDialog(StringHelper.Get("HUDPage_Export_Message") ?? "导出完成");
		}
		catch (Exception ex)
		{
			await MessageDialog.ShowDialog($"导出失败: {ex.Message}");
		}
	}

	[RelayCommand]
	async Task Reset()
	{
		if (MessageBox.Show(
			StringHelper.Get("HUDPage_Reset_Confirm") ?? "确定要重置所有界面配置吗？此操作不可撤销。",
			StringHelper.Get("ApplicationName") ?? "应用程序",
			MessageBoxButton.YesNo,
			MessageBoxImage.Warning) != MessageBoxResult.Yes)
			return;

		try
		{
			Data.Clear();

			// 重新加载默认配置
			var newData = HUDCollection.Load();
			foreach (var item in newData)
			{
				Data.Add(item);
			}

			await MessageDialog.ShowDialog(StringHelper.Get("HUDPage_Reset_Message") ?? "重置完成");
		}
		catch (Exception ex)
		{
			await MessageDialog.ShowDialog($"重置失败: {ex.Message}");
		}
	}
	#endregion
}
