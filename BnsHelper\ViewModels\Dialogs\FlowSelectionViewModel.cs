using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using Xylia.BnsHelper.Models.Api;

namespace BnsHelper.ViewModels.Dialogs;
public partial class FlowSelectionViewModel : ObservableObject
{
    #region Fields
    [ObservableProperty]
    private ObservableCollection<FlowGroup> _flowGroups = new();

    [ObservableProperty]
    private FlowGroup? _selectedGroup;

    [ObservableProperty]
    private ActivityFlow? _selectedFlow;

    [ObservableProperty]
    private bool _isLoading;

    [ObservableProperty]
    private string _title = "选择要执行的流程";

    private ActivityInfo? _activity;
    #endregion

    #region Properties
    /// <summary>
    /// 是否可以确认选择
    /// </summary>
    public bool CanConfirm => SelectedFlow != null;
    #endregion

    #region Commands
    [RelayCommand]
    private void Confirm()
    {
        if (SelectedFlow != null)
        {
            OnFlowSelected?.Invoke(SelectedFlow);
        }
    }

    [RelayCommand]
    private void Cancel()
    {
        OnCancelled?.Invoke();
    }
    #endregion

    #region Events
    /// <summary>
    /// 流程选择事件
    /// </summary>
    public event Action<ActivityFlow>? OnFlowSelected;

    /// <summary>
    /// 取消事件
    /// </summary>
    public event Action? OnCancelled;
    #endregion

    #region Methods
    /// <summary>
    /// 初始化流程组数据
    /// </summary>
    /// <param name="activity">活动数据</param>
    public void Initialize(ActivityInfo activity)
    {
        _activity = activity;
        Title = $"选择要执行的流程 - {activity.ActivityName}";
        
        LoadFlowGroups();
    }

    /// <summary>
    /// 加载流程组数据
    /// </summary>
    private void LoadFlowGroups()
    {
        if (_activity?.Flows == null) return;

        FlowGroups.Clear();

        // 按分组整理流程
        var groupedFlows = _activity.Flows
            .Where(f => f.Value.Group > 0) // 只显示有分组的流程
            .GroupBy(f => f.Value.Group)
            .OrderBy(g => g.Key);

        foreach (var group in groupedFlows)
        {
            var flowGroup = new FlowGroup
            {
                GroupId = group.Key,
                //GroupName = group.First().Value.GroupName ?? $"流程组{group.Key}",
                Flows = new ObservableCollection<ActivityFlow>(group.OrderBy(f => f.Value.SortOrder).Select(f => f.Value))
            };

            FlowGroups.Add(flowGroup);
        }

        // 默认选择第一个分组
        SelectedGroup = FlowGroups.FirstOrDefault();
    }

    /// <summary>
    /// 当选中分组改变时
    /// </summary>
    partial void OnSelectedGroupChanged(FlowGroup? value)
    {
        // 清除之前的流程选择
        SelectedFlow = null;
        
        // 通知属性变化
        OnPropertyChanged(nameof(CanConfirm));
    }

    /// <summary>
    /// 当选中流程改变时
    /// </summary>
    partial void OnSelectedFlowChanged(ActivityFlow? value)
    {
        // 通知属性变化
        OnPropertyChanged(nameof(CanConfirm));
    }
    #endregion
}

/// <summary>
/// 流程组数据模型
/// </summary>
public partial class FlowGroup : ObservableObject
{
    [ObservableProperty]
    private uint _groupId;

    [ObservableProperty]
    private string _groupName = string.Empty;

    [ObservableProperty]
    private ObservableCollection<ActivityFlow> _flows = new();
}
