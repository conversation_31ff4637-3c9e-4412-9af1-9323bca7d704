package model

import (
	"time"
)

// RiskEvent 风险事件模型
type RiskEvent struct {
	ID          uint       `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	EventType   string     `gorm:"column:event_type;size:50;index" json:"event_type"`   // 事件类型
	DeviceID    string     `gorm:"column:device_id;size:64;index" json:"device_id"`     // 设备ID
	IPAddress   string     `gorm:"column:ip_address;size:45;index" json:"ip_address"`   // IP地址
	QQNumbers   string     `gorm:"column:qq_numbers;size:1000" json:"qq_numbers"`       // 涉及的QQ号列表（逗号分隔）
	Count       int        `gorm:"column:count" json:"count"`                           // 触发次数
	Severity    string     `gorm:"column:severity;size:20" json:"severity"`             // 严重程度：low, medium, high, critical
	Status      string     `gorm:"column:status;size:20;default:pending" json:"status"` // 处理状态：pending, approved, rejected, ignored
	Description string     `gorm:"column:description;size:500" json:"description"`      // 事件描述
	ProcessedBy *uint64    `gorm:"column:processed_by;index" json:"processed_by"`       // 处理管理员ID
	ProcessedAt *time.Time `gorm:"column:processed_at" json:"processed_at"`             // 处理时间
	CreatedAt   time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`  // 创建时间
	UpdatedAt   time.Time  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`  // 更新时间
}

// TableName 指定表名
func (RiskEvent) TableName() string {
	return "risk_events"
}

// RiskControlConfig 风控配置模型
type RiskControlConfig struct {
	ID          uint64    `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	ConfigKey   string    `gorm:"column:config_key;size:100;uniqueIndex:uk_config_key;not null" json:"config_key"`
	ConfigValue string    `gorm:"column:config_value;size:500;not null" json:"config_value"`
	Description string    `gorm:"column:description;size:200" json:"description"`
	CreatedAt   time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName 指定表名
func (RiskControlConfig) TableName() string {
	return "risk_control_config"
}

// ==================== 风控接口结构 ==================== //

// 高德地图IP定位API响应结构
type AmapIPResponse struct {
	Status    string `json:"status"`
	Info      string `json:"info"`
	InfoCode  string `json:"infocode"`
	Province  string `json:"province"`
	City      string `json:"city"`
	AdCode    string `json:"adcode"`
	Rectangle string `json:"rectangle"`
}
