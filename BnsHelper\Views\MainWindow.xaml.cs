﻿using System.ComponentModel;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Interop;
using System.Windows.Threading;
using Vanara.PInvoke;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.Services;
using Xylia.BnsHelper.ViewModels;
using Xylia.BnsHelper.Views.Pages;
using Xylia.Preview.Common.Extension;
using Xylia.Preview.UI.Views.Dialogs;

namespace Xylia.BnsHelper.Views;
public partial class MainWindow
{
    #region Constructor	   
    readonly MainWindowViewModel _viewModel;

    public MainWindow()
    {
        InitializeComponent();
        DataContext = _viewModel = MainWindowViewModel.Instance;
        _viewModel.SessionStateChanged += OnSessionStateChanged;
        _viewModel.DamageMeterPermissionChanged += (_, _) => CheckAndCloseDamageMeterPanel();

        #region Navigation
        Navigation.ItemsSource = new List<IPageController>()
        {
            new PageController<HomePage>("HomeGeometry"),
            new PageController<ActivityPage>("GiftGeometry",1),
            new PageController<AssetPage>("PaintGeometry", 2),
            new PageController<EffectPage>("FilterGeometry", 1),
            new PageController<DamageMeterPanel>("ChartBarGeometry", 2),
            new PageController<TriggerManagerPage>("ToolBoxGeometry"),
#if DEBUG
            new PageController<HUDPage>("ChartBarGeometry"),
            new PageController<ToolBoxPage>("ToolBoxGeometry"),
#endif
        };
        Navigation.SelectedIndex = 0;
        #endregion

        #region Music
        Task.Run(async () =>
        {
            var last = DateTimeOffset.FromUnixTimeSeconds(SettingHelper.Default.Time);
            if (DateTime.Now.Year != last.Year && DateTime.Now.Month == 4 && DateTime.Now.Day == 1)
            {
                await AudioSteamReader.Play($"Xylia.BnsHelper.Resources.Musics.alarm.mp3");
                SettingHelper.Default.Time = DateTimeOffset.Now.ToUnixTimeSeconds();
            }
        });
        #endregion
    }
    #endregion

    #region Methods
    protected override void OnClosing(CancelEventArgs e)
    {
        if (SettingHelper.Default.UseNotifyIcon && !_viewModel.OnShutdown)
        {
            e.Cancel = true;
            Hide();
            MainWindowViewModel.SendBalloonTip(StringHelper.Get("Application_Minimum"));
        }
        else
        {
            base.OnClosing(e);
        }
    }

    private void OnLoaded(object sender, EventArgs args)
    {
        new UserAgreementService().Register();

        // server latency
        var timer = new DispatcherTimer(DispatcherPriority.SystemIdle, Dispatcher)
        {
            Interval = TimeSpan.FromSeconds(1)
        };
        timer.Tick += UpdateData;
        timer.Start();
    }

    private void OpenSetting(object sender, RoutedEventArgs e)
    {
        Presenter.Content = new SettingPage();
        Navigation.SelectedIndex = -1;
        _viewModel.IsSettingPage = true;
    }

    private void OpenAnnouncement(object sender, RoutedEventArgs e)
    {
        try
        {
            var announcementWindow = new AnnouncementWindow
            {
                Owner = this
            };
            announcementWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] Failed to open announcement window: {ex.Message}");
            HandyControl.Controls.Growl.Error($"打开公告窗口失败: {ex.Message}");
        }
    }

    private void UpdateData(object? sender, EventArgs e)
    {
        try
        {
            // Refresh only in the foreground state
            if (User32.GetForegroundWindow() == new WindowInteropHelper(this).Handle)
            {
                _viewModel.UserCount = Application.Current.Properties["user-count"].To<int>();
                SettingHelper.Default.Server?.UpdateLatency();
            }
        }
        catch
        {

        }
    }

    /// <summary>
    /// 导航栏事件
    /// </summary>
    private async void Navigation_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        // 重置设置页选择状态
        _viewModel.IsSettingPage = false;

        var page = (IPageController)Navigation.SelectedItem;
        if (page is null) return;

        bool gotoHome = true;

        try
        {
            // 检查功能是否需要登录使用
            if (!page.CheckLogin)
            {
                Presenter.Content = null;
                if (await MessageDialog.ShowDialog(
                    StringHelper.Get("UserLogin_RequsetLoginTip" + page.RequiredPermission),  // 根据权限要求显示不同的提示消息
                    StringHelper.Get("UserLogin_RequsetLogin"), MessageBoxButton.OKCancel) != MessageBoxResult.OK) return;

                // 判断是否登录成功
                if (await _viewModel.Login() == false || !page.CheckLogin) return;
            }

            // 初始化处理
            await page.Initialize();

            var content = page.Content;
            if (content is Window window)
            {
                window.Closed += (s, e) => page.Content = null;
                window.Show();
                window.Activate();
            }
            else if (content is FrameworkElement element)
            {
                element.DataContext ??= this.DataContext;
                Presenter.Content = element;
                gotoHome = false;

                // 如果是 HomePage，触发刷新以重启动画
                if (element is HomePage homePage)
                {
                    // 使用 Dispatcher 延迟执行，确保页面已完全加载
                    await Dispatcher.BeginInvoke(() => homePage.Refresh(this, EventArgs.Empty), DispatcherPriority.Loaded);
                }
            }
        }
        finally
        {
            if (gotoHome) Navigation.SelectedIndex = 0;
        }
    }

    /// <summary>
    /// 处理用户会话状态变化
    /// </summary>
    private void OnSessionStateChanged(object? sender, bool isLoggedIn)
    {
        // 处理当前分页：如果需要权限则跳转到首页
        if (Navigation.SelectedItem is IPageController currentPage && !currentPage.CheckLogin)
        {
            Presenter.Content = null;
            Navigation.SelectedIndex = 0;
        }

        // 检查战斗统计面板权限：如果用户登出或权限不足则关闭
        CheckAndCloseDamageMeterPanel();
    }

    /// <summary>
    /// 检查并关闭战斗统计面板（如果权限不足）
    /// </summary>
    private void CheckAndCloseDamageMeterPanel()
    {
        if (_viewModel.CanOpenDamageMeter) return;

        // 使用Dispatcher.BeginInvoke安全关闭窗口
        Dispatcher.BeginInvoke(() =>
        {
            try
            {
                var damageMeter = DamageMeterPanel.Instance;
                if (damageMeter.IsVisible) damageMeter.Close();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error closing DamageMeterPanel: {ex.Message}");
            }
        });
    }

    /// <summary>
    /// 用户菜单打开时的事件处理
    /// </summary>
    private void ContextMenu_Opened(object sender, RoutedEventArgs e)
    {
        if (_viewModel.User != null)
        {
            _viewModel.User.CheckAndRefreshSignInStatusIfNeeded();
            _viewModel.User.RefreshDisplayTexts();
        }
    }
    #endregion
}
