﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections;
using System.Collections.ObjectModel;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Threading;
using Vanara.PInvoke;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Properties;
using Xylia.BnsHelper.Services.Network;
using Xylia.BnsHelper.Services.Network.Plugin;
using Xylia.Preview.Data.Engine.DatData;
using Xylia.Preview.Data.Models;

namespace Xylia.BnsHelper.ViewModels;
internal partial class DamageMeterViewModel : ObservableObject, IDisposable
{
    #region Fields
    internal static EventHandler? OnRefresh;

    private readonly DispatcherTimer Timer;
    private PluginSession? Service;
    private readonly DispatcherTimer _delayedRefreshTimer;
    private bool _refreshPending = false;
    private DateTime LastAutoPauseTime;

    [ObservableProperty] CombatCollection _players = [];
    #endregion

    #region Properties
    // Status
    public enum StatusType
    {
        Wait,
        Work,
        Pause,
        PauseAuto
    }

    StatusType _status;
    public StatusType Status
    {
        get => _status;
        set
        {
            if (_status == value) return;
            switch (value)
            {
                case StatusType.Wait:
                    Timer.Stop();
                    Reset();
                    break;
                case StatusType.Work:
                {
                    // 自动暂停的自动重置
                    if (_status == StatusType.Pause || _status == StatusType.PauseAuto)
                    {
                        Reset();
                    }

                    // 读图过程中会出现多线程异常，因此此时不要发送消息
                    if (!Timer.IsEnabled) Timer.Start();
                    break;
                }
                case StatusType.Pause:
                case StatusType.PauseAuto:
                {
                    // 暂停时保存当前数据并停止定时器
                    Timer.Stop();
                    ScheduleDelayedRefresh();

                    Players.Save(Zone);
                    OnPropertyChanged(nameof(History));
                    break;
                }
            }

            SetProperty(ref _status, value);
            OnPropertyChanged(nameof(IsWork));
            OnPropertyChanged(nameof(IsPause));
        }
    }

    public bool IsWork => Status == StatusType.Work || Status == StatusType.Wait;
    public bool IsPause => Status == StatusType.Pause || Status == StatusType.PauseAuto;

    // Statistics Type
    StatisticsType _statisticsType = StatisticsType.Damage;
    public StatisticsType StatisticsType
    {
        get => _statisticsType;
        set
        {
            if (SetProperty(ref _statisticsType, value))
            {
                OnPropertyChanged(nameof(StatisticsTypeDisplayName));

                // 更新Players集合的统计类型
                Players.StatisticsType = value;

                // 触发界面刷新
                OnPropertyChanged(nameof(Players));
            }
        }
    }

    public string StatisticsTypeDisplayName => StatisticsType switch
    {
        StatisticsType.Damage => "伤害输出",
        StatisticsType.DamageTaken => "承受伤害",
        StatisticsType.Healing => "治疗统计",
        _ => "伤害输出"
    };

    public bool GroupMode
    {
        get => SettingHelper.Default.GroupMode;
        set
        {
            SettingHelper.Default.GroupMode = value;
            OnPropertyChanged(nameof(History));
        }
    }

    public IEnumerable History
    {
        get
        {
            var data = new List<HistoryData>();
            var logs = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");

            // 检查logs目录是否存在
            if (Directory.Exists(logs))
            {
                foreach (var file in new DirectoryInfo(logs).GetFiles("act*.json"))
                {
                    try
                    {
                        data.Add(HistoryData.Load(file));
                    }
                    catch
                    {
                        Debug.Fail(file.FullName);
                    }
                }
            }

            // 返回按时间或区域分组的历史数据
            return HistoryGroup.GroupBy<object>(data, o =>
            {
                if (GroupMode) return o.Zone;

                var date = (DateTime.Now - o.Time).TotalDays;
                if (date <= 1) return HistoryType.Today;
                else if (date <= 7) return HistoryType.Week;
                else return HistoryType.Other;
            });
        }
    }

    bool _isHitVisible = true;
    public bool IsHitTestVisible
    {
        get => _isHitVisible;
        set => SetProperty(ref _isHitVisible, value);
    }

    int _page;
    public int Page
    {
        get => _page;
        set => SetProperty(ref _page, value);
    }


    // 当前区域
    public int Zone { get; set; }

    int _currentChannel = 0; // 之前的频道号
    public int CurrentChannel
    {
        get => _currentChannel;
        set
        {
            // 切换频道时清除统计数据（排除初始化时的设置）
            var oldChannel = _currentChannel;
            if (oldChannel != 0 && value != 0 && oldChannel != value)
            {
                Status = StatusType.Wait;
                Debug.WriteLine($"[DamageMeter] 切换频道 {oldChannel} -> {value}，清除统计数据");
            }
            SetProperty(ref _currentChannel, value);
        }
    }
    #endregion

    #region Target
    // Target selection
    private TargetOption? _selectedTarget;
    public TargetOption? SelectedTarget
    {
        get => _selectedTarget;
        set
        {
            if (SetProperty(ref _selectedTarget, value))
            {
                OnPropertyChanged(nameof(CurrentTargetName));
            }
        }
    }

    public IEnumerable<TargetOption> TargetOptions
    {
        get
        {
            var targetNames = GetValidTargetNames();
            if (targetNames.Length == 0)
            {
                return [new TargetOption { Name = "无目标", IsNoTarget = true }];
            }

            // 添加"全部目标"选项
            List<TargetOption> options = [CreateAllTargetsOption(targetNames), .. targetNames.Select(CreateTargetOption)];
            return options;
        }
    }

    public string CurrentTargetName
    {
        get
        {
            if (SelectedTarget == null || SelectedTarget.IsAllTargets)
            {
                return GetDisplayNameForAllTargets();
            }

            return SelectedTarget.OriginalName ?? SelectedTarget.Name;
        }
    }

    /// <summary>
    /// 计算目标的战斗时间
    /// </summary>
    private double CalculateTargetSeconds(string? targetName)
    {
        // 全部目标：使用整体战斗时间
        if (string.IsNullOrEmpty(targetName))
        {
            return Players.StartTime.HasValue ?
                Math.Round((Players.EndTime - Players.StartTime.Value).TotalSeconds, 0) : 0;
        }

        // 单个目标：计算该目标的战斗时间
        var targetSkills = Players.GetSkillsForTarget(targetName).ToList();
        if (targetSkills.Count != 0)
        {
            var startTime = targetSkills.Where(s => s.StartTime.HasValue).Min(s => s.StartTime);
            var endTime = targetSkills.Max(s => s.EndTime);
            return startTime.HasValue ? Math.Round((endTime - startTime.Value).TotalSeconds, 0) : 0;
        }

        return 0;
    }

    /// <summary>
    /// 获取当前选中目标的实时战斗时间
    /// </summary>
    public double CurrentTargetSeconds
    {
        get
        {
            // 当没有选择目标时（默认状态）或选择了"全部目标"时，显示整体战斗时间
            if (SelectedTarget == null || SelectedTarget.IsAllTargets)
            {
                return CalculateTargetSeconds(null);
            }

            // 对于单个目标，当目标名称超过10个字符时不显示秒数
            if (CurrentTargetName.Length > 10) return 0;

            if (!SelectedTarget.IsNoTarget && !string.IsNullOrEmpty(SelectedTarget.OriginalName))
            {
                return CalculateTargetSeconds(SelectedTarget.OriginalName);
            }

            return 0;
        }
    }

    /// <summary>
    /// 获取有效目标名称列表（优先显示BOSS级NPC，然后显示其他目标）
    /// </summary>
    private string[] GetValidTargetNames()
    {
        var allTargets = Players.GetTargetNames()
            .Where(t => Players.GetTotalDamageForTarget(t) > 0)
            .ToArray();

        // 分离BOSS级NPC和其他目标
        var bossTargets = allTargets.Where(t => IsBossTarget(t))
            .OrderByDescending(t => Players.GetTotalDamageForTarget(t));

        var otherTargets = allTargets.Where(t => !IsBossTarget(t))
            .OrderByDescending(t => Players.GetTotalDamageForTarget(t));

        // 优先显示BOSS级NPC，然后显示其他目标
        return bossTargets.Concat(otherTargets).ToArray();
    }

    /// <summary>
    /// 获取所有目标的显示名称（优先显示BOSS目标）
    /// </summary>
    private string GetDisplayNameForAllTargets()
    {
        var targetNames = GetValidTargetNames();
        if (targetNames.Length == 0) return "无目标";

        // 分离BOSS级别目标和其他目标
        var bossTargets = targetNames.Where(t => IsBossTarget(t)).ToArray();

        // 优先显示BOSS目标，如果没有BOSS则显示全部目标
        var displayTargets = bossTargets.Length > 0 ? bossTargets : targetNames;

        return string.Join(", ", displayTargets);
    }

    /// <summary>
    /// 创建全部目标选项（优先显示BOSS目标，支持多BOSS和混战）
    /// </summary>
    private TargetOption CreateAllTargetsOption(string[] targetNames)
    {
        // 分离BOSS级别目标和其他目标
        var bossTargets = targetNames.Where(t => IsBossTarget(t)).ToArray();
        var otherTargets = targetNames.Where(t => !IsBossTarget(t)).ToArray();

        // 确定显示策略和前缀
        string[] displayTargets;
        long totalDamage;

        if (bossTargets.Length > 0)
        {
            // 有BOSS时，优先显示BOSS，但统计包含所有目标的伤害
            displayTargets = bossTargets;
            totalDamage = Players.TotalDamage; // 包含所有目标的总伤害
        }
        else
        {
            // 没有BOSS时，显示全部目标
            displayTargets = targetNames;
            totalDamage = Players.TotalDamage;
        }

        double totalSeconds = CalculateTargetSeconds(null);
        string allTargetsName = $"{string.Join(", ", displayTargets)} ({totalSeconds:F0}秒)";

        return new TargetOption
        {
            Name = allTargetsName,
            TotalDamage = totalDamage,
            Seconds = totalSeconds,
            IsAllTargets = true
        };
    }

    /// <summary>
    /// 创建单个目标选项
    /// </summary>
    private TargetOption CreateTargetOption(string targetName)
    {
        var targetDamage = Players.GetTotalDamageForTarget(targetName);
        var seconds = CalculateTargetSeconds(targetName);

        return new TargetOption
        {
            Name = $"{targetName} ({seconds:F0}秒)",
            OriginalName = targetName,
            TotalDamage = targetDamage,
            Seconds = seconds,
        };
    }

    public partial class TargetOption : ObservableObject
    {
        [ObservableProperty] private string _name = "";
        [ObservableProperty] private string? _originalName;
        [ObservableProperty] private long _totalDamage;
        [ObservableProperty] private double _seconds;

        /// <summary>
        /// 标识是否为"全部目标"选项
        /// </summary>
        public bool IsAllTargets { get; set; }

        /// <summary>
        /// 标识是否为"无目标"选项
        /// </summary>
        public bool IsNoTarget { get; set; }
    }


    // BOSS级NPC名称缓存
    private static readonly Lazy<HashSet<string>> _bossNpcNames = new(() => LoadBossNpcNames());
    private static HashSet<string> BossNpcNames => _bossNpcNames.Value;

    /// <summary>
    /// 加载BOSS级NPC名称
    /// </summary>
    private static HashSet<string> LoadBossNpcNames()
    {
        try
        {
            // 直接获取所有NPC名称并转换为HashSet进行快速查找
            var bossNames = ResourceProvider.Instance.Provider.GetTable<Npc>()
                .Select(x => x.Name)
                .Where(name => !string.IsNullOrEmpty(name))
                .ToHashSet(StringComparer.OrdinalIgnoreCase); // 忽略大小写比较

            return bossNames;
        }
        catch (Exception ex)
        {
            return new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        }
    }

    /// <summary>
    /// 检查目标是否为BOSS级NPC
    /// </summary>
    public static bool IsBossTarget(string targetName)
    {
        return !string.IsNullOrEmpty(targetName) && BossNpcNames.Contains(targetName);
    }
    #endregion

    #region Methods
    public DamageMeterViewModel()
    {
        Players.Clear();

        // 刷新定时器
        Timer = new DispatcherTimer(new TimeSpan(TimeSpan.TicksPerMillisecond * 500), DispatcherPriority.Render, Refresh, Application.Current.Dispatcher);
        _delayedRefreshTimer = new DispatcherTimer(new TimeSpan(TimeSpan.TicksPerMillisecond * 500), DispatcherPriority.Render, OnDelayedRefresh, Application.Current.Dispatcher);

        // Subscribe to all targets dead event
        Players.AllTargetsDead += OnAllTargetsDead;
    }

    public void Initialize()
    {
        Service = PluginSession.GetOrCreate(WindowHelper.GetGameWindow());
        Service.PacketReceived += OnReceived;

        // 通信成功后立即查询一次角色信息
        Service.Send(new QueryWorld());
    }

    private void OnReceived(object? sender, IPacket packet)
    {
        switch (packet)
        {
            case QueryWorld i:
            {
                lock (Players)
                {
                    // 切换地区立即初始化
                    Zone = i.ZoneId;
                    Status = StatusType.Wait;
                    Players.Player = null;
                    Reset();

                    if (i.Player != null)
                    {
                        // 更新默认玩家信息，并同步更新缓存
                        Players.UpdateDefaultPlayer(i.Player);

                        // 获取服务器ID用于BOSS倒计时器
                        _currentServerId = i.Player.world;
                        Debug.WriteLine($"[BossTimer] 更新服务器ID: {_currentServerId} ({i.Player.World})");
                    }
                }
                break;
            }
            case EnterChannel i:
            {
                // 设置待确认频道，等待后续消息确认是否切换成功
                _pendingChannel = i.Channel;
                Debug.WriteLine($"[BossTimer] 尝试进入频道: {i.Channel}");
                break;
            }
            case KeyInput i when i.Key == User32.VK.VK_F1: SwitchStauts(); break;
            case InstantNotification i: Parse(i.Text); break;
            case InstantEffectNotification i: Parse(i); break;
            case InstantEffectNotification2 i: Parse(i); break;
        }
    }

    private void Parse(string? message)
    {
        if (string.IsNullOrEmpty(message)) return;

        // BOSS倒计时器检测 - 在指定区域内才处理
        if (BossTimerZone.Contains(Zone))
        {
            CheckBossTimerMessages(message);
        }
    }

    private void Parse(InstantEffectNotification instant)
    {
        if (Status == StatusType.Pause) return;
        Players.Add(instant);
    }

    private void Parse(InstantEffectNotification2 instant)
    {
        // 检查是否触发统计
        if (Status == StatusType.Pause) return;
        if ((DateTime.Now - LastAutoPauseTime).TotalMilliseconds < 500) return;

        Status = StatusType.Work;

        // 检查是否为BOSS战斗，如果是且之前有非BOSS统计则重置
        CheckAndResetForBossBattle(instant);

        // 让 CombatCollection 处理所有逻辑
        Application.Current.Dispatcher.Invoke(() => Players.Add(instant));
    }

    /// <summary>
    /// 检查BOSS战斗并在必要时重置统计
    /// </summary>
    private void CheckAndResetForBossBattle(InstantEffectNotification2 instant)
    {
        // 检查当前事件是BOSS目标
        bool isCurrentEventBoss = !string.IsNullOrEmpty(instant.TargetName) && IsBossTarget(instant.TargetName);
        if (isCurrentEventBoss)
        {
            // 检查是否已经有BOSS目标在统计中
            // 如果没有时遇到BOSS，自动重置
            bool hasExistingBoss = Players.GetTargetNames().Any(IsBossTarget);
            if (!hasExistingBoss) Reset();
        }
    }

    /// <summary>
    /// 重置统计
    /// </summary>
    public void Reset()
    {
        // 重置集合，如果没有默认角色则请求
        Players.Clear();

        // Reset target selection
        SelectedTarget = null;

        // Refresh target options and related properties
        OnPropertyChanged(nameof(TargetOptions));
        OnPropertyChanged(nameof(CurrentTargetName));
        OnPropertyChanged(nameof(CurrentTargetSeconds)); // 重置秒数显示
    }

    /// <summary>
    /// 主计时器刷新
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void Refresh(object? sender, EventArgs e)
    {
        // auto encounter
        if (Players.LastCombatTime.Ticks != 0 &&
            (DateTime.Now - Players.LastCombatTime).TotalSeconds > SettingHelper.Default.AutoResetEncounter)
        {
            Timer.Stop();
            Status = StatusType.PauseAuto;
        }

        ScheduleDelayedRefresh();
    }

    /// <summary>
    /// 安排延迟刷新，避免频繁的UI更新
    /// </summary>
    private void ScheduleDelayedRefresh()
    {
        if (!_refreshPending)
        {
            _refreshPending = true;
            _delayedRefreshTimer.Stop();
            _delayedRefreshTimer.Start();
        }
    }

    /// <summary>
    /// 延迟刷新事件处理
    /// </summary>
    private void OnDelayedRefresh(object? sender, EventArgs e)
    {
        OnRefresh?.Invoke(this, EventArgs.Empty);

        _delayedRefreshTimer.Stop();
        _refreshPending = false;

        // 更新BossTimer
        UpdateBossTimers();

        // 更新当前选中目标的实时数据
        UpdateSelectedTargetData();

        // 批量更新UI，减少重绘次数
        OnPropertyChanged(nameof(TargetOptions));
        OnPropertyChanged(nameof(CurrentTargetName));
        OnPropertyChanged(nameof(CurrentTargetSeconds));
        OnPropertyChanged(nameof(BossTimers));
    }

    /// <summary>
    /// 处理所有目标死亡事件
    /// </summary>
    private void OnAllTargetsDead()
    {
        // 所有正在统计的目标都死亡了，停止记录
        LastAutoPauseTime = DateTime.Now;
        Status = StatusType.PauseAuto;
        Timer.Stop();

        Debug.WriteLine($"所有目标都已死亡，自动停止伤害统计");
        // 更新目标选项显示
        OnPropertyChanged(nameof(TargetOptions));
        OnPropertyChanged(nameof(CurrentTargetName));
    }

    /// <summary>
    /// 更新当前选中目标的实时数据
    /// </summary>
    private void UpdateSelectedTargetData()
    {
        if (SelectedTarget == null) return;

        if (SelectedTarget.IsAllTargets)
        {
            SelectedTarget.Seconds = CalculateTargetSeconds(null);
        }
        else if (!SelectedTarget.IsNoTarget && !string.IsNullOrEmpty(SelectedTarget.OriginalName))
        {
            SelectedTarget.Seconds = CalculateTargetSeconds(SelectedTarget.OriginalName);
        }
    }

    /// <summary>
    /// 销毁并回收对象
    /// </summary>
    public void Dispose()
    {
        OnRefresh = null;

        // 停止并清理主定时器
        Timer?.Stop();

        // 清理延迟刷新定时器
        if (_delayedRefreshTimer != null)
        {
            _delayedRefreshTimer.Stop();
            _delayedRefreshTimer.Tick -= OnDelayedRefresh;
        }

        // 取消事件订阅
        Players.AllTargetsDead -= OnAllTargetsDead;
        if (Service != null) Service.PacketReceived -= OnReceived;

        ClearAllBossTimers();
    }


    [RelayCommand]
    void ResetData()
    {
        Status = StatusType.Wait;
    }

    [RelayCommand]
    void SwitchStauts()
    {
        Status = Status switch
        {
            StatusType.Work => StatusType.Pause,
            StatusType.PauseAuto => StatusType.Pause, // 自动暂停状态切换到手动暂停，保持数据不清除
            _ => StatusType.Wait,
        };
    }

    [RelayCommand]
    void SetStatisticsType(string type)
    {
        if (Enum.TryParse<StatisticsType>(type, out var statisticsType))
        {
            StatisticsType = statisticsType;
        }
    }

    [RelayCommand]
    void SelectTarget(TargetOption? target)
    {
        SelectedTarget = target;

        // Update the target filter in Players collection
        if (target != null && !target.IsAllTargets && !target.IsNoTarget)
        {
            Players.TargetFilter = target.OriginalName;
        }
        else if (target != null && target.IsAllTargets)
        {
            // 对于"全部目标"，如果有BOSS则只显示BOSS相关统计
            var allTargets = GetValidTargetNames();
            var bossTargets = allTargets.Where(t => IsBossTarget(t)).ToArray();
            if (bossTargets.Length > 0)
            {
                // 有BOSS目标时，设置BOSS过滤器（支持多BOSS）
                Players.TargetFilter = string.Join(",", bossTargets);
            }
            else
            {
                // 没有BOSS目标时，清除过滤器显示全部
                Players.TargetFilter = null;
            }
        }
        else
        {
            Players.TargetFilter = null;
        }

        // 立即安排一次重绘
        ScheduleDelayedRefresh();
    }
    #endregion

    #region BossTimer
    private readonly object _bossTimerLock = new();
    private readonly Dictionary<int, BossTimer> _bossTimers = [];
    private readonly int[] BossTimerZone = [2000, 2300, 2440, 3010, 4000, 4250, 4302, 4400, 3086, 5200, 5295, 5500];

    public ObservableCollection<BossTimer> BossTimers { get; } = [];

    // Current Server ID for Boss Timer
    int _currentServerId = 0;
    int _pendingChannel = 0; // 待确认的频道号，用于处理频道切换失败的情况

    /// <summary>
    /// 检查BOSS倒计时器相关消息
    /// </summary>
    /// <param name="message">消息内容</param>
    private void CheckBossTimerMessages(string message)
    {
        try
        {
            Debug.WriteLine($"[BossTimer] 区域{Zone}频道{CurrentChannel}消息: {message}");

            // 检查频道切换失败消息
            if (message.Contains("切换频道失败") || message.Contains("无法切换频道"))
            {
                // 快捷键按太快的时候也会提示，需要排除
                if (message.Contains("9 秒后重试")) return;

                _pendingChannel = 0;
                Debug.WriteLine($"[BossTimer] 检测到频道切换失败消息: {message}");
                return;
            }
            else if (_pendingChannel > 0)
            {
                CurrentChannel = _pendingChannel;
                _pendingChannel = 0;
            }

            // 正则表达式：消灭了<arg p="2:npc.name2"/><eul/>，消耗了<arg p="3:integer"/> 点洪门庇护
            var bossKillRegex = new Regex(@"消灭了(.+?)，消耗了(\d+)\s*点洪门庇护", RegexOptions.Compiled);
            var bossKillMatch = bossKillRegex.Match(message);
            if (bossKillMatch.Success)
            {
                var bossName = bossKillMatch.Groups[1].Value.Trim();
                var isMutant = bossName.Contains("变异体");

                switch (BnsWorld.GetPublisher(_currentServerId))
                {
                    // 怀旧服白青山脉地区
                    case EPublisher.ZTX when Zone is 5200 or 5295 or 5500:
                        AddOrUpdateBossTimer(CurrentChannel, BossTimerType.ZTX_Normal2);
                        break;

                    // 怀旧服其他地区
                    case EPublisher.ZTX:
                        AddOrUpdateBossTimer(CurrentChannel, BossTimerType.ZTX_Normal);
                        break;

                    // NEO通用
                    case EPublisher.ZNCS:
                    case EPublisher.ZNCG:
                        AddOrUpdateBossTimer(CurrentChannel, isMutant ? BossTimerType.ZNCS_Mutant : BossTimerType.ZNCS_Normal);
                        break;
                }

                Debug.WriteLine($"[BossTimer] 检测到BOSS击杀: {bossName}, 频道: {CurrentChannel}, 服务器: {_currentServerId}, 区域: {Zone}, 变异体: {isMutant}");
            }

            // 检查不祥力量消息：<image imagesetpath="00027918.Portrait_Alert"/>不祥的力量开始笼罩。
            if (message.Contains("不祥的力量开始笼罩"))
            {
                AddOrUpdateBossTimer(CurrentChannel, BnsWorld.GetPublisher(_currentServerId) == EPublisher.ZTX ? BossTimerType.ZTX_MutantAlarm : BossTimerType.ZNCS_MutantAlarm);
                Debug.WriteLine($"[BossTimer] 检测到不祥力量: 频道: {CurrentChannel}");
            }
        }
        catch (Exception ex)
        {
        }
    }

    /// <summary>
    /// 更新所有BossTimer
    /// </summary>
    private void UpdateBossTimers()
    {
        lock (_bossTimerLock)
        {
            // 更新所有活跃倒计时器
            foreach (var timer in _bossTimers.Values)
            {
                if (!timer.IsExpired)
                {
                    timer.NotifyPropertyChanged();
                }
            }

            // 移除已过期的计时器
            var expiredChannels = _bossTimers.Where(kvp => kvp.Value.IsExpired).Select(kvp => kvp.Key).ToList();
            foreach (var channel in expiredChannels)
            {
                _bossTimers.Remove(channel);
                Debug.WriteLine($"[BossTimer] 倒计时器已过期: 频道{channel}");
            }

            // 在UI线程上移除过期的UI元素和重新排序
            if (expiredChannels.Count > 0 || BossTimers.Count > 1)
            {
                // 移除过期的UI元素
                foreach (var channel in expiredChannels)
                {
                    var uiTimer = BossTimers.FirstOrDefault(t => t.Channel == channel);
                    if (uiTimer != null) BossTimers.Remove(uiTimer);
                }

                // 重新排序倒计时器
                SortBossTimers();
            }
        }
    }

    /// <summary>
    /// 排序BossTimer列表（必须在UI线程上调用）
    /// </summary>
    private void SortBossTimers()
    {
        if (BossTimers.Count <= 1) return;

        var sortedTimers = BossTimers
            .OrderBy(t => t.State == BossTimerState.Finished ? 1 : 0)
            .ThenBy(t => t.RemainingTime)
            .ToList();

        BossTimers.Clear();
        foreach (var timer in sortedTimers)
        {
            BossTimers.Add(timer);
        }
    }

    /// <summary>
    /// 添加或更新BOSS倒计时器
    /// </summary>
    private void AddOrUpdateBossTimer(int channel, BossTimerType type)
    {
        lock (_bossTimerLock)
        {
            // 检查是否存在该频道的旧计时器
            if (_bossTimers.TryGetValue(channel, out BossTimer? oldTimer))
            {
                // 如果旧计时器是高优先级类型，且新计时器不是，则不覆盖
                if (IsHighPriorityTimer(oldTimer.Type) && !IsHighPriorityTimer(type))
                {
                    Debug.WriteLine($"[BossTimer] 频道{channel}已有高优先级倒计时器({oldTimer.Type})，忽略低优先级计时器({type})");
                    return;
                }

                // 移除旧计时器
                _bossTimers.Remove(channel);
            }

            // 添加新计时器
            var newTimer = new BossTimer(channel, type);
            _bossTimers[channel] = newTimer;

            Application.Current.Dispatcher.Invoke(() =>
            {
                // 移除旧的UI计时器（如果存在）
                if (oldTimer != null)
                {
                    var uiTimer = BossTimers.FirstOrDefault(t => t.Channel == channel);
                    if (uiTimer != null) BossTimers.Remove(uiTimer);
                }

                // 添加新计时器
                BossTimers.Add(newTimer);
                SortBossTimers();
            });

            Debug.WriteLine($"[BossTimer] 添加倒计时器: 频道{channel}, 类型{type}, 持续{newTimer.TotalDuration}秒");
        }
    }

    /// <summary>
    /// 判断倒计时器类型是否为高优先级（不祥力量类型）
    /// </summary>
    private bool IsHighPriorityTimer(BossTimerType type)
    {
        return type is BossTimerType.ZNCS_MutantAlarm or BossTimerType.ZTX_MutantAlarm;
    }

    [RelayCommand]
    void ClearSingleBossTimer(BossTimer? timer)
    {
        if (timer == null) return;

        lock (_bossTimerLock)
        {
            _bossTimers.Remove(timer.Channel);
            BossTimers.Remove(timer);
        }
    }

    /// <summary>
    /// 清理所有计时器
    /// </summary>
    [RelayCommand]
    void ClearAllBossTimers()
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            lock (_bossTimerLock)
            {
                _bossTimers.Clear();
                BossTimers.Clear();
            }
        });
    }
    #endregion
}