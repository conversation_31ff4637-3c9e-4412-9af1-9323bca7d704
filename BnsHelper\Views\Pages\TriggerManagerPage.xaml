<Page x:Class="Xylia.BnsHelper.Views.Pages.TriggerManagerPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:hc="https://handyorg.github.io/handycontrol"
      xmlns:models="clr-namespace:Xylia.BnsHelper.Models.Triggers">

    <Page.Resources>
        <!-- 文件夹菜单 -->
        <ContextMenu x:Key="FolderMenu">
            <MenuItem Header="新建触发器" Command="{Binding DataContext.CreateTriggerCommand, RelativeSource={RelativeSource AncestorType=Page}}" CommandParameter="{Binding}" />
            <MenuItem Header="新建子文件夹" Command="{Binding DataContext.CreateSubFolderCommand, RelativeSource={RelativeSource AncestorType=Page}}" CommandParameter="{Binding}" />
            <Separator />

            <!-- 通用菜单 -->
            <MenuItem Header="复制" Command="{Binding DataContext.CopyItemCommand, RelativeSource={RelativeSource AncestorType=Page}}" CommandParameter="{Binding}"/>
            <MenuItem Header="粘贴" Command="{Binding DataContext.PasteItemCommand, RelativeSource={RelativeSource AncestorType=Page}}" CommandParameter="{Binding}"/>
            <MenuItem Header="删除" Command="{Binding DataContext.DeleteItemCommand, RelativeSource={RelativeSource AncestorType=Page}}" CommandParameter="{Binding}"/>
            <Separator/>
            <MenuItem Header="禁用" Command="{Binding DataContext.ToggleItemCommand, RelativeSource={RelativeSource AncestorType=Page}}" CommandParameter="{Binding}" Visibility="{Binding IsEnabled,Converter={StaticResource Boolean2VisibilityConverter}}" />
            <MenuItem Header="启用" Command="{Binding DataContext.ToggleItemCommand, RelativeSource={RelativeSource AncestorType=Page}}" CommandParameter="{Binding}" Visibility="{Binding IsEnabled,Converter={StaticResource Boolean2VisibilityReConverter}}" />
        </ContextMenu>

        <!-- 触发器菜单 -->
        <ContextMenu x:Key="TriggerMenu">
            <MenuItem Header="编辑触发器" Command="{Binding DataContext.EditTriggerCommand, RelativeSource={RelativeSource AncestorType=Page}}" CommandParameter="{Binding}" />
            <MenuItem Header="执行触发器" Command="{Binding DataContext.TestTriggerCommand, RelativeSource={RelativeSource AncestorType=Page}}" CommandParameter="{Binding}" />
            <Separator />

            <!-- 通用菜单 -->
            <MenuItem Header="复制" Command="{Binding DataContext.CopyItemCommand, RelativeSource={RelativeSource AncestorType=Page}}" CommandParameter="{Binding}"/>
            <MenuItem Header="粘贴" Command="{Binding DataContext.PasteItemCommand, RelativeSource={RelativeSource AncestorType=Page}}" CommandParameter="{Binding}"/>
            <MenuItem Header="删除" Command="{Binding DataContext.DeleteItemCommand, RelativeSource={RelativeSource AncestorType=Page}}" CommandParameter="{Binding}"/>
            <Separator/>
            <MenuItem Header="禁用" Command="{Binding DataContext.ToggleItemCommand, RelativeSource={RelativeSource AncestorType=Page}}" CommandParameter="{Binding}" Visibility="{Binding IsEnabled,Converter={StaticResource Boolean2VisibilityConverter}}" />
            <MenuItem Header="启用" Command="{Binding DataContext.ToggleItemCommand, RelativeSource={RelativeSource AncestorType=Page}}" CommandParameter="{Binding}" Visibility="{Binding IsEnabled,Converter={StaticResource Boolean2VisibilityReConverter}}" />
        </ContextMenu>

        <!-- 文件夹模板 -->
        <HierarchicalDataTemplate DataType="{x:Type models:TriggerFolder}" ItemsSource="{Binding Nodes}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="{Binding NodeIcon}" Margin="0,0,5,0" />
                <TextBlock Text="{Binding Name}" FontWeight="Bold" />
            </StackPanel>
        </HierarchicalDataTemplate>

        <!-- 触发器模板 -->
        <DataTemplate DataType="{x:Type models:Trigger}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="⚡" Foreground="Green" Margin="0,0,5,0" Visibility="{Binding IsEnabled,Converter={StaticResource Boolean2VisibilityConverter}}" />
                <TextBlock Text="🚫" Foreground="Gray" Margin="0,0,5,0" Visibility="{Binding IsEnabled,Converter={StaticResource Boolean2VisibilityReConverter}}" />

                <TextBlock Text="{Binding Name}"/>
                <TextBlock Text="{Binding ExecutionCount, StringFormat=' [{0}]'}" Foreground="{DynamicResource SecondaryTextBrush}" Margin="5,0,0,0"/>

                <TextBlock Text=" ❌" Foreground="Red" Margin="5,0,0,0" Visibility="{Binding HasError, Converter={StaticResource Boolean2VisibilityConverter}}" ToolTip="{Binding LastError}"/>
            </StackPanel>
        </DataTemplate>
    </Page.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="{DynamicResource SecondaryRegionBrush}" Padding="0 5 10 8">
            <StackPanel Orientation="Horizontal">
                <ToggleButton Content="启用管理器" IsChecked="{Binding IsEnabled}" Command="{Binding ToggleManagerCommand}" Margin="0,0,10,0" />
                <Button Content="导入" Command="{Binding ImportTriggersCommand}" Margin="0,0,5,0"/>
                <Button Content="导出" Command="{Binding ExportTriggersCommand}" Margin="0,0,5,0"/>
            </StackPanel>
        </Border>

        <!-- 触发器树状列表 -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 树状视图 -->
            <TreeView Grid.Row="1" Background="Transparent" BorderThickness="0" ItemsSource="{Binding RootFolders}" SelectedItemChanged="TreeView_SelectedItemChanged">
                <TreeView.ItemContainerStyle>
                    <Style TargetType="TreeViewItem" BasedOn="{StaticResource TreeViewItemBaseStyle}">
                        <Setter Property="IsExpanded" Value="{Binding IsExpanded, Mode=TwoWay}"/>
                        <Setter Property="FontWeight" Value="Normal"/>

                        <Style.Triggers>
                            <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                                <Setter Property="Foreground" Value="Gray"/>
                            </DataTrigger>

                            <!--- 根据类型显示对应的右键菜单 -->
                            <DataTrigger Binding="{Binding NodeType}" Value="Folder">
                                <Setter Property="ContextMenu" Value="{StaticResource FolderMenu}" />
                            </DataTrigger>
                            <DataTrigger Binding="{Binding NodeType}" Value="Trigger">
                                <Setter Property="ContextMenu" Value="{StaticResource TriggerMenu}" />
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </TreeView.ItemContainerStyle>
            </TreeView>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="{DynamicResource SecondaryRegionBrush}" Padding="10,5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" TextElement.Foreground="{DynamicResource PrimaryTextBrush}" >
                    <TextBlock Text="{Binding TotalTriggers, StringFormat='总触发器: {0}'}" Margin="0,0,20,0"/>
                    <TextBlock Text="{Binding EnabledTriggers, StringFormat='已启用: {0}'}" Margin="0,0,20,0"/>
                    <TextBlock Text="{Binding TotalExecutions, StringFormat='总执行: {0}'}" Margin="0,0,20,0"/>
                </StackPanel>

                <TextBlock Grid.Column="1" Text="{Binding StatusMessage}" Foreground="{DynamicResource SecondaryTextBrush}" />
            </Grid>
        </Border>
    </Grid>
</Page>
