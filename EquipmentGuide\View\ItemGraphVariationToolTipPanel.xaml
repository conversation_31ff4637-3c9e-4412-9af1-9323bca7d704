﻿<BnsCustomWindowWidget x:Class="Xylia.Preview.UI.GameUI.Scene.Game_Tooltip2.ItemGraphVariationToolTipPanel"
	xmlns="https://github.com/xyliaup/bns-preview-tools"
	xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	xmlns:s="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<BnsCustomWindowWidget.BaseImageProperty>
		<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_Window.BNSR_Window" ImageUV="7 7" ImageUVSize="49 49" EnableDrawImage="true" EnableSkinAlpha="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" StaticPadding="-17 -17" Opacity="1" />
	</BnsCustomWindowWidget.BaseImageProperty>
	<BnsCustomWindowWidget.ExpansionComponentList>
		<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Line" MetaData="">
			<UBnsCustomExpansionComponent.ImageProperty>
				<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_Window_Outfocus.BNSR_Window_Outfocus" EnableBrushOnly="true" ImageUV="7 7" ImageUVSize="49 49" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" StaticPadding="-13 -13" Opacity="1" />
			</UBnsCustomExpansionComponent.ImageProperty>
		</UBnsCustomExpansionComponent>
	</BnsCustomWindowWidget.ExpansionComponentList>
	<BnsCustomLabelWidget Name="ItemGraphVariationToolTipPanel_TitleLabel" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="15 15 15 22" MetaData="textref=UI.ItemGraphVariationTooltip.Title">
		<BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="15" Offset2="15" />
		</BnsCustomLabelWidget.HorizontalResizeLink>
		<BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Offset1="15" />
		</BnsCustomLabelWidget.VerticalResizeLink>
		<BnsCustomLabelWidget.String>
			<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_14.Normal_14" LabelText="장비 베리에이션#" SpaceBetweenLines="3" />
		</BnsCustomLabelWidget.String>
	</BnsCustomLabelWidget>
	<BnsCustomImageWidget Name="ItemGraphVariationToolTipPanel_Item_1" LayoutData.Offsets="22 47 40 40">
		<BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Offset1="22" />
		</BnsCustomImageWidget.HorizontalResizeLink>
		<BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphVariationToolTipPanel_TitleLabel" Offset1="10" />
		</BnsCustomImageWidget.VerticalResizeLink>
		<BnsCustomImageWidget.BaseImageProperty>
			<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" ImageScale="1.1" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
		</BnsCustomImageWidget.BaseImageProperty>
		<BnsCustomImageWidget.ExpansionComponentList>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
			</UBnsCustomExpansionComponent>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
				<UBnsCustomExpansionComponent.StringProperty>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Stackable_10.Stackable_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
				</UBnsCustomExpansionComponent.StringProperty>
			</UBnsCustomExpansionComponent>
		</BnsCustomImageWidget.ExpansionComponentList>
		<BnsCustomLabelWidget Name="ItemGraphVariationToolTipPanel_Item_1_Name" LayoutData.Offsets="47 0 208 40">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink Offset1="15" Offset2="50" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Item Name" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
	</BnsCustomImageWidget>
	<BnsCustomImageWidget Name="ItemGraphVariationToolTipPanel_Item_2" LayoutData.Offsets="22 97 40 40">
		<BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Offset1="22" />
		</BnsCustomImageWidget.HorizontalResizeLink>
		<BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphVariationToolTipPanel_Item_1" Offset1="10" />
		</BnsCustomImageWidget.VerticalResizeLink>
		<BnsCustomImageWidget.BaseImageProperty>
			<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" ImageScale="1.1" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
		</BnsCustomImageWidget.BaseImageProperty>
		<BnsCustomImageWidget.ExpansionComponentList>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
			</UBnsCustomExpansionComponent>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
				<UBnsCustomExpansionComponent.StringProperty>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Stackable_10.Stackable_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
				</UBnsCustomExpansionComponent.StringProperty>
			</UBnsCustomExpansionComponent>
		</BnsCustomImageWidget.ExpansionComponentList>
		<BnsCustomLabelWidget Name="ItemGraphVariationToolTipPanel_Item_2_Name" LayoutData.Offsets="47 0 208 40">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink Offset1="15" Offset2="50" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Item Name" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
	</BnsCustomImageWidget>
	<BnsCustomImageWidget Name="ItemGraphVariationToolTipPanel_Item_3" LayoutData.Offsets="22 147 40 40">
		<BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Offset1="22" />
		</BnsCustomImageWidget.HorizontalResizeLink>
		<BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphVariationToolTipPanel_Item_2" Offset1="10" />
		</BnsCustomImageWidget.VerticalResizeLink>
		<BnsCustomImageWidget.BaseImageProperty>
			<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" ImageScale="1.1" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
		</BnsCustomImageWidget.BaseImageProperty>
		<BnsCustomImageWidget.ExpansionComponentList>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
			</UBnsCustomExpansionComponent>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
				<UBnsCustomExpansionComponent.StringProperty>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Stackable_10.Stackable_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
				</UBnsCustomExpansionComponent.StringProperty>
			</UBnsCustomExpansionComponent>
		</BnsCustomImageWidget.ExpansionComponentList>
		<BnsCustomLabelWidget Name="ItemGraphVariationToolTipPanel_Item_3_Name" LayoutData.Offsets="47 0 208 40">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink Offset1="15" Offset2="50" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Item Name" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
	</BnsCustomImageWidget>
	<BnsCustomImageWidget Name="ItemGraphVariationToolTipPanel_Item_4" LayoutData.Offsets="22 197 40 40">
		<BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Offset1="22" />
		</BnsCustomImageWidget.HorizontalResizeLink>
		<BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphVariationToolTipPanel_Item_3" Offset1="10" />
		</BnsCustomImageWidget.VerticalResizeLink>
		<BnsCustomImageWidget.BaseImageProperty>
			<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" ImageScale="1.1" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
		</BnsCustomImageWidget.BaseImageProperty>
		<BnsCustomImageWidget.ExpansionComponentList>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
			</UBnsCustomExpansionComponent>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
				<UBnsCustomExpansionComponent.StringProperty>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Stackable_10.Stackable_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
				</UBnsCustomExpansionComponent.StringProperty>
			</UBnsCustomExpansionComponent>
		</BnsCustomImageWidget.ExpansionComponentList>
		<BnsCustomLabelWidget Name="ItemGraphVariationToolTipPanel_Item_4_Name" LayoutData.Offsets="47 0 201 40">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink Offset1="15" Offset2="50" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Item Name" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
	</BnsCustomImageWidget>
	<BnsCustomImageWidget Name="ItemGraphVariationToolTipPanel_Item_5" LayoutData.Offsets="22 247 40 40">
		<BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Offset1="22" />
		</BnsCustomImageWidget.HorizontalResizeLink>
		<BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphVariationToolTipPanel_Item_4" Offset1="10" />
		</BnsCustomImageWidget.VerticalResizeLink>
		<BnsCustomImageWidget.BaseImageProperty>
			<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" ImageScale="1.1" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
		</BnsCustomImageWidget.BaseImageProperty>
		<BnsCustomImageWidget.ExpansionComponentList>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
			</UBnsCustomExpansionComponent>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
				<UBnsCustomExpansionComponent.StringProperty>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Stackable_10.Stackable_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
				</UBnsCustomExpansionComponent.StringProperty>
			</UBnsCustomExpansionComponent>
		</BnsCustomImageWidget.ExpansionComponentList>
		<BnsCustomLabelWidget Name="ItemGraphVariationToolTipPanel_Item_5_Name" LayoutData.Offsets="47 0 208 40">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink Offset1="15" Offset2="50" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Item Name" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
	</BnsCustomImageWidget>
	<BnsCustomImageWidget Name="ItemGraphVariationToolTipPanel_Item_6" LayoutData.Offsets="22 297 40 40">
		<BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Offset1="22" />
		</BnsCustomImageWidget.HorizontalResizeLink>
		<BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphVariationToolTipPanel_Item_5" Offset1="10" />
		</BnsCustomImageWidget.VerticalResizeLink>
		<BnsCustomImageWidget.BaseImageProperty>
			<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" ImageScale="1.1" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
		</BnsCustomImageWidget.BaseImageProperty>
		<BnsCustomImageWidget.ExpansionComponentList>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
			</UBnsCustomExpansionComponent>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
				<UBnsCustomExpansionComponent.StringProperty>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Stackable_10.Stackable_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
				</UBnsCustomExpansionComponent.StringProperty>
			</UBnsCustomExpansionComponent>
		</BnsCustomImageWidget.ExpansionComponentList>
		<BnsCustomLabelWidget Name="ItemGraphVariationToolTipPanel_Item_6_Name" LayoutData.Offsets="47 0 208 40">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink Offset1="15" Offset2="50" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Item Name" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
	</BnsCustomImageWidget>
	<BnsCustomImageWidget Name="ItemGraphVariationToolTipPanel_Item_7" LayoutData.Offsets="22 347 40 40">
		<BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Offset1="22" />
		</BnsCustomImageWidget.HorizontalResizeLink>
		<BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphVariationToolTipPanel_Item_6" Offset1="10" />
		</BnsCustomImageWidget.VerticalResizeLink>
		<BnsCustomImageWidget.BaseImageProperty>
			<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" ImageScale="1.1" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
		</BnsCustomImageWidget.BaseImageProperty>
		<BnsCustomImageWidget.ExpansionComponentList>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
			</UBnsCustomExpansionComponent>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
				<UBnsCustomExpansionComponent.StringProperty>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Stackable_10.Stackable_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
				</UBnsCustomExpansionComponent.StringProperty>
			</UBnsCustomExpansionComponent>
		</BnsCustomImageWidget.ExpansionComponentList>
		<BnsCustomLabelWidget Name="ItemGraphVariationToolTipPanel_Item_7_Name" LayoutData.Offsets="47 0 208 40">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink Offset1="15" Offset2="50" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Item Name" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
	</BnsCustomImageWidget>
	<BnsCustomImageWidget Name="ItemGraphVariationToolTipPanel_Item_8" LayoutData.Offsets="22 397 40 40">
		<BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Offset1="22" />
		</BnsCustomImageWidget.HorizontalResizeLink>
		<BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphVariationToolTipPanel_Item_7" Offset1="10" />
		</BnsCustomImageWidget.VerticalResizeLink>
		<BnsCustomImageWidget.BaseImageProperty>
			<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" ImageScale="1.1" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
		</BnsCustomImageWidget.BaseImageProperty>
		<BnsCustomImageWidget.ExpansionComponentList>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
			</UBnsCustomExpansionComponent>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
				<UBnsCustomExpansionComponent.StringProperty>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Stackable_10.Stackable_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
				</UBnsCustomExpansionComponent.StringProperty>
			</UBnsCustomExpansionComponent>
		</BnsCustomImageWidget.ExpansionComponentList>
		<BnsCustomLabelWidget Name="ItemGraphVariationToolTipPanel_Item_8_Name" LayoutData.Offsets="47 0 208 40">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink Offset1="15" Offset2="50" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Item Name" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
	</BnsCustomImageWidget>
	<BnsCustomImageWidget Name="ItemGraphVariationToolTipPanel_Item_9" LayoutData.Offsets="22 447 40 40">
		<BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Offset1="22" />
		</BnsCustomImageWidget.HorizontalResizeLink>
		<BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphVariationToolTipPanel_Item_8" Offset1="10" />
		</BnsCustomImageWidget.VerticalResizeLink>
		<BnsCustomImageWidget.BaseImageProperty>
			<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" ImageScale="1.1" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
		</BnsCustomImageWidget.BaseImageProperty>
		<BnsCustomImageWidget.ExpansionComponentList>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
			</UBnsCustomExpansionComponent>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
				<UBnsCustomExpansionComponent.StringProperty>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Stackable_10.Stackable_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
				</UBnsCustomExpansionComponent.StringProperty>
			</UBnsCustomExpansionComponent>
		</BnsCustomImageWidget.ExpansionComponentList>
		<BnsCustomLabelWidget Name="ItemGraphVariationToolTipPanel_Item_9_Name" LayoutData.Offsets="47 0 208 40">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink Offset1="15" Offset2="50" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Item Name" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
	</BnsCustomImageWidget>
	<BnsCustomImageWidget Name="ItemGraphVariationToolTipPanel_Item_10" LayoutData.Offsets="22 497 40 40">
		<BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Offset1="22" />
		</BnsCustomImageWidget.HorizontalResizeLink>
		<BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphVariationToolTipPanel_Item_9" Offset1="10" />
		</BnsCustomImageWidget.VerticalResizeLink>
		<BnsCustomImageWidget.BaseImageProperty>
			<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" ImageScale="1.1" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
		</BnsCustomImageWidget.BaseImageProperty>
		<BnsCustomImageWidget.ExpansionComponentList>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
			</UBnsCustomExpansionComponent>
			<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
				<UBnsCustomExpansionComponent.ImageProperty>
					<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
				</UBnsCustomExpansionComponent.ImageProperty>
				<UBnsCustomExpansionComponent.StringProperty>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Stackable_10.Stackable_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
				</UBnsCustomExpansionComponent.StringProperty>
			</UBnsCustomExpansionComponent>
		</BnsCustomImageWidget.ExpansionComponentList>
		<BnsCustomLabelWidget Name="ItemGraphVariationToolTipPanel_Item_10_Name" LayoutData.Offsets="47 0 208 40">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink Offset1="15" Offset2="50" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Item Name" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
	</BnsCustomImageWidget>
</BnsCustomWindowWidget>