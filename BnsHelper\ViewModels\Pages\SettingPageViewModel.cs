﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using HandyControl.Tools.Extension;
using Microsoft.Win32;
using System.Diagnostics;
using System.IO;
using Xylia.BnsHelper.Common;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Resources;
using Xylia.Preview.Data.Engine.DatData;
using Xylia.Preview.UI.Views.Dialogs;

namespace Xylia.BnsHelper.ViewModels.Pages;
internal partial class SettingPageViewModel : ObservableObject
{
    #region Constructor
    public SettingPageViewModel()
    {
        // 监听游戏目录变化，更新插件版本显示
        SettingHelper.Default.GameDirectoryChanged += (sender, e) =>
        {
            OnPropertyChanged(nameof(PluginVersion));
        };
    }
    #endregion

    #region Properties
    /// <summary>
    /// Gets the default instance of the <see cref="SettingHelper"/> class.
    /// </summary>
    public SettingHelper Setting => SettingHelper.Default;

    /// <summary>
    /// Gets the default instance of the <see cref="RegistryHelper"/> class.
    /// </summary>
    public RegistryHelper Registry => RegistryHelper.Default;


    /// <summary>
    /// Gets the version of the plugin currently in use.
    /// </summary>
    public string? PluginVersion => MainWindowViewModel.GetPluginVersion();

    /// <summary>
    /// Gets a collection of all available skins, represented as <see cref="SkinInfo"/> objects.
    /// </summary>
    public IEnumerable<SkinInfo> Skins { get; } = Enum.GetValues<SkinType>().Select(x => new SkinInfo(x));

    /// <summary>
    /// Gets or sets the currently selected skin.
    /// </summary>
    public SkinInfo SelectedSkin
    {
        get => Skins.First(x => x.Type == SettingHelper.Default.SkinType);
        set => SettingHelper.Default.SkinType = value.Type;
    }

    /// <summary>
    /// 获取可用的游戏目录列表
    /// </summary>
    public List<BnsGameInfo> AvailableDirectories
    {
        get
        {
            var games = new List<BnsGameInfo>();
            var registryPaths = new[]
            {
                @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\剑灵_腾讯",
                @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\剑灵(先遣)_腾讯",
                @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\剑灵怀旧服",
                @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\《剑灵》巅峰服",
            };

            foreach (var registryPath in registryPaths)
            {
                try
                {
                    using var key = RegistryHelper.LocalMachine.OpenSubKey(registryPath);

                    // wegame
                    if (key?.GetValue("InstallSource") is string InstallSource)
                    {
                        var game = FindGameDirectory(InstallSource);
                        if (game != null) games.Add(game.Value);
                    }

                    // 登录器
                    if (key?.GetValue("DisplayIcon") is string installPath && !string.IsNullOrEmpty(installPath))
                    {
                        var prod = Path.GetDirectoryName(Path.GetDirectoryName(installPath));
                        if (prod is null || !Directory.Exists(prod)) continue;

                        foreach (var line in new DirectoryInfo(prod)
                            .GetFiles("game-install-path.log", SearchOption.AllDirectories)
                            .SelectMany(o => File.ReadAllLines(o.FullName)))
                        {
                            var game = FindGameDirectory(line);
                            if (game != null) games.Add(game.Value);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"搜索注册表路径 {registryPath} 时出错: {ex.Message}");
                }
            }

            // 确保当前设置的目录也包含在列表中（如果是有效的游戏目录）
            try
            {
                var current = SettingHelper.Default.Game;
                if (!current.IsEmpty) games.Add(current);
            }
            catch
            {

            }

            return [.. games.Distinct()];
        }
    }
    #endregion

    #region Methods
    [RelayCommand]
    async Task OpenAboutView() => await Dialog.Show<AboutDialog>().GetResultAsync<bool>();

    [RelayCommand]
    void BrowseDirectory()
    {
        var dialog = new OpenFolderDialog() { Title = null };
        if (dialog.ShowDialog() == true)
        {
            SettingHelper.Default.Game = FindGameDirectory(dialog.FolderName) ?? throw new AppException(ExceptionCode.InvalidGame);

            // 通知相关属性更新
            OnPropertyChanged(nameof(PluginVersion));
            OnPropertyChanged(nameof(AvailableDirectories));
        }
    }

    /// <summary>
    /// Searches for game directories containing the "BNSR.exe" executable within the specified root directory.
    /// </summary>
    /// <param name="root">The root directory to search. Must be a valid directory path. If <paramref name="root"/> is null, empty, or does
    /// not exist, an empty collection is returned.</param>
    /// <returns>A collection of strings representing the full paths to the game directories. Returns an empty collection if no
    /// valid game directories are found or if an error occurs during the search.</returns>
    public static BnsGameInfo? FindGameDirectory(string? root)
    {
        try
        {
            if (string.IsNullOrEmpty(root) || !Directory.Exists(root)) return null;

            // 找到客户端主程序路径
            foreach (var client in new DirectoryInfo(root).GetFiles("BNSR.exe", SearchOption.AllDirectories))
            {
                // 检查客户端发布区域
                var info = FileVersionInfo.GetVersionInfo(client.FullName);
                var publisher = (EPublisher)info.FileMinorPart;
                if (publisher is not (EPublisher.Tencent or EPublisher.ZTX or EPublisher.ZNCG)) continue;

                // 找到客户端根目录
                var parent = client.Directory!;
                while (!parent.Name.Equals("BNSR", StringComparison.OrdinalIgnoreCase))
                {
                    parent = parent.Parent;
                    if (parent is null) continue;
                }

                return new BnsGameInfo(parent.FullName);
            }
        }
        catch
        {

        }

        return null;
    }
    #endregion
}
