package model

import (
	"time"
)

// ==================== 基本模型 ==================== //

type Announcement struct {
	ID        uint32     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Title     string     `gorm:"column:title;size:200;not null" json:"title"`
	Content   string     `gorm:"column:content;type:text;not null" json:"content"`
	Type      uint8      `gorm:"column:type;type:tinyint unsigned;not null;default:0" json:"type"` // 公告类型：0-信息，1-警告，2-更新，3-维护
	Status    uint8      `gorm:"column:status;type:tinyint;not null" json:"status"`                // 0-草稿，1-已发布，2-已下线
	Priority  uint8      `gorm:"column:priority;type:tinyint;not null;default:0" json:"priority"`  // 优先级，0-10
	Version   uint16     `gorm:"column:version;type:smallint;not null;default:1" json:"version"`   // 公告版本号，用于增量更新
	StartTime *time.Time `gorm:"column:start_time" json:"start_time"`                              // 开始显示时间
	EndTime   *time.Time `gorm:"column:end_time" json:"end_time"`                                  // 结束显示时间
	AdminUID  uint32     `gorm:"column:admin;type:int unsigned;not null" json:"admin"`             // 发布管理员
	CreatedAt time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`               // 创建时间
	UpdatedAt time.Time  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`               // 更新时间
}

// TableName 指定表名
func (Announcement) TableName() string {
	return "bns_announcement"
}
