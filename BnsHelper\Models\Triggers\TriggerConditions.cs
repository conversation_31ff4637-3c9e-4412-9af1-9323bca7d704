using System.Collections.Concurrent;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;

namespace Xylia.BnsHelper.Models.Triggers;

/// <summary>
/// 触发器条件基类
/// </summary>
public abstract class TriggerCondition
{
    /// <summary>
    /// 评估条件
    /// </summary>
    public abstract bool Evaluate(ITriggerEvent triggerEvent, Dictionary<string, object> variables);

    /// <summary>
    /// 克隆条件
    /// </summary>
    /// <returns></returns>
    public virtual TriggerCondition Clone()
    {
        return (TriggerCondition)this.MemberwiseClone();
    }
}

/// <summary>
/// 正则表达式条件
/// </summary>
public class RegexCondition : TriggerCondition
{
    public string Pattern { get; set; } = "";
    public bool IgnoreCase { get; set; } = true;

    private Regex? _compiledRegex;

    [JsonIgnore]
    public Regex? CompiledRegex
    {
        get
        {
            if (_compiledRegex == null && !string.IsNullOrEmpty(Pattern))
            {
                try
                {
                    var options = RegexOptions.Compiled;
                    if (IgnoreCase) options |= RegexOptions.IgnoreCase;
                    _compiledRegex = new Regex(Pattern, options);
                }
                catch
                {
                    _compiledRegex = null;
                }
            }
            return _compiledRegex;
        }
    }

    public override bool Evaluate(ITriggerEvent triggerEvent, Dictionary<string, object> variables)
    {
        if (CompiledRegex == null) return false;
        return CompiledRegex.IsMatch(triggerEvent.RawMessage);
    }
}

/// <summary>
/// 计数器条件
/// </summary>
public class CounterCondition : TriggerCondition
{
    private static readonly ConcurrentDictionary<string, int> _counters = new();

    public string CounterName { get; set; } = "";
    public ComparisonOperator Operator { get; set; } = ComparisonOperator.Equal;
    public int Value { get; set; } = 0;

    public override bool Evaluate(ITriggerEvent triggerEvent, Dictionary<string, object> variables)
    {
        if (string.IsNullOrEmpty(CounterName)) return false;

        var currentValue = GetCounter(CounterName);
        return Operator switch
        {
            ComparisonOperator.Equal => currentValue == Value,
            ComparisonOperator.NotEqual => currentValue != Value,
            ComparisonOperator.Greater => currentValue > Value,
            ComparisonOperator.GreaterOrEqual => currentValue >= Value,
            ComparisonOperator.Less => currentValue < Value,
            ComparisonOperator.LessOrEqual => currentValue <= Value,
            _ => false
        };
    }

    /// <summary>
    /// 增加计数器值
    /// </summary>
    public static void IncrementCounter(string name, int value = 1)
    {
        _counters.AddOrUpdate(name, value, (key, oldValue) => oldValue + value);
    }

    /// <summary>
    /// 重置计数器
    /// </summary>
    public static void ResetCounter(string name)
    {
        _counters.TryRemove(name, out _);
    }

    /// <summary>
    /// 获取计数器值
    /// </summary>
    public static int GetCounter(string name)
    {
        return _counters.GetValueOrDefault(name, 0);
    }

    /// <summary>
    /// 设置计数器值
    /// </summary>
    public static void SetCounter(string name, int value)
    {
        _counters.AddOrUpdate(name, value, (key, oldValue) => value);
    }
}

/// <summary>
/// 时间条件
/// </summary>
public class TimeCondition : TriggerCondition
{
    #region Fields
    public TimeSpan Time { get; set; }
    public int RepeatMode { get; set; } = 1; // 0=仅一次, 1=每天, 2=每周

    // 星期选择（仅在RepeatMode=2时有效）
    public bool IsMonday { get; set; } = false;
    public bool IsTuesday { get; set; } = false;
    public bool IsWednesday { get; set; } = false;
    public bool IsThursday { get; set; } = false;
    public bool IsFriday { get; set; } = false;
    public bool IsSaturday { get; set; } = false;
    public bool IsSunday { get; set; } = false;

    // 运行时参数
    public bool IsExecuted = false;
    #endregion

    #region Methods
    public override bool Evaluate(ITriggerEvent triggerEvent, Dictionary<string, object> variables)
    {
        // 检查是否已经执行过
        if (IsExecuted) return false;

        var now = DateTime.Now;
        var currentTime = now.TimeOfDay;

        // 精确时间匹配（允许1分钟误差）
        var diff = Math.Abs((currentTime - Time).TotalMinutes);
        if (diff > 1) return false;

        // 根据重复模式判断
        switch (RepeatMode)
        {
            case 0: // 仅一次
                IsExecuted = true;
                return true;

            case 1: // 每天
                return true;

            case 2: // 每周
                var dayOfWeek = now.DayOfWeek;
                return dayOfWeek switch
                {
                    DayOfWeek.Monday => IsMonday,
                    DayOfWeek.Tuesday => IsTuesday,
                    DayOfWeek.Wednesday => IsWednesday,
                    DayOfWeek.Thursday => IsThursday,
                    DayOfWeek.Friday => IsFriday,
                    DayOfWeek.Saturday => IsSaturday,
                    DayOfWeek.Sunday => IsSunday,
                    _ => false
                };

            default:
                return false;
        }
    }

    public override string ToString()
    {
        var baseName = $"定时触发 {Time:HHmm}";

        switch (RepeatMode)
        {
            case 0:
                return $"{baseName} (仅一次)";
            case 1:
                return $"{baseName} (每天)";
            case 2:
                var selectedDays = new List<string>();
                if (IsMonday) selectedDays.Add("周一");
                if (IsTuesday) selectedDays.Add("周二");
                if (IsWednesday) selectedDays.Add("周三");
                if (IsThursday) selectedDays.Add("周四");
                if (IsFriday) selectedDays.Add("周五");
                if (IsSaturday) selectedDays.Add("周六");
                if (IsSunday) selectedDays.Add("周日");

                if (selectedDays.Count == 7)
                    return $"{baseName} (每天)";
                else if (selectedDays.Count == 0)
                    return $"{baseName} (每周)";
                else return $"{baseName} ({string.Join(",", selectedDays)})";

            default: return baseName;
        }
    }
    #endregion
}

/// <summary>
/// 变量条件
/// </summary>
public class VariableCondition : TriggerCondition
{
    public string VariableName { get; set; } = "";
    public string Value { get; set; } = "";
    public ComparisonOperator Operator { get; set; } = ComparisonOperator.Equal;

    public override bool Evaluate(ITriggerEvent triggerEvent, Dictionary<string, object> variables)
    {
        if (string.IsNullOrEmpty(VariableName)) return false;
        if (!variables.TryGetValue(VariableName, out var currentValue)) return false;

        var currentStr = currentValue?.ToString() ?? "";

        return Operator switch
        {
            ComparisonOperator.Equal => currentStr.Equals(Value, StringComparison.OrdinalIgnoreCase),
            ComparisonOperator.NotEqual => !currentStr.Equals(Value, StringComparison.OrdinalIgnoreCase),
            ComparisonOperator.Contains => currentStr.Contains(Value, StringComparison.OrdinalIgnoreCase),
            ComparisonOperator.StartsWith => currentStr.StartsWith(Value, StringComparison.OrdinalIgnoreCase),
            ComparisonOperator.EndsWith => currentStr.EndsWith(Value, StringComparison.OrdinalIgnoreCase),
            _ => false
        };
    }
}

/// <summary>
/// 复合条件
/// </summary>
public class CompositeCondition : TriggerCondition
{
    public List<TriggerCondition> Conditions { get; set; } = new();
    public ConditionCombination Combination { get; set; } = ConditionCombination.And;

    public override bool Evaluate(ITriggerEvent triggerEvent, Dictionary<string, object> variables)
    {
        if (Conditions.Count == 0) return false;

        var results = Conditions.Select(c => c.Evaluate(triggerEvent, variables));
        if (!results.Any()) return false;

        return Combination switch
        {
            ConditionCombination.And => results.All(r => r),
            ConditionCombination.Or => results.Any(r => r),
            ConditionCombination.Not => !results.Any(r => r),
            _ => false
        };
    }
}

/// <summary>
/// 比较操作符
/// </summary>
public enum ComparisonOperator
{
    Equal,
    NotEqual,
    Greater,
    GreaterOrEqual,
    Less,
    LessOrEqual,
    Contains,
    StartsWith,
    EndsWith
}

/// <summary>
/// 条件组合类型
/// </summary>
public enum ConditionCombination
{
    And,
    Or,
    Not
}