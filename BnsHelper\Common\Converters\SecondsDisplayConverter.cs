using System.Globalization;
using System.Text;
using System.Windows.Data;
using System.Windows.Markup;
using Xylia.BnsHelper.Resources;

namespace Xylia.BnsHelper.Common.Converters;
public class SecondsDisplayConverter : MarkupExtension, IValueConverter
{
    public override object ProvideValue(IServiceProvider serviceProvider) => this;

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is double seconds)
        {
            // 当秒数为0时，返回空字符串（不显示）
            if (seconds <= 0) return string.Empty;

            // 检查参数，如果是"simple"则使用简单格式，否则使用友好格式
            bool useSimpleFormat = parameter?.ToString() == "simple";

            if (useSimpleFormat)
            {
                // 简单格式：只显示数字
                return seconds.ToString("F0");
            }
            else
            {
                // 友好格式：使用天时分秒显示
                return FormatTimeSpan(TimeSpan.FromSeconds(seconds));
            }
        }

        return string.Empty;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }

    /// <summary>
    /// 格式化时间跨度为友好的显示格式
    /// </summary>
    private static string FormatTimeSpan(TimeSpan time)
    {
        var builder = new StringBuilder();
        if (time.Days > 0) builder.Append(time.Days + StringHelper.Get("Text.Time_Day"));
        if (time.Hours > 0) builder.Append(time.Hours + StringHelper.Get("Text.Time_Hour"));
        if (time.Minutes > 0) builder.Append(time.Minutes + StringHelper.Get("Text.Time_Minute"));
        if (time.Seconds >= 0) builder.Append(time.Seconds + StringHelper.Get("Text.Time_Second"));

        return builder.ToString();
    }
}
