﻿using RestSharp;
using System.Diagnostics;
using System.Net;
using System.Text.RegularExpressions;
using System.Web;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Models.Api;
using Xylia.Preview.Data.Engine.DatData;

namespace Xylia.BnsHelper.Services.ApiEndpoints;

/// <summary>
/// 腾讯游戏AMS端点服务
/// </summary>
internal class AMSEndpoint(string accessToken, string openId, string accType, string appId, string targetAppId)
{
    #region 登录请求
    public const string APPID = "101491592";

    private readonly string _openId = openId;
    private readonly string _accessToken = accessToken;
    private readonly string _appId = appId;
    private readonly string _accType = accType;

    public AMSEndpoint(string accessToken, string openId, string accType = "qc") : this(accessToken, openId, accType,
        accType is "wx" ? "wxfa0c35392d06b82f" : APPID,
        accType is "wx" ? "wx92a4aa7b8d0ff288" : "1112228889")
    {

    }

    /// <summary>
    /// 使用授权码进行AMS登录
    /// </summary>
    /// <param name="code">OAuth授权码</param>
    /// <param name="filteredCookies">过滤后的cookies</param>
    /// <returns>AMS登录响应</returns>
    public static async Task<AmsLoginResponse> LoginWithCodeAsync(string code, IDictionary<string, string> cookies)
    {
        var client = new RestClient(new RestClientOptions
        {
            Proxy = new WebProxy(),
            Timeout = TimeSpan.FromSeconds(10),
            UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
        });

        // 设置请求头
        client.AddDefaultHeader("accept", "*/*");
        client.AddDefaultHeader("referer", "https://bns.qq.com/");
        client.AddDefaultHeader("Cookie", string.Join("; ", cookies.Select(x => $"{x.Key}={x.Value}")));

        // 构建请求
        var request = new RestRequest("https://ams.game.qq.com/ams/userLoginSvr", Method.Get);
        request.AddParameter("a", "qcCodeToOpenId");
        request.AddParameter("qc_code", code);
        request.AddParameter("appid", "101491592");
        request.AddParameter("redirect_uri", "https://milo.qq.com/comm-htdocs/login/qc_redirect.html");

        var response = await client.ExecuteAsync(request);
        if (!response.IsSuccessful || string.IsNullOrEmpty(response.Content))
        {
            throw new Exception($"AMS登录请求失败: {response.ErrorMessage}");
        }

        return ParseAmsResponse(response.Content);
    }

    /// <summary>
    /// 解析AMS登录响应
    /// </summary>
    /// <param name="content">响应内容</param>
    /// <returns>解析后的响应对象</returns>
    private static AmsLoginResponse ParseAmsResponse(string content)
    {
        var response = new AmsLoginResponse();

        // 使用正则表达式提取各个字段
        var iRetMatch = Regex.Match(content, @"""iRet""\s*:\s*""([^""]+)""");
        var sSerialNumMatch = Regex.Match(content, @"""sSerialNum""\s*:\s*""([^""]+)""");
        var sMsgMatch = Regex.Match(content, @"""sMsg""\s*:\s*""([^""]+)""");
        var openIdMatch = Regex.Match(content, @"""openid""\s*:\s*""([^""]+)""");
        var accessTokenMatch = Regex.Match(content, @"""access_token""\s*:\s*""([^""]+)""");
        var expiresInMatch = Regex.Match(content, @"""expires_in""\s*:\s*""([^""]+)""");

        response.IRet = iRetMatch.Success ? long.Parse(iRetMatch.Groups[1].Value) : 0;
        response.SSerialNum = sSerialNumMatch.Success ? sSerialNumMatch.Groups[1].Value : null;
        response.SMsg = sMsgMatch.Success ? sMsgMatch.Groups[1].Value : null;
        response.OpenId = openIdMatch.Success ? openIdMatch.Groups[1].Value : null;
        response.AccessToken = accessTokenMatch.Success ? accessTokenMatch.Groups[1].Value : null;
        response.ExpiresIn = expiresInMatch.Success ? expiresInMatch.Groups[1].Value : null;

        Debug.WriteLine($"[AMS] openid: {response.OpenId}, access_token: {response.AccessToken}");
        return response;
    }

    /// <summary>
    /// AMS登录响应模型
    /// </summary>
    public class AmsLoginResponse
    {
        public long IRet { get; set; }
        public string? SSerialNum { get; set; }
        public string? SMsg { get; set; }
        public string? OpenId { get; set; }
        public string? AccessToken { get; set; }
        public string? ExpiresIn { get; set; }
    }
    #endregion

    #region 业务请求
    private RestClient? _client;

    private RestClient Client => _client ??= CreateRestClient();

    private RestClient CreateRestClient()
    {
        var client = new RestClient(new RestClientOptions
        {
            Proxy = new WebProxy(),
            Timeout = TimeSpan.FromSeconds(5),
            UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        }, configureSerialization: s => s.UseSerializer<JsonNetSerializer>());

        client.AddDefaultHeader("accept", "application/json, text/plain, */*");
        client.AddDefaultHeader("cookie", $"acctype={_accType};openid={_openId};appid={_appId};access_token={_accessToken}");
        client.AddDefaultHeader("referer", "https://bns.qq.com/");
        return client;
    }

    /// <summary>
    /// 执行流程请求
    /// </summary>
    /// <param name="parameters">请求参数</param>
    /// <returns>活动奖励结果</returns>
    public async Task<ActivityFlowResponse> ExecuteFlowAsync(IDictionary<string, string> parameters)
    {
        var request = new RestRequest("https://comm.ams.game.qq.com/ide/", Method.Post);
        request.AddParameter("needGopenid", "1");
        request.AddParameter("isPreengage", "1");

        foreach (var (name, value) in parameters)
        {
            request.AddParameter(name, value);
        }

        var response = await Client.ExecuteAsync<ActivityFlowResponse>(request).ConfigureAwait(false);
        Debug.WriteLine("[AMS] 执行流程请求响应: " + response.Content);
        return response.Data ?? throw new Exception("无效的AMS响应");
    }

    /// <summary>
    /// 查询角色信息
    /// </summary>
    public async Task<QueryRoleResult?> QueryRole(BnsWorld world)
    {
        // 填充参数
        var request = new RestRequest("https://comm.aci.game.qq.com/main", Method.Get);
        request.AddParameter("needGopenid", "1");
        request.AddParameter("isPreengage", "1");
        request.AddParameter("sCloudApiName", "ams.gameattr.role");
        request.AddParameter("game", "bns");
        request.AddParameter("area", world.Id);

        if (world.Publisher != EPublisher.Tencent)
        {
            request.AddParameter("sAMSAcctype", "qq");
            request.AddParameter("sAMSAccessToken", _accessToken);
            request.AddParameter("sAMSAppOpenId", _openId);
            request.AddParameter("sAMSSourceAppId", _appId);
            request.AddParameter("sAMSTargetAppId", targetAppId);
            request.AddOrUpdateParameter("game", "neo");
        }

        var response = await Client.ExecuteAsync(request).ConfigureAwait(false);
        if (string.IsNullOrEmpty(response.Content)) return null;

        var result = new QueryRoleResult();

        // 提取checkparam
        var checkParamMatch = Regex.Match(response.Content, @"checkparam:'([^']*)'");
        if (checkParamMatch.Success) result.CheckParam = checkParamMatch.Groups[1].Value;

        // 提取md5str
        var md5Match = Regex.Match(response.Content, @"md5str:'([^']*)'");
        if (md5Match.Success) result.Md5Str = md5Match.Groups[1].Value;

        // 提取data中的list部分
        var dataMatch = Regex.Match(response.Content, @"data:'([^']*)'");
        if (dataMatch.Success)
        {
            var data = dataMatch.Groups[1].Value;
            var listMatch = Regex.Match(data, @"list=([^&]+)");
            if (listMatch.Success)
            {
                var listData = HttpUtility.UrlDecode(listMatch.Groups[1].Value);
                foreach (var entry in listData.Split('|'))
                {
                    if (string.IsNullOrEmpty(entry)) continue;

                    var parts = entry.Split(' ');
                    if (parts.Length >= 7)
                    {
                        var roleId = long.Parse(parts[0]);
                        var time1 = long.Parse(parts[1]);   // 注册时间
                        var time2 = long.Parse(parts[2]);   // 最后登录时间
                        var roleName = HttpUtility.UrlDecode(parts[5]);
                        result.Roles.Add(new Creature { Id = roleId, Name = roleName, world = world.Id });
                    }
                }
            }
        }

        return result;
    }
    #endregion
}