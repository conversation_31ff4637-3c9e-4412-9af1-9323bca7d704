package repository

import (
	"errors"
	"time"

	"udp-server/server/internal/model"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

var (
	ErrUserNotFound = errors.New("用户不存在")
	ErrUserExists   = errors.New("用户已存在")
)

// UserRepository 用户数据访问层
type UserRepository struct {
	db *gorm.DB
}

// NewUserRepository 创建用户仓库
func NewUserRepository(db *gorm.DB) *UserRepository {
	return &UserRepository{db: db}
}

// FindByUin 根据UIN查找用户
func (r *UserRepository) FindByUin(uin uint64) (*model.User, error) {
	var user model.User
	err := r.db.Where("uin = ?", uin).First(&user).Error
	if err == gorm.ErrRecordNotFound {
		return nil, ErrUserNotFound
	}
	if err != nil {
		logger.Error("查询用户失败: UIN=%d, Error=%v", uin, err)
		return nil, err
	}
	return &user, nil
}

// FindByUID 根据UID查找用户
func (r *UserRepository) FindByUID(uid uint64) (*model.User, error) {
	var user model.User
	err := r.db.Where("uid = ?", uid).First(&user).Error
	if err == gorm.ErrRecordNotFound {
		return nil, ErrUserNotFound
	}
	if err != nil {
		logger.Error("查询用户失败: UID=%d, Error=%v", uid, err)
		return nil, err
	}
	return &user, nil
}

// FindByToken 根据Token查找用户
func (r *UserRepository) FindByToken(token string) (*model.User, error) {
	var user model.User
	err := r.db.Where("token = ?", token).First(&user).Error
	if err == gorm.ErrRecordNotFound {
		return nil, ErrUserNotFound
	}
	if err != nil {
		logger.Error("根据Token查询用户失败: Token=%s, Error=%v", token, err)
		return nil, err
	}
	return &user, nil
}

// FindByUins 批量根据UIN查找用户
func (r *UserRepository) FindByUins(uins []uint64) ([]*model.User, error) {
	var users []*model.User
	err := r.db.Where("uin IN ?", uins).Find(&users).Error
	if err != nil {
		logger.Error("批量查询用户失败: UINs=%v, Error=%v", uins, err)
		return nil, err
	}
	return users, nil
}

// Create 创建用户
func (r *UserRepository) Create(user *model.User) error {
	// 检查用户是否已存在
	existing, err := r.FindByUin(user.Uin)
	if err != nil && err != ErrUserNotFound {
		return err
	}
	if existing != nil {
		return ErrUserExists
	}

	err = r.db.Create(user).Error
	if err != nil {
		logger.Error("创建用户失败: UIN=%d, Error=%v", user.Uin, err)
		return err
	}

	logger.Info("用户创建成功: UID=%d, UIN=%d", user.UID, user.Uin)
	return nil
}

// Update 更新用户
func (r *UserRepository) Update(user *model.User) error {
	err := r.db.Save(user).Error
	if err != nil {
		logger.Error("更新用户失败: UID=%d, Error=%v", user.UID, err)
		return err
	}

	logger.Debug("用户更新成功: UID=%d", user.UID)
	return nil
}

// UpdateToken 更新用户Token
func (r *UserRepository) UpdateToken(uin uint64, token string) error {
	result := r.db.Model(&model.User{}).Where("uin = ?", uin).Update("token", token)
	if result.Error != nil {
		logger.Error("更新用户Token失败: UIN=%d, Error=%v", uin, result.Error)
		return result.Error
	}
	if result.RowsAffected == 0 {
		return ErrUserNotFound
	}

	logger.Debug("用户Token更新成功: UIN=%d", uin)
	return nil
}

// UpdateStatus 更新用户状态
func (r *UserRepository) UpdateStatus(uin uint64, status int) error {
	result := r.db.Model(&model.User{}).Where("uin = ?", uin).Update("status", status)
	if result.Error != nil {
		logger.Error("更新用户状态失败: UIN=%d, Status=%d, Error=%v", uin, status, result.Error)
		return result.Error
	}
	if result.RowsAffected == 0 {
		return ErrUserNotFound
	}

	logger.Info("用户状态更新成功: UIN=%d, Status=%d", uin, status)
	return nil
}

// ClearToken 清空用户Token
func (r *UserRepository) ClearToken(uin uint64) error {
	return r.UpdateToken(uin, "")
}

// GetActiveUsers 获取活跃用户（最近登录的用户）
func (r *UserRepository) GetActiveUsers(since time.Time) ([]*model.User, error) {
	var users []*model.User
	err := r.db.Where("updated_at >= ?", since).Find(&users).Error
	if err != nil {
		logger.Error("查询活跃用户失败: Since=%v, Error=%v", since, err)
		return nil, err
	}
	return users, nil
}

// GetUsersByStatus 根据状态获取用户
func (r *UserRepository) GetUsersByStatus(status int) ([]*model.User, error) {
	var users []*model.User
	err := r.db.Where("status = ?", status).Find(&users).Error
	if err != nil {
		logger.Error("根据状态查询用户失败: Status=%d, Error=%v", status, err)
		return nil, err
	}
	return users, nil
}

// Count 统计用户数量
func (r *UserRepository) Count() (int64, error) {
	var count int64
	err := r.db.Model(&model.User{}).Count(&count).Error
	if err != nil {
		logger.Error("统计用户数量失败: Error=%v", err)
		return 0, err
	}
	return count, nil
}

// CountByStatus 根据状态统计用户数量
func (r *UserRepository) CountByStatus(status int) (int64, error) {
	var count int64
	err := r.db.Model(&model.User{}).Where("status = ?", status).Count(&count).Error
	if err != nil {
		logger.Error("根据状态统计用户数量失败: Status=%d, Error=%v", status, err)
		return 0, err
	}
	return count, nil
}
