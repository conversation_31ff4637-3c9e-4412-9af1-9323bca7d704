package handler

import (
	"fmt"
	"net"
	"udp-server/server/internal/model"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/binary"
	"udp-server/server/pkg/logger"
)

// LuckyHandler 签到抽奖处理器
type LuckyHandler struct {
	luckyService      *service.LuckyService
	authService       *service.AuthService
	permissionService *service.PermissionService
	cdkeyService      *service.CDKeyService
}

// 创建签到抽奖处理器
func NewLuckyHandler(luckyService *service.LuckyService, authService *service.AuthService, permissionService *service.PermissionService, cdkeyService *service.CDKeyService) *LuckyHandler {
	return &LuckyHandler{
		luckyService:      luckyService,
		authService:       authService,
		permissionService: permissionService,
		cdkeyService:      cdkeyService,
	}
}

// 处理签到抽奖请求
func (h *LuckyHandler) HandleLuckyDrawRequest(msg *binary.Message, remoteAddr *net.UDPAddr) (*binary.BinaryWriter, error) {
	// 使用统一验证机制
	reader := binary.NewBinaryReader(msg.Body)
	userInterface, err := reader.ValidateToken(h.authService)
	if err != nil {
		return nil, err
	}

	// 类型转换
	user, ok := userInterface.(*model.User)
	if !ok {
		logger.Error("签到处理失败，用户类型错误")
		return nil, fmt.Errorf("用户类型错误")
	}

	// 使用设备指纹
	finalDeviceFingerprint := ""
	// 如果没有提供设备指纹，尝试从用户的最新设备历史记录中获取
	latestFingerprint, err := h.authService.GetLatestDeviceFingerprint(int64(user.Uin))
	if err == nil && latestFingerprint != "" {
		finalDeviceFingerprint = latestFingerprint
	}

	// 执行抽奖
	response, err := h.luckyService.Draw(user.UID, finalDeviceFingerprint, remoteAddr)
	if err != nil {
		return nil, err
	}

	// 检查签到是否成功，如果成功则刷新权限信息
	if true {
		// 先清除权限缓存，确保获取最新的权限状态
		if err := h.permissionService.ClearUserPermissionCache(user.UID, "client"); err != nil {
			logger.Warn("清除用户权限缓存失败: UID=%d, Error=%v", user.UID, err)
		}

		// 重新计算权限并返回
		finalPermission := h.authService.GetPermission(int64(user.Uin))
		permissionExpiration, err := h.permissionService.GetPermissionExpiration(user.UID, "client")
		if err != nil {
			logger.Warn("获取用户权限过期时间失败: UID=%d, Error=%v", user.UID, err)
			permissionExpiration = 0 // 无权限
		}

		logger.Debug("签到成功，返回刷新后的权限信息: UID=%d, Permission=%d, Expiration=%d", user.UID, finalPermission, permissionExpiration)
		response.Permission = &finalPermission
		response.PermissionExpiration = &permissionExpiration
	}

	return h.EncodeBinaryLuckyDrawResponse(response), nil
}

// 处理获取签到状态请求
func (h *LuckyHandler) HandleLuckyStatusRequest(msg *binary.Message, remoteAddr *net.UDPAddr) (*binary.BinaryWriter, error) {
	// 使用统一验证机制
	reader := binary.NewBinaryReader(msg.Body)
	userInterface, err := reader.ValidateToken(h.authService)
	if err != nil {
		return nil, err
	}

	// 类型转换
	user, ok := userInterface.(*model.User)
	if !ok {
		logger.Error("获取签到状态失败，用户类型错误")
		return nil, fmt.Errorf("用户类型错误")
	}

	// 获取用户签到状态
	status, err := h.luckyService.GetUserStatus(user.UID)
	if err != nil {
		return nil, err
	}

	// 创建成功响应
	writer := binary.NewBinaryWriter()
	writer.WriteUint32(binary.ErrorCodeSuccess)
	writer.WriteUint32(uint32(status.Point))  // 签到总天数
	writer.WriteUint32(status.AvailableCount) // 今日可用次数
	return writer, nil
}

// 处理口令码激活请求
func (h *LuckyHandler) HandleCDKeyActivateRequest(msg *binary.Message, remoteAddr *net.UDPAddr) (*binary.BinaryWriter, error) {
	// 使用统一验证机制
	reader := binary.NewBinaryReader(msg.Body)
	userInterface, err := reader.ValidateToken(h.authService)
	if err != nil {
		return nil, err
	}

	// 类型转换
	user, ok := userInterface.(*model.User)
	if !ok {
		logger.Error("口令码激活失败，用户类型错误")
		return nil, fmt.Errorf("用户类型错误")
	}

	// 读取CDKey
	cdkey, err := reader.ReadString()
	if err != nil {
		return nil, fmt.Errorf("无效的请求格式")
	}

	// 调用口令码激活功能（用户操作）
	err = h.cdkeyService.ActivateCDKey(cdkey, user.UID, -1, remoteAddr.IP.String())
	if err != nil {
		return nil, err
	}

	// 激活成功，返回更新后的权限信息
	finalPermission := h.authService.GetPermission(int64(user.Uin))

	// 通过CDKeyService获取权限过期时间
	permissionExpiration, err := h.cdkeyService.GetPermissionExpiration(user.UID, "client")
	if err != nil {
		logger.Warn("获取用户权限过期时间失败: UID=%d, Error=%v", user.UID, err)
		permissionExpiration = 0 // 无权限
	}

	// 激活成功响应（包含Permission和PermissionExpiration）
	writer := binary.NewBinaryWriter()
	writer.WriteUint32(binary.ErrorCodeSuccess) // ErrorCode = 0 (成功)
	writer.WriteUint8(finalPermission)          // Permission (1字节)
	writer.WriteInt64(permissionExpiration)     // PermissionExpiration (8字节)
	return writer, nil
}

// 编码签到抽奖响应
func (h *LuckyHandler) EncodeBinaryLuckyDrawResponse(resp *model.LuckyDrawResponse) *binary.BinaryWriter {
	writer := binary.NewBinaryWriter()
	writer.WriteUint32(binary.ErrorCodeSuccess)
	writer.WriteString(resp.Message)

	// 如果包含权限信息，编码权限字段
	if resp.Permission != nil {
		writer.WriteUint8(*resp.Permission)
	}
	if resp.PermissionExpiration != nil {
		writer.WriteInt64(*resp.PermissionExpiration)
	}

	return writer
}
