<Page x:Class="Xylia.BnsHelper.Views.Pages.HomePage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:hc="https://handyorg.github.io/handycontrol"
      xmlns:helper="clr-namespace:Xylia.BnsHelper.Common.Helpers"
      xmlns:rc="clr-namespace:Xylia.BnsHelper.Resources">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- 主要内容区域 -->
        <Border Grid.Row="0" Background="{DynamicResource SecondaryRegionBrush}"
                MouseLeftButtonDown="ContentArea_MouseLeftButtonDown"
                MouseMove="ContentArea_MouseMove"
                MouseLeftButtonUp="ContentArea_MouseLeftButtonUp">
            <Grid Margin="5 10 10 0">
                <!-- 设置选项页面 -->
                <Grid x:Name="HomePageContent" Visibility="Visible">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!-- Options Section -->
                    <Border Grid.Row="0" Background="{DynamicResource RegionBrush}" CornerRadius="8" Padding="15">
                        <StackPanel>
                            <!-- Header with Title and Actions -->
                            <Grid Margin="0,0,0,10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="{DynamicResource HomePage_GameSettings}" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryTextBrush}" VerticalAlignment="Center" />

                                <!-- Option Control Buttons -->
                                <hc:ButtonGroup Grid.Column="1" HorizontalAlignment="Left" Margin="15,0,10,0">
                                    <Button x:Name="SelectAllButton" Content="{DynamicResource HomePage_SelectAll}"
                                            ToolTip="{DynamicResource HomePage_SelectAll_Tooltip}" Visibility="Collapsed"
                                            Click="SelectAllButton_Click"
                                            Style="{StaticResource ButtonDefault}" Padding="8,4" FontSize="11" />
                                    <Button x:Name="ViewSelectedButton" Content="{DynamicResource HomePage_ViewSelected}"
                                            ToolTip="{DynamicResource HomePage_ViewSelected_Tooltip}"
                                            Click="ViewSelectedButton_Click"
                                            Style="{StaticResource ButtonDefault}" Padding="8,4" FontSize="11" />
                                </hc:ButtonGroup>

                                <!-- Actions in Top Right -->
                                <hc:ButtonGroup Grid.Column="2" HorizontalAlignment="Right">
                                    <Button Content="{DynamicResource MainWindow_SetupLocal}" Command="{Binding SetuptLocalCommand}" Style="{StaticResource ButtonPrimary}" Padding="8,4" FontSize="11" Visibility="Collapsed" />
                                    <Button Content="{DynamicResource MainWindow_Revert}" Command="{Binding RevertPluginCommand}" Style="{StaticResource ButtonWarning}" Padding="8,4" FontSize="11" />
                                    <Button Content="{DynamicResource MainWindow_Execute}" Command="{Binding SetupPluginCommand}" Style="{StaticResource ButtonSuccess}" Padding="8,4" FontSize="11" />
                                    <Button Content="{DynamicResource MainWindow_StartGame}" ToolTip="{DynamicResource MainWindow_StartGame_Tooltip}" Command="{Binding StartGameCommand}" Style="{StaticResource ButtonInfo}" Padding="8,4" FontSize="11" Visibility="{Binding Server.CanStartUp,Source={x:Static helper:SettingHelper.Default},Converter={StaticResource Boolean2VisibilityConverter}}" />
                                </hc:ButtonGroup>
                            </Grid>

                            <!-- Options Grid with Scrollable Layout -->
                            <Border Background="Transparent" CornerRadius="4" MaxHeight="210">
                                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" Padding="0,0,8,0">
                                    <ItemsControl x:Name="OptionGroup">
                                        <ItemsControl.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <UniformGrid Columns="3" />
                                            </ItemsPanelTemplate>
                                        </ItemsControl.ItemsPanel>
                                        <ItemsControl.ItemContainerStyle>
                                            <Style TargetType="{x:Type ContentPresenter}">
                                                <Setter Property="Visibility" Value="{Binding Visiable,Converter={StaticResource Boolean2VisibilityConverter}}" />
                                            </Style>
                                        </ItemsControl.ItemContainerStyle>
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Grid ClipToBounds="False" Margin="2" MaxWidth="145">
                                                    <!-- 主要内容区域 -->
                                                    <Border Background="{DynamicResource SecondaryRegionBrush}" CornerRadius="4" Margin="1" Padding="4,2"
                                                            BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" ToolTip="{Binding Tooltip}">
                                                        <Border.Style>
                                                            <Style TargetType="Border">
                                                                <Setter Property="MinHeight" Value="24" />
                                                                <Style.Triggers>
                                                                    <Trigger Property="IsMouseOver" Value="True">
                                                                        <Setter Property="Background" Value="{DynamicResource DarkMaskBrush}" />
                                                                        <Setter Property="BorderBrush" Value="{DynamicResource AccentBrush}" />
                                                                    </Trigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </Border.Style>

                                                        <Grid VerticalAlignment="Center">
                                                            <!-- Boolean Options -->
                                                            <CheckBox Visibility="{Binding IsBool,Converter={StaticResource Boolean2VisibilityConverter}}"
                                                                      IsChecked="{Binding IsActivate}" FontSize="10"
                                                                      Foreground="{DynamicResource PrimaryTextBrush}"
                                                                      VerticalAlignment="Center" VerticalContentAlignment="Center"
                                                                      Padding="4,0,0,0">
                                                                <CheckBox.Content>
                                                                    <TextBlock Text="{Binding Text}" TextWrapping="Wrap" TextAlignment="Left"/>
                                                                </CheckBox.Content>
                                                            </CheckBox>

                                                            <!-- Enum Options -->
                                                            <StackPanel Visibility="{Binding IsEnum,Converter={StaticResource Boolean2VisibilityConverter}}"
                                                                        VerticalAlignment="Center">
                                                                <TextBlock Text="{Binding Text}" FontSize="10" FontWeight="Medium"
                                                                           Foreground="{DynamicResource PrimaryTextBrush}"
                                                                           TextWrapping="NoWrap" TextTrimming="CharacterEllipsis"
                                                                           MaxHeight="12" Margin="0,0,0,1" />
                                                                <ComboBox ItemsSource="{Binding Source}" SelectedItem="{Binding Value}"
                                                                          FontSize="9" Height="18" HorizontalAlignment="Stretch" Padding="4,0">
                                                                    <ComboBox.ItemTemplate>
                                                                        <DataTemplate>
                                                                            <TextBlock Text="{Binding Converter={StaticResource NameConverter}}" FontSize="9" />
                                                                        </DataTemplate>
                                                                    </ComboBox.ItemTemplate>
                                                                </ComboBox>
                                                            </StackPanel>
                                                        </Grid>
                                                    </Border>

                                                    <!-- 右上角三角形限免标签 -->
                                                    <Canvas HorizontalAlignment="Right" VerticalAlignment="Top" Visibility="Collapsed"
                                                            Width="24" Height="24" Panel.ZIndex="100" ToolTip="{DynamicResource OptionLIMF_Tooltip}"
                                                            Margin="0,2,2,0" ClipToBounds="False">
                                                        <!-- 三角形路径 -->
                                                        <Path Fill="#FF4CAF50" Stroke="#FF388E3C" StrokeThickness="0.5">
                                                            <Path.Data>
                                                                <PathGeometry>
                                                                    <PathFigure StartPoint="3,0">
                                                                        <LineSegment Point="24,0" />
                                                                        <ArcSegment Point="24,3" Size="3,3" SweepDirection="Clockwise" />
                                                                        <LineSegment Point="24,24" />
                                                                        <LineSegment Point="3,0" />
                                                                    </PathFigure>
                                                                </PathGeometry>
                                                            </Path.Data>
                                                            <Path.Effect>
                                                                <DropShadowEffect Color="Black" BlurRadius="1.5" ShadowDepth="0.6" Opacity="0.4" />
                                                            </Path.Effect>
                                                        </Path>
                                                        <!-- 限免文字 -->
                                                        <TextBlock Text="{DynamicResource HomePage_LimitedFree}" FontSize="8.5" FontWeight="Black"
                                                                   Foreground="White" Canvas.Left="8" Canvas.Top="4"
                                                                   RenderTransformOrigin="0.5,0.5">
                                                            <TextBlock.Effect>
                                                                <DropShadowEffect Color="Black" BlurRadius="0.8" ShadowDepth="0.4" Opacity="1" />
                                                            </TextBlock.Effect>
                                                            <TextBlock.RenderTransform>
                                                                <RotateTransform Angle="45" />
                                                            </TextBlock.RenderTransform>
                                                        </TextBlock>
                                                    </Canvas>
                                                </Grid>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </ScrollViewer>
                            </Border>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- 角色信息页面 -->
                <Grid x:Name="CharacterInfoContent" Visibility="Collapsed">
                    <Border Background="{DynamicResource RegionBrush}" CornerRadius="8" Padding="15">
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>

                                <TextBlock Grid.Column="0" Text="{DynamicResource HomePage_CharacterInfo}" FontSize="14" FontWeight="Bold" Foreground="{DynamicResource PrimaryTextBrush}" VerticalAlignment="Center" />
                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                    <ToggleButton x:Name="AllowStatisticsButton" Padding="12,6" IsChecked="{Binding IsActivate}">
                                        <ToggleButton.Style>
                                            <Style TargetType="ToggleButton">
                                                <Setter Property="Background" Value="{DynamicResource SecondaryRegionBrush}" />
                                                <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}" />
                                                <Setter Property="BorderThickness" Value="1" />
                                                <Setter Property="Foreground" Value="{DynamicResource SecondaryTextBrush}" />
                                                <Setter Property="FontSize" Value="12" />
                                                <Setter Property="FontWeight" Value="Medium" />
                                                <Setter Property="Cursor" Value="Hand" />
                                                <Setter Property="Template">
                                                    <Setter.Value>
                                                        <ControlTemplate TargetType="ToggleButton">
                                                            <Border Background="{TemplateBinding Background}"
                                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                                    CornerRadius="4"
                                                                    Padding="{TemplateBinding Padding}">
                                                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                                                    <TextBlock x:Name="IconText" Text="📊" FontSize="14" Margin="0,0,6,0" VerticalAlignment="Center" />
                                                                    <TextBlock Text="{DynamicResource HomePage_AllowStatistics}" FontSize="{TemplateBinding FontSize}"
                                                                               FontWeight="{TemplateBinding FontWeight}"
                                                                               Foreground="{TemplateBinding Foreground}"
                                                                               VerticalAlignment="Center" />
                                                                </StackPanel>
                                                            </Border>
                                                            <ControlTemplate.Triggers>
                                                                <Trigger Property="IsMouseOver" Value="True">
                                                                    <Setter Property="Background" Value="{DynamicResource DarkMaskBrush}" />
                                                                    <Setter Property="BorderBrush" Value="{DynamicResource AccentBrush}" />
                                                                </Trigger>
                                                                <Trigger Property="IsChecked" Value="True">
                                                                    <Setter Property="Background" Value="{DynamicResource SuccessBrush}" />
                                                                    <Setter Property="BorderBrush" Value="{DynamicResource SuccessBrush}" />
                                                                    <Setter Property="Foreground" Value="White" />
                                                                    <Setter TargetName="IconText" Property="Text" Value="✅" />
                                                                </Trigger>
                                                                <Trigger Property="IsPressed" Value="True">
                                                                    <Setter Property="Background" Value="{DynamicResource PrimaryBrush}" />
                                                                    <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}" />
                                                                </Trigger>
                                                            </ControlTemplate.Triggers>
                                                        </ControlTemplate>
                                                    </Setter.Value>
                                                </Setter>
                                            </Style>
                                        </ToggleButton.Style>
                                    </ToggleButton>
                                </StackPanel>
                            </Grid>

                            <!-- 角色卡片网格 -->
                            <Border Background="Transparent" CornerRadius="4" MaxHeight="190" Margin="0,10,0,0"
                                    x:Name="CharacterListContainer">
                                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" Padding="0,0,8,0">
                                    <ItemsControl ItemsSource="{Binding AutoSaved.User}">
                                        <ItemsControl.ItemsPanel>
                                            <ItemsPanelTemplate>
                                                <UniformGrid Columns="2" />
                                            </ItemsPanelTemplate>
                                        </ItemsControl.ItemsPanel>
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <Grid MaxWidth="218">
                                                    <Border Background="{DynamicResource SecondaryRegionBrush}" CornerRadius="4" Padding="10" Margin="3" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1">
                                                        <Border.Style>
                                                            <Style TargetType="Border">
                                                                <Style.Triggers>
                                                                    <Trigger Property="IsMouseOver" Value="True">
                                                                        <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}" />
                                                                        <Setter Property="Background" Value="{DynamicResource RegionBrush}" />
                                                                    </Trigger>
                                                                </Style.Triggers>
                                                            </Style>
                                                        </Border.Style>

                                                        <Border.InputBindings>
                                                            <MouseBinding MouseAction="LeftDoubleClick" Command="{Binding ViewCommand}" />
                                                        </Border.InputBindings>

                                                        <Border.ContextMenu>
                                                            <ContextMenu>
                                                                <MenuItem Header="{DynamicResource MenuItem_ViewDetails}" Command="{Binding ViewCommand}" />
                                                                <MenuItem Header="{DynamicResource MenuItem_DeleteCharacter}" Command="{Binding DeleteCommand}" Foreground="{DynamicResource DangerBrush}" />
                                                            </ContextMenu>
                                                        </Border.ContextMenu>

                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="Auto" />
                                                                <ColumnDefinition Width="*" />
                                                            </Grid.ColumnDefinitions>

                                                            <!-- 角色头像 -->
                                                            <Border Grid.Column="0" Width="45" Height="45" CornerRadius="22" Margin="0,0,10,0">
                                                                <Image Source="{Binding Job, Converter={StaticResource ImageSourceSelector}}" Width="30" Height="30" HorizontalAlignment="Center" VerticalAlignment="Center" />
                                                            </Border>

                                                            <!-- 角色信息 -->
                                                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                                <TextBlock Text="{Binding Name}" FontSize="14" FontWeight="Bold" Foreground="{DynamicResource PrimaryTextBrush}" />
                                                                <TextBlock Text="{Binding World}" FontSize="12" Margin="0,1,0,0" Foreground="{DynamicResource SecondaryTextBrush}" />
                                                                <TextBlock Text="{Binding Level}" FontSize="12" Margin="0,1,0,0" Foreground="{DynamicResource PrimaryBrush}" ToolTip="{Binding NeedExp}" />
                                                            </StackPanel>
                                                        </Grid>
                                                    </Border>
                                                </Grid>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </ScrollViewer>
                            </Border>

                            <!-- 空状态提示 -->
                            <Border Background="{DynamicResource SecondaryRegionBrush}" CornerRadius="4" Padding="30" Margin="0,10,0,0"
                                    Visibility="Visible" x:Name="EmptyState" MaxHeight="190">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <TextBlock FontSize="14" FontWeight="Medium" Foreground="{DynamicResource SecondaryTextBrush}" HorizontalAlignment="Center">
                                        <Run Text="{DynamicResource HomePage_AuthorizeMessage}" />
                                    </TextBlock>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Border>
                </Grid>
            </Grid>
        </Border>

        <!-- 页面指示器 -->
        <Border Grid.Row="1" Background="{DynamicResource SecondaryRegionBrush}" Padding="0,8">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <!-- 页面指示点 -->
                <Ellipse x:Name="Page1Indicator" Width="8" Height="8" Margin="4,0" Fill="{DynamicResource PrimaryBrush}" Cursor="Hand"
                         MouseLeftButtonDown="Page1Indicator_Click" ToolTip="{DynamicResource Tooltip_GameSettings}" />
                <Ellipse x:Name="Page2Indicator" Width="8" Height="8" Margin="4,0" Fill="{DynamicResource SecondaryBorderBrush}" Cursor="Hand"
                         MouseLeftButtonDown="Page2Indicator_Click" ToolTip="{DynamicResource Tooltip_CharacterInfo}" />
            </StackPanel>
        </Border>

        <!-- Bottom Info Area -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="10 0">
            <rc:FlipbookBox Width="60" Height="30" Visibility="{Binding IsRunning,Converter={StaticResource Boolean2VisibilityConverter}}" Margin="0,0,8,0" />

            <TextBlock FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}" VerticalAlignment="Bottom">
                  <Run Text="插件版本:" />
                  <Run Text="{Binding PluginVersion,Mode=OneWay}" />
            </TextBlock>
            <TextBlock FontSize="12" Text="(有更新)" Foreground="{StaticResource DangerBrush}" FontWeight="Bold" VerticalAlignment="Bottom" Margin="5 0 0 0" ToolTip="重新点击修改按钮即可更新"
                       Visibility="{Binding NewPlugin,Converter={StaticResource Boolean2VisibilityConverter}}"/>
        </StackPanel>


        <!-- Ad Banner -->
        <Grid Grid.Row="2" Visibility="{Binding UseAds,Converter={StaticResource Boolean2VisibilityConverter}}">
            <!-- 分隔线 -->
            <Border Height="1" Background="{DynamicResource BorderBrush}" VerticalAlignment="Bottom" Margin="5,0,5,105" />

            <!-- Ad Banner -->
            <Button Command="hc:ControlCommands.OpenLink" CommandParameter="https://pxb7.com/api/promotion/s?p=tp00Tcn"
                    Style="{StaticResource ButtonCustom}" Cursor="Hand"
                    HorizontalAlignment="Right" VerticalAlignment="Bottom" ToolTip="{DynamicResource Tooltip_VisitWebsite}">
                <hc:GifImage x:Name="AdGifImage" Uri="pack://application:,,,/Resources/Images/ad.gif" Stretch="Fill" Width="505" Height="100" />
            </Button>
        </Grid>
    </Grid>
</Page>
