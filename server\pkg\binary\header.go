package binary

import (
	"encoding/binary"
	"fmt"
	"time"
)

// MessageHeader 消息头结构
type MessageHeader struct {
	Magic     uint8  // 协议魔数
	Version   uint8  // 协议版本
	MsgType   uint8  // 消息类型
	Flags     uint8  // 标志位
	Length    uint32 // 消息总长度
	Timestamp uint64 // 时间戳(毫秒)
}

// NewMessageHeader 创建新的消息头
func NewMessageHeader(msgType uint8, flags uint8, bodyLength uint32) *MessageHeader {
	return &MessageHeader{
		Magic:     ProtocolMagic,
		Version:   ProtocolVersion,
		MsgType:   msgType,
		Flags:     flags,
		Length:    HeaderSize + bodyLength,
		Timestamp: uint64(time.Now().UnixMilli()),
	}
}

// Encode 编码消息头为字节流
func (h *MessageHeader) Encode() []byte {
	buf := make([]byte, HeaderSize)

	buf[0] = h.Magic
	buf[1] = h.Version
	buf[2] = h.MsgType
	buf[3] = h.Flags

	// 大端序编码长度
	binary.BigEndian.PutUint32(buf[4:8], h.Length)

	// 大端序编码时间戳
	binary.BigEndian.PutUint64(buf[8:16], h.Timestamp)

	return buf
}

// DecodeHeader 从字节流解码消息头
func DecodeHeader(data []byte) (*MessageHeader, error) {
	if len(data) < HeaderSize {
		return nil, fmt.Errorf("insufficient data for header: need %d bytes, got %d", HeaderSize, len(data))
	}

	header := &MessageHeader{
		Magic:     data[0],
		Version:   data[1],
		MsgType:   data[2],
		Flags:     data[3],
		Length:    binary.BigEndian.Uint32(data[4:8]),
		Timestamp: binary.BigEndian.Uint64(data[8:16]),
	}

	// 验证魔数
	if header.Magic != ProtocolMagic {
		return nil, fmt.Errorf("invalid protocol magic: expected 0x%02X, got 0x%02X", ProtocolMagic, header.Magic)
	}

	// 验证版本
	if header.Version != ProtocolVersion {
		return nil, fmt.Errorf("unsupported protocol version: expected 0x%02X, got 0x%02X", ProtocolVersion, header.Version)
	}

	// 验证消息长度
	if header.Length < HeaderSize || header.Length > MaxMessageSize {
		return nil, fmt.Errorf("invalid message length: %d (min: %d, max: %d)", header.Length, HeaderSize, MaxMessageSize)
	}

	return header, nil
}

// GetBodyLength 获取消息体长度
func (h *MessageHeader) GetBodyLength() uint32 {
	if h.Length < HeaderSize {
		return 0
	}
	return h.Length - HeaderSize
}

// HasFlag 检查是否设置了指定标志位
func (h *MessageHeader) HasFlag(flag uint8) bool {
	return (h.Flags & flag) != 0
}

// SetFlag 设置标志位
func (h *MessageHeader) SetFlag(flag uint8) {
	h.Flags |= flag
}

// ClearFlag 清除标志位
func (h *MessageHeader) ClearFlag(flag uint8) {
	h.Flags &= ^flag
}

// IsRequest 检查是否为请求消息
func (h *MessageHeader) IsRequest() bool {
	return !IsResponseType(h.MsgType)
}

// IsResponse 检查是否为响应消息
func (h *MessageHeader) IsResponse() bool {
	return IsResponseType(h.MsgType)
}

// GetTime 获取时间戳对应的时间
func (h *MessageHeader) GetTime() time.Time {
	return time.UnixMilli(int64(h.Timestamp))
}

// String 返回消息头的字符串表示
func (h *MessageHeader) String() string {
	msgTypeName := MsgTypeNames[h.MsgType]
	if msgTypeName == "" {
		msgTypeName = fmt.Sprintf("Unknown(0x%02X)", h.MsgType)
	}

	return fmt.Sprintf("Header{Magic:0x%02X, Version:0x%02X, Type:%s, Flags:0x%02X, Length:%d, Time:%s}",
		h.Magic, h.Version, msgTypeName, h.Flags, h.Length, h.GetTime().Format(time.RFC3339))
}

// Validate 验证消息头的有效性
func (h *MessageHeader) Validate() error {
	if h.Magic != ProtocolMagic {
		return fmt.Errorf("invalid protocol magic: 0x%02X", h.Magic)
	}

	if h.Version != ProtocolVersion {
		return fmt.Errorf("unsupported protocol version: 0x%02X", h.Version)
	}

	if h.Length < HeaderSize || h.Length > MaxMessageSize {
		return fmt.Errorf("invalid message length: %d", h.Length)
	}

	// 验证消息类型
	if _, exists := MsgTypeNames[h.MsgType]; !exists {
		return fmt.Errorf("unknown message type: 0x%02X", h.MsgType)
	}

	return nil
}
