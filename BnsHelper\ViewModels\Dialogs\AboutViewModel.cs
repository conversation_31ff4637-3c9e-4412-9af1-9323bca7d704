﻿using CommunityToolkit.Mvvm.ComponentModel;
using HandyControl.Tools.Extension;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Services.Network.BinaryProtocol;
using Xylia.BnsHelper.Services.Network.Service;

namespace Xylia.BnsHelper.ViewModels;
internal partial class AboutViewModel : ObservableObject, IDialogResultable<bool>
{
    #region Properties
    [ObservableProperty] static Team? _team;
    [ObservableProperty] static string? _referencesLabel;

    public bool Result { get; set; }
    public Action? CloseAction { get; set; }
    #endregion

    #region Methods
    public async Task Initialize()
    {
        ReferencesLabel ??= string.Join(", ",
            "BnsModPolice", "CUE4Parse", "HandyControl", "AutoUpdater.NET",
            "ini-parser-netstandard", "Oodle.NET", "RestSharp", "Serilog", "SkiaSharp", "Vanara.PInvoke");

        Team ??= await GetTeamAsync();
    }

    /// <summary>
    /// 异步获取团队信息
    /// </summary>
    private async Task<Team?> GetTeamAsync()
    {
        try
        {
            using var session = new BnszsGateSession();
            session.SendPacket(new TeamPacket(), MessageTypes.GetTeamInfo);

            // 等待团队信息响应
            var response = await session.WaitForResponseWithRetry(MessageTypes.GetTeamInfoResponse, 3, 10 * 1000);
            if (response is TeamPacket teamResponse) return teamResponse.TeamInfo;
        }
        catch
        {

        }

        return null;
    }
    #endregion
}
