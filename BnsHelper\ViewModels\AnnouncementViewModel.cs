using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Diagnostics;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Services;

namespace Xylia.BnsHelper.ViewModels;

/// <summary>
/// 公告窗口视图模型
/// </summary>
internal partial class AnnouncementViewModel : ObservableObject
{
    #region Constructor
    readonly AnnouncementService _announcementService;

    public AnnouncementViewModel()
    {
        _announcementService = AnnouncementService.Instance;
        _announcementService.OnUnreadCountChanged += (s, e) =>
        {
            OnPropertyChanged(nameof(Announcements));
            OnPropertyChanged(nameof(UnreadCount));
            OnPropertyChanged(nameof(HasUnread));
            OnPropertyChanged(nameof(TotalCount));
            OnPropertyChanged(nameof(StatusText));
        };
    }
    #endregion

    #region Properties
    [ObservableProperty] bool _isRefreshing = false;

    /// <summary>
    /// 公告列表
    /// </summary>
    public IEnumerable<Announcement> Announcements => _announcementService.Announcements;

    /// <summary>
    /// 未读公告数量
    /// </summary>
    public int UnreadCount => _announcementService.UnreadCount;

    /// <summary>
    /// 是否有未读公告
    /// </summary>
    public bool HasUnread => UnreadCount > 0;

    /// <summary>
    /// 公告总数
    /// </summary>
    public int TotalCount => Announcements.Count();

    /// <summary>
    /// 状态文本
    /// </summary>
    public string StatusText => HasUnread ? $"未读 {UnreadCount} 条" : "全部已读";
    #endregion

    #region Commands
    [RelayCommand] void MarkAsRead(uint announcementId) => _announcementService.MarkAsRead(announcementId);
    [RelayCommand] void MarkAllAsRead() => _announcementService.MarkAllAsRead();

    [RelayCommand]
    private async Task Refresh()
    {
        if (IsRefreshing) return;

        try
        {
            IsRefreshing = true;
            await _announcementService.RefreshAnnouncementsAsync();
            await Task.Delay(1000); // 显示完成状态1秒
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"刷新公告失败: {ex.Message}");
            await Task.Delay(2000);
        }
        finally
        {
            IsRefreshing = false;
        }
    }
    #endregion
}
