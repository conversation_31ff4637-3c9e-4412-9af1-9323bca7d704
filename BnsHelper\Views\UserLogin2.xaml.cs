﻿using HandyControl.Interactivity;
using Microsoft.Web.WebView2.Core;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RestSharp;
using Serilog;
using System.Net;
using System.Net.Http;
using System.Web;
using System.Windows;
using System.Windows.Interop;
using Vanara.PInvoke;
using Xylia.BnsHelper.Common;
using Xylia.BnsHelper.Common.Extensions;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.Services;
using Xylia.BnsHelper.Services.ApiEndpoints;
using Xylia.BnsHelper.Services.Network.BinaryProtocol;
using Xylia.BnsHelper.Services.Network.Service;
using Xylia.BnsHelper.ViewModels;
using Xylia.Preview.Common.Extension;

namespace Xylia.BnsHelper.Views;
public partial class UserLogin2
{
    #region Constructor
    //const string Source = "https://xui.ptlogin2.qq.com/cgi-bin/xlogin?appid=715030901&s_url=https%3A%2F%2Fqun.qq.com/&pt_disable_pwd=1&hide_close_icon=1";
    const string Source = $"https://xui.ptlogin2.qq.com/cgi-bin/xlogin?appid=716027609&daid=383&target=self&s_url=https%3A%2F%2Fgraph.qq.com%2Foauth2.0%2Flogin_jump&pt_3rd_aid={AMSEndpoint.APPID}&pt_disable_pwd=1&hide_close_icon=1";

    public UserLogin2()
    {
        InitializeComponent();
        CheckWebView2Runtime();

        // 窗口失去焦点时刷新二维码
        Loaded += OnLoaded;
        Deactivated += OnDeactivated;

        // 获取必要参数
        var query = HttpUtility.ParseQueryString(new Uri(Source).Query);
        callback = HttpUtility.UrlDecode(query["s_url"]);
    }
    #endregion

    #region Methods
    private void OnLoaded(object? sender, EventArgs e)
    {
        Activate();
        User32.SetWindowDisplayAffinity(new WindowInteropHelper(this).Handle, User32.WindowDisplayAffinity.WDA_MONITOR);
    }

    private void OnDeactivated(object? sender, EventArgs e)
    {
        if (wv?.CoreWebView2 != null)
        {
            wv.CoreWebView2.ExecuteScriptAsync("pt.plogin.begin_qrlogin();");
        }
    }

    private void CheckWebView2Runtime()
    {
        try
        {
            // 尝试获取WebView2运行时版本
            var version = CoreWebView2Environment.GetAvailableBrowserVersionString();
            if (string.IsNullOrEmpty(version)) throw new WebView2RuntimeNotFoundException();

            // 运行时可用，设置WebView2源
            wv.Source = new Uri(Source);
        }
        catch (Exception ex)
        {
            ShowWebView2Error(ex);
        }
    }

    private void CoreWebView2InitializationCompleted(object? sender, CoreWebView2InitializationCompletedEventArgs e)
    {
        if (!e.IsSuccess)
        {
            ShowWebView2Error(null);
            return;
        }

        wv.CoreWebView2.Settings.AreDevToolsEnabled = wv.CoreWebView2.Settings.AreBrowserAcceleratorKeysEnabled = false;
        wv.CoreWebView2.Settings.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 bnszs/0.1";
        wv.CoreWebView2.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);

        wv.CoreWebView2.ContextMenuRequested += (s, e) => e.Handled = true;
        wv.CoreWebView2.NewWindowRequested += CoreWebView2_NewWindowRequested;
        wv.CoreWebView2.SourceChanged += CoreWebView2_SourceChanged;
        wv.CoreWebView2.WebResourceResponseReceived += CoreWebView2_WebResourceResponseReceived;
    }

    private void CoreWebView2_NewWindowRequested(object? sender, CoreWebView2NewWindowRequestedEventArgs e)
    {
        e.Handled = true;
        new OpenLinkCommand().Execute(e.Uri.ToString());
    }

    private void CoreWebView2_SourceChanged(object? sender, CoreWebView2SourceChangedEventArgs e)
    {
        var source = wv.CoreWebView2.Source;
        if (source.StartsWith("https://xui.ptlogin2.qq.com/")) uin = 0;
        else if (source == callback)
        { 
        
        }
    }

    private async void CoreWebView2_WebResourceResponseReceived(object? sender, CoreWebView2WebResourceResponseReceivedEventArgs e)
    {
        if (e.Request.Uri.Contains("https://ssl.ptlogin2.graph.qq.com/check_sig"))
        {
            // 用户授权快捷登录，开始登录流程
            var query = HttpUtility.ParseQueryString(e.Request.Uri);
            uin = query["uin"].To<long>();
            wv.Visibility = Visibility.Collapsed;
            ShowLoading();

            try
            {
                // 获取AMS信息
                var cookies = HttpHelper.ParseCookies(e.Response.Headers);
                await ProcessOAuthLogin(cookies);
                await ProcessUDPLogin();
            }
            catch (Exception ex)
            {
                ShowError(ex.Message);
            }
        }
    }

    private void ShowError(string message, string? retry = null)
    {
        // Hide WebView2 control
        wv.Visibility = Visibility.Collapsed;

        // Hide loading icon and status text, show error container
        LoadingIcon.Visibility = Visibility.Collapsed;
        StatusText.Visibility = Visibility.Collapsed;
        ErrorContainer.Visibility = Visibility.Visible;

        // Update error text
        ErrorText.Text = message;

        // Update button text for error
        RetryButton.Content = retry ?? "重试";
        CancelButton.Content = "取消";

        // Show container
        ButtonContainer.Visibility = Visibility.Visible;
        LoadingOverlay.Visibility = Visibility.Visible;
    }

    private void ShowWebView2Error(Exception? ex)
    {
        Log.Error(ex, "WebView2 initialization failed");
        ShowError("浏览器组件初始化失败\n请下载 Microsoft Edge WebView2 组件", "下载");
    }

    private void ShowLoading()
    {
        // Show loading icon and status text, hide error elements
        LoadingIcon.Visibility = Visibility.Visible;
        StatusText.Visibility = Visibility.Visible;
        ErrorContainer.Visibility = Visibility.Collapsed;
        ButtonContainer.Visibility = Visibility.Collapsed;

        // Reset status text
        StatusText.Text = "登录中";

        // Show overlay
        LoadingOverlay.Visibility = Visibility.Visible;
    }

    private void RetryButton_Click(object sender, RoutedEventArgs e)
    {
        // Check if this is a WebView2 error (button text is "下载")
        if (RetryButton.Content.ToString() == "下载")
        {
            new OpenLinkCommand().Execute("https://developer.microsoft.com/zh-cn/microsoft-edge/webview2/consumer/");
        }

        // Normal retry logic
        // Hide error state and show browser again
        LoadingOverlay.Visibility = Visibility.Collapsed;
        wv.Visibility = Visibility.Visible;
        wv.Source = new Uri(Source);
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        // Close the login window
        DialogResult = false;
        Close();
    }
    #endregion

    #region Verify
    int GenerateGTK(string? skey)
    {
        if (string.IsNullOrEmpty(skey)) return 0;

        int t = 5381;
        for (int n = 0; n < skey.Length; n++)
            t += (t << 5) + skey[n];

        return t & 2147483647;
    }

    // 检查群组信息
    private async Task CheckWhiteGroup()
    {
        // 生成请求信息
        var cookies = await wv.CoreWebView2.CookieManager.GetCookiesAsync(callback);
        var data = new Dictionary<string, string>
        {
            ["bkn"] = GenerateGTK(cookies.FirstOrDefault(x => x.Name == "skey")?.Value).ToString(),
            ["ts"] = DateTimeOffset.Now.ToUnixTimeMilliseconds().ToString(),
        };

        // 请求群组接口
        var client = new HttpClient();
        client.DefaultRequestHeaders.Add("accept", "application/json, text/plain, */*");
        client.DefaultRequestHeaders.Add("cookie", string.Join(";", cookies.Select(x => $"{x.Name}={x.Value}")));

        var response = await client.PostAsync("https://qun.qq.com/cgi-bin/qun_mgr/get_group_list", new FormUrlEncodedContent(data));
        var content = JsonConvert.DeserializeObject<JToken>(await response.Content.ReadAsStringAsync()) ?? throw new Exception("无法获取群组信息，请稍后再试");

        var groups = new HashSet<long>();
        content["join"]?.ForEach(o => groups.Add(o.Value<long>("gc")!));
        content["manage"]?.ForEach(o => groups.Add(o.Value<long>("gc")!));

        // 检查是否加入了白名单群
        var whitegroup = AppExtensions.FindProperty<long[]>("Groups");
        var flag = groups.Any(o => whitegroup.Contains(o));
        if (!flag) throw new AppException("请先加入剑灵小助手交流群\n群列表可访问 www.bnszs.com 查看");
    }

    // 处理OAuth登录流程
    private async Task ProcessOAuthLogin(IDictionary<string, string> cookies)
    {
        // 请求初始化
        if (!cookies.TryGetValue("p_skey", out var skey)) throw new UnauthorizedAccessException("未获取到必要的登录信息");
        var client = new RestClient(new RestClientOptions() { Proxy = new WebProxy(), FollowRedirects = false });
        var request = new RestRequest("https://graph.qq.com/oauth2.0/authorize", Method.Post);
        request.AddHeader("Cookie", string.Join("; ", cookies.Select(x => $"{x.Key}={x.Value}")));
        request.AddParameter("response_type", "code");
        request.AddParameter("client_id", AMSEndpoint.APPID);
        request.AddParameter("redirect_uri", "https://milo.qq.com/comm-htdocs/login/qc_redirect.html?parent_domain=https%3A%2F%2Fbns.qq.com&isMiloSDK=1&isPc=1");
        request.AddParameter("state", "STATE");
        request.AddParameter("from_ptlogin", "1");
        request.AddParameter("src", "1");
        request.AddParameter("update_auth", "1");
        request.AddParameter("openapi", "1010");
        request.AddParameter("g_tk", GenerateGTK(skey));
        request.AddParameter("auth_time", DateTimeOffset.Now.ToUnixTimeMilliseconds());

        // 获取AccessCode
        var response = await client.ExecuteAsync(request);
        var location = response.GetHeader("Location") ?? throw new UnauthorizedAccessException("未返回有效的授权信息");
        var query = HttpUtility.ParseQueryString(new Uri(location).Query);
        OAuthException.ThrowIfError(query["error"].To<int>());

        var code = query["code"];
        if (string.IsNullOrEmpty(code)) throw new OAuthException(100005);

        // 获取AccessToken
        var response2 = await AMSEndpoint.LoginWithCodeAsync(code, cookies);
        if (response2.IRet == 0) ApiEndpointService.AMSEndpoint = new AMSEndpoint(response2.AccessToken!, response2.OpenId!);
    }

    // 处理助手后端登录
    private async Task ProcessUDPLogin()
    {
        if (uin == 0) throw new AppException(StringHelper.Get("UserLogin_Error"));

        try
        {
            // Generate device fingerprint locally
            var deviceFingerprint = DeviceHelper.GenerateDeviceFingerprint();

            // Create session with retry on connection failure
            var session = new BnszsSession();
            session.SendPacket(new LoginPacket { Uin = uin, DeviceFingerprint = deviceFingerprint }, MessageTypes.Login);

            // Wait for login response specifically, ignoring heartbeat packets
            var response = await session.WaitForResponseWithRetry(MessageTypes.LoginResponse, 3, 3000);
            if (response is LoginPacket login)
            {
                if (login.ErrorCode != 0) throw new AppException(login.ErrorMessage);

                // 验证成功完成登录流程
                var user = MainWindowViewModel.Instance.User = new User(session, uin);
                user.UpdatePermissionInfo(login.Permission, login.PermissionExpiration);
                DialogResult = true;
            }
        }
        catch (Exception ex)
        {
            ShowError(ex.Message);
            return;
        }
        finally
        {
            // Hide loading animation only on success
            if (DialogResult == true)
            {
                LoadingOverlay.Visibility = Visibility.Collapsed;
            }
        }
    }
    #endregion

    #region Fields
    private readonly string? callback;
    private long uin;
    #endregion
}
