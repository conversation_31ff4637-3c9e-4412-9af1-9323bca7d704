database:
    charset: utf8mb4
    connmaxlifetime: 3600
    dbname: bns
    driver: mysql
    host: localhost
    loc: Local
    maxidleconns: 10
    maxopenconns: 100
    parsetime: true
    password: root
    port: 3306
    username: root
heartbeat:
    cleanup_interval: 5m
    client_interval: 25m
    server_timeout: 30m
http:
    host: 0.0.0.0
    port: 8080
log:
    console: true
    file: false
    file_path: logs/server.log
    level: INFO
redis:
    db: 4
    host: localhost
    password: ""
    port: 6379
    test_db: 5
server:
    host: 0.0.0.0
    port: 8081
test_server:
    enabled: false
