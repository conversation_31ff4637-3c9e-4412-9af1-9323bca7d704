using Newtonsoft.Json;

namespace Xylia.BnsHelper.Models;

/// <summary>
/// 服务器配置模型
/// </summary>
internal class ServerConfig
{
    public int id;
    public string? region;
    public string ip = string.Empty;
    public int port;
    public int mode;
}

/// <summary>
/// 服务器配置集合
/// </summary>
internal class ServerConfigCollection
{
    [JsonProperty("servers")]
    public List<ServerConfig> Servers { get; set; } = [];

    /// <summary>
    /// 根据测试模式获取合适的服务器
    /// </summary>
    /// <param name="useTestServer">是否使用测试服务器</param>
    public ServerConfig GetServer(bool useTestServer)
    {
        var servers = Servers.Where(s => useTestServer == (s.mode == 1));
        return servers.FirstOrDefault() ?? throw new Exception("没有有效的服务器配置");
    }

    #region Constructor
    public ServerConfigCollection() { }

    public ServerConfigCollection(params ServerConfig[] servers)
    {
        Servers = [.. servers];
    }
    #endregion
}
