using CommunityToolkit.Mvvm.ComponentModel;
using Serilog;
using System.Diagnostics;
using System.IO;
using System.Speech.Synthesis;
using System.Text.Json.Serialization;
using Vanara.PInvoke;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Services.Network.Plugin;
using Xylia.BnsHelper.ViewModels;
using static Xylia.Preview.Data.Models.ChatChannelOption;

namespace Xylia.BnsHelper.Models.Triggers;

/// <summary>
/// 音频播放动作
/// </summary>
public partial class AudioPlayAction : TriggerAction
{
    [ObservableProperty] string? _audioPath;    // 音频文件路径
    [ObservableProperty] int _volume = 80;      // 音量 (0-100)
    [ObservableProperty] bool _loop = false;    // 是否循环播放

    public override string Describe => $"播放音频 (音量: {Volume} 循环: {Loop})";

    protected override async Task ExecuteActionAsync(TriggerExecutionContext context)
    {
        var file = ReplaceVariables(AudioPath, context.Variables);
        if (string.IsNullOrEmpty(file) || !File.Exists(file)) throw new FileNotFoundException("未找到指定的音频文件");

        await AudioSteamReader.Play(File.OpenRead(file), Volume * 0.01f);
    }
}

/// <summary>
/// 计数器动作
/// </summary>
public partial class CounterAction : TriggerAction
{
    #region Fields
    [ObservableProperty] string _counterName = string.Empty;     // 计数器名称
    [ObservableProperty] CounterOperation _operation;            // 计数器类型
    [ObservableProperty] int _value = 1;                         // 计数器变化值

    /// <summary>
    /// 计数器操作类型
    /// </summary>
    public enum CounterOperation
    {
        Increment,
        Decrement,
        Reset,
        Set
    }
    #endregion

    #region Methods
    public override string Describe => $"计数器操作: {CounterName} ({Operation}) 值: {Value}";

    protected override async Task ExecuteActionAsync(TriggerExecutionContext context)
    {
        var name = ReplaceVariables(CounterName, context.Variables);

        switch (Operation)
        {
            case CounterOperation.Increment:
                CounterCondition.IncrementCounter(name, Value);
                break;
            case CounterOperation.Decrement:
                CounterCondition.IncrementCounter(name, -Value);
                break;
            case CounterOperation.Reset:
                CounterCondition.ResetCounter(name);
                break;
            case CounterOperation.Set:
                CounterCondition.ResetCounter(name);
                CounterCondition.IncrementCounter(name, Value);
                break;
        }
    }
    #endregion
}

/// <summary>
/// 日志记录动作
/// </summary>
public partial class LogAction : TriggerAction
{
    #region Fields
    [ObservableProperty] string _message = string.Empty;     // 日志内容
    [ObservableProperty] LogLevel _level = LogLevel.Info;    // 日志级别

    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error,
        Fatal
    }
    #endregion

    #region Methods
    public override string Describe => $"记录日志: {Message} (级别: {Level})";

    protected override async Task ExecuteActionAsync(TriggerExecutionContext context)
    {
        var message = ReplaceVariables(Message, context.Variables);

        switch (Level)
        {
            case LogLevel.Debug:
                Log.Debug(message);
                break;
            case LogLevel.Info:
                Log.Information(message);
                break;
            case LogLevel.Warning:
                Log.Warning(message);
                break;
            case LogLevel.Fatal:
                Log.Fatal(message);
                break;
        }
    }
    #endregion
}

/// <summary>
/// 系统通知动作
/// </summary>
public partial class NotificationAction : TriggerAction
{
    [ObservableProperty] string _title = string.Empty;      // 通知标题
    [ObservableProperty] string _message = string.Empty;    // 通知内容
    [ObservableProperty] string _icon = string.Empty;       // 通知图标
    [ObservableProperty] int _duration = 5000;              // 显示持续时间（毫秒）

    public override string Describe => $"发送消息通知: {Message}";

    protected override async Task ExecuteActionAsync(TriggerExecutionContext context)
    {
        var title = ReplaceVariables(Title, context.Variables);
        var message = ReplaceVariables(Message, context.Variables);
        MainWindowViewModel.SendBalloonTip(title, message);
    }
}

/// <summary>
/// 文本显示动作
/// </summary>
public partial class TextDisplayAction : TriggerAction
{
    [ObservableProperty] string _uniqueId = string.Empty;   // 唯一标识符
    [ObservableProperty] string _text = string.Empty;       // 显示文本
    [ObservableProperty] int _durationMs = 5000;            // 显示时长（毫秒，0表示永久显示）
    [ObservableProperty] int _fontSize = 16;                // 字体大小
    [ObservableProperty] string _textColor = "#FFFFFF";     // 文本颜色
    [ObservableProperty] string _backgroundColor = "#000000";  // 背景颜色
    [ObservableProperty] int _positionX = 100;              // 显示位置X
    [ObservableProperty] int _positionY = 100;              // 显示位置Y

    public override string Describe => $"显示文本: {Text} (位置: {PositionX}, {PositionY})";

    protected override async Task ExecuteActionAsync(TriggerExecutionContext context)
    {
        var displayText = ReplaceVariables(Text, context.Variables);
        if (string.IsNullOrEmpty(displayText)) return;

        Debug.WriteLine($"[TextDisplayAction] 显示文本: {displayText}");

        // 这里可以集成到游戏覆盖层或WPF窗口
        // 暂时使用Debug输出作为占位符
        Debug.WriteLine($"[TextDisplay] 文本: {displayText}, 位置: ({PositionX}, {PositionY}), 时长: {DurationMs}ms");
    }
}

/// <summary>
/// TTS语音播报动作
/// </summary>
public partial class TTSAction : TriggerAction
{
    #region Fields
    [ObservableProperty] string _text = string.Empty;   // 播报文本
    [ObservableProperty] int _rate = 0;                 // 语音速度 (0-10)
    [ObservableProperty] int _volume = 100;             // 音量 (0-100)
    [ObservableProperty] string _voice = string.Empty;  // 语音名称
    #endregion

    #region Methods
    [JsonIgnore] public IEnumerable<string> Voices => new SpeechSynthesizer().GetInstalledVoices().Select(o => o.VoiceInfo.Name);

    public override string Describe => $"语音播报: {Text} (语速: {Rate} 音量: {Volume})";

    protected override async Task ExecuteActionAsync(TriggerExecutionContext context)
    {
        var speechText = ReplaceVariables(Text, context.Variables);
        if (string.IsNullOrEmpty(speechText)) return;

        using var synthesizer = new SpeechSynthesizer();
        synthesizer.Rate = Math.Max(0, Math.Min(10, Rate));
        synthesizer.Volume = Math.Max(0, Math.Min(100, Volume));

        // 设置语音
        if (!string.IsNullOrEmpty(Voice))
        {
            try
            {
                synthesizer.SelectVoice(Voice);
            }
            catch
            {
                // 如果指定语音不可用，使用默认语音
            }
        }

        // 播报文本
        synthesizer.Speak(speechText);
    }
    #endregion
}

/// <summary>
/// 发送游戏消息动作
/// </summary>
public partial class SendGameMessageAction : TriggerAction
{
    [ObservableProperty] string _message = string.Empty;    // 消息内容
    [ObservableProperty] bool _headline;                    // 标题消息

    public override string Describe => $"发送游戏内通知: {Message}";

    protected override async Task ExecuteActionAsync(TriggerExecutionContext context)
    {
        var message = ReplaceVariables(Message, context.Variables);
        if (string.IsNullOrEmpty(message)) return;

        var session = PluginSession.GetOrCreate(WindowHelper.GetGameWindow());
        session.Send(new InstantNotification()
        {
            Text = Message,
            Category = CategorySeq.Info,
            Headline = Headline
        });
    }
}

/// <summary>
/// 键盘输入动作
/// </summary>
public partial class KeyboardInputAction : TriggerAction
{
    [ObservableProperty] string _keys = string.Empty;       // 按键序列
    [ObservableProperty] int _keyDelay = 100;                // 按键间隔（毫秒）

    public override string Describe => $"发送按键: {Keys} (延时: {KeyDelay}ms)";

    protected override async Task ExecuteActionAsync(TriggerExecutionContext context)
    {
        var keys = ReplaceVariables(Keys, context.Variables);
        if (string.IsNullOrEmpty(keys)) return;

        var window = WindowHelper.GetGameWindow();
        if (window == nint.Zero) return;

        // 发送按键
        foreach (char key in keys)
        {
            User32.SendMessage(window, User32.WindowMessage.WM_KEYDOWN, key);
            Thread.Sleep(KeyDelay);
            User32.SendMessage(window, User32.WindowMessage.WM_KEYUP, key);
        }
    }
}

/// <summary>
/// 鼠标操作动作
/// </summary>
public partial class MouseAction : TriggerAction
{
    #region Fields
    [ObservableProperty] MouseOperation _operation;         // 操作类型
    [ObservableProperty] int _x = 0;                        // X坐标
    [ObservableProperty] int _y = 0;                        // Y坐标
    [ObservableProperty] bool _relative = false;            // 是否相对坐标
    [ObservableProperty] int _clickCount = 1;               // 点击次数

    /// <summary>
    /// 鼠标操作类型
    /// </summary>
    public enum MouseOperation
    {
        LeftClick,
        RightClick,
        Move,
        Scroll
    }
    #endregion

    #region Methods
    public override string Describe => $"鼠标操作: {Operation} ({X}, {Y}) 点击次数: {ClickCount} 相对: {Relative}";

    protected override async Task ExecuteActionAsync(TriggerExecutionContext context)
    {
        var targetX = X;
        var targetY = Y;

        if (Relative)
        {
            // 相对于当前鼠标位置
            var currentPos = System.Windows.Forms.Cursor.Position;
            targetX += currentPos.X;
            targetY += currentPos.Y;
        }

        switch (Operation)
        {
            case MouseOperation.LeftClick:
                User32.SetCursorPos(targetX, targetY);
                for (int i = 0; i < ClickCount; i++)
                {
                    User32.mouse_event(User32.MOUSEEVENTF.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, nint.Zero);
                    User32.mouse_event(User32.MOUSEEVENTF.MOUSEEVENTF_LEFTUP, 0, 0, 0, nint.Zero);

                    // 点击间隔
                    if (i < ClickCount - 1) Thread.Sleep(50);
                }
                break;
            case MouseOperation.RightClick:
                User32.SetCursorPos(targetX, targetY);
                for (int i = 0; i < ClickCount; i++)
                {
                    User32.mouse_event(User32.MOUSEEVENTF.MOUSEEVENTF_RIGHTDOWN, 0, 0, 0, nint.Zero);
                    User32.mouse_event(User32.MOUSEEVENTF.MOUSEEVENTF_RIGHTUP, 0, 0, 0, nint.Zero);

                    // 点击间隔
                    if (i < ClickCount - 1) Thread.Sleep(50);
                }
                break;

            case MouseOperation.Move:
                User32.SetCursorPos(targetX, targetY);
                break;
            case MouseOperation.Scroll:
                User32.SetCursorPos(targetX, targetY);
                User32.mouse_event(User32.MOUSEEVENTF.MOUSEEVENTF_WHEEL, 0, 0, ClickCount * 120, nint.Zero);
                break;
        }
    }
    #endregion
}