﻿using System.Diagnostics;
using System.Windows;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.Services.Network.BinaryProtocol;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Service;

/// <summary>
/// 服务端业务会话
/// </summary>
internal sealed class BnszsSession(ServerConfig config) : BnszsGateSession(config)
{
    #region Fields
    public string? SessionToken { get; private set; }

    /// <summary>
    /// 心跳间隔时间（毫秒）
    /// 默认：25分钟
    /// </summary>
    public const int HeartbeatInterval = 25 * 60 * 1000;
    private volatile bool _heartbeatRunning = false;
    private Thread? _heartbeatThread;

    public event EventHandler? HeartbeatFailed;
    public event EventHandler<uint>? AnnouncementVersionChanged;
    #endregion

    #region Override Methods
    internal BnszsSession() : this(new ServerConfigHelper().GetServerConfig())
    {

    }

    protected override byte[] XorKey { get; } = [92, 62, 100, 99, 255, 94, 254, 99, 33, 8, 246, 154, 15, 194, 179, 148, 252, 85, 170, 16];

    protected override IPacket OnHandlePacket(byte type, DataArchive reader)
    {
        IPacket packet = type switch
        {
            MessageTypes.LoginResponse => new LoginPacket(),
            MessageTypes.HeartbeatResponse => new HeartbeatPacket(),
            MessageTypes.LogoutResponse => new LogoutPacket(),
            MessageTypes.LuckyDrawResponse => new LuckyDrawPacket(),
            MessageTypes.LuckyStatusResponse => new LuckyStatusPacket(),
            MessageTypes.CDKeyActivateResponse => new CDKeyActivatePacket(),
            MessageTypes.ActivityVersionResponse => new ActivityVersionPacket(),
            MessageTypes.ActivityListResponse => new ActivityListPacket(),
            MessageTypes.ActivityDetailResponse => new ActivityPacket(),

            _ => throw new NotSupportedException($"Unsupported message type: 0x{type:X2}"),
        };

        packet.Read(reader);

        // 保存登录成功后的token
        if (packet is LoginPacket login && login.ErrorCode == 0)
        {
            SessionToken = login.Token;
            Debug.WriteLine($"[INFO] 登录成功，Token已保存: {SessionToken}");

            StartHeartbeat();
        }

        return packet;
    }

    protected internal override void SendPacket(IPacket packet, byte type)
    {
        if (packet is BasePacket @base) @base.Token = this.SessionToken;

        base.SendPacket(packet, type);
    }
    #endregion

    #region Methods
    private void StartHeartbeat()
    {
        if (_heartbeatRunning) return;

        _heartbeatRunning = true;
        _heartbeatThread = new Thread(Heartbeat) { IsBackground = true };
        _heartbeatThread.Start();
    }

    private void StopHeartbeat()
    {
        _heartbeatRunning = false;

        // 等待心跳线程结束，减少等待时间避免程序关闭时卡顿
        if (_heartbeatThread != null)
        {
            try
            {
                if (!_heartbeatThread.Join(500)) // 减少到500毫秒，避免关闭时卡顿
                {
                    Debug.WriteLine("[WARNING] 心跳线程未能在超时时间内结束，继续关闭");
                    // 注意：Thread.Abort在.NET Core中已被移除，这里只是记录警告
                }
                _heartbeatThread = null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 停止心跳线程时发生异常: {ex.Message}");
            }
        }
    }

    private async void Heartbeat()
    {
        int consecutiveFailures = 0;
        const int maxConsecutiveFailures = 3;

        try
        {
            while (_heartbeatRunning && IsConnected)
            {
                try
                {
                    if (!IsConnected || !_heartbeatRunning)
                    {
                        Debug.WriteLine("连接已断开或心跳已停止，退出心跳循环");
                        break;
                    }

                    Debug.WriteLine("发送心跳...");
                    var success = await SendHeartbeatAsync();
                    if (success)
                    {
                        consecutiveFailures = 0; // 重置失败计数

                        // 使用CancellationToken来支持快速退出
                        using var cts = new CancellationTokenSource();
                        try
                        {
                            await Task.Delay(HeartbeatInterval, cts.Token);
                        }
                        catch (OperationCanceledException)
                        {
                            // 心跳被取消，正常退出
                            break;
                        }
                    }
                    else
                    {
                        consecutiveFailures++;
                        Debug.WriteLine($"心跳失败 (连续失败: {consecutiveFailures}/{maxConsecutiveFailures})");

                        if (consecutiveFailures >= maxConsecutiveFailures)
                        {
                            Debug.WriteLine("连续心跳失败次数过多，触发登出");
                            OnHeartbeatFailed();
                            break;
                        }

                        // 失败后等待较短时间再重试
                        using var cts = new CancellationTokenSource();
                        try
                        {
                            await Task.Delay(5000, cts.Token);
                        }
                        catch (OperationCanceledException)
                        {
                            // 心跳被取消，正常退出
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    consecutiveFailures++;
                    Debug.WriteLine($"心跳异常 (连续失败: {consecutiveFailures}/{maxConsecutiveFailures}): {ex.Message}");

                    if (consecutiveFailures >= maxConsecutiveFailures)
                    {
                        Debug.WriteLine("连续心跳异常次数过多，触发登出");
                        OnHeartbeatFailed();
                        break;
                    }

                    // 异常后等待较短时间再重试
                    using var cts = new CancellationTokenSource();
                    try
                    {
                        await Task.Delay(5000, cts.Token);
                    }
                    catch (OperationCanceledException)
                    {
                        // 心跳被取消，正常退出
                        break;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 心跳线程发生未处理异常: {ex.Message}");
        }
        finally
        {
            Debug.WriteLine("心跳线程已退出");
            _heartbeatRunning = false;
        }
    }

    private async Task<bool> SendHeartbeatAsync()
    {
        try
        {
            if (!IsConnected)
            {
                Debug.WriteLine("连接已断开，心跳失败");
                return false;
            }

            SendPacket(new HeartbeatPacket(), MessageTypes.Heartbeat);

            // 减少超时时间和重试次数，避免服务端掉线时长时间阻塞
            var response = await WaitForResponseWithRetry(MessageTypes.HeartbeatResponse, 2, 10 * 1000);
            if (response is HeartbeatPacket hbResponse)
            {
                if (hbResponse.ErrorCode != 0)
                {
                    Debug.WriteLine($"心跳失败 - Error Code: {hbResponse.ErrorCode}, Message: {hbResponse.ErrorMessage}");
                    return false;
                }

                Debug.WriteLine($"心跳成功 - 当前在线用户数: {hbResponse.OnlineUserCount}, 强制更新: {hbResponse.ForceUpdate}, 公告版本: {hbResponse.AnnouncementVersion}");

                AnnouncementVersionChanged?.Invoke(this, hbResponse.AnnouncementVersion);

                // 检查是否需要强制更新
                if (hbResponse.ForceUpdate)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        MessageBox.Show(StringHelper.Get("Application_Update"), StringHelper.Get("Application_UpdateTitle"), MessageBoxButton.OK, MessageBoxImage.Information);
                        Process.Start(Environment.ProcessPath!, "--updated");
                        Environment.Exit(0);
                    });

                    return false; 
                }

                return true;
            }

            Debug.WriteLine("心跳响应类型异常");
            return false;
        }
        catch (TimeoutException)
        {
            Debug.WriteLine("心跳超时");
            return false;
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("连接已断开"))
        {
            Debug.WriteLine("连接已断开，心跳失败");
            return false;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"发送心跳异常: {ex.Message}");
            return false;
        }
    }

    private void OnHeartbeatFailed()
    {
        _heartbeatRunning = false;
        HeartbeatFailed?.Invoke(this, EventArgs.Empty);
    }

    public override void Dispose()
    {
        try
        {
            Debug.WriteLine("[INFO] 开始释放BnszsSession资源");

            // 停止心跳
            StopHeartbeat();

            // 清理事件订阅
            HeartbeatFailed = null;
            SessionToken = null;

            // 调用基类的Dispose
            base.Dispose();

            Debug.WriteLine("[INFO] BnszsSession资源释放完成");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 释放BnszsSession资源时发生异常: {ex.Message}");
        }
    }
    #endregion
}

/// <summary>
/// 服务端基本信息通信
/// </summary>
/// <param name="config"></param>
internal class BnszsGateSession(ServerConfig config) : EventSession(config.ip, config.port)
{
    #region Override Methods
    internal BnszsGateSession() : this(new ServerConfigHelper().GetServerConfig()) { }

    protected override byte[] XorKey { get; } = [92, 62, 100, 99, 255, 94, 254, 99, 33, 8, 246, 154, 15, 194, 179, 148, 252, 85, 170, 16];

    protected override IPacket OnHandlePacket(byte type, DataArchive reader)
    {
        IPacket packet = type switch
        {
            MessageTypes.UpdateConfigResponse => new UpdateConfigPacket(),
            MessageTypes.GetTeamInfoResponse => new TeamPacket(),
            MessageTypes.AnnouncementDetailResponse => new AnnouncementDetailPacket(),
            MessageTypes.AnnouncementVersionResponse => new AnnouncementVersionPacket(),
            MessageTypes.AnnouncementIdsResponse => new AnnouncementIdsPacket(),

            _ => throw new NotSupportedException($"Unsupported message type: 0x{type:X2}"),
        };

        packet.Read(reader);
        return packet;
    }
    #endregion
}
