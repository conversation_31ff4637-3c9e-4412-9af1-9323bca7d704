﻿using System.Collections;
using System.ComponentModel;
using System.Diagnostics;
using System.Windows;
using System.Windows.Data;
using System.Windows.Threading;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.ViewModels;

namespace Xylia.BnsHelper.Views;
public partial class DamageMeterTooltipPanel  : IDisposable
{
	#region Constructor
	public DamageMeterTooltipPanel()
	{
		InitializeComponent();
		DataContextChanged += OnDataContextChanged;
		DamageMeterViewModel.OnRefresh += OnRefresh;
		IsVisibleChanged += OnVisibilityChanged;
		Loaded += OnLoaded;
		SizeChanged += OnSizeChanged;
	}
	#endregion

	#region Methods
	private CreatureStats? Current;
	private bool _isRefreshing = false;
	private CollectionViewSource? _skillsViewSource;

	public void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
	{
		Current = e.NewValue as CreatureStats;
	}

	public void OnRefresh(object? sender, EventArgs e)
	{
		// 强制刷新当前队友的数据，确保技能详情能正常显示
		Current?.Refresh();

		// 使用温和的刷新方式，避免闪烁
		// 延迟执行以确保数据更新完成
		Dispatcher.BeginInvoke(new Action(() =>
		{
			// 确保技能详情在暂停状态下也能正常更新
			RefreshSkillDetailsForPausedState();
			GentleRefreshSkillList();
			// 只在必要时重置布局
			if (IsVisible) InvalidateVisual();
		}), DispatcherPriority.DataBind);
	}

	private void OnVisibilityChanged(object sender, DependencyPropertyChangedEventArgs e)
	{
		// 当控件变为可见时，重置布局缓存
		if (IsVisible)
		{
			// 延迟执行重置布局，确保控件完全加载
			Dispatcher.BeginInvoke(new Action(() =>
			{
				GentleRefreshSkillList();
				UpdateLayout();
			}), DispatcherPriority.Loaded);
		}
	}

	/// <summary>
	/// 应用排序到技能列表
	/// </summary>
	private void ApplySortToSkillList(IList sortDescriptions)
	{
		sortDescriptions.Clear();
		sortDescriptions.Add(new SortDescription("TotalDamage", ListSortDirection.Descending));
	}

	/// <summary>
	/// 专门处理暂停状态下的技能详情更新
	/// </summary>
	private void RefreshSkillDetailsForPausedState()
	{
		if (Current == null) return;

		// 确保在暂停状态下技能详情仍能正常显示
		if (Current.Skills != null)
		{
			// 如果数据源发生变化或者首次绑定，重新创建CollectionViewSource
			if (_skillsViewSource?.Source != Current.Skills)
			{
				// 重新绑定数据源，确保技能详情能正常显示
				_skillsViewSource = new CollectionViewSource
				{
					Source = Current.Skills
				};

				// 添加排序
				ApplySortToSkillList(_skillsViewSource.SortDescriptions);

				// 绑定到ListView
				if (SkillHolder != null)
				{
					SkillHolder.ItemsSource = _skillsViewSource.View;
				}
			}
			else if (_skillsViewSource?.View != null)
			{
				// 如果数据源没变，只需要刷新视图
				var view = _skillsViewSource.View;
				view?.Refresh();
			}
		}
	}

	/// <summary>
	/// 温和地刷新技能列表，避免闪烁
	/// </summary>
	private void GentleRefreshSkillList()
	{
		// 防止重复刷新
		if (_isRefreshing || Current?.DamageSkills == null)
			return;

		try
		{
			_isRefreshing = true;

			// 如果使用CollectionViewSource，刷新视图并重新排序
			if (_skillsViewSource?.View != null)
			{
				ApplySortToSkillList(_skillsViewSource.View.SortDescriptions);
                _skillsViewSource.View.Refresh();
            }
			else if (SkillHolder?.Items != null)
			{
				// 回退到原来的方法
				// 检查是否需要重新排序
				bool needsResorting = SkillHolder.Items.SortDescriptions.Count == 0;

				// 如果需要排序，先添加排序描述
				if (needsResorting)
				{
					ApplySortToSkillList(SkillHolder.Items.SortDescriptions);
				}

				// 只在有数据时才刷新
				if (Current.Skills.Count > 0)
				{
					// 使用最温和的刷新方式
					SkillHolder.Items.Refresh();
				}
			}
		}
		catch (Exception ex)
		{
            // 记录异常但不抛出，避免影响UI
            Debug.WriteLine($"GentleRefreshSkillList error: {ex.Message}");
		}
		finally
		{
			_isRefreshing = false;
		}
	}

	private void OnLoaded(object sender, RoutedEventArgs e)
	{
		// 触发一次刷新，防止界面没有加载成功
		OnRefresh(sender, e);
    }

	private void OnSizeChanged(object sender, SizeChangedEventArgs e)
	{
		// 当尺寸发生变化时，确保布局正确更新
		if (IsVisible)
		{
			InvalidateVisual();
		}
	}

	/// <summary>
	/// 重写测量方法，确保每次都能正确计算尺寸
	/// </summary>
	protected override Size MeasureOverride(Size constraint)
	{
		try
		{
			// 清除高度缓存，但保持固定宽度370
			ClearValue(HeightProperty);
			// 不清除宽度，保持固定宽度370
			// ClearValue(WidthProperty);

			// 调用基类的测量方法
			var size = base.MeasureOverride(constraint);

			// 确保ListView能够正确测量
			if (SkillHolder != null && SkillHolder.Items.Count > 0)
			{
				SkillHolder.InvalidateMeasure();
			}

			return size;
		}
		catch (Exception ex)
		{
			System.Diagnostics.Debug.WriteLine($"MeasureOverride error: {ex.Message}");
			return base.MeasureOverride(constraint);
		}
	}

	/// <summary>
	/// 重写排列方法，确保布局正确
	/// </summary>
	protected override Size ArrangeOverride(Size arrangeBounds)
	{
		try
		{
			var size = base.ArrangeOverride(arrangeBounds);

			// 确保ListView能够正确排列
			SkillHolder?.InvalidateArrange();

			return size;
		}
		catch (Exception ex)
		{
			System.Diagnostics.Debug.WriteLine($"ArrangeOverride error: {ex.Message}");
			return base.ArrangeOverride(arrangeBounds);
		}
	}

    public void Dispose()
    {
        _skillsViewSource = null;
        GC.SuppressFinalize(this);
    }
    #endregion
}
