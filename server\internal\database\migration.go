package database

import (
	"fmt"
	"time"
	gatewayModel "udp-server/server/internal/gateway/model"
	"udp-server/server/internal/model"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// AutoMigrate 自动执行数据库迁移
func AutoMigrate() error {
	// 先执行SQL命令禁用外键检查
	DB.Exec("SET FOREIGN_KEY_CHECKS = 0")
	defer DB.Exec("SET FOREIGN_KEY_CHECKS = 1")

	// 检查表是否存在，如果不存在则创建
	migrator := DB.Migrator()

	// 按照依赖关系顺序创建表
	tables := []interface{}{
		&model.User{},
		&model.DeviceHistory{},
		&model.Lucky{},
		&model.UserDraw{},
		&model.LuckyReward{},
		&model.CDkey{},
		&model.CDKeyCustomize{}, // CDKey自定义配置表
		&model.UserDrawResult{},
		&model.OnlineStatsHistory{},
		&model.RiskEvent{},         // 风控事件表
		&model.RiskControlConfig{}, // 风控配置表
		&gatewayModel.AdminItem{},  // 管理员权限项表
		&gatewayModel.AdminLog{},   // 管理员操作日志表
		&model.Announcement{},      // 公告表
		&model.UpdateConfig{},      // 更新配置表
		&model.WhitelistGroup{},    // 白名单群组表
		&model.Activity{},          // 活动表
		&model.ActivityFlow{},      // 活动流程表
		&model.ActivityGroup{},     // 活动分组表
	}

	// 只创建不存在的表，跳过已存在的表以避免索引冲突
	for _, table := range tables {
		if !migrator.HasTable(table) {
			fmt.Printf("Creating table for %T\n", table)
			if err := migrator.CreateTable(table); err != nil {
				return fmt.Errorf("failed to create table %T: %v", table, err)
			}
		} else {
			fmt.Printf("Table for %T already exists, skipping migration\n", table)
		}
	}

	// 插入初始数据
	if err := insertInitialData(DB); err != nil {
		return fmt.Errorf("failed to insert initial data: %v", err)
	}

	// 执行CDKey数据迁移
	if err := migrateCDKeyData(DB); err != nil {
		return fmt.Errorf("failed to migrate CDKey data: %v", err)
	}

	fmt.Println("Database migration completed successfully")
	return nil
}

// insertInitialData 插入初始数据
func insertInitialData(db *gorm.DB) error {
	// 公告版本号现在使用Redis管理，不需要数据库初始化

	// 创建测试活动数据
	if err := createTestActivityData(db); err != nil {
		logger.Error("创建测试活动数据失败: %v", err)
	}

	return nil
}

// createTestActivityData 创建测试活动数据
func createTestActivityData(db *gorm.DB) error {
	// 检查是否已存在测试活动
	var count int64
	db.Model(&model.Activity{}).Count(&count)
	if count > 0 {
		return nil // 已有活动数据，跳过创建
	}

	// 创建测试活动
	now := time.Now()
	testActivity := &model.Activity{
		ActivityId:   999999,
		ActivityName: "测试活动",
		Status:       1, // 活跃状态
		Priority:     1,
		Version:      1,
		BeginTime:    now.Add(-7 * 24 * time.Hour),  // 7天前开始
		EndTime:      now.Add(365 * 24 * time.Hour), // 1年后结束
	}

	if err := db.Create(testActivity).Error; err != nil {
		return fmt.Errorf("创建测试活动失败: %w", err)
	}

	// 创建测试流程
	testFlows := []model.ActivityFlow{
		{
			ActivityID:  999999,
			FlowId:      1001,
			FlowName:    "测试流程1",
			IdeToken:    "test_token_1",
			AccountType: 7,
			FlowType:    1,
			Custom:      1,
			Group:       1, // 分组1
			Parameters:  `{"test_param": "value1"}`,
			Status:      1,
			SortOrder:   1,
		},
		{
			ActivityID:  999999,
			FlowId:      1002,
			FlowName:    "测试流程2",
			IdeToken:    "test_token_2",
			AccountType: 7,
			FlowType:    1,
			Custom:      1,
			Group:       1, // 分组1
			Parameters:  `{"test_param": "value2"}`,
			Status:      1,
			SortOrder:   2,
		},
		{
			ActivityID:  999999,
			FlowId:      1003,
			FlowName:    "测试流程3",
			IdeToken:    "test_token_3",
			AccountType: 7,
			FlowType:    1,
			Custom:      1,
			Group:       2, // 分组2
			Parameters:  `{"test_param": "value3"}`,
			Status:      1,
			SortOrder:   3,
		},
	}

	for _, flow := range testFlows {
		if err := db.Create(&flow).Error; err != nil {
			return fmt.Errorf("创建测试流程失败: %w", err)
		}
	}

	logger.Info("成功创建测试活动数据：1个活动，3个流程")
	return nil
}

// migrateCDKeyData 迁移CDKey数据，为现有CDKey创建对应的CDKeyCustomize记录
func migrateCDKeyData(db *gorm.DB) error {
	fmt.Println("开始迁移CDKey数据...")

	// 查找所有没有对应CDKeyCustomize记录的CDKey
	var cdkeys []model.CDkey
	err := db.Raw(`
		SELECT c.* FROM bns_cdkey c
		LEFT JOIN bns_cdkey_customize cc ON c.cdkey = cc.cdkey
		WHERE cc.cdkey IS NULL
	`).Scan(&cdkeys).Error

	if err != nil {
		return fmt.Errorf("查询需要迁移的CDKey失败: %v", err)
	}

	if len(cdkeys) == 0 {
		fmt.Println("没有需要迁移的CDKey数据")
		return nil
	}

	fmt.Printf("找到 %d 个需要迁移的CDKey\n", len(cdkeys))

	// 为每个CDKey创建对应的CDKeyCustomize记录
	for _, cdkey := range cdkeys {
		customize := model.CDKeyCustomize{
			CDkey:    cdkey.CDkey,
			TimeType: "duration", // 默认使用持续时间类型
			Duration: 30,         // 默认30天
			Fixed:    nil,        // 固定时间为空
		}

		// 如果CDKey有结束时间，使用固定时间类型
		if cdkey.EndTime != nil {
			customize.TimeType = "fixed"
			customize.Fixed = cdkey.EndTime
			customize.Duration = 0
		}

		if err := db.Create(&customize).Error; err != nil {
			fmt.Printf("为CDKey %s 创建配置记录失败: %v\n", cdkey.CDkey, err)
			continue
		}
	}

	fmt.Printf("CDKey数据迁移完成，成功迁移 %d 个CDKey\n", len(cdkeys))
	return nil
}
