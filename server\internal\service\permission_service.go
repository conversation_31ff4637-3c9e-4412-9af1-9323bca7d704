package service

import (
	"fmt"
	"sort"
	"time"
	"udp-server/server/internal/config"
	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// PermissionService 权限服务
type PermissionService struct {
	db        *gorm.DB
	cache     cache.Cache
	hashCache CacheService // 支持哈希操作的缓存服务
}

// NewPermissionService 创建权限服务
func NewPermissionService(db *gorm.DB, cache cache.Cache, hashCache CacheService) *PermissionService {
	return &PermissionService{
		db:        db,
		cache:     cache,
		hashCache: hashCache,
	}
}

// CDKey信息结构
type CDKeyInfo struct {
	CDKey      string     `gorm:"column:cdkey"`
	Type       string     `gorm:"column:type"`
	TimeType   string     `gorm:"column:timeType"`
	Fixed      *time.Time `gorm:"column:fixed"`
	Duration   int        `gorm:"column:duration"`
	Permission uint8      `gorm:"column:permission"` // 权限等级
}

// CDKeyPermissionInfo CDKey权限信息结构
type CDKeyPermissionInfo struct {
	CDKey      string
	Permission uint8
	StartTime  int64
	EndTime    int64
	TimeType   string     // 时间类型：duration 或 fixed
	Duration   int        // 持续天数（仅duration类型使用）
	Fixed      *time.Time // 固定过期时间（仅fixed类型使用）
}

// 获取用户权限过期时间戳
// 参数：uid - 用户ID，permissionType - 权限类型（如 'client'）
func (s *PermissionService) GetPermissionExpiration(uid uint64, permissionType string) (int64, error) {
	// 使用哈希表存储用户权限过期时间
	hashKey := fmt.Sprintf("expiration_%s", permissionType)
	fieldKey := fmt.Sprintf("%d", uid)

	// 先检查缓存
	if cachedValue, err := s.hashCache.HGet(hashKey, fieldKey); err == nil && cachedValue != nil {
		// 处理类型转换
		var cachedTime int64
		if floatVal, ok := cachedValue.(float64); ok {
			cachedTime = int64(floatVal)
		} else if intVal, ok := cachedValue.(int64); ok {
			cachedTime = intVal
		} else {
			logger.Warn("缓存值类型转换失败: %T", cachedValue)
		}

		logger.Debug("从哈希表缓存获取权限过期时间: UID=%d, Type=%s, Time=%d", uid, permissionType, cachedTime)
		return cachedTime, nil
	}

	logger.Debug("哈希表缓存未命中，开始计算权限过期时间: UID=%d, Type=%s", uid, permissionType)

	// 缓存未命中，使用新的权限计算逻辑
	calculatedTime, err := s.calculatePermissionByPriority(uid, permissionType)
	if err != nil {
		logger.Error("计算权限失败: UID=%d, Error=%v", uid, err)
		return 0, err
	}

	logger.Debug("CDKey权限计算结果: UID=%d, Type=%s, Time=%d", uid, permissionType, calculatedTime)

	// 检查活动权限并取最大值
	activityConfig := config.GetGlobalActivityConfig()
	var finalTime int64 = calculatedTime
	var useActivityPermission bool

	// 如果活动正在进行中，获取活动结束时间
	if activityConfig.IsActive() {
		activityEndTime := activityConfig.EndTime.Unix()

		// 取CDKey权限时间和活动结束时间的最大值
		if activityEndTime > finalTime {
			finalTime = activityEndTime
			useActivityPermission = true
			logger.Debug("活动权限更长，使用活动权限: UID=%d, CDKeyTime=%d, ActivityEndTime=%d", uid, calculatedTime, activityEndTime)
		} else {
			logger.Debug("CDKey权限更长或相等，使用CDKey权限: UID=%d, CDKeyTime=%d, ActivityEndTime=%d", uid, calculatedTime, activityEndTime)
		}
	} else {
		logger.Debug("活动未进行，仅使用CDKey权限: UID=%d, CDKeyTime=%d", uid, calculatedTime)
	}

	calculatedTime = finalTime

	logger.Debug("最终权限过期时间: UID=%d, Type=%s, Time=%d", uid, permissionType, calculatedTime)

	// 根据权限状态设置不同的缓存时间
	var cacheDuration time.Duration
	if calculatedTime == -1 {
		// 永久权限，缓存24小时
		cacheDuration = 24 * time.Hour
	} else if calculatedTime == 0 {
		// 无权限时，需要考虑活动状态来设置缓存时间
		if activityConfig.IsActive() {
			// 活动进行中但用户无权限，使用短缓存（1小时），以便活动状态变化时能及时更新
			cacheDuration = 1 * time.Hour
			logger.Debug("无权限但活动进行中，设置短缓存时间: UID=%d, Duration=%v", uid, cacheDuration)
		} else {
			// 活动未进行且无权限，缓存15天（避免频繁查询，CDKey插入时会清理缓存）
			cacheDuration = 15 * 24 * time.Hour
		}
	} else {
		// 有期限权限，根据是否使用活动权限设置不同的缓存时间
		if useActivityPermission {
			// 使用活动权限时，缓存时间较短（1小时），确保活动结束后能及时更新
			cacheDuration = 1 * time.Hour
			logger.Debug("使用活动权限，设置短缓存时间: UID=%d, Duration=%v", uid, cacheDuration)
		} else {
			// 仅CDKey权限，但需要考虑活动状态
			if activityConfig.IsActive() {
				// 活动进行中，使用短缓存（1小时），以防权限状态变化
				cacheDuration = 1 * time.Hour
				logger.Debug("CDKey权限且活动进行中，设置短缓存时间: UID=%d, Duration=%v", uid, cacheDuration)
			} else {
				// 活动未进行，CDKey权限缓存15天（CDKey插入时会清理缓存）
				cacheDuration = 15 * 24 * time.Hour
			}
		}
	}

	// 将结果存入哈希表缓存
	if err := s.hashCache.HSet(hashKey, fieldKey, calculatedTime); err != nil {
		logger.Warn("缓存权限过期时间到哈希表失败: %v", err)
	} else {
		// 设置整个哈希表的过期时间
		if cacheDuration > 0 {
			if err := s.hashCache.Expire(hashKey, cacheDuration); err != nil {
				logger.Warn("设置哈希表过期时间失败: HashKey=%s, Error=%v", hashKey, err)
			}
		}
		logger.Debug("权限过期时间已缓存到哈希表: UID=%d, Type=%s, Duration=%v", uid, permissionType, cacheDuration)
	}

	return calculatedTime, nil
}

// 获取用户的权限等级（0-3）
func (s *PermissionService) GetUserPermissionLevel(uid uint64, permissionType string) (uint8, error) {
	// 使用哈希表存储用户权限等级缓存
	hashKey := fmt.Sprintf("user_level_%s", permissionType)
	fieldKey := fmt.Sprintf("%d", uid)

	// 先检查缓存
	if cachedValue, err := s.hashCache.HGet(hashKey, fieldKey); err == nil && cachedValue != nil {
		// 处理类型转换
		var cachedLevel uint8
		if floatVal, ok := cachedValue.(float64); ok {
			cachedLevel = uint8(floatVal)
		} else if intVal, ok := cachedValue.(int64); ok {
			cachedLevel = uint8(intVal)
		} else if intVal, ok := cachedValue.(int); ok {
			cachedLevel = uint8(intVal)
		} else {
			logger.Warn("权限等级缓存值类型转换失败: %T", cachedValue)
		}

		logger.Debug("从哈希表缓存获取用户权限等级: UID=%d, Type=%s, Level=%d", uid, permissionType, cachedLevel)
		return cachedLevel, nil
	}

	logger.Debug("权限等级缓存未命中，开始计算: UID=%d, Type=%s", uid, permissionType)

	// 缓存未命中，重新计算
	result, err := s.calculateUserPermission(uid, permissionType)
	if err != nil {
		return 0, err
	}

	// 将权限等级存入缓存（与权限过期时间使用相同的缓存策略）
	if err := s.hashCache.HSet(hashKey, fieldKey, result.Level); err != nil {
		logger.Warn("缓存用户权限等级失败: UID=%d, Type=%s, Error=%v", uid, permissionType, err)
	} else {
		// 设置缓存过期时间（与权限过期时间缓存保持一致）
		var cacheDuration time.Duration
		if result.Expiration == -1 {
			// 永久权限，缓存24小时
			cacheDuration = 24 * time.Hour
		} else if result.Expiration == 0 {
			// 无权限，缓存15天（CDKey插入时会清理缓存）
			cacheDuration = 15 * 24 * time.Hour
		} else {
			// 有期限权限，缓存15天（CDKey插入时会清理缓存）
			cacheDuration = 15 * 24 * time.Hour
		}

		if err := s.hashCache.Expire(hashKey, cacheDuration); err != nil {
			logger.Warn("设置权限等级缓存过期时间失败: HashKey=%s, Error=%v", hashKey, err)
		}
		logger.Debug("用户权限等级已缓存: UID=%d, Type=%s, Level=%d, Duration=%v", uid, permissionType, result.Level, cacheDuration)
	}

	return result.Level, nil
}

// 获取用户权限级别
func (s *PermissionService) GetPermissionLevel(uid uint64, permissionType string) (int, error) {
	// 先获取权限过期时间（包含CDKey权限和活动权限的综合计算）
	expirationTime, err := s.GetPermissionExpiration(uid, permissionType)
	if err != nil {
		return 0, err
	}

	// 权限判断逻辑：
	// expirationTime == 0: 无权限
	// expirationTime == -1: 永久权限
	// expirationTime > 0: 有期限权限，需要检查是否过期
	now := time.Now().Unix()

	if expirationTime == 0 {
		logger.Debug("用户无权限: UID=%d", uid)
		return 0, nil
	}

	if expirationTime == -1 {
		logger.Debug("用户有永久权限: UID=%d", uid)
		return 1, nil
	}

	if expirationTime <= now {
		logger.Debug("用户权限已过期: UID=%d, ExpirationTime=%d, Now=%d", uid, expirationTime, now)
		return 0, nil
	}

	logger.Debug("用户有有效权限: UID=%d, ExpirationTime=%d, Now=%d", uid, expirationTime, now)
	return 1, nil
}

// isInFreeTrialPeriod 检查是否在免费体验期间
func (s *PermissionService) isInFreeTrialPeriod() bool {
	activityConfig := config.GetGlobalActivityConfig()
	return activityConfig.IsActive()
}

// GetActivityInfoForUser 获取特定用户的活动信息
func (s *PermissionService) GetActivityInfoForUser(userPermission uint8) map[string]interface{} {
	now := time.Now()
	activityConfig := config.GetGlobalActivityConfig()
	isActive := activityConfig.IsActive()

	result := map[string]interface{}{
		"is_active":           isActive,
		"start_time":          activityConfig.StartTime.Unix(),
		"end_time":            activityConfig.EndTime.Unix(),
		"sign_in_resume_time": activityConfig.SignInResumeTime.Unix(),
		"title":               activityConfig.Title,
		"description":         activityConfig.Description,
	}

	// 高级用户和会员用户（userPermission > 0）不显示活动剩余时间
	if userPermission > 0 {
		result["message"] = "您拥有永久权限"
		return result
	}

	if isActive {
		// 计算剩余时间（仅对普通用户）
		remaining := activityConfig.EndTime.Sub(now)
		result["remaining_days"] = int(remaining.Hours() / 24)
		result["remaining_hours"] = int(remaining.Hours()) % 24
		result["message"] = fmt.Sprintf("免费体验还剩 %d 天 %d 小时",
			int(remaining.Hours()/24), int(remaining.Hours())%24)
	} else if now.Before(activityConfig.StartTime) {
		result["message"] = "活动尚未开始"
	} else {
		result["message"] = "活动已结束，已恢复签到获得权限方式"
	}

	return result
}

// ClearUserPermissionCache 清除用户权限缓存（CDKey激活时调用）
// 参数：uid - 用户ID，permissionType - 权限类型（如 'client'）
func (s *PermissionService) ClearUserPermissionCache(uid uint64, permissionType string) error {
	fieldKey := fmt.Sprintf("%d", uid)

	// 清除权限过期时间缓存
	expirationHashKey := fmt.Sprintf("expiration_%s", permissionType)
	if err := s.hashCache.HDel(expirationHashKey, fieldKey); err != nil {
		logger.Error("清除用户权限过期时间缓存失败: UID=%d, Type=%s, Error=%v", uid, permissionType, err)
		return err
	}

	// 清除权限等级缓存
	levelHashKey := fmt.Sprintf("user_level_%s", permissionType)
	if err := s.hashCache.HDel(levelHashKey, fieldKey); err != nil {
		logger.Error("清除用户权限等级缓存失败: UID=%d, Type=%s, Error=%v", uid, permissionType, err)
		return err
	}

	logger.Debug("用户权限缓存已清除: UID=%d, Type=%s (包括过期时间和等级)", uid, permissionType)
	return nil
}

// parseTimeString 解析时间字符串，支持多种格式
func (s *PermissionService) parseTimeString(timeStr string) (time.Time, error) {
	// 支持的时间格式列表
	timeFormats := []string{
		"2006-01-02T15:04:05Z07:00", // ISO 8601 with timezone: 2025-02-16T22:49:22+08:00
		"2006-01-02T15:04:05Z",      // ISO 8601 UTC: 2025-02-16T22:49:22Z
		"2006-01-02T15:04:05",       // ISO 8601 without timezone: 2025-02-16T22:49:22
		"2006-01-02 15:04:05",       // MySQL format: 2025-02-16 22:49:22
		"2006-01-02",                // Date only: 2025-02-16
		time.RFC3339,                // RFC3339: 2025-02-16T22:49:22Z
		time.RFC3339Nano,            // RFC3339 with nanoseconds
	}

	// 尝试每种格式
	for _, format := range timeFormats {
		if t, err := time.Parse(format, timeStr); err == nil {
			return t, nil
		}
	}

	// 如果所有格式都失败，返回错误
	return time.Time{}, fmt.Errorf("无法解析时间格式: %s", timeStr)
}

// PermissionResult 权限计算结果
type PermissionResult struct {
	Level      uint8 // 权限等级：0=普通用户，1=高级用户，2=超级用户，3=特级用户
	Expiration int64 // 权限过期时间：0=无权限，-1=永久权限，>0=具体时间戳
}

// 返回用户的权限等级和过期时间
func (s *PermissionService) calculateUserPermission(uid uint64, permissionType string) (*PermissionResult, error) {
	// 查询用户所有口令码使用记录
	var logs []model.UserLog
	if err := s.db.Where("uid = ? AND type = ?", uid, "cdkey").Order("id").Find(&logs).Error; err != nil {
		logger.Error("查询用户CDKey日志失败: UID=%d, Error=%v", uid, err)
		return &PermissionResult{Level: 0, Expiration: 0}, err
	}

	logger.Debug("找到用户CDKey记录数量: UID=%d, Count=%d", uid, len(logs))

	if len(logs) == 0 {
		logger.Debug("用户无CDKey记录，返回普通用户: UID=%d", uid)
		return &PermissionResult{Level: 0, Expiration: 0}, nil
	}

	// 按权限等级分组存储CDKey信息
	permissionGroups := make(map[uint8][]CDKeyPermissionInfo)

	// 遍历每条CDKey记录，获取权限信息
	for _, logRecord := range logs {
		// 解析激活时间
		startTime, err := s.parseTimeString(logRecord.Time)
		if err != nil {
			logger.Warn("解析时间失败: %s, Error=%v", logRecord.Time, err)
			continue
		}

		// 查询CDKey配置信息（包含权限等级）
		var cdkeyInfo CDKeyInfo
		query := `
			SELECT c.cdkey, c.type, c.permission, t.timeType, t.fixed, t.duration
			FROM bns_cdkey c
			JOIN bns_cdkey_customize t ON t.cdkey = c.cdkey
			WHERE c.type = ? AND c.cdkey = ?
		`

		if err := s.db.Raw(query, permissionType, logRecord.Extra).Scan(&cdkeyInfo).Error; err != nil {
			logger.Warn("查询CDKey配置失败: CDKey=%s, Error=%v", logRecord.Extra, err)
			continue
		}

		// 将CDKey信息添加到对应权限等级的组中
		permissionInfo := CDKeyPermissionInfo{
			CDKey:      cdkeyInfo.CDKey,
			Permission: cdkeyInfo.Permission,
			StartTime:  startTime.Unix(),
			EndTime:    0, // 后续统一计算
			TimeType:   cdkeyInfo.TimeType,
			Duration:   cdkeyInfo.Duration,
			Fixed:      cdkeyInfo.Fixed,
		}

		permissionGroups[cdkeyInfo.Permission] = append(permissionGroups[cdkeyInfo.Permission], permissionInfo)
		logger.Debug("CDKey权限信息收集: CDKey=%s, Permission=%d, TimeType=%s, StartTime=%d",
			cdkeyInfo.CDKey, cdkeyInfo.Permission, cdkeyInfo.TimeType, startTime.Unix())
	}

	// 计算各权限等级的过期时间（包含延长逻辑）
	time1, time2, time3 := s.calculatePermissionTimesWithExtension(uid, permissionType, permissionGroups)

	// 按权限等级优先级返回最高有效权限
	if time3 != 0 {
		logger.Debug("用户拥有有效的特级权限: UID=%d, Expiration=%d", uid, time3)
		return &PermissionResult{Level: 3, Expiration: time3}, nil
	}

	if time2 != 0 {
		logger.Debug("用户拥有有效的超级权限: UID=%d, Expiration=%d", uid, time2)
		return &PermissionResult{Level: 2, Expiration: time2}, nil
	}

	if time1 != 0 {
		logger.Debug("用户拥有有效的高级权限: UID=%d, Expiration=%d", uid, time1)
		return &PermissionResult{Level: 1, Expiration: time1}, nil
	}

	logger.Debug("用户无有效权限，返回普通用户: UID=%d", uid)
	return &PermissionResult{Level: 0, Expiration: 0}, nil
}

// calculatePermissionByPriority 按权限等级优先级计算用户权限过期时间（保持向后兼容）
func (s *PermissionService) calculatePermissionByPriority(uid uint64, permissionType string) (int64, error) {
	result, err := s.calculateUserPermission(uid, permissionType)
	if err != nil {
		return 0, err
	}
	return result.Expiration, nil
}

// calculatePermissionTimesWithExtension 计算各权限等级的过期时间（包含延长逻辑）
func (s *PermissionService) calculatePermissionTimesWithExtension(uid uint64, permissionType string, permissionGroups map[uint8][]CDKeyPermissionInfo) (int64, int64, int64) {
	now := time.Now().Unix()

	// 分别计算各权限等级的基础过期时间
	time1 := s.calculatePermissionEndTime(permissionGroups[1], now)
	time2 := s.calculatePermissionEndTime(permissionGroups[2], now)
	time3 := s.calculatePermissionEndTime(permissionGroups[3], now)

	logger.Debug("权限基础时间计算: UID=%d, Level1=%d, Level2=%d, Level3=%d", uid, time1, time2, time3)

	// 应用权限延长逻辑：低级权限在高级权限生效期间时间不流逝
	extendedTime1, extendedTime2, extendedTime3 := s.calculateExtendedPermissionTimes(time1, time2, time3)
	logger.Debug("权限延长后时间: UID=%d, Level1=%d, Level2=%d, Level3=%d", uid, extendedTime1, extendedTime2, extendedTime3)

	return extendedTime1, extendedTime2, extendedTime3
}

// calculatePermissionEndTime 计算单个权限等级的过期时间
func (s *PermissionService) calculatePermissionEndTime(cdkeys []CDKeyPermissionInfo, now int64) int64 {
	if len(cdkeys) == 0 {
		return 0
	}

	// 按激活时间排序
	sort.Slice(cdkeys, func(i, j int) bool {
		return cdkeys[i].StartTime < cdkeys[j].StartTime
	})

	var time int64 = 0
	for _, cdkey := range cdkeys {
		// 计算起始时间戳
		startTime := max(time, cdkey.StartTime)

		switch cdkey.TimeType {
		case "fixed":
			if cdkey.Fixed == nil {
				logger.Debug("发现永久权限CDKey: %s", cdkey.CDKey)
				return -1
			} else {
				endTime := cdkey.Fixed.Unix()
				time = max(time, endTime)
				logger.Debug("Fixed CDKey处理: CDKey=%s, EndTime=%d, CurrentTime=%d", cdkey.CDKey, endTime, time)
			}
		case "duration":
			time = startTime + int64(cdkey.Duration*24*3600)
			logger.Debug("Duration CDKey叠加: CDKey=%s, Duration=%d天, StartTime=%d, NewTime=%d",
				cdkey.CDKey, cdkey.Duration, startTime, time)
		}
	}

	logger.Debug("权限时间计算完成: FinalTime=%d", time)
	return time
}

// 实现高权限生效期间低权限时间不流逝的逻辑
// 核心思路：低权限在高权限生效期间暂停计时，高权限结束后继续计时
func (s *PermissionService) calculateExtendedPermissionTimes(time1, time2, time3 int64) (int64, int64, int64) {
	now := time.Now().Unix()

	// 权限3不需要延长，它是最高权限
	extendedTime3 := time3

	// 权限2延长逻辑：在权限3生效期间，权限2时间暂停
	extendedTime2 := time2
	if time2 > 0 && time2 > now && time3 > now && time3 != -1 {
		// 计算权限2和权限3的重叠时间段
		// 重叠开始时间：当前时间
		// 重叠结束时间：权限2和权限3中较早结束的时间
		overlapEnd := s.minTime(time2, time3)

		if overlapEnd > now {
			// 权限2在权限3生效期间的重叠时间需要延长
			overlapDuration := overlapEnd - now
			extendedTime2 = time2 + overlapDuration
			logger.Debug("权限2延长: 原=%d, 重叠时间=%d, 延长后=%d", time2, overlapDuration, extendedTime2)
		}
	}

	// 权限1延长逻辑：分别计算权限3和权限2对权限1的延长影响
	extendedTime1 := time1
	if time1 > 0 && time1 > now {
		var extension3 int64 = 0
		var extension2 int64 = 0

		// 计算权限3对权限1的延长影响
		if time3 > now && time3 != -1 {
			// 权限1和权限3的重叠时间
			overlapEnd := s.minTime(time1, time3)
			if overlapEnd > now {
				extension3 = overlapEnd - now
				logger.Debug("权限3对权限1的延长: %d", extension3)
			}
		}

		// 计算权限2对权限1的延长影响
		if time2 > 0 && time2 > now {
			// 权限2的有效时间段（排除权限3覆盖的部分）
			time2Start := s.maxTime(now, time3) // 权限2开始生效的时间
			time2End := extendedTime2           // 权限2结束时间（已考虑延长）

			if time2End > time2Start {
				// 计算权限2有效期间与权限1的重叠
				// 权限1在此时的结束时间需要考虑权限3的延长
				time1WithExtension3 := time1 + extension3
				overlapEnd := s.minTime(time1WithExtension3, time2End)

				if overlapEnd > time2Start {
					extension2 = overlapEnd - time2Start
					logger.Debug("权限2对权限1的延长: %d", extension2)
				}
			}
		}

		// 应用总延长时间
		totalExtension := extension3 + extension2
		if totalExtension > 0 {
			extendedTime1 = time1 + totalExtension
			logger.Debug("权限1延长: 原=%d, 权限3延长=%d, 权限2延长=%d, 总延长=%d, 延长后=%d",
				time1, extension3, extension2, totalExtension, extendedTime1)
		}
	}

	return extendedTime1, extendedTime2, extendedTime3
}

// maxTime 返回两个时间戳的最大值，处理永久权限(-1)的特殊情况
func (s *PermissionService) maxTime(time1, time2 int64) int64 {
	if time1 == -1 || time2 == -1 {
		return -1 // 任一为永久权限，返回永久
	}
	if time1 > time2 {
		return time1
	}
	return time2
}

// minTime 返回两个时间戳的最小值，处理永久权限(-1)的特殊情况
func (s *PermissionService) minTime(time1, time2 int64) int64 {
	if time1 == -1 {
		return time2 // time1为永久权限，返回time2
	}
	if time2 == -1 {
		return time1 // time2为永久权限，返回time1
	}
	if time1 < time2 {
		return time1
	}
	return time2
}
