package service

import (
	"fmt"
	"time"

	"udp-server/server/internal/database"
	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// 口令码服务
type CDKeyService struct {
	db                *gorm.DB
	cache             cache.Cache
	permissionService *PermissionService
}

func NewCDKeyService(cache cache.Cache, permissionService *PermissionService) *CDKeyService {
	return &CDKeyService{
		db:                database.GetDB(),
		cache:             cache,
		permissionService: permissionService,
	}
}

// ActivateCDKey 统一的CDKEY激活方法
// adminID: -1用户操作，0系统操作，>0管理员ID
func (s *CDKeyService) ActivateCDKey(cdkeyStr string, uid uint64, adminID int64, clientIP string) error {
	// 先查询CDKey类型，用于后续的缓存清除
	var cdkey model.CDkey
	if err := s.db.Where("cdkey = ?", cdkeyStr).First(&cdkey).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("口令码不存在")
		}
		return err
	}

	// 在事务中执行激活逻辑
	err := s.ActivateCDKeyInTransaction(s.db, cdkeyStr, uid, adminID, clientIP)
	if err != nil {
		return err
	}

	// 事务成功后，清除相关缓存（缓存失败不影响激活结果）
	if cdkey.Type == "client" && s.permissionService != nil {
		if err := s.permissionService.ClearUserPermissionCache(uid, cdkey.Type); err != nil {
			logger.Warn("清除用户权限缓存失败: UID=%d, Type=%s, Error=%v", uid, cdkey.Type, err)
		}
	}

	return nil
}

// 在指定事务中激活CDKEY
// tx: 数据库事务
// adminID: -1用户操作，0系统操作，>0管理员ID
func (s *CDKeyService) ActivateCDKeyInTransaction(tx *gorm.DB, cdkeyStr string, uid uint64, adminID int64, clientIP string) error {
	// 用户端提交频率限制
	if adminID == -1 {
		// 检查用户CDkey提交频率限制（防止恶意提交）
		if s.cache != nil {
			// 使用哈希表存储用户CDkey提交时间戳
			var userCdkeyTimes map[uint64]int64
			err := s.cache.Get("user_cdkey_times", &userCdkeyTimes)
			if err != nil {
				userCdkeyTimes = make(map[uint64]int64)
			}

			// 检查是否在15秒内提交过
			if lastTime, exists := userCdkeyTimes[uid]; exists {
				if time.Now().Unix()-lastTime < 15 {
					logger.Warn("用户CDkey提交过于频繁: UID=%d", uid)
					return fmt.Errorf("请求过多，请稍后再试")
				}
			}

			// 更新时间戳
			userCdkeyTimes[uid] = time.Now().Unix()

			// 清理过期的时间戳（超过15秒的）
			currentTime := time.Now().Unix()
			for u, t := range userCdkeyTimes {
				if currentTime-t >= 15 {
					delete(userCdkeyTimes, u)
				}
			}

			// 保存回缓存，设置30秒过期（给清理留余量）
			s.cache.Set("user_cdkey_times", userCdkeyTimes, 30*time.Second)
		}

		// 检查CDkey是否在不存在缓存中（避免重复查询不存在的CDkey）
		if s.cache != nil {
			// 使用哈希表存储无效CDkey
			var invalidCdkeys map[string]int64
			err := s.cache.Get("invalid_cdkeys", &invalidCdkeys)
			if err != nil {
				invalidCdkeys = make(map[string]int64)
			}

			// 检查CDkey是否在无效缓存中且未过期
			if timestamp, exists := invalidCdkeys[cdkeyStr]; exists {
				if time.Now().Unix()-timestamp < 300 { // 5分钟内有效
					logger.Info("CDKey在无效缓存中，直接返回不存在: %s", cdkeyStr)
					return fmt.Errorf("口令码不存在")
				} else {
					// 过期了，从缓存中删除
					delete(invalidCdkeys, cdkeyStr)
					// 保存更新后的缓存
					s.cache.Set("invalid_cdkeys", invalidCdkeys, 10*time.Minute)
				}
			}
		}
	}

	// 检查CDKey是否存在
	var cdkey model.CDkey
	if err := tx.Where("cdkey = ?", cdkeyStr).First(&cdkey).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Error("CDKey不存在: %s", cdkeyStr)
			// 用户操作时将不存在的CDkey加入缓存，避免重复查询（缓存5分钟）
			if adminID == -1 && s.cache != nil {
				// 使用哈希表存储无效CDkey
				var invalidCdkeys map[string]int64
				err := s.cache.Get("invalid_cdkeys", &invalidCdkeys)
				if err != nil {
					invalidCdkeys = make(map[string]int64)
				}

				// 添加当前CDkey到无效缓存
				invalidCdkeys[cdkeyStr] = time.Now().Unix()

				// 清理过期的无效CDkey（超过5分钟的）
				currentTime := time.Now().Unix()
				for key, timestamp := range invalidCdkeys {
					if currentTime-timestamp >= 300 { // 5分钟
						delete(invalidCdkeys, key)
					}
				}

				// 保存回缓存，设置10分钟过期（给清理留余量）
				s.cache.Set("invalid_cdkeys", invalidCdkeys, 10*time.Minute)
			}
			return fmt.Errorf("口令码不存在")
		}
		logger.Error("查询CDKey失败: %s, Error=%v", cdkeyStr, err)
		return err
	}

	// 检查是否有更高权限的CDKey正在生效
	if err := s.checkHigherPermissionConflict(uid, cdkey.Permission); err != nil {
		return err
	}

	// 用户操作时需要进行各种检查，系统和管理员操作跳过大部分检查
	if adminID == -1 {
		// 检查CDKey是否可以使用
		if cdkey.StartTime == nil {
			return fmt.Errorf("口令码不存在")
		}
		if cdkey.IsUsed() {
			return fmt.Errorf("口令码已使用完毕")
		}
		if !cdkey.IsActive() {
			return fmt.Errorf("口令码不在有效期内")
		}

		// 检查用户是否已经使用过此CDKey
		var existingLog model.UserLog
		if err := tx.Where("uid = ? AND type = ? AND extra = ?", uid, "cdkey", cdkeyStr).First(&existingLog).Error; err == nil {
			return fmt.Errorf("你已经使用过此口令码，无法重复使用")
		}

		// 检查批次限制（同一批次的CDKey不能被同一用户重复使用）
		if cdkey.Batch != nil && *cdkey.Batch != 0 {
			var batchUsageCount int64
			err := tx.Model(&model.UserLog{}).
				Joins("JOIN bns_cdkey ON user_log.extra = bns_cdkey.cdkey").
				Where("user_log.uid = ? AND user_log.type = ? AND bns_cdkey.batch = ?", uid, "cdkey", *cdkey.Batch).
				Count(&batchUsageCount).Error
			if err != nil {
				logger.Error("检查批次使用情况失败: UID=%d, Batch=%d, Error=%v", uid, *cdkey.Batch, err)
				return fmt.Errorf("系统错误，请稍后重试")
			}

			if batchUsageCount > 0 {
				return fmt.Errorf("你已经使用过相同的口令码，无法重复使用")
			}
		}

		// 减少次数信息
		if err := tx.Model(&cdkey).Update("RemainNum", gorm.Expr("RemainNum - 1")).Error; err != nil {
			logger.Error("更新CDKey剩余次数失败: %s, Error=%v", cdkeyStr, err)
			return err
		}
	}

	// 记录用户激活日志
	now := time.Now()
	userLog := model.UserLog{
		UID:   uid,
		Type:  "cdkey",
		Extra: cdkeyStr,
		Time:  now.Format("2006-01-02T15:04:05Z07:00"),
		IP:    clientIP,
	}

	// 设置admin字段
	if adminID > 0 {
		userLog.Admin = adminID
	}
	if err := tx.Create(&userLog).Error; err != nil {
		logger.Error("创建用户CDKey日志失败: UID=%d, CDKey=%s, Error=%v", uid, cdkeyStr, err)
		return err
	}

	// 根据CDKey类型进行后续处理（在事务内执行）
	if err := s.processCDKeyByType(tx, &cdkey, uid); err != nil {
		logger.Error("处理CDKey类型特定逻辑失败: UID=%d, CDKey=%s, Type=%s, Error=%v", uid, cdkeyStr, cdkey.Type, err)
		return err
	}

	logger.Info("用户CDKey激活成功: UID=%d, CDKey=%s", uid, cdkeyStr)

	return nil
}

// 根据CDKey类型进行特定的业务逻辑处理（在事务内执行）
func (s *CDKeyService) processCDKeyByType(tx *gorm.DB, cdkey *model.CDkey, uid uint64) error {
	switch cdkey.Type {
	case "drawtimes":
		return s.processDrawtimesCDKey(tx, cdkey.CDkey, uid)
	case "client", "customize":
		// 这些类型只影响权限，不需要在事务内处理额外业务逻辑
		return nil
	default:
		logger.Warn("未知的CDKey类型: %s", cdkey.Type)
		return nil
	}
}

// 处理抽奖次数类型的CDKey
func (s *CDKeyService) processDrawtimesCDKey(tx *gorm.DB, cdkeyStr string, uid uint64) error {
	// 查询CDKey的抽奖次数配置
	var drawtimes model.CDKeyDrawtimes
	if err := tx.Where("cdkey = ?", cdkeyStr).First(&drawtimes).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Error("Drawtimes CDKey配置不存在: %s", cdkeyStr)
			return fmt.Errorf("抽奖次数配置不存在")
		}
		logger.Error("查询Drawtimes CDKey配置失败: %s, Error=%v", cdkeyStr, err)
		return err
	}

	// 如果没有指定Schedule，记录警告但不报错
	if drawtimes.Schedule == nil {
		logger.Warn("Drawtimes CDKey没有指定Schedule: %s", cdkeyStr)
		return fmt.Errorf("抽奖次数配置缺少活动ID")
	}

	schedule := uint(*drawtimes.Schedule)

	// 查找或创建用户抽奖记录
	var userDraw model.UserDraw
	err := tx.Where("uid = ? AND schedule = ?", uid, schedule).First(&userDraw).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建新记录
			userDraw = model.UserDraw{
				UID:      uid,
				Schedule: schedule,
				Extra:    drawtimes.Number,
				Day:      1,
				Number:   0,
				Today:    0,
				Point:    1,
				Time:     time.Now(),
			}
			if err := tx.Create(&userDraw).Error; err != nil {
				logger.Error("创建用户抽奖记录失败: UID=%d, Schedule=%d, Error=%v", uid, schedule, err)
				return fmt.Errorf("创建用户抽奖记录失败: %v", err)
			}
			logger.Info("创建用户抽奖记录成功: UID=%d, Schedule=%d, Extra=%d", uid, schedule, drawtimes.Number)
		} else {
			logger.Error("查询用户抽奖记录失败: UID=%d, Schedule=%d, Error=%v", uid, schedule, err)
			return err
		}
	} else {
		// 更新现有记录的extra字段
		newExtra := userDraw.Extra + drawtimes.Number
		if err := tx.Model(&userDraw).Update("extra", newExtra).Error; err != nil {
			logger.Error("更新用户抽奖次数失败: UID=%d, Schedule=%d, Error=%v", uid, schedule, err)
			return fmt.Errorf("更新用户抽奖次数失败: %v", err)
		}
		logger.Info("更新用户抽奖次数成功: UID=%d, Schedule=%d, Extra=%d->%d", uid, schedule, userDraw.Extra, newExtra)
	}

	return nil
}

// 检查是否有更高权限的CDKey正在生效
func (s *CDKeyService) checkHigherPermissionConflict(uid uint64, cdkeyPermission uint8) error {
	// 如果没有权限服务，跳过检查
	if s.permissionService == nil {
		return nil
	}

	// 获取用户当前的权限等级
	currentLevel, err := s.permissionService.GetUserPermissionLevel(uid, "client")
	if err != nil {
		logger.Warn("获取用户权限等级失败: UID=%d, Error=%v", uid, err)
		return nil // 获取失败时不阻止CDKey使用
	}

	// 如果当前权限等级高于要使用的CDKey权限等级，则不允许使用
	if currentLevel > cdkeyPermission {
		logger.Info("用户尝试使用低权限CDKey被阻止: UID=%d, 当前权限=%d, CDKey权限=%d",
			uid, currentLevel, cdkeyPermission)
		return fmt.Errorf("正在生效更高权限的口令码，暂时无法使用此口令码")
	}

	return nil
}

// 获取用户权限过期时间（供Handler使用）
func (s *CDKeyService) GetPermissionExpiration(uid uint64, permissionType string) (int64, error) {
	if s.permissionService != nil {
		return s.permissionService.GetPermissionExpiration(uid, permissionType)
	}
	return 0, fmt.Errorf("权限服务不可用")
}
