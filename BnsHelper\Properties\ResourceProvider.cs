﻿using System.IO;
using System.Reflection;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.Preview.Common.Extension;
using Xylia.Preview.Data.Client;
using Xylia.Preview.Data.Common.DataStruct;
using Xylia.Preview.Data.Engine.BinData.Helpers;
using Xylia.Preview.Data.Engine.DatData;
using Xylia.Preview.Data.Engine.Definitions;

namespace Xylia.BnsHelper.Properties;
internal sealed class ResourceProvider : IDataProvider
{
    #region Properties
    private static Assembly Assembly { get; } = Assembly.GetExecutingAssembly() ?? throw new Exception("无法获取到程序资产信息");

    public static BnsDatabase Instance { get; } = new BnsDatabase(new ResourceProvider(), new ResourceDefinition());
    #endregion

    #region IDataProvider
    public string Name => string.Empty;
    public Time64 CreatedAt => default;
    public BnsVersion ClientVersion => default;
    public TableCollection? Tables { get; private set; }

    public Stream[] GetFiles(string pattern)
    {
        var publisher = SettingHelper.Default.Publisher.ToString().ToUpperInvariant();
        var path1 = "Xylia.BnsHelper.Properties.Data." + pattern.Replace("*", "_" + publisher);
        var path2 = "Xylia.BnsHelper.Properties.Data." + pattern.Replace("*", string.Empty);

        var stream = Assembly.GetManifestResourceStream(path1) ?? Assembly.GetManifestResourceStream(path2);
        return stream is null ? [] : [stream];
    }

    public void LoadData(DatafileDefinition definitions)
    {
        Tables = [];
        definitions.ForEach(x => Tables.Add(new() { Owner = this, Name = x.Name }));
    }

    public void Dispose()
    {
        Tables?.Clear();
        Tables = null;

        GC.SuppressFinalize(this);
    }
    #endregion
}

internal class ResourceDefinition : DatafileDefinition
{
    public ResourceDefinition() : base(string.Empty)
    {
        Add("ItemGraph", new AttributeDefinition("id", AttributeType.Int32, 8) { IsKey = true });
        Add("ItemGraphSeedGroup", new AttributeDefinition("id", AttributeType.Int32, 8) { IsKey = true });
        Add("Level", new AttributeDefinition("level", AttributeType.Int16, 8) { IsKey = true });
        Add("MasteryLevel", new AttributeDefinition("mastery-level", AttributeType.Int8, 8) { IsKey = true });
        Add("Npc", new AttributeDefinition("alias", AttributeType.String, 16));
        Add("Zone", new AttributeDefinition("id", AttributeType.Int32, 8) { IsKey = true });
    }

    private void Add(string name, params AttributeDefinition[] attributes)
    {
        var definition = new TableDefinition()
        {
            Name = name,
            Pattern = name + "Data*.xml"
        };

        var element0 = new ElementDefinition() { Name = "table" };
        var element1 = new ElementDefinition { Name = "record", Size = 8 };
        definition.Elements.Add(element0);
        definition.Elements.Add(element1);

        element0.Children.Add(element1);
        element1.ExpandedAttributes.AddRange(attributes);

        Add(definition);
    }
}
