﻿using System.Runtime.CompilerServices;
using System.Security.Cryptography;
using System.Text;

namespace Xylia.BnsHelper.Common.Extensions;
internal static class StringExtensions
{
    internal static string? Hash(this string source, Encoding? encoding = null)
    {
        ArgumentNullException.ThrowIfNull(source);
        encoding ??= Encoding.UTF8;

        var data = MD5.HashData(encoding.GetBytes(source));
        var str = new StringBuilder();

        for (int i = 0; i < data.Length; i++)
        {
            str.Append(data[i].ToString("x2"));
        }

        return str.ToString();
    }

    internal static string MD5Sign(this string source, string secretKey)
    {
        ArgumentNullException.ThrowIfNull(source);
        ArgumentNullException.ThrowIfNull(secretKey);

        var combined = secretKey + "|" + source;
        return combined.Hash(Encoding.UTF8);
    }


    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static string SubstringBeforeLast(this string s, char delimiter)
    {
        int num = s.LastIndexOf(delimiter);
        return (num == -1) ? s : s.Substring(0, num);
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public static string SubstringAfterLast(this string s, char delimiter)
    {
        int num = s.LastIndexOf(delimiter);
        return (num == -1) ? s : s.Substring(num + 1, s.Length - num - 1);
    }
}
