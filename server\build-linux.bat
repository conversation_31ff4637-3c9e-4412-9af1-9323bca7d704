@echo off
REM Build Go UDP Server for Linux on Windows

echo Starting build for Linux BNSZS Server...

REM Check Go environment
go version >nul 2>&1
if errorlevel 1 (
    echo Error: Go environment not found, please install Go first
    pause
    exit /b 1
)

echo Go version:
go version

REM Clean previous builds
echo Cleaning previous builds...
if exist "bin\server" del "bin\server"
if exist "server" del "server"
if exist "main" del "main"
if exist "main.exe" del "main.exe"

REM Create bin directory
if not exist "bin" mkdir "bin"

REM Set environment variables for Linux
set GOOS=linux
set GOARCH=amd64
set CGO_ENABLED=0

echo Target platform: %GOOS%/%GOARCH%

REM Download dependencies
echo Downloading dependencies...
go mod download
if errorlevel 1 (
    echo Error: Failed to download dependencies
    pause
    exit /b 1
)

REM Build static binary
echo Building static binary...
go build -a -ldflags="-s -w -extldflags '-static'" -o bin/server ./cmd/main.go
if errorlevel 1 (
    echo Error: Build failed
    pause
    exit /b 1
)

REM Check build result
if exist "bin\server" (
    echo Build successful!
    echo Binary file: bin\server
    for %%A in (bin\server) do echo File size: %%~zA bytes
) else (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build completed! Upload these files to Linux server:
echo - bin/server
echo - deploy.sh
echo - config/config.yaml (if exists)
echo.
echo Run on Linux server:
echo sudo chmod +x deploy.sh
echo sudo ./deploy.sh

pause
