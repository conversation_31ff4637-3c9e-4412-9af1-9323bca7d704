﻿using BnsHelper.Common.Helpers;
using BnsHelper.Views.Dialogs;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Windows;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Models.Api;
using Xylia.BnsHelper.Services;
using Xylia.Preview.UI.Views.Dialogs;

namespace Xylia.BnsHelper.ViewModels.Pages;
internal partial class ActivityPageViewModel : ObservableObject
{
    #region Constructor
    public ActivityPageViewModel()
    {
        Initialize();

        // 目录改变可能需要切换服务器类型
        SettingHelper.Default.GameDirectoryChanged += (s, e) =>
        {
            OnPropertyChanged(nameof(Worlds));
        };
    }
    #endregion

    #region Properties
    IEnumerable<ActivityInfo> _activities = [];
    public IEnumerable<ActivityInfo> Activities
    {
        get => _activities;
        set => SetProperty(ref _activities, value);
    }

    /// <summary>
    /// 当前选中的活动
    /// </summary>
    [ObservableProperty] ActivityInfo? _selectedActivity;

    /// <summary>
    /// 服务器列表
    /// </summary>
    public IEnumerable<BnsWorld> Worlds => BnsWorld.GetWorlds();

    /// <summary>
    /// 当前选中的服务器
    /// </summary>
    [ObservableProperty] BnsWorld? _selectedWorld;

    /// <summary>
    /// 角色列表
    /// </summary>
    [ObservableProperty] ObservableCollection<Creature> _roles = [];

    /// <summary>
    /// 当前选中的角色
    /// </summary>
    [ObservableProperty] Creature? _selectedRole;

    /// <summary>
    /// 角色查询结果（包含绑定参数）
    /// </summary>
    private QueryRoleResult? _currentRoleResult;

    /// <summary>
    /// 状态消息
    /// </summary>
    [ObservableProperty] string _statusMessage = string.Empty;
    #endregion

    #region Commands
    /// <summary>
    /// 刷新数据
    /// </summary>
    /// <returns></returns>
    [RelayCommand]
    private async Task Refresh()
    {
        if (SelectedWorld != null)
        {
            await LoadRolesAsync(SelectedWorld);
        }

        try
        {
            StatusMessage = "正在加载活动列表...";
            var allActivities = await GetActivityListAsync();
            Activities = allActivities.Where(a => a.IsActive).ToArray();
            StatusMessage = $"成功加载 {Activities.Count()} 个进行中的活动";
        }
        catch (Exception ex)
        {
            StatusMessage = $"加载活动失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 批量执行所有流程
    /// </summary>
    [RelayCommand]
    private async Task ExecuteAllFlows()
    {
        ArgumentNullException.ThrowIfNull(ApiEndpointService.AMSEndpoint);

        // 获取全部需要领取的流程信息
        var flows = Activities.Where(a => a.ShouldExecute).SelectMany(a => a.Flows!.Where(f => !f.Value.NeedsUserInput)).ToArray();
        if (flows.Length == 0)
        {
            StatusMessage = "没有可领取的活动";
            return;
        }

        // 开始执行流程
        int currentIndex = 0;
        int successCount = 0;
        foreach (var (flowId, flow) in flows)
        {
            try
            {
                currentIndex++;

                // 对于批量执行，只使用基础参数（不弹出参数对话框）
                var parameters = flow.BuildBaseParameters(flowId);
                await RequestFlowAsync(flow, parameters);

                // 处理结果
                if (flow.Reward?.IsSuccess == true) successCount++;

                StatusMessage = $"{flow.Reward?.Message} ({currentIndex}/{flows.Length})";

                // 添加延迟避免请求过快
                await Task.Delay(2000);
            }
            catch (Exception ex)
            {
                StatusMessage = $"{ex.Message} ({currentIndex}/{flows.Length})";
                Debug.WriteLine($"执行流程 {flow.Name} 失败: {ex.Message}");
            }
        }

        StatusMessage = $"活动领取结束";
        ViewExecutionHistory();
    }

    /// <summary>
    /// 显示自主领取活动
    /// </summary>
    [RelayCommand]
    private async Task ShowManualActivity()
    {
        // 检查是否有需要手动选择的流程组
        var flows = Activities.SelectMany(x => x.Flows?.Values).Where(f => f.Group > 0).GroupBy(f => f.Group).ToList();
        if (flows == null || flows.Count == 0)
        {
            await MessageDialog.ShowDialog($"暂时没有需要自主选择奖励的活动");
            return;
        }

        var dialog = new FlowSelectionDialog();
        dialog.ShowDialog();
    }

    /// <summary>
    /// 查看执行历史
    /// </summary>
    [RelayCommand]
    private void ViewExecutionHistory()
    {
        var historys = Activities.SelectMany(a => a.Flows!.Select(f => f.Value.Reward)).Where(o => o != null);
        if (historys.Any())
        {
            var history = string.Join("\n", historys.Select(x => x!.Message));
            MessageBox.Show(history, "领取历史", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        else
        {
            MessageBox.Show("暂无活动领取记录", "领取历史", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
    #endregion

    #region ActivityInfo
    private uint CacheVersion = 0;
    private readonly string CacheFilePath = Path.Combine(SettingHelper.CacheFolder, "activity.json");

    /// <summary>
    /// 获取活动列表
    /// </summary>
    public async Task<IEnumerable<ActivityInfo>> GetActivityListAsync()
    {
        var cachedActivities = GetCachedActivities();

        // 获取活动信息总版本号
        var user = MainWindowViewModel.Instance.User ?? throw new ArgumentNullException("请先登录账号");
        var totalVersion = await user.GetActivityTotalVersionAsync();
        if (totalVersion is null || totalVersion == CacheVersion) return cachedActivities;

        // 如果缓存版本不一致，获取活动列表
        var activityItems = await user.GetActivityListItemsAsync();
        if (activityItems == null || activityItems.Length == 0) return cachedActivities;

        // 比较并获取需要更新的活动详情
        var activities = new List<ActivityInfo>();
        var cachedActivityDict = cachedActivities.ToDictionary(a => a.ActivityId, a => a);

        foreach (var item in activityItems)
        {
            // 检查是否需要获取详情
            if (!cachedActivityDict.TryGetValue(item.Key, out var cachedActivity) || cachedActivity.Version != item.Value)
            {
                var activityDetail = await user.GetActivityDetailAsync(item.Key);
                if (activityDetail != null) activities.Add(activityDetail);
            }
            else
            {
                // 使用缓存的活动
                activities.Add(cachedActivity);
            }
        }

        // 更新缓存版本
        CacheVersion = totalVersion ?? 0;
        Activities = activities;
        await SaveCacheToFileAsync();
        return activities;
    }

    /// <summary>
    /// 获取测试活动列表
    /// </summary>
    /// <returns></returns>
    private async Task<IEnumerable<ActivityInfo>> LoadActivitiesTestAsync()
    {
        var activities = new List<ActivityInfo>();

        // 活动URL列表
        var activityUrls = new[]
        {
            "https://bns.qq.com/comm-htdocs/js/ams/actDesc/071/745071/gmi_act.desc.js",
            "https://bns.qq.com/comm-htdocs/js/ams/actDesc/361/738361/gmi_act.desc.js",
            //"https://bns.qq.com/comm-htdocs/js/ams/actDesc/155/688155/gmi_act.desc.js"
        };

        // 并行加载所有活动，提高性能
        var client = new HttpClient();
        var loadTasks = activityUrls.Select(async url =>
        {
            try
            {
                var response = await client.GetAsync(url);
                response.EnsureSuccessStatusCode();

                var content = await response.Content.ReadAsStringAsync();
                var activity = JsonConvert.DeserializeObject<ActivityInfo>(content);
                if (activity != null) return activity;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载活动失败 {url}: {ex.Message}");
            }

            return null;
        });

        var result = await Task.WhenAll(loadTasks);
        return result.Where(x => x != null).ToList()!;
    }

    /// <summary>
    /// 获取缓存的活动列表
    /// </summary>
    private IEnumerable<ActivityInfo>? GetCachedActivities()
    {
        // 如果活动列表已加载，直接返回
        if (Activities != null && Activities.Any()) return Activities;

        try
        {
            var cache = JsonConvert.DeserializeObject<JToken>(EncryptionHelper.ReadFile(CacheFilePath)) ?? throw new FileFormatException();

            // 加载活动数据
            CacheVersion = cache.Value<ushort>("Version");
            var activities = cache.Value<JArray>("Activities");
            return Activities = [.. activities?.Select(o => o.ToObject<ActivityInfo>()!) ?? []];
        }
        catch
        {
            Debug.WriteLine("未找到缓存文件或解析失败，返回空活动列表");
            return [];
        }
    }

    /// <summary>
    /// 保存缓存到文件
    /// </summary>
    private async Task SaveCacheToFileAsync()
    {
        if (Activities is null || !Activities.Any()) return;

        try
        {
            var targetPath = CacheFilePath;

            // 在锁外准备数据，避免在锁内进行IO操作
            object cacheData;
            lock (Activities)
            {
                cacheData = new
                {
                    Version = CacheVersion,
                    Activities = Activities?.Where(c => c.IsActive),
                };
            }

            await EncryptionHelper.EncryptToFileAsync(targetPath, JsonConvert.SerializeObject(cacheData, Formatting.Indented));
            Debug.WriteLine($"活动已加密缓存到文件: {targetPath}");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"缓存活动文件失败: {ex.Message}");
        }
    }
    #endregion

    #region Methods
    /// <summary>
    /// 初始化
    /// </summary>
    public void Initialize()
    {
        // 加载服务器信息
        SelectedWorld = Worlds.FirstOrDefault();

        // 恢复最后选择的角色
        var currentUser = MainWindowViewModel.Instance.User;
        if (currentUser is null) return;

        var role = SettingHelper.Default.GetRole(currentUser.Uin);
        if (role is null) return;

        SelectedRole = role;
        SelectedWorld = Worlds.FirstOrDefault(o => o.Id == role.world) ?? Worlds.FirstOrDefault();

        // 初始化活动列表
        Task.Run(Refresh);
    }

    /// <summary>
    /// 当选中服务器改变时，加载角色列表
    /// </summary>
    partial void OnSelectedWorldChanged(BnsWorld? value)
    {
        if (value != null)
        {
            _ = LoadRolesAsync(value);
        }
        else
        {
            Roles.Clear();
            SelectedRole = null;
        }
    }

    /// <summary>
    /// 当选中角色改变时，保存角色选择
    /// </summary>
    partial void OnSelectedRoleChanged(Creature? value)
    {
        if (value is null) return;

        // 从当前用户获取UIN
        var currentUser = MainWindowViewModel.Instance.User;
        if (currentUser is null) return;

        SettingHelper.Default.SaveRole(currentUser.Uin, value);
    }

    /// <summary>
    /// 加载角色列表
    /// </summary>
    private async Task LoadRolesAsync(BnsWorld world)
    {
        try
        {
            ArgumentNullException.ThrowIfNull(ApiEndpointService.AMSEndpoint);

            var result = await ApiEndpointService.AMSEndpoint.QueryRole(world);
            if (result != null && result.Roles.Count != 0)
            {
                _currentRoleResult = result; // 保存完整的查询结果
                Roles = new(result.Roles);
                SelectedRole = Roles.FirstOrDefault(o => o.Id == SelectedRole?.Id) ?? Roles.FirstOrDefault();
                StatusMessage = $"成功加载 {result.Roles.Count} 个角色";
            }
            else
            {
                _currentRoleResult = null;
                Roles.Clear();
                StatusMessage = "该服务器下没有找到角色";
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"加载角色失败: {ex.Message}";
            Debug.WriteLine($"[ERROR] 加载角色失败: {ex}");
            Roles.Clear();
        }
    }

    /// <summary>
    /// 检查当前流程的信息
    /// </summary>
    /// <param name="flow"></param>
    /// <param name="parameters"></param>
    /// <returns>是否应该执行这个流程</returns>
    private bool CheckFlow(ActivityFlow flow, Dictionary<string, string> parameters)
    {
        switch (flow.TplType)
        {
            case "bindarea#query_map_id#": return false;
            case "bindarea#bind_map_id#":
            {
                if (SelectedWorld != null)
                {
                    parameters["sArea"] = SelectedWorld.Id.ToString();
                }

                if (SelectedRole != null)
                {
                    parameters["sRoleId"] = SelectedRole.Id.ToString();
                }

                if (_currentRoleResult != null)
                {
                    parameters["sCheckparam"] = _currentRoleResult.CheckParam;
                    parameters["sMd5str"] = _currentRoleResult.Md5Str;
                }
                break;
            }
        }

        return true;
    }

    /// <summary>
    /// 请求流程执行
    /// </summary>
    private async Task RequestFlowAsync(ActivityFlow flow, Dictionary<string, string> parameters)
    {
        ArgumentNullException.ThrowIfNull(ApiEndpointService.AMSEndpoint);

        var flag = CheckFlow(flow, parameters);
        if (!flag) return;

        // 使用全局AMSEndpoint执行流程请求
        flow.Reward = await ApiEndpointService.AMSEndpoint.ExecuteFlowAsync(parameters);
    }
    #endregion
}