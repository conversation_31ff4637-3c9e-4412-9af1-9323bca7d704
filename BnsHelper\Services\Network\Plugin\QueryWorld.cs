﻿using Xylia.BnsHelper.Models;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Plugin;
internal class QueryWorld : IPacket
{
    #region Fields
    public int ZoneId = 0;
    public Creature? Player;
    public Creature? PlayerSummoned;
    #endregion

    #region Methods
    public DataArchiveWriter Create()
    {
        var writer = new DataArchiveWriter();
        writer.Write((short)11);
        return writer;
    }

    public void Read(DataArchive reader)
    {
        ZoneId = reader.Read<int>();
        Player = reader.ReadByte() == 1 ? new Creature(reader) : null;
        PlayerSummoned = reader.ReadByte() == 1 ? new Creature(reader) : null;
    }
    #endregion
}
