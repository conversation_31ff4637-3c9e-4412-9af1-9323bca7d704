package binary

import (
	"fmt"
	"udp-server/server/pkg/logger"
)

// XOR加密密钥
var encryptionKey = []byte{
	92, 62, 100, 99, 255, 94, 254, 99, 33, 8,
	246, 154, 15, 194, 179, 148, 252, 85, 170, 16,
}

// Encryptor XOR加密器
type Encryptor struct {
	key []byte
}

// NewEncryptor 创建新的加密器
func NewEncryptor() *Encryptor {
	// 复制密钥以避免外部修改
	key := make([]byte, len(encryptionKey))
	copy(key, encryptionKey)

	return &Encryptor{
		key: key,
	}
}

// Encrypt 加密数据
func (e *Encryptor) Encrypt(data []byte) []byte {
	if len(data) == 0 {
		return []byte{}
	}

	encrypted := make([]byte, len(data))
	keyLen := len(e.key)

	for i, b := range data {
		encrypted[i] = b ^ e.key[i%keyLen]
	}

	return encrypted
}

// Decrypt 解密数据（XOR加密是对称的）
func (e *Encryptor) Decrypt(data []byte) []byte {
	// XOR加密是对称的，解密和加密使用相同的操作
	return e.Encrypt(data)
}

// EncryptData 加密原始数据
func (e *Encryptor) EncryptData(data []byte) []byte {
	return e.Encrypt(data)
}

// DecryptData 解密原始数据
func (e *Encryptor) DecryptData(data []byte) []byte {
	return e.Decrypt(data)
}

// 全局加密器实例
var defaultEncryptor = NewEncryptor()

// 加密消息体（跳过消息头）
func EncryptMessageBody(data []byte) []byte {
	if len(data) < HeaderSize {
		logger.Error("Failed to encrypt message：data too short for message header: %d bytes", len(data))
		return data
	}

	// 复制数据
	result := make([]byte, len(data))
	copy(result, data)

	// 只加密消息体部分（跳过消息头）
	if len(data) > HeaderSize {
		bodyData := result[HeaderSize:]
		encryptedBody := defaultEncryptor.Encrypt(bodyData)
		copy(result[HeaderSize:], encryptedBody)

		// 设置加密标志位
		result[3] |= FlagEncrypted
	}

	return result
}

// DecryptMessageBody 解密消息体（跳过消息头）
func DecryptMessageBody(data []byte) ([]byte, error) {
	if len(data) < HeaderSize {
		return nil, fmt.Errorf("data too short for message header: %d bytes", len(data))
	}

	// 检查加密标志位
	if (data[3] & FlagEncrypted) == 0 {
		return data, nil // 未加密，直接返回
	}

	// 复制数据
	result := make([]byte, len(data))
	copy(result, data)

	// 只解密消息体部分（跳过消息头）
	if len(data) > HeaderSize {
		bodyData := result[HeaderSize:]
		decryptedBody := defaultEncryptor.Decrypt(bodyData)
		copy(result[HeaderSize:], decryptedBody)

		// 清除加密标志位
		result[3] &= ^uint8(FlagEncrypted)
	}

	return result, nil
}

// IsEncrypted 检查数据是否已加密
func IsEncrypted(data []byte) bool {
	if len(data) < HeaderSize {
		return false
	}

	return (data[3] & FlagEncrypted) != 0
}
