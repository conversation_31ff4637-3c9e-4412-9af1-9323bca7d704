﻿<Border x:Class="Xylia.Preview.UI.Views.Dialogs.MessageDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008" mc:Ignorable="d"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
		xmlns:hc="https://handyorg.github.io/handycontrol"
        Background="{DynamicResource RegionBrush}" CornerRadius="10"
		MaxWidth="400" MinHeight="140" Margin="75 0 0 30">

    <!-- 添加微妙的阴影效果 -->
    <Border.Effect>
        <DropShadowEffect Color="Black" Opacity="0.15" BlurRadius="12" ShadowDepth="4" Direction="270"/>
    </Border.Effect>

    <Grid Margin="7 10">
		<Grid.RowDefinitions>
			<RowDefinition Height="*" />
			<RowDefinition Height="Auto" />
		</Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <!-- 消息文本区域 -->
        <Path x:Name="IconPath" Width="30" Height="30" Margin="5 0 8 0" Data="{Binding IconGeometry}" Fill="{Binding IconBrush}" Stretch="Fill" />
        <TextBlock d:Text="Message" Text="{Binding Message}" FontSize="16" VerticalAlignment="Center" Margin="5" TextWrapping="Wrap" Grid.Column="1" MinWidth="275" />

        <!-- 按钮区域 -->
        <hc:UniformSpacingPanel Grid.Row="1" Grid.ColumnSpan="99" Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Bottom" Spacing="10">
            <Button d:Content="取消" Content="{DynamicResource Text.Cancel}" Width="75" Height="30" Command="{Binding CancelCommand}"
					Visibility="{Binding ShowCancelButton, Converter={StaticResource Boolean2VisibilityConverter}}" />
            <Button d:Content="否" Content="{DynamicResource Text.No}" Width="75" Height="30" Command="{Binding NoCommand}"
					Visibility="{Binding ShowNoButton, Converter={StaticResource Boolean2VisibilityConverter}}" />
            <Button d:Content="是" Content="{DynamicResource Text.Yes}" Style="{StaticResource ButtonPrimary}" Width="75" Height="30" Command="{Binding YesCommand}"
					Visibility="{Binding ShowYesButton, Converter={StaticResource Boolean2VisibilityConverter}}" />
            <Button d:Content="确定" Content="{Binding OkText}" Style="{StaticResource ButtonPrimary}" Width="75" Height="30" Command="{Binding OkCommand}"
					Visibility="{Binding ShowOkButton, Converter={StaticResource Boolean2VisibilityConverter}}" />
		</hc:UniformSpacingPanel>
	</Grid>
</Border>
