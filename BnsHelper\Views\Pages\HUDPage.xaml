﻿<Page x:Class="Xylia.BnsHelper.Views.Pages.HUDPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:hc="https://handyorg.github.io/handycontrol"
      Title="HUDPage">

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Border Grid.Row="0" Background="{DynamicResource SecondaryRegionBrush}"
                BorderBrush="{DynamicResource BorderBrush}" BorderThickness="0,0,0,1"
                Padding="15,10" Margin="-3,-5,-3,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="界面配置" FontSize="16" FontWeight="SemiBold" 
                               Foreground="{DynamicResource PrimaryTextBrush}" VerticalAlignment="Center"
                               ToolTip="用于配置游戏界面元素的显示状态" />
                </StackPanel>

                <hc:ButtonGroup Grid.Column="1" Margin="0">
                    <Button Content="重新加载" Command="{Binding ReloadCommand}"
                            Style="{StaticResource ButtonDefault}" Padding="12,6">
                        <Button.ToolTip>
                            <ToolTip Content="重新加载配置" />
                        </Button.ToolTip>
                    </Button>

                    <Button Content="添加" Command="{Binding AddCommand}" Style="{StaticResource ButtonPrimary}" Padding="12,6">
                        <Button.ToolTip>
                            <ToolTip Content="添加新的HUD配置" />
                        </Button.ToolTip>
                    </Button>
                    <Button Content="导入" Command="{Binding ImportCommand}" Style="{StaticResource ButtonDefault}" Padding="12,6">
                        <Button.ToolTip>
                            <ToolTip Content="从文件导入配置" />
                        </Button.ToolTip>
                    </Button>
                    <Button Content="导出" Command="{Binding ExportCommand}" Style="{StaticResource ButtonDefault}" Padding="12,6">
                        <Button.ToolTip>
                            <ToolTip Content="导出配置到文件" />
                        </Button.ToolTip>
                    </Button>
                    <Button Content="重置" Command="{Binding ResetCommand}" Style="{StaticResource ButtonWarning}" Padding="12,6">
                        <Button.ToolTip>
                            <ToolTip Content="重置为默认配置" />
                        </Button.ToolTip>
                    </Button>
                </hc:ButtonGroup>
            </Grid>
        </Border>

        <!-- Content Section -->
        <Grid Grid.Row="1" Margin="0,10,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!-- Column Headers -->
            <Border Grid.Row="0" Background="{DynamicResource ThirdlyRegionBrush}"
                    BorderBrush="{DynamicResource BorderBrush}"
                    BorderThickness="1,1,1,0"
                    Padding="15,8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="200" />
                        <ColumnDefinition Width="150" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="120" />
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" Text="界面名称" FontWeight="SemiBold"
                               Foreground="{DynamicResource PrimaryTextBrush}" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                    <TextBlock Grid.Column="1" Text="显示名称" FontWeight="SemiBold"
                               Foreground="{DynamicResource PrimaryTextBrush}" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                    <TextBlock Grid.Column="2" Text="快速操作" FontWeight="SemiBold"
                               Foreground="{DynamicResource PrimaryTextBrush}" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                    <TextBlock Grid.Column="3" Text="操作" FontWeight="SemiBold"
                               Foreground="{DynamicResource PrimaryTextBrush}" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                </Grid>
            </Border>

            <!-- Data Rows -->
            <ScrollViewer Grid.Row="1" HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
                <ItemsControl ItemsSource="{Binding Data}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Background="{DynamicResource RegionBrush}"
                                    BorderBrush="{DynamicResource BorderBrush}"
                                    BorderThickness="1,0,1,1"
                                    Padding="15,8">
                                <Border.ContextMenu>
                                    <ContextMenu>
                                        <MenuItem Header="删除" Command="{Binding DeleteCommand}"
                                                  Foreground="{DynamicResource DangerBrush}">
                                            <MenuItem.Icon>
                                                <TextBlock Text="&#xE74D;" Style="{StaticResource SegoeIcon}"
                                                           Foreground="{DynamicResource DangerBrush}" />
                                            </MenuItem.Icon>
                                        </MenuItem>
                                    </ContextMenu>
                                </Border.ContextMenu>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="200" />
                                        <ColumnDefinition Width="150" />
                                        <ColumnDefinition Width="100" />
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="120" />
                                    </Grid.ColumnDefinitions>

                                    <!-- 界面名称 -->
                                    <TextBox Grid.Column="0" Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                                             Style="{StaticResource TextBoxExtend}" Margin="2" />

                                    <!-- 显示名称 -->
                                    <TextBlock Grid.Column="1" Text="{Binding DisplayName}"
                                               VerticalAlignment="Center" Margin="5,2" />
                                    
                                    <!-- 快速操作 -->
                                    <Button Grid.Column="2" Content="{Binding StatusText}"
                                            Command="{Binding ToggleStatusCommand}"
                                            Style="{StaticResource ButtonDefault}" Padding="8,4" Margin="2"
                                            FontSize="12" ToolTip="点击切换状态" />

                                    <!-- 操作按钮 -->
                                    <Button Grid.Column="3" Content="删除" Command="{Binding DeleteCommand}"
                                            Style="{StaticResource ButtonDanger}" Padding="8,4" Margin="2"
                                            FontSize="12" />
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </Grid>
    </Grid>
</Page>