using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.ObjectModel;
using System.Diagnostics;
using Xylia.BnsHelper.Models.Triggers;

namespace Xylia.BnsHelper.Common.Converters;

/// <summary>
/// 触发器JSON转换器
/// 用于所有触发器相关的序列化，支持多种格式，保持文件夹结构
/// </summary>
public class TriggerConverter : JsonConverter
{
    public override bool CanConvert(Type objectType)
    {
        return objectType == typeof(object) ||
               objectType == typeof(ObservableCollection<TriggerFolder>) ||
               objectType == typeof(List<TriggerFolder>) ||
               objectType == typeof(List<Trigger>);
    }

    public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
    {
        if (value == null)
        {
            writer.WriteNull();
            return;
        }

        // 直接序列化，保持原有结构
        var token = JToken.FromObject(value, CreateInnerSerializer(serializer));
        token.WriteTo(writer);
    }

    public override object? ReadJson(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
    {
        var token = JToken.Load(reader);
        if (token.Type == JTokenType.Null) return null;

        var innerSerializer = CreateInnerSerializer(serializer);

        // 如果目标类型是ObservableCollection<TriggerFolder>，处理配置文件加载
        if (objectType == typeof(ObservableCollection<TriggerFolder>))
        {
            return ReadAsObservableCollection(token, innerSerializer);
        }

        // 处理导入导出的通用情况
        if (token.Type == JTokenType.Array)
        {
            var firstItem = token.First;
            if (firstItem != null && HasFolderProperties(firstItem))
            {
                return token.ToObject<List<TriggerFolder>>(innerSerializer);
            }
            else
            {
                return token.ToObject<List<Trigger>>(innerSerializer);
            }
        }
        else if (token.Type == JTokenType.Object)
        {
            if (HasFolderProperties(token))
            {
                return token.ToObject<TriggerFolder>(innerSerializer);
            }
            else
            {
                return token.ToObject<Trigger>(innerSerializer);
            }
        }

        return null;
    }

    private ObservableCollection<TriggerFolder> ReadAsObservableCollection(JToken token, JsonSerializer serializer)
    {
        var result = new ObservableCollection<TriggerFolder>();

        try
        {
            if (token.Type == JTokenType.Array)
            {
                var array = (JArray)token;

                foreach (var item in array)
                {
                    try
                    {
                        // 这是一个文件夹对象
                        var folder = item.ToObject<TriggerFolder>(serializer);
                        if (folder != null) result.Add(folder);
                    }
                    catch (Exception itemEx)
                    {
                        // 记录单个项目的解析错误，但继续处理其他项目
                       Debug.WriteLine($"[TriggerConverter] 解析项目失败: {itemEx.Message}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            // 如果解析失败，记录错误并返回空集合
            Debug.WriteLine($"[TriggerConverter] ReadAsObservableCollection 失败: {ex.Message}");
        }

        return result;
    }

    /// <summary>
    /// 创建一个不包含TriggerConverter的序列化器，避免无限递归
    /// </summary>
    private JsonSerializer CreateInnerSerializer(JsonSerializer originalSerializer)
    {
        var innerSerializer = new JsonSerializer();
        innerSerializer.TypeNameHandling = originalSerializer.TypeNameHandling;
        foreach (var converter in originalSerializer.Converters)
        {
            if (converter != this) // 排除当前转换器
            {
                innerSerializer.Converters.Add(converter);
            }
        }
        return innerSerializer;
    }

    private static bool HasFolderProperties(JToken token)
    {
        if (token.Type != JTokenType.Object)
            return false;

        var obj = (JObject)token;

        // 检查$type属性，如果明确指定了类型
        var typeProperty = obj["$type"]?.ToString();
        if (!string.IsNullOrEmpty(typeProperty)) return typeProperty.Contains("TriggerFolder");

        // 检查是否包含文件夹特有的属性
        if (obj.ContainsKey("Nodes")) return true;

        return false;
    }
}