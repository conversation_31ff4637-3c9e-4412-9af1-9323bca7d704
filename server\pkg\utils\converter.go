package utils

// interface{}转换为uint64
func convertToUint64(value interface{}) uint64 {
	switch v := value.(type) {
	case uint64:
		return v
	case int64:
		if v >= 0 {
			return uint64(v)
		}
	case int:
		if v >= 0 {
			return uint64(v)
		}
	case uint:
		return uint64(v)
	case uint32:
		return uint64(v)
	case int32:
		if v >= 0 {
			return uint64(v)
		}
	case float64:
		if v >= 0 && v <= float64(^uint64(0)) {
			return uint64(v)
		}
	case float32:
		if v >= 0 && v <= float32(^uint64(0)) {
			return uint64(v)
		}
	}
	return 0
}

// 将interface{}转换为uint8
func convertToUint8(value interface{}) uint8 {
	switch v := value.(type) {
	case uint8:
		return v
	case int8:
		if v >= 0 {
			return uint8(v)
		}
	case int:
		if v >= 0 && v <= 255 {
			return uint8(v)
		}
	case uint:
		if v <= 255 {
			return uint8(v)
		}
	case uint32:
		if v <= 255 {
			return uint8(v)
		}
	case int32:
		if v >= 0 && v <= 255 {
			return uint8(v)
		}
	case uint64:
		if v <= 255 {
			return uint8(v)
		}
	case int64:
		if v >= 0 && v <= 255 {
			return uint8(v)
		}
	case float64:
		if v >= 0 && v <= 255 {
			return uint8(v)
		}
	case float32:
		if v >= 0 && v <= 255 {
			return uint8(v)
		}
	}
	return 0
}

// 安全地将interface{}转换为uint32
func convertToUint32(value interface{}) uint32 {
	switch v := value.(type) {
	case uint32:
		return v
	case int32:
		if v >= 0 {
			return uint32(v)
		}
	case int:
		if v >= 0 && v <= int(^uint32(0)) {
			return uint32(v)
		}
	case uint:
		if v <= uint(^uint32(0)) {
			return uint32(v)
		}
	case uint64:
		if v <= uint64(^uint32(0)) {
			return uint32(v)
		}
	case int64:
		if v >= 0 && v <= int64(^uint32(0)) {
			return uint32(v)
		}
	case float64:
		if v >= 0 && v <= float64(^uint32(0)) {
			return uint32(v)
		}
	case float32:
		if v >= 0 && v <= float32(^uint32(0)) {
			return uint32(v)
		}
	}
	return 0
}
