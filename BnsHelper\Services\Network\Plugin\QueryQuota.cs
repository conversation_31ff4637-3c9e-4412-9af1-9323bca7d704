﻿using System.Diagnostics;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Plugin;
internal class QueryQuote(int id) : IPacket
{
	public DataArchiveWriter Create()
	{
		var writer = new DataArchiveWriter();
		writer.Write((short)12);
		writer.Write(id);
		return writer;
	}

	public void Read(DataArchive reader)
	{
		long value = reader.Read<int>();
        Debug.WriteLine(value);
	}
}