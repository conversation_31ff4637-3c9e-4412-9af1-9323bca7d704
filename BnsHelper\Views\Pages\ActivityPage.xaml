﻿<Page x:Class="Xylia.BnsHelper.Views.Pages.ActivityPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:ui="http://schemas.lepo.co/wpfui/2022/xaml"
      xmlns:hc="https://handyorg.github.io/handycontrol"
      xmlns:WebView="clr-namespace:Microsoft.Web.WebView2.Wpf;assembly=Microsoft.Web.WebView2.Wpf">

    <TabControl>
        <TabItem Header="一键领取">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 顶部工具栏 -->
                <Border Grid.Row="0" Background="{DynamicResource ApplicationBackgroundBrush}" BorderBrush="{DynamicResource ControlElevationBorderBrush}" BorderThickness="0,0,0,1" Padding="10,7,0,15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- 服务器和角色选择 -->
                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <StackPanel Orientation="Horizontal" Margin="0,0,5,0">
                                <TextBlock Text="服务器" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                <ComboBox ItemsSource="{Binding Worlds}" SelectedItem="{Binding SelectedWorld}" DisplayMemberPath="Name" Width="120" Margin="0,0,8,0"/>
                            </StackPanel>

                            <StackPanel Orientation="Horizontal" Margin="0,0,5,0">
                                <TextBlock Text="角色" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                <ComboBox ItemsSource="{Binding Roles}" SelectedItem="{Binding SelectedRole}" DisplayMemberPath="Name" Width="120" Margin="0,0,4,0" />
                            </StackPanel>
                        </StackPanel>

                        <!-- 执行按钮 -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="0,0,12,0">
                            <Button Content="刷新数据" Command="{Binding RefreshCommand}" Margin="0,0,8,0" Style="{StaticResource ButtonInfo}" Padding="8,4"/>
                            <Button Content="选择奖励" Command="{Binding ShowManualActivityCommand}" Style="{StaticResource ButtonPrimary}" Margin="0,0,8,0" />
                            <Button Content="领取活动" Command="{Binding ExecuteAllFlowsCommand}" Style="{StaticResource ButtonSuccess}" />
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- 活动列表 -->
                <Border Grid.Row="1" Background="{DynamicResource RegionBrush}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <ScrollViewer Grid.Row="1" Margin="8,0,8,8" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                            <ItemsControl ItemsSource="{Binding Activities}">
                                <ItemsControl.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <UniformGrid Columns="3" />
                                    </ItemsPanelTemplate>
                                </ItemsControl.ItemsPanel>
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" CornerRadius="8" Margin="4" Padding="12" MinHeight="80">
                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto" />
                                                    <RowDefinition Height="*" />
                                                    <RowDefinition Height="Auto" />
                                                </Grid.RowDefinitions>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>

                                                <!-- 标题信息 -->
                                                <CheckBox Grid.Column="0" IsChecked="{Binding ShouldExecute}" Margin="0,0,10,0" ToolTip="标记为需要领取" />
                                                <TextBlock Grid.Column="1" Text="{Binding ActivityName}" FontWeight="SemiBold" TextWrapping="Wrap" TextTrimming="CharacterEllipsis" HorizontalAlignment="Left" MaxWidth="120" />

                                                <!-- 状态标签 -->
                                                <StackPanel Grid.ColumnSpan="2" Grid.Row="1" Orientation="Horizontal" Margin="0,6,0,0" Visibility="Collapsed">
                                                    <Border Background="{DynamicResource InfoBrush}" CornerRadius="3" Padding="6,2" Visibility="Collapsed">

                                                    </Border>
                                                </StackPanel>

                                                <!-- 时间信息 -->
                                                <StackPanel Grid.ColumnSpan="2" Grid.Row="2" Orientation="Horizontal">
                                                    <Path Fill="#ff6b6b" Stretch="Fill" Width="12" Height="12" Data="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8zm.5-13H11v6l5.2 3.2.8-1.3-4.5-2.7V7z" />
                                                    <TextBlock Foreground="#ff6b6b" Text="{Binding RemainingTime}" FontSize="12" VerticalAlignment="Bottom" Margin="7 0 0 0" />
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </Border>

                <!-- 底部状态栏 -->
                <Border Grid.Row="2" Background="{DynamicResource RegionBrush}" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="0,1,0,0" Padding="16,8">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 执行历史按钮 -->
                        <Button Grid.Column="0" Content="执行历史" Command="{Binding ViewExecutionHistoryCommand}" Style="{StaticResource ButtonIcon}" Padding="8,4" FontSize="12"/>

                        <!-- 状态消息 -->
                        <TextBlock Grid.Column="1" HorizontalAlignment="Right" VerticalAlignment="Center" Text="{Binding StatusMessage}" Foreground="{DynamicResource SecondaryTextBrush}" />
                    </Grid>
                </Border>
            </Grid>
        </TabItem>

        <TabItem Header="合集页">
            <WebView:WebView2 Name="WebView" CreationProperties="{StaticResource EvergreenWebView2CreationProperties}" Width="1070" Height="675" />
        </TabItem>
    </TabControl>
</Page>