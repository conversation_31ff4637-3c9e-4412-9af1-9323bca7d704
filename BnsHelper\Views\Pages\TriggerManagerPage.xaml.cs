using System.Windows;
using Xylia.BnsHelper.ViewModels.Pages;

namespace Xylia.BnsHelper.Views.Pages;
public partial class TriggerManagerPage 
{
    readonly TriggerManagerViewModel _viewModel;

    public TriggerManagerPage()
    {
        InitializeComponent();
        DataContext = _viewModel = new TriggerManagerViewModel();
    }

    private void TreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
    {
        _viewModel.SelectedItem = e.NewValue;
    }
}
