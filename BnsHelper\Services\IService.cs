﻿using System.Collections.ObjectModel;
using System.Diagnostics;

namespace Xylia.BnsHelper.Services;
public interface IService
{
	/// <summary>
	/// Initiaze service
	/// </summary>
	/// <returns>regist result</returns>
	void Register();
}

public class ServiceManager : Collection<IService>
{
	private static ServiceManager? _instance;
	private readonly Dictionary<Type, IService> _services = new();

	public ServiceManager(params IService[] services)
	{
		_instance = this;

		foreach (var s in services)
		{
			try
			{
				_services[s.GetType()] = s;
				s.Register();
			}
			catch (Exception ex)
			{
				Debug.Fail(string.Format("{0} register failed.", s.GetType()), ex.Message);
			}
		}
	}

	public static T? GetService<T>() where T : class, IService
	{
		return _instance?._services.GetValueOrDefault(typeof(T)) as T;
	}

	public static void RegisterService<T>(T service) where T : class, IService
	{
		if (_instance != null)
		{
			_instance._services[typeof(T)] = service;
		}
	}
}