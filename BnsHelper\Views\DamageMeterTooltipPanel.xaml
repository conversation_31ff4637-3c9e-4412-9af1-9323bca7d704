﻿<Grid x:Class="Xylia.BnsHelper.Views.DamageMeterTooltipPanel"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:hc="https://handyorg.github.io/handycontrol"
	  xmlns:helper="clr-namespace:Xylia.BnsHelper.Common.Helpers"
	  Width="370" MinWidth="370" MaxWidth="370">
	<Grid.RowDefinitions>
		<RowDefinition Height="Auto" />
		<RowDefinition Height="Auto" />
		<RowDefinition Height="5" x:Name="Padding" />
		<RowDefinition Height="*" />
	</Grid.RowDefinitions>

	<!-- Result -->
	<UniformGrid x:Name="ResultHolder" Grid.Row="0" Columns="2">
		<UniformGrid.Resources>
			<Style TargetType="TextBlock">
				<Style.Setters>
					<Setter Property="FontSize" Value="13" />
					<Setter Property="Padding" Value="15 3" />
					<Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}" />
				</Style.Setters>
			</Style>
		</UniformGrid.Resources>

        <TextBlock Text="{Binding Seconds,Mode=OneWay,StringFormat={}战斗总时间 {0}秒}" />
        <TextBlock Text="{Binding DamageRate,Mode=OneWay,StringFormat={}队伍贡献率 {0:P1}}" Visibility="{Binding Mode,Converter={StaticResource Boolean2VisibilityConverter}}"/>
        <TextBlock Text="{Binding TotalValueDisplayText,Mode=OneWay}" Visibility="{Binding Mode,Converter={StaticResource Boolean2VisibilityReConverter}}"/>
        <TextBlock Text="{Binding ParryRate,Mode=OneWay,StringFormat={}战斗格挡率 {0:P2}}" Visibility="{Binding IsDamageOutputType,Converter={StaticResource Boolean2VisibilityConverter}}" />
        <TextBlock Text="{Binding DodgeRate,Mode=OneWay,StringFormat={}战斗闪避率 {0:P2}}" Visibility="{Binding IsDamageOutputType,Converter={StaticResource Boolean2VisibilityConverter}}" />
        <TextBlock Text="{Binding PerSecondValueText,Mode=OneWay,StringFormat={}每秒伤害    {0}}" Visibility="{Binding DisplayPerSecondValue,Converter={StaticResource Boolean2VisibilityConverter}}" />
        <TextBlock Text="{Binding ExhaustionCount,Mode=OneWay,StringFormat={}濒死次数    {0}}" Visibility="{Binding IsDamageTakenType,Converter={StaticResource Boolean2VisibilityConverter}}" />
        <TextBlock Text="{Binding DeathCount,Mode=OneWay,StringFormat={}死亡次数    {0}}" Visibility="{Binding IsDamageTakenType,Converter={StaticResource Boolean2VisibilityConverter}}" />
        <TextBlock Text="{Binding TotalSpRestored,Mode=OneWay,StringFormat={}总内力恢复  {0}}" Visibility="{Binding IsHealingType,Converter={StaticResource Boolean2VisibilityConverter}}" />
    </UniformGrid>
	<TextBlock Grid.Row="1" Padding="15 3" Visibility="{Binding Self,Converter={StaticResource Boolean2VisibilityReConverter}}">
		<Run Text="&#xE7BA;" FontFamily="{StaticResource SegoeAssets}" />
		<Run Text="隐藏其他角色时，无法记录暴击次数数据" />
	</TextBlock>

	<!-- Container -->
	<Grid>
		<Grid.ColumnDefinitions>
			<ColumnDefinition Width="1.2*" />
			<ColumnDefinition Width="1.2*" />
			<ColumnDefinition Width="1.6*"/>
		</Grid.ColumnDefinitions>

		<Grid Grid.Column="0" x:Name="col1" />
		<Grid Grid.Column="1" x:Name="col2" />
		<Grid Grid.Column="2" x:Name="col3" />
	</Grid>
    <ListView Grid.Row="3" x:Name="SkillHolder" ItemsSource="{Binding Skills}"
              ScrollViewer.HorizontalScrollBarVisibility="Disabled">
		<ListView.ItemContainerStyle>
			<Style TargetType="{x:Type ListViewItem}" BasedOn="{StaticResource ListViewItemBaseStyle}">
				<Setter Property="Focusable" Value="False" />
				<Setter Property="HorizontalContentAlignment" Value="Stretch"/>
				<Setter Property="MinHeight" Value="30" />
			</Style>
		</ListView.ItemContainerStyle>
		<ListView.View>
			<GridView>
				<GridView.ColumnHeaderContainerStyle>
					<Style TargetType="{x:Type GridViewColumnHeader}" BasedOn="{StaticResource InputElementBaseStyle}">
						<Setter Property="IsEnabled" Value="False"/>
						<Setter Property="HorizontalAlignment" Value="Stretch"/>
						<Setter Property="HorizontalContentAlignment" Value="Center"/>
					</Style>
				</GridView.ColumnHeaderContainerStyle>

                <!-- 技能名称 -->
                <GridViewColumn Width="{Binding ElementName=col1, Path=ActualWidth}">
					<GridViewColumn.Header>
						<TextBlock Text="{DynamicResource DamageMeterPanel_Skill}" HorizontalAlignment="Center" />
					</GridViewColumn.Header>
					<GridViewColumn.CellTemplate>
						<DataTemplate>
							<StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Image Source="/Resources/Images/race_cat.png" Visibility="{Binding Summoned, Converter={StaticResource Boolean2VisibilityConverter}}" Margin="4 0 4 0" Width="18" Height="18" />
                                <TextBlock Text="{Binding Name}" VerticalAlignment="Center" />
                            </StackPanel>
						</DataTemplate>
					</GridViewColumn.CellTemplate>
				</GridViewColumn>
                
                <!-- 命中次数 -->
				<GridViewColumn Width="{Binding ElementName=col2, Path=ActualWidth}">
					<GridViewColumn.Header>
						<TextBlock Text="{Binding HitFormat,Source={x:Static helper:SettingHelper.Default}}" HorizontalAlignment="Center" />
					</GridViewColumn.Header>
					<GridViewColumn.CellTemplate>
						<DataTemplate>
							<TextBlock Text="{Binding Converter={StaticResource DamageMeterHitFormat}}" HorizontalAlignment="Center" />
						</DataTemplate>
					</GridViewColumn.CellTemplate>
				</GridViewColumn>

                <!-- 伤害信息 -->
                <GridViewColumn Width="{Binding ElementName=col3, Path=ActualWidth}">
					<GridViewColumn.Header>
						<TextBlock Text="{DynamicResource DamageMeterPanel_AccumulateDamage}" HorizontalAlignment="Center" />
					</GridViewColumn.Header>
					<GridViewColumn.CellTemplate>
						<DataTemplate>
                            <ProgressBar Value="{Binding TotalRate,Mode=OneWay}" Maximum="1" Style="{StaticResource ProgressBarNoAnimation}" Margin="0 0 30 0">
								<hc:VisualElement.Text>
									<MultiBinding StringFormat="{} {0} ({1:P0})">
                                        <Binding Path="TotalDamage" />
										<Binding Path="TotalRate" />
									</MultiBinding>
								</hc:VisualElement.Text>
							</ProgressBar>
						</DataTemplate>
					</GridViewColumn.CellTemplate>
				</GridViewColumn>
			</GridView>
		</ListView.View>
	</ListView>
</Grid>