using System.Diagnostics;
using Xylia.BnsHelper.Common.Extensions;
using Xylia.BnsHelper.Models.Api;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Service;

/// <summary>
/// 活动总版本
/// </summary>
internal class ActivityVersionPacket : BasePacket
{
    public ushort? TotalVersion;

    protected override void ReadResponse(DataArchive reader)
    {
        TotalVersion = reader.Read<ushort>();
    }
}

/// <summary>
/// 活动版本列表
/// </summary>
internal class ActivityListPacket : BasePacket
{
    /// <summary>
    /// 活动版本列表
    /// </summary>
    public KeyValuePair<ulong, ushort>[] Items { get; set; } = [];

    protected override void ReadResponse(DataArchive reader)
    {
        Items = new KeyValuePair<ulong, ushort>[reader.Read<uint>()];

        for (int i = 0; i < Items.Length; i++)
        {
            Items[i] = new(reader.Read<ulong>(), reader.Read<ushort>());
        }
    }
}

/// <summary>
/// 活动详细信息包
/// </summary>
internal class ActivityPacket : BasePacket
{
    #region Fields
    // Request
    public ulong ActivityId;

    // Response
    public ActivityInfo? Activity;
    #endregion

    #region Methods
    public override DataArchiveWriter Create()
    {
        var writer = base.Create();
        writer.Write(ActivityId);
        return writer;
    }

    protected override void ReadResponse(DataArchive reader)
    {
        Activity = ReadActivityData(reader);
    }

    /// <summary>
    /// 读取活动数据
    /// </summary>
    private ActivityInfo? ReadActivityData(DataArchive reader)
    {
        try
        {
            var activity = new ActivityInfo();

            // 获取活动信息
            activity.ActivityId = reader.Read<ulong>();
            activity.ActivityName = reader.ReadString();
            activity.Version = reader.Read<ushort>();
            activity.EndTime = reader.Read<long>().ToTime();

            // 读取分组信息
            var groupCount = reader.Read<ushort>();
            var groups = new Dictionary<uint, string>();
            for (int i = 0; i < groupCount; i++)
            {
                var groupId = reader.Read<uint>();
                var groupName = reader.ReadString();
                groups[groupId] = groupName;
                Debug.WriteLine($"[UDP] 分组信息: ID={groupId}, Name={groupName}");
            }

            // 创建IDE对象
            var ide = activity.Ide = new ActivityIde
            {
                RetCode = 0,
                Message = "success",
                Flows = []
            };

            // 解析流程
            var flowCount = reader.Read<ushort>();
            for (int i = 0; i < flowCount; i++)
            {
                var flow = ReadFlowData(reader);
                if (flow.HasValue) ide.Flows[flow.Value.Item1] = flow.Value.Item2;
            }

            Debug.WriteLine($"活动ID: {activity.ActivityId}, 状态: {activity.ActivityStatus}");
            Debug.WriteLine($"开始时间: {activity.BeginTime}, 结束时间: {activity.EndTime}");
            Debug.WriteLine($"成功解析活动: {activity.ActivityName} (流程数: {ide.Flows.Count})");
            return activity;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"解析活动失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 读取流程数据
    /// </summary>
    private (ulong, ActivityFlow)? ReadFlowData(DataArchive reader)
    {
        var flow = new ActivityFlow();
        var flowId = reader.Read<ulong>();
        flow.Name = reader.ReadString();
        flow.IdeToken = reader.ReadString();
        flow.AccountType = reader.Read<byte>().ToString();
        flow.FlowType = reader.Read<byte>();
        flow.Custom = reader.Read<byte>().ToString();
        flow.Group = reader.Read<uint>();

        // 参数数量
        var paramCount = reader.Read<ushort>();

        // 解析参数
        flow.InputParams = new List<InputParam>();
        for (int i = 0; i < paramCount; i++)
        {
            flow.InputParams.Add(new InputParam
            {
                Key = reader.ReadString(),
                Value = reader.ReadString()
            });
        }

        return (flowId, flow);
    }
    #endregion
}