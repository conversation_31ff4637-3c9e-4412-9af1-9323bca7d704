﻿<BnsCustomWindowWidget x:Class="Xylia.Preview.UI.GameUI.Scene.Game_Tooltip.ItemTooltipPanel"
	xmlns="https://github.com/xyliaup/bns-preview-tools"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	xmlns:s="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
	xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" mc:Ignorable="d"
	MetaData="2" Width="400">

	<Overlay LayoutData.Anchors="0 0 1 1">
		<BnsCustomWindowWidget>
			<BnsCustomLabelWidget Name="ItemName" String="Item Name Item Name" FontSize="18" LayoutData.Anchors="0 0 .8 0" />
			<BnsCustomLabelWidget Name="Category" String="{Binding GameCategory3, Converter={TextResource}}" d:String="카테고리카테">
				<BnsCustomLabelWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="0" />
				</BnsCustomLabelWidget.HorizontalResizeLink>
			</BnsCustomLabelWidget>
			<BnsCustomImageWidget Name="ItemIcon" LayoutData.Offsets="0 0 80 80" Foreground="White">
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" Offset1="3" LinkWidgetName1="ItemName" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bShow="true" ExpansionType="IMAGE" ExpansionName="BackgroundImage">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="0 66" ImageUVSize="48 48" EnableDrawImage="true" TintColor="FFFFFFFF" GrayWeightValue="0.15" Opacity="0.5" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="IMAGE" ExpansionName="IconImage">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Icon/Acc_Bangle_Legendry_2-1Phase.Acc_Bangle_Legendry_2-1Phase" ImageUVSize="64 64" EnableMultiImage="true" TintColor="FFFFFFFF" GrayWeightValue="0.15" Opacity="1" ImageScale="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="IMAGE" ExpansionName="Grade_Image">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty TintColor="FFFFFFFF" GrayWeightValue="0.15" Opacity="1" ImageScale="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="IMAGE" ExpansionName="UnusableImage">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty EnableDrawImage="true" TintColor="FFFFFFFF" GrayWeightValue="0.15" Opacity="1" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="IMAGE" ExpansionName="CanSaleItem" >
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" TintColor="FFFFFFFF" GrayWeightValue="0.15" Opacity="1" ImageScale="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="IMAGE" ExpansionName="AppearanceChanged" >
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="704 0" ImageUVSize="64 64" EnableDrawImage="true" EnableResourceSize="true" TintColor="FFFFFF" GrayWeightValue="0.15" Opacity="1" ImageScale="0.35" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="IMAGE" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" TintColor="FFFFFF" GrayWeightValue="0.15" Opacity="1" ImageScale="0.5" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="IMAGE" ExpansionName="SymbolImage_Chacked" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" TintColor="FFFFFF" GrayWeightValue="0.15" Opacity="1" ImageScale="0.5" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="false" ExpansionType="IMAGE" ExpansionName="UnIdentifiedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Icon/unuseable_seal.unuseable_seal" ImageUVSize="64 64" EnableDrawImage="true" EnableFittedImage="true" TintColor="FFFFFF" GrayWeightValue="0.15" Opacity="1" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="false" ExpansionType="IMAGE" ExpansionName="GrowthMileage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="360 389" ImageUVSize="26 26" EnableDrawImage="true" EnableResourceSize="true" TintColor="FFFFFF" GrayWeightValue="0.15" Opacity="1" ImageScale="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>

					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="false" ExpansionType="STRING" ExpansionName="Count" >
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_16.Normal_16" LabelText="99999" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" Padding="2 2" Opacity="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomLabelWidget Name="CollectionSubstituteText" String="만으로도 완료 할 수 있습니다." FontSize="15" LayoutData.Anchors="0 0 1 1">
				<BnsCustomLabelWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" Offset1="7" LinkWidgetName1="ItemIcon" />
				</BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomLabelWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" Offset1="3" LinkWidgetName1="ItemName" />
				</BnsCustomLabelWidget.VerticalResizeLink>
			</BnsCustomLabelWidget>
			<BnsCustomLabelWidget Name="CollectionSubstitute2Text" String="만으로도 완료 할 수 있습니다." LayoutData.Anchors="0 0 1 1">
				<BnsCustomLabelWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" Offset1="7" LinkWidgetName1="ItemIcon" />
				</BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomLabelWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" Offset1="0" LinkWidgetName1="CollectionSubstituteText" />
				</BnsCustomLabelWidget.VerticalResizeLink>
			</BnsCustomLabelWidget>
			<BnsCustomLabelWidget Name="ProbabilityText" LayoutData.Anchors="0 0 1 1">
				<BnsCustomLabelWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" Offset1="7" LinkWidgetName1="ItemIcon" />
				</BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomLabelWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" Offset1="0" LinkWidgetName1="CollectionSubstitute2Text" />
				</BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomLabelWidget.String>
					<StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Label_Green03_12.Label_Green03_12" SpaceBetweenLines="3" />
				</BnsCustomLabelWidget.String>
			</BnsCustomLabelWidget>
		</BnsCustomWindowWidget>

		<BnsCustomWindowWidget Name="SetItemEffect">
			<BnsCustomLabelWidget Name="SetItemEffect_Name" String="SetItemEffect.Name" />
			<BnsCustomLabelWidget Name="SetItemEffect_Title" String="SetItemEffect.Title" MetaData="textref=UI.ItemTooltip.SetItemEffect.Title">
				<BnsCustomLabelWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" Offset1="3" LinkWidgetName1="SetItemEffect_Name" />
				</BnsCustomLabelWidget.VerticalResizeLink>
			</BnsCustomLabelWidget>
			<BnsCustomLabelWidget Name="SetItemEffect_Effect">
				<BnsCustomLabelWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" Offset1="3" LinkWidgetName1="SetItemEffect_Title" />
				</BnsCustomLabelWidget.VerticalResizeLink>
			</BnsCustomLabelWidget>
		</BnsCustomWindowWidget>

		<BnsCustomLabelWidget Name="ItemDescription7" d:String="Item Description7" />
		<BnsCustomWindowWidget Name="Combat_Holder_Parent">
			<VerticalBox Name="Combat_Holder" LayoutData.Anchors="0 0 1 0" Margin="3 10">
				<BnsCustomLabelWidget Name="Combat_Holder_Title">
					<BnsCustomLabelWidget.String>
						<StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Label_LightYellow_12.Label_LightYellow_12" LabelText="Combat Holder Title" SpaceBetweenLines="3" />
					</BnsCustomLabelWidget.String>
				</BnsCustomLabelWidget>
			</VerticalBox>
		</BnsCustomWindowWidget>

		<!-- Description -->
		<BnsCustomLabelWidget Name="DecomposeDescription_Title">
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Label_LightYellow_12.Label_LightYellow_12" LabelText="Decompose Result Description Title" SpaceBetweenLines="3" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
		<VerticalBox Name="DecomposeDescription" Margin="0 0 0 5" />
		<BnsCustomLabelWidget Name="ItemDescription_4_Title">
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Label_LightYellow_12.Label_LightYellow_12" LabelText="Item Description 4 Title" SpaceBetweenLines="3" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
		<BnsCustomLabelWidget Name="ItemDescription_4" String="Item Description4" />
		<BnsCustomLabelWidget Name="ItemDescription_5_Title">
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Label_LightYellow_12.Label_LightYellow_12" LabelText="Item Description 5 Title" SpaceBetweenLines="3" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
		<BnsCustomLabelWidget Name="ItemDescription_5" String="Item Description5" />
		<BnsCustomLabelWidget Name="ItemDescription_6_Title">
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Label_LightYellow_12.Label_LightYellow_12" LabelText="Item Description 6 Title" SpaceBetweenLines="3" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
		<BnsCustomLabelWidget Name="ItemDescription_6" String="Item Description6" />
		<BnsCustomLabelWidget Name="ItemDescription" String="Item Description2" />

		<!-- ProcessComparisonHolder -->
		<BnsCustomLabelWidget Name="ItemTooltipPanel_Required" Margin="0 7" />
		<BnsCustomLabelWidget Name="ItemTooltipPanel_SealEnable" />
		<BnsCustomLabelWidget Name="ItemTooltipPanel_ExpirationTime" Visibility="Collapsed">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="15" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" Offset1="10" LinkWidgetName1="GrowthMileage" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_12.Normal_Out_12" LabelText="기간제 만료 시간" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" LastRenderWidth="82.50001" LastRenderHeight="18" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>

		<BnsCustomLabelWidget Name="ItemTooltipPanel_Price" LayoutData.Anchors="0 0 1 0">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="15" Offset2="15" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="/Game/Art/UI/GameUI/Resource/GameUI_FontSet/UI/Normal_12.Normal_12" LabelText="판매가+보유수량" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" ClippingBound="-2 0" ClippingBoundFace_Horizontal="WidgetFaceFace_Right" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
	</Overlay>
</BnsCustomWindowWidget>