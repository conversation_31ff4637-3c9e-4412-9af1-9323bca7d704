﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

	<Color x:Key="LightPrimaryColor">#044289</Color>
	
	<Color x:Key="LightDangerColor">#450c0f</Color>
	<Color x:Key="DangerColor">#db3340</Color>
	<Color x:Key="DarkDangerColor">#db3340</Color>

	<Color x:Key="LightWarningColor">#4c3a0f</Color>
	<Color x:Key="WarningColor">#e9af20</Color>
	<Color x:Key="DarkWarningColor">#e9af20</Color>

	<Color x:Key="LightInfoColor">#003c44</Color>
	<Color x:Key="InfoColor">#00bcd4</Color>
	<Color x:Key="DarkInfoColor">#00bcd4</Color>

	<Color x:Key="LightSuccessColor">#113a1b</Color>
	<Color x:Key="SuccessColor">#2db84d</Color>
	<Color x:Key="DarkSuccessColor">#2db84d</Color>

	<Color x:Key="PrimaryTextColor">#ffffff</Color>
	<Color x:Key="SecondaryTextColor">#a6a6a6</Color>
	<Color x:Key="ThirdlyTextColor">#3f3f46</Color>
	<Color x:Key="ReverseTextColor">#212121</Color>
	<Color x:Key="TextIconColor">White</Color>

	<Color x:Key="BorderColor">#3f3f46</Color>
	<Color x:Key="SecondaryBorderColor">#555555</Color>
	<Color x:Key="BackgroundColor">#1e1e1e</Color>
	<Color x:Key="RegionColor">#2d2d30</Color>
	<Color x:Key="SecondaryRegionColor">#3c3c3c</Color>
	<Color x:Key="ThirdlyRegionColor">#424242</Color>
	<Color x:Key="TitleColor">#326cf3</Color>
	<Color x:Key="SecondaryTitleColor">#326cf3</Color>

	<Color x:Key="DefaultColor">#686868</Color>
	<Color x:Key="DarkDefaultColor">#686868</Color>

	<Color x:Key="AccentColor">#ff5722</Color>
	<Color x:Key="DarkAccentColor">#d43f3a</Color>

	<Color x:Key="DarkMaskColor">#40000000</Color>
	<Color x:Key="DarkOpacityColor">#40000000</Color>
	<system:UInt32 x:Key="BlurGradientValue">0x99000000</system:UInt32>

	<!-- Additional Brush Resources -->
	<SolidColorBrush x:Key="LightDangerBrush" Color="{StaticResource LightDangerColor}"/>
	<SolidColorBrush x:Key="DangerBrush" Color="{StaticResource DangerColor}"/>
	<SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
	<SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}"/>
	<SolidColorBrush x:Key="TextIconBrush" Color="{StaticResource TextIconColor}"/>
	<SolidColorBrush x:Key="DarkPrimaryBrush" Color="{StaticResource DarkPrimaryColor}"/>
	<SolidColorBrush x:Key="SecondaryBorderBrush" Color="{StaticResource SecondaryBorderColor}"/>
	<SolidColorBrush x:Key="ThirdlyRegionBrush" Color="{StaticResource ThirdlyRegionColor}"/>
	<SolidColorBrush x:Key="DarkOpacityBrush" Color="{StaticResource DarkOpacityColor}"/>


	<!-- AvalonEdit -->
	<Color x:Key="ControlAccentColorKey">#1ba1e2</Color>
	<Color x:Key="EditorBackgroundColor">#FF181818</Color>
	<Color x:Key="EditorForegroundColor">#FFFFFFFF</Color>
	<Color x:Key="EditorLineNumbersForegroundColor">#ff929292</Color>
	<Color x:Key="EditorNonPrintableCharacterColor">#2FFFFFFF</Color>
	<Color x:Key="EditorLinkTextForegroundColor">#FFAAAAFF</Color>
	<Color x:Key="EditorLinkTextBackgroundColor">Transparent</Color>

	<Color x:Key="XML_XmlDeclaration">#569CD6</Color>
	<Color x:Key="XML_XmlTag">#569CD6</Color>
	<Color x:Key="XML_AttributeName">#9CDCFE</Color>
	<Color x:Key="XML_AttributeValue">#CE9178</Color>
	<!-- AvalonEdit -->

</ResourceDictionary>
