package service

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"udp-server/server/internal/model"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// 风控管理服务
type RiskControlAdminService struct {
	db                 *gorm.DB
	riskControlService *service.RiskControlService
}

// 创建风控管理服务
func NewRiskControlAdminService(db *gorm.DB, riskControlService *service.RiskControlService) *RiskControlAdminService {
	return &RiskControlAdminService{
		db:                 db,
		riskControlService: riskControlService,
	}
}

// 获取风控事件列表
func (s *RiskControlAdminService) GetRiskEvents(page, limit int, eventType, dateStr string) ([]map[string]interface{}, error) {
	var events []struct {
		model.RiskEvent
		ProcessedByName string `gorm:"column:processed_by_name"`
	}
	offset := (page - 1) * limit

	// 使用LEFT JOIN获取管理员名称
	query := s.db.Table("risk_events").
		Select("risk_events.*, bns_useradmin.username as processed_by_name").
		Joins("LEFT JOIN bns_useradmin ON risk_events.processed_by = bns_useradmin.uid").
		Order("risk_events.created_at DESC").
		Offset(offset).Limit(limit)

	// 按事件类型过滤
	if eventType != "" {
		query = query.Where("risk_events.event_type = ?", eventType)
	}

	// 按日期过滤
	if dateStr != "" {
		startTime, err := time.Parse("2006-01-02", dateStr)
		if err == nil {
			endTime := startTime.Add(24 * time.Hour)
			query = query.Where("risk_events.created_at >= ? AND risk_events.created_at < ?", startTime, endTime)
		}
	}

	err := query.Find(&events).Error
	if err != nil {
		return nil, err
	}

	// 转换为管理后台格式
	result := make([]map[string]interface{}, len(events))
	for i, event := range events {
		result[i] = map[string]interface{}{
			"id":           event.ID,
			"event_type":   event.EventType,
			"device_id":    event.DeviceID,
			"ip_address":   event.IPAddress,
			"qq_numbers":   event.QQNumbers,
			"count":        event.Count,
			"severity":     event.Severity,
			"status":       event.Status,
			"description":  event.Description,
			"processed_by": event.ProcessedByName, // 从JOIN查询获取的管理员名称
			"processed_at": event.ProcessedAt,
			"created_at":   event.CreatedAt.Format("2006-01-02 15:04:05"),
		}
	}

	return result, nil
}

// 处理风控事件
func (s *RiskControlAdminService) ProcessRiskEvent(eventID uint, action, adminNote string, adminID uint64, adminUsername string) error {
	var event model.RiskEvent
	if err := s.db.First(&event, eventID).Error; err != nil {
		return fmt.Errorf("风险事件不存在: %v", err)
	}

	now := time.Now()

	switch action {
	case "approve":
		// 批准：恢复用户正常状态
		event.Status = "approved"
		if err := s.RestoreUsersFromAudit(event.QQNumbers, "管理员批准"); err != nil {
			logger.Error("恢复用户状态失败: %v", err)
		}
	case "reject":
		// 拒绝：保持审核状态或进一步处理
		event.Status = "rejected"
	case "ignore":
		// 忽略：标记为已忽略
		event.Status = "ignored"
	default:
		return fmt.Errorf("无效的处理动作: %s", action)
	}

	event.ProcessedBy = &adminID
	event.ProcessedAt = &now

	if err := s.db.Save(&event).Error; err != nil {
		return fmt.Errorf("更新风险事件失败: %v", err)
	}

	// 记录管理员操作日志
	logger.Info("风控事件处理: EventID=%d, Action=%s, AdminID=%d, AdminUsername=%s",
		eventID, action, adminID, adminUsername)

	return nil
}

// 获取风控配置
func (s *RiskControlAdminService) GetRiskConfig() (map[string]interface{}, error) {
	// 从数据库获取配置
	var configs []model.RiskControlConfig
	if err := s.db.Find(&configs).Error; err != nil {
		return nil, fmt.Errorf("查询配置失败: %v", err)
	}

	// 创建配置映射
	configMap := make(map[string]interface{})
	for _, config := range configs {
		configMap[config.ConfigKey] = config.ConfigValue
	}

	// 如果没有配置，使用默认值
	if len(configMap) == 0 {
		defaultConfig := service.DefaultRiskControlConfig()
		configMap = map[string]interface{}{
			"max_qq_per_device_per_day":   defaultConfig.MaxQQPerDevicePerDay,
			"max_qq_per_ip_per_day":       defaultConfig.MaxQQPerIPPerDay,
			"max_devices_per_qq_per_day":  defaultConfig.MaxDevicesPerQQPerDay,
			"max_login_attempts_per_hour": defaultConfig.MaxLoginAttemptsPerHour,
			"enable_new_device_check":     defaultConfig.EnableNewDeviceCheck,
			"amap_api_key":                defaultConfig.AmapAPIKey,
		}
	}

	configMap["last_updated"] = time.Now().Format("2006-01-02 15:04:05")

	return configMap, nil
}

// 更新风控配置
func (s *RiskControlAdminService) UpdateRiskConfig(configData map[string]interface{}) error {
	// 保存配置到数据库
	for key, value := range configData {
		if key == "last_updated" {
			continue // 跳过时间戳字段
		}

		var strValue string
		switch v := value.(type) {
		case string:
			strValue = v
		case float64:
			strValue = strconv.FormatFloat(v, 'f', -1, 64)
		case int:
			strValue = strconv.Itoa(v)
		case bool:
			if v {
				strValue = "true"
			} else {
				strValue = "false"
			}
		default:
			strValue = fmt.Sprintf("%v", v)
		}

		// 更新或创建配置项
		var config model.RiskControlConfig
		err := s.db.Where("config_key = ?", key).First(&config).Error
		if err == gorm.ErrRecordNotFound {
			// 创建新配置项
			config = model.RiskControlConfig{
				ConfigKey:   key,
				ConfigValue: strValue,
				Description: s.getConfigDescription(key),
			}
			if err := s.db.Create(&config).Error; err != nil {
				return fmt.Errorf("创建配置项失败: %v", err)
			}
		} else if err != nil {
			return fmt.Errorf("查询配置项失败: %v", err)
		} else {
			// 更新现有配置项
			config.ConfigValue = strValue
			if err := s.db.Save(&config).Error; err != nil {
				return fmt.Errorf("更新配置项失败: %v", err)
			}
		}
	}

	logger.Info("风控配置已更新并保存到数据库")
	return nil
}

// 获取配置项描述
func (s *RiskControlAdminService) getConfigDescription(key string) string {
	descriptions := map[string]string{
		"max_qq_per_device_per_day":   "同设备登录不同QQ号的阈值",
		"max_qq_per_ip_per_day":       "同IP登录不同QQ号的阈值",
		"max_devices_per_qq_per_day":  "单账号单日多IP登录检查阈值（3个或更多城市时触发风控）",
		"max_login_attempts_per_hour": "短时间内频繁登录的阈值",
		"enable_new_device_check":     "新设备登录检查",
		"amap_api_key":                "高德地图API密钥",
	}

	if desc, exists := descriptions[key]; exists {
		return desc
	}
	return ""
}

// 从审核状态恢复用户
func (s *RiskControlAdminService) RestoreUsersFromAudit(qqNumbersStr, reason string) error {
	logger.Info("恢复用户状态: QQNumbers=%s, Reason=%s", qqNumbersStr, reason)

	if qqNumbersStr == "" {
		return fmt.Errorf("QQ号列表为空")
	}

	// 解析QQ号字符串
	var qqNumbers []int64

	// 尝试解析JSON格式
	if strings.HasPrefix(qqNumbersStr, "[") && strings.HasSuffix(qqNumbersStr, "]") {
		var jsonNumbers []interface{}
		if err := json.Unmarshal([]byte(qqNumbersStr), &jsonNumbers); err == nil {
			for _, num := range jsonNumbers {
				switch v := num.(type) {
				case float64:
					qqNumbers = append(qqNumbers, int64(v))
				case string:
					if qq, err := strconv.ParseInt(v, 10, 64); err == nil {
						qqNumbers = append(qqNumbers, qq)
					}
				}
			}
		}
	} else {
		// 解析空格或逗号分隔的格式
		qqNumbersStr = strings.ReplaceAll(qqNumbersStr, ",", " ")
		parts := strings.Fields(qqNumbersStr)
		for _, part := range parts {
			if qq, err := strconv.ParseInt(part, 10, 64); err == nil {
				qqNumbers = append(qqNumbers, qq)
			} else {
				logger.Warn("无法解析QQ号: %s", part)
			}
		}
	}

	if len(qqNumbers) == 0 {
		return fmt.Errorf("未找到有效的QQ号")
	}

	logger.Info("解析到 %d 个QQ号: %v", len(qqNumbers), qqNumbers)

	// 批量更新用户状态为正常（状态0）
	successCount := 0
	for _, qqNumber := range qqNumbers {
		// 查找用户
		var user model.User
		if err := s.db.Where("uin = ?", qqNumber).First(&user).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				logger.Warn("用户不存在，跳过恢复: QQ=%d", qqNumber)
				continue
			}
			logger.Error("查询用户失败: QQ=%d, Error=%v", qqNumber, err)
			continue
		}

		// 检查用户当前状态
		if user.Status == 0 {
			logger.Info("用户状态已是正常，跳过: QQ=%d", qqNumber)
			continue
		}

		// 更新用户状态为正常
		if err := s.db.Model(&user).Update("status", 0).Error; err != nil {
			logger.Error("更新用户状态失败: QQ=%d, Error=%v", qqNumber, err)
			continue
		}

		logger.Info("用户状态恢复成功: QQ=%d, 原状态=%d -> 新状态=0", qqNumber, user.Status)
		successCount++
	}

	logger.Info("用户状态恢复完成: 成功=%d, 总数=%d, 原因=%s", successCount, len(qqNumbers), reason)

	if successCount == 0 {
		return fmt.Errorf("没有用户状态被成功恢复")
	}

	return nil
}
