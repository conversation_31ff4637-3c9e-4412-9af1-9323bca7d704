using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Win32;
using System.IO;
using System.Windows;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models.Triggers;
using TriggerAction = Xylia.BnsHelper.Models.Triggers.TriggerAction;

namespace Xylia.BnsHelper.ViewModels.Dialogs;
public partial class TriggerActionEditorViewModel : ObservableObject
{
    #region Fields
    [ObservableProperty] string _title;
    [ObservableProperty] TriggerAction? _action;

    public bool DialogResult { get; private set; } = false;

    /// <summary>
    /// 关闭请求事件
    /// </summary>
    public event EventHandler? CloseRequested;
    #endregion

    #region Constructor
    public TriggerActionEditorViewModel()
    {
        _title = "编辑动作";
    }
    #endregion

    #region Methods
    /// <summary>
    /// 设置要编辑的动作
    /// </summary>
    public void SetAction(TriggerAction action)
    {
        Action = action;
        Title = $"编辑动作 - {GetActionTypeName(action)}";
    }

    /// <summary>
    /// 获取动作类型名称
    /// </summary>
    private string GetActionTypeName(TriggerAction action)
    {
        return action switch
        {
            AudioPlayAction => "音频播放",
            TTSAction => "TTS语音播报",
            NotificationAction => "系统通知",
            TextDisplayAction => "文本显示",
            LogAction => "日志记录",
            SendGameMessageAction => "游戏内消息",
            KeyboardInputAction => "键盘输入",
            MouseAction => "鼠标操作",
            CounterAction => "计数器",
            _ => "未知动作"
        };
    }

    /// <summary>
    /// 取消
    /// </summary>
    [RelayCommand]
    private void Cancel()
    {
        DialogResult = false;
        CloseRequested?.Invoke(this, EventArgs.Empty);
    }

    /// <summary>
    /// 确定
    /// </summary>
    [RelayCommand]
    private void Ok()
    {
        // 验证输入
        if (Action == null)
        {
            MessageBox.Show("动作不能为空", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // 根据动作类型进行特定验证
        try
        {
            ValidateAction(Action);
        }
        catch (Exception ex)
        {
            MessageBox.Show(ex.Message, "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        DialogResult = true;
        CloseRequested?.Invoke(this, EventArgs.Empty);
    }

    /// <summary>
    /// 验证动作
    /// </summary>
    private void ValidateAction(TriggerAction action)
    {
        switch (action)
        {
            case AudioPlayAction a:
                if (string.IsNullOrWhiteSpace(a.AudioPath)) throw new Exception("请选择音频文件");
                break;

            case TTSAction a:
                if (string.IsNullOrWhiteSpace(a.Text)) throw new Exception("请输入要播报的文本");
                break;

            case NotificationAction a:
                if (string.IsNullOrWhiteSpace(a.Message)) throw new Exception("请输入通知消息");
                break;

            case TextDisplayAction a:
                if (string.IsNullOrWhiteSpace(a.Text)) throw new Exception("请输入要显示的文本");
                break;

            case LogAction a:
                if (string.IsNullOrWhiteSpace(a.Message)) throw new Exception("请输入消息文本");
                break;

            case SendGameMessageAction a:
                if (string.IsNullOrWhiteSpace(a.Message)) throw new Exception("请输入消息文本");
                break;

            case KeyboardInputAction a:
                if (string.IsNullOrWhiteSpace(a.Keys)) throw new Exception("请输入按键序列");
                break;

            case CounterAction a:
                if (string.IsNullOrWhiteSpace(a.CounterName)) throw new Exception("请输入计数器名称");
                break;
        }
    }

    /// <summary>
    /// 浏览音频文件
    /// </summary>
    [RelayCommand]
    private void BrowseAudioFile()
    {
        if (Action is not AudioPlayAction audioAction) return;

        var dialog = new OpenFileDialog()
        {
            Title = "选择音频文件",
            Filter = "音频文件|*.mp3;*.wav;*.wma;*.m4a;*.aac;*.ogg;*.flac|MP3文件|*.mp3|WAV文件|*.wav|所有文件|*.*",
            FilterIndex = 1
        };

        // 如果当前已有路径且文件存在，设置初始目录
        if (!string.IsNullOrWhiteSpace(audioAction.AudioPath) && File.Exists(audioAction.AudioPath))
        {
            dialog.InitialDirectory = Path.GetDirectoryName(audioAction.AudioPath);
            dialog.FileName = Path.GetFileName(audioAction.AudioPath);
        }

        if (dialog.ShowDialog() == true)
        {
            audioAction.AudioPath = dialog.FileName;
        }
    }

    /// <summary>
    /// 测试播放音频
    /// </summary>
    [RelayCommand]
    private async Task TestPlayAudio()
    {
        if (Action is not AudioPlayAction audioAction) return;

        if (string.IsNullOrWhiteSpace(audioAction.AudioPath))
        {
            MessageBox.Show("请先选择音频文件", "提示", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            return;
        }

        if (!File.Exists(audioAction.AudioPath))
        {
            MessageBox.Show("音频文件不存在", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            return;
        }

        try
        {
            await AudioSteamReader.Play(File.OpenRead(audioAction.AudioPath), audioAction.Volume * 0.01f);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"播放音频失败: {ex.Message}", "错误", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }
    #endregion
}
