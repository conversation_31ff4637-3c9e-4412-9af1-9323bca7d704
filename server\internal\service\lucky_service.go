package service

import (
	"fmt"
	"math/rand"
	"net"
	"strings"
	"time"

	"udp-server/server/internal/database"
	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"
	"udp-server/server/pkg/utils"

	"gorm.io/gorm"
)

// LuckyService 签到抽奖服务
type LuckyService struct {
	db                *gorm.DB
	cache             cache.Cache
	hashCache         CacheService // 支持Hash操作的缓存服务
	permissionService *PermissionService
	cdkeyService      *CDKeyService // CDKEY服务
}

// NewLuckyService 创建签到抽奖服务实例
func NewLuckyService(cache cache.Cache, permissionService *PermissionService, hashCache *RedisCache) *LuckyService {
	// 创建CDKEY服务实例
	cdkeyService := NewCDKeyService(cache, permissionService)

	return &LuckyService{
		db:                database.GetDB(),
		cache:             cache,
		hashCache:         hashCache,
		permissionService: permissionService,
		cdkeyService:      cdkeyService,
	}
}

// 获取抽奖活动公告
func (s *LuckyService) GetRewardList() (*model.LuckyRewardList, error) {
	// 获取当前活动
	activity, err := s.GetCurrentActivity()
	if err != nil {
		return nil, fmt.Errorf("没有正在进行中的活动")
	}

	// 获取奖励列表
	rewards, err := s.GetAvailableRewards(activity.ID)
	if err != nil {
		return nil, err
	}

	// 转换奖励信息
	var LuckyRewardInfos []model.LuckyRewardInfo
	for _, reward := range rewards {
		LuckyRewardInfos = append(LuckyRewardInfos, model.LuckyRewardInfo{
			ID:          reward.ID,
			Name:        reward.Text,
			Type:        reward.Type,
			Weight:      reward.Weight,
			Point:       reward.Point,
			Description: reward.Text, // 使用Text作为描述
		})
	}

	return &model.LuckyRewardList{
		ActivityID:   activity.ID,
		ActivityName: activity.Name,
		Description:  activity.Name, // 使用Name字段作为描述
		StartTime:    activity.StartTime,
		EndTime:      activity.EndTime,
		Rewards:      LuckyRewardInfos,
	}, nil
}

// 获取用户活动状态
func (s *LuckyService) GetUserStatus(uid uint64) (*model.LuckyStatusResponse, error) {
	// 获取当前活动
	activity, err := s.GetCurrentActivity()
	if err != nil {
		logger.Warn("获取当前活动失败: %v", err)
		return nil, fmt.Errorf("暂无开启的活动")
	}

	// 获取用户签到数据
	userDraw, err := s.getUserDraw(uid, activity.ID)
	if err != nil {
		return nil, err
	}

	// 检查用户签到状态，如果断签了需要调整显示的Point数和Today计算
	displayPoint := userDraw.Point
	displayDay := userDraw.Day
	effectiveToday := userDraw.Today // 用于计算可用次数的Today值

	if !userDraw.IsToday() && !userDraw.IsYesterday() {
		// 用户断签了，需要根据活动类型调整显示数据
		displayDay = 0     // 断签时连签天数显示为0
		effectiveToday = 0 // 断签时Today应该视为0来计算可用次数

		if activity.IsContinue {
			// 连续签到活动：重置Point
			displayPoint = 0
		} else {
			// 累积签到活动：显示先前值
			displayPoint = userDraw.Point
		}
	} else if !userDraw.IsToday() {
		// 昨天签到的用户，Today应该重置为0
		effectiveToday = 0
	}

	// 正确计算可用次数：extra + free - effectiveToday
	availableCount := int(userDraw.Extra) + int(activity.Free) - effectiveToday
	if availableCount < 0 {
		availableCount = 0
	}

	return &model.LuckyStatusResponse{
		UID:            uid,
		ActivityID:     activity.ID,
		ActivityName:   activity.Name,
		Day:            displayDay,   // 使用调整后的连签天数
		Point:          displayPoint, // 使用调整后的奖励点数
		Number:         userDraw.Number,
		Today:          userDraw.Today,
		Extra:          userDraw.Extra,
		LastSignTime:   userDraw.Time,
		AvailableCount: uint32(availableCount),
	}, nil
}

// 执行抽奖
func (s *LuckyService) Draw(uid uint64, device string, remoteAddr *net.UDPAddr) (*model.LuckyDrawResponse, error) {
	// 获取当前活动
	activity, err := s.GetCurrentActivity()
	if err != nil {
		return nil, fmt.Errorf("没有正在进行中的活动")
	}

	// 检查用户签到不可用缓存
	if err := s.checkDeviceSignIn(device, activity.ID); err != nil {
		return nil, err
	}

	// 获取或创建用户签到数据
	userDraw, err := s.getUserDraw(uid, activity.ID)
	if err != nil {
		return nil, err
	}

	// 更新用户签到状态
	if err := s.updateUserDrawStatus(userDraw, activity); err != nil {
		return nil, err
	}

	// 获取可用奖励
	rewards, err := s.GetAvailableRewards(activity.ID)
	if err != nil {
		return nil, err
	}

	// 计算奖励
	selectedReward, err := s.calculateReward(rewards, userDraw.Point)
	if err != nil {
		return nil, err
	}

	// 使用事务确保奖励处理和签到数据更新的原子性
	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 处理奖励
		if err := s.processRewardInTransaction(tx, selectedReward, userDraw, uid); err != nil {
			logger.Error("处理奖励失败: UID=%d, Reward=%s, Error=%v", uid, selectedReward.Reward, err)
			return err
		}

		// 更新用户签到数据（使用 Updates 方法避免复合主键问题）
		if err := tx.Model(&model.UserDraw{}).Where("uid = ? AND schedule = ?", userDraw.UID, userDraw.Schedule).Updates(map[string]interface{}{
			"extra":  userDraw.Extra,
			"day":    userDraw.Day,
			"point":  userDraw.Point,
			"number": userDraw.Number,
			"today":  userDraw.Today,
			"time":   userDraw.Time,
		}).Error; err != nil {
			logger.Error("更新用户签到数据失败: UID=%d, Error=%v", uid, err)
			return fmt.Errorf("更新用户签到数据失败: %v", err)
		}

		logger.Info("签到事务成功完成: UID=%d, Reward=%s", uid, selectedReward.Reward)
		return nil
	})

	if err != nil {
		return nil, err
	}

	// 检查用户是否用完所有可用次数
	// 如果用完次数记录设备签到状态以阻止后续签到
	availableCount := int(userDraw.Extra) + int(activity.Free) - int(userDraw.Today)
	if availableCount <= 0 {
		s.recordSignInStatus(device, activity.ID)
	}

	// 签到成功后，如果有实际奖励清除用户权限缓存以便重新计算
	if s.permissionService != nil && selectedReward.Reward != "" {
		if err := s.permissionService.ClearUserPermissionCache(uid, "client"); err != nil {
			logger.Warn("清除用户权限缓存失败: UID=%d, Error=%v", uid, err)
		}
	}

	// 生成响应内容
	text := s.generateResponseText(selectedReward, userDraw)
	return &model.LuckyDrawResponse{
		Message:    text,
		Day:        userDraw.Day,
		Point:      userDraw.Point,
		RewardName: selectedReward.Reward, // 使用 Reward 字段作为奖励名称
	}, nil
}

// 获取当前活动
func (s *LuckyService) GetCurrentActivity() (*model.Lucky, error) {
	cacheKey := "current_activity"

	// 先检查缓存
	var cachedActivity model.Lucky
	if err := s.cache.Get(cacheKey, &cachedActivity); err == nil {
		// 检查缓存的活动是否仍然有效
		now := time.Now()
		if cachedActivity.IsActivity &&
			now.After(cachedActivity.StartTime) &&
			now.Before(cachedActivity.EndTime) {
			return &cachedActivity, nil
		}
		// 缓存的活动已过期，删除缓存
		s.cache.Delete(cacheKey)
	}

	// 缓存未命中或已过期，从数据库查询
	var activity model.Lucky
	now := time.Now()

	err := s.db.Where("is_activity = ? AND startTime <= ? AND endTime >= ?",
		true, now, now).First(&activity).Error
	if err != nil {
		return nil, err
	}

	// 计算缓存时间：到活动结束时间，但最多缓存1小时
	cacheDuration := time.Until(activity.EndTime)
	if cacheDuration > time.Hour {
		cacheDuration = time.Hour
	}
	if cacheDuration < time.Minute {
		cacheDuration = time.Minute // 最少缓存1分钟
	}

	// 将结果存入缓存
	if err := s.cache.Set(cacheKey, activity, cacheDuration); err != nil {
		logger.Warn("缓存当前活动失败: %v", err)
	}

	return &activity, nil
}

// 获取特定活动的可用奖励
func (s *LuckyService) GetAvailableRewards(activityID uint) ([]model.LuckyReward, error) {
	cacheKey := fmt.Sprintf("activity_rewards_%d", activityID)

	// 先检查缓存
	var cachedRewards []model.LuckyReward
	if err := s.cache.Get(cacheKey, &cachedRewards); err == nil {
		return cachedRewards, nil
	}

	// 缓存未命中，从数据库查询
	var rewards []model.LuckyReward
	err := s.db.Where("activity = ? AND is_use = ?", activityID, true).Find(&rewards).Error
	if err != nil {
		return nil, err
	}

	// 将结果存入缓存（缓存24小时）
	if err := s.cache.Set(cacheKey, rewards, 24*time.Hour); err != nil {
		logger.Warn("缓存活动奖励失败: ActivityID=%d, Error=%v", activityID, err)
	}

	return rewards, nil
}

// 获取或创建用户签到数据
func (s *LuckyService) getUserDraw(uid uint64, activityID uint) (*model.UserDraw, error) {
	var userDraw model.UserDraw

	// 先尝试查询现有记录
	err := s.db.Where("uid = ? AND schedule = ?", uid, activityID).First(&userDraw).Error
	if err == gorm.ErrRecordNotFound {
		// 记录不存在，创建新记录
		currentTime := time.Now()
		userDraw = model.UserDraw{
			UID:      uid,
			Schedule: activityID,
			Extra:    0,
			Day:      1,
			Point:    1,
			Number:   0,
			Today:    0,
			Time:     currentTime,
		}

		// 使用 FirstOrCreate 确保记录被创建（处理并发情况）
		result := s.db.Where("uid = ? AND schedule = ?", uid, activityID).FirstOrCreate(&userDraw)
		if result.Error != nil {
			logger.Error("创建用户签到记录失败: UID=%d, ActivityID=%d, Error=%v", uid, activityID, result.Error)
			return nil, result.Error
		}
	} else if err != nil {
		// 其他数据库错误
		logger.Error("查询用户签到记录失败: UID=%d, ActivityID=%d, Error=%v", uid, activityID, err)
		return nil, err
	}

	return &userDraw, nil
}

// 更新用户签到状态
func (s *LuckyService) updateUserDrawStatus(userDraw *model.UserDraw, activity *model.Lucky) error {
	now := time.Now()

	if userDraw.IsToday() {
		// 今天已经签到过
		if userDraw.Today >= activity.Free {
			if userDraw.Extra == 0 {
				return &model.AlreadySignedInError{}
			}
			userDraw.Extra--
		}
		userDraw.Today++
		// 今天重复签到，连签天数和积分不变
	} else if userDraw.IsYesterday() {
		// 昨天签到，连续签到
		userDraw.Today = 1
		userDraw.Day++
		userDraw.Point++
	} else {
		// 断签或首次签到
		userDraw.Today = 1
		userDraw.Day = 1 // 重置为1天
		if activity.IsContinue {
			userDraw.Point = 1 // 连续签到活动：重置奖励点数
		} else {
			userDraw.Point++ // 累积签到活动：奖励点数累积
		}
	}

	// 更新总签到次数
	userDraw.Number++
	userDraw.Time = now

	return nil
}

// 计算本次抽奖奖励
func (s *LuckyService) calculateReward(rewards []model.LuckyReward, userPoint int) (*model.LuckyReward, error) {
	// 分离保底奖励和随机奖励
	var guardRewards []model.LuckyReward
	var randomRewards []model.LuckyReward

	for _, reward := range rewards {
		// 检查点数要求和可用性
		if reward.Point <= userPoint && reward.IsAvailable() {
			if reward.Type == "guard" {
				guardRewards = append(guardRewards, reward)
			} else {
				randomRewards = append(randomRewards, reward)
			}
		}
	}

	// 先检查保底奖励
	if len(guardRewards) > 0 {
		for _, reward := range guardRewards {
			randNum := rand.Intn(10000) + 1
			if randNum <= reward.Weight {
				return &reward, nil
			}
		}
	}

	// 随机奖励计算
	if len(randomRewards) > 0 {
		totalWeight := 0
		for _, reward := range randomRewards {
			totalWeight += reward.Weight
		}

		if totalWeight > 0 {
			randNum := rand.Intn(totalWeight) + 1
			cumulative := 0

			for _, reward := range randomRewards {
				cumulative += reward.Weight
				if randNum <= cumulative {
					return &reward, nil
				}
			}
		}
	}

	return nil, fmt.Errorf("非常抱歉，本次未中奖")
}

// 在事务中处理奖励
func (s *LuckyService) processRewardInTransaction(tx *gorm.DB, reward *model.LuckyReward, userDraw *model.UserDraw, uid uint64) error {
	if reward.IsResetPoint {
		userDraw.Point = 0
	}
	// 注意：Number 字段已经在 updateUserDrawStatus 中增加过了，这里不需要再次增加

	// 如果有实际奖励
	if reward.Reward != "" {
		// 更新限制次数
		if reward.Limit > 0 {
			if err := tx.Model(reward).Update("limit_current", reward.LimitCurrent+1).Error; err != nil {
				logger.Error("更新奖励限制次数失败: RewardID=%d, Error=%v", reward.ID, err)
				return err
			}
		}

		// 激活CDKey（签到触发的激活使用系统模式）
		if err := s.cdkeyService.ActivateCDKeyInTransaction(tx, reward.Reward, uid, 0, ""); err != nil { // 0表示系统操作
			logger.Error("激活CDKey失败: UID=%d, CDKey=%s, Error=%v", uid, reward.Reward, err)
			return err
		}

		// 记录抽奖结果
		result := model.UserDrawResult{
			Reward: reward.ID,
			UID:    uid,
		}
		if err := tx.Create(&result).Error; err != nil {
			logger.Error("创建抽奖结果记录失败: UID=%d, RewardID=%d, Error=%v", uid, reward.ID, err)
			return err
		}
	}

	return nil
}

// generateResponseText 生成响应文本
func (s *LuckyService) generateResponseText(reward *model.LuckyReward, userDraw *model.UserDraw) string {
	text := reward.Text
	text = strings.ReplaceAll(text, "{day}", fmt.Sprintf("%d", userDraw.Day))
	text = strings.ReplaceAll(text, "{num}", fmt.Sprintf("%d", userDraw.Number))
	text = strings.ReplaceAll(text, "{point}", fmt.Sprintf("%d", userDraw.Point))
	return text
}

// 检查设备是否今天已经签到
func (s *LuckyService) checkDeviceSignIn(deviceFingerprint string, activityID uint) error {
	isSignedToday, err := s.checkDeviceSignInStatus(deviceFingerprint, activityID)
	if err != nil {
		// 检查出错时跳过检查，不阻止签到
		return nil
	}

	if isSignedToday {
		return &model.AlreadySignedInError{Message: "当前设备今天已经签到过啦~"}
	}

	return nil
}

// 检查设备签到状态
func (s *LuckyService) checkDeviceSignInStatus(deviceFingerprint string, activityID uint) (bool, error) {
	hashKey := fmt.Sprintf("activity_signin_%d", activityID)
	field := fmt.Sprintf("device_%s", deviceFingerprint)

	lastSignTime, err := s.hashCache.HGet(hashKey, field)
	if err != nil {
		// 缓存未命中，表示未签到
		return false, nil
	}

	if timestamp, ok := lastSignTime.(float64); ok {
		lastTime := time.Unix(int64(timestamp), 0)
		isToday := utils.IsToday(lastTime)

		// 检查是否为今天签到
		return isToday, nil
	}

	return false, nil
}

// 记录设备签到状态
func (s *LuckyService) recordSignInStatus(deviceFingerprint string, activityID uint) {
	hashKey := fmt.Sprintf("activity_signin_%d", activityID)
	currentTime := time.Now().Unix()

	// 记录设备签到状态
	if deviceFingerprint != "" {
		deviceField := fmt.Sprintf("device_%s", deviceFingerprint)
		if err := s.hashCache.HSet(hashKey, deviceField, currentTime); err != nil {
			logger.Warn("设置设备签到状态缓存失败: DeviceFingerprint=%s, Error=%v", deviceFingerprint, err)
		} else {
			// 设置哈希表过期时间到明天凌晨
			tomorrow := time.Now().AddDate(0, 0, 1)
			tomorrowMidnight := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, tomorrow.Location())
			cacheDuration := time.Until(tomorrowMidnight)

			if err := s.hashCache.Expire(hashKey, cacheDuration); err != nil {
				logger.Warn("设置设备签到状态哈希表过期时间失败: HashKey=%s, Error=%v", hashKey, err)
			}
		}
	}
}

// 清除设备签到状态（用于测试或管理）
func (s *LuckyService) ClearDeviceSignInStatus(deviceFingerprint string, activityID uint) error {
	hashKey := fmt.Sprintf("activity_signin_%d", activityID)
	field := fmt.Sprintf("device_%s", deviceFingerprint)

	// 删除哈希字段
	if err := s.hashCache.HDel(hashKey, field); err != nil {
		logger.Error("清除设备签到状态失败: DeviceFingerprint=%s, ActivityID=%d, Error=%v", deviceFingerprint, activityID, err)
		return err
	}

	// 设备签到状态清除成功
	return nil
}
