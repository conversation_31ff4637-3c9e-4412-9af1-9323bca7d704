﻿using Microsoft.Win32;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using Xylia.Preview.Common.Extension;

namespace Xylia.BnsHelper.Common.Helpers;
internal class RegistryHelper : INotifyPropertyChanged
{
    #region Constructor
    public static RegistryHelper Default { get; } = new();


    readonly RegistryKey Root = LocalMachine.CreateSubKey($@"Software\Xylia\bns-plugins", true);
    #endregion

    #region Properties
    public static RegistryKey LocalMachine => Registry.LocalMachine;

    public bool UseTestServer => Root.GetValue("UseTestServer").To<int>() == 1;

    public bool UseDisplayMode
    {
        get => Root.GetValue("UseDisplayMode").To<int>() == 1;
        set
        {
            Root.SetValue("UseDisplayMode", value ? 1 : 0);
            OnPropertyChanged();
        }
    }
    #endregion

    #region Interface
    public event PropertyChangedEventHandler? PropertyChanged;

    private void OnPropertyChanged([CallerMemberName] string? name = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }
    #endregion
}
