﻿using System.Diagnostics;
using System.IO;
using System.Management;

namespace Xylia.BnsHelper.Common.Extensions;
public static class ProcessExtensions
{
	public static IEnumerable<Process> Find(string? path)
	{
		if (string.IsNullOrWhiteSpace(path)) return [];

		var processes = Process.GetProcessesByName(Path.GetFileNameWithoutExtension(path));
		return processes.Where(x => path.Equals(x.ProcessSearcher("ExecutablePath"), StringComparison.Ordinal));
	}

	public static IEnumerable<string> GetCommandLineArgs(string name)
	{
		return Process.GetProcessesByName(name).Select(x => x.ProcessSearcher("CommandLine")).Where(x => x != null)!;
	}

	private static string? ProcessSearcher(this Process process, string argument)
	{
		ArgumentNullException.ThrowIfNull(process);

		try
		{
			using var searcher = new ManagementObjectSearcher($"SELECT {argument} FROM Win32_Process WHERE ProcessId = " + process.Id);
			foreach (var item in searcher.Get()) return item[argument].ToString()!;

			return null;
		}
		catch
		{
			return null;
		}
	}
}

//143662D20
