using Xylia.BnsHelper.Models.Triggers;
using Xylia.BnsHelper.ViewModels.Dialogs;

namespace Xylia.BnsHelper.Views.Dialogs;
public partial class TriggerEditorDialog
{
    #region Constructor
    private readonly TriggerEditorViewModel _viewModel;

    public TriggerEditorDialog(Trigger? trigger)
    {
        InitializeComponent();

        DataContext = _viewModel = new TriggerEditorViewModel();
        _viewModel.SetTrigger(trigger);
        _viewModel.CloseRequested += OnCloseRequested;
    }
    #endregion

    #region Methods
    public Trigger Trigger => _viewModel.Trigger ?? throw new NotSupportedException();

    private void OnCloseRequested(object? sender, EventArgs e)
    {
        DialogResult = _viewModel.DialogResult;
        Close();
    }

    protected override void OnClosed(EventArgs e)
    {
        _viewModel.CloseRequested -= OnCloseRequested;
        base.OnClosed(e);
    }
    #endregion
}
