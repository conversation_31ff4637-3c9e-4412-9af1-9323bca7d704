package model

import (
	"encoding/json"
	"time"
)

// ==================== 基本模型 ==================== //

// 活动模型
type Activity struct {
	ActivityId   uint64    `gorm:"column:activity_id;primaryKey" json:"activity_id"`                // 活动ID（主键，ulong）
	ActivityName string    `gorm:"column:activity_name;size:200;not null" json:"activity_name"`     // 活动名称
	Status       byte      `gorm:"column:status;type:tinyint;not null;default:0" json:"status"`     // 状态：0-草稿，1-活跃，2-已结束
	Priority     byte      `gorm:"column:priority;type:tinyint;not null;default:0" json:"priority"` // 优先级
	Version      uint16    `gorm:"column:version;type:smallint;not null;default:1" json:"version"`  // 活动版本号
	BeginTime    time.Time `gorm:"column:begin_time;not null" json:"begin_time"`                    // 开始时间
	EndTime      time.Time `gorm:"column:end_time;not null" json:"end_time"`                        // 结束时间
	CreatedAt    time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`              // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`              // 更新时间

	// 关联的流程
	Flows []ActivityFlow `json:"flows"`
}

// 设置表名
func (Activity) TableName() string {
	return "bns_activity"
}

// 活动流程模型
type ActivityFlow struct {
	ID          uint64         `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	ActivityID  uint64         `gorm:"column:activity_id;not null;index" json:"activity_id"`              // 关联的活动ID
	FlowId      uint64         `gorm:"column:flow_id;not null" json:"flow_id"`                            // 流程ID
	FlowName    string         `gorm:"column:flow_name;size:200;not null" json:"flow_name"`               // 流程名称
	Group       uint32         `gorm:"column:group" json:"group"`                                         // 流程组，存在组的流程都需要手动领取
	GroupInfo   *ActivityGroup `gorm:"foreignKey:Group;references:Group" json:"group_info,omitempty"`     // 分组信息
	IdeToken    string         `gorm:"column:ide_token;size:100;not null" json:"ide_token"`               // IDE令牌
	AccountType byte           `gorm:"column:account_type;size:2;not null;default:7" json:"account_type"` // 账户类型
	FlowType    byte           `gorm:"column:flow_type;size:2;not null;default:1" json:"flow_type"`       // 流程类型
	Custom      byte           `gorm:"column:custom;size:10;not null;default:1" json:"custom"`            // 是否自定义
	Parameters  string         `gorm:"column:parameters;type:text" json:"parameters"`                     // 参数配置（JSON格式）
	Status      byte           `gorm:"column:status;type:tinyint;not null;default:1" json:"status"`       // 状态：0-禁用，1-启用
	SortOrder   int            `gorm:"column:sort_order;not null;default:0" json:"sort_order"`            // 排序
	CreatedAt   time.Time      `gorm:"column:created_at;autoCreateTime" json:"created_at"`                // 创建时间
	UpdatedAt   time.Time      `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`                // 更新时间
}

// 设置表名
func (ActivityFlow) TableName() string {
	return "bns_activity_flow"
}

// 活动分组模型
type ActivityGroup struct {
	Group uint32 `gorm:"primaryKey;column:group" json:"group"` // 分组ID（主键）
	Text  string `gorm:"column:text" json:"text"`              // 分组说明
}

// 设置表名
func (ActivityGroup) TableName() string {
	return "bns_activity_group"
}

// 活动流程参数结构
type ActivityFlowParameter struct {
	Key   string // 参数键
	Name  string // 参数描述
	Value string // 设定的参数值
}

// 解析Parameters字符串为结构化数据
func (af *ActivityFlow) GetParameters() []ActivityFlowParameter {
	// 尝试解析为参数对象格式
	var paramMap map[string]interface{}
	err := json.Unmarshal([]byte(af.Parameters), &paramMap)
	if err != nil {
		return []ActivityFlowParameter{}
	}

	var params []ActivityFlowParameter
	for key, value := range paramMap {
		param := ActivityFlowParameter{
			Key:  key,
			Name: key,
		}

		// 尝试解析详细信息
		if paramObj, ok := value.(map[string]interface{}); ok {
			if name, exists := paramObj["name"]; exists {
				if nameStr, ok := name.(string); ok {
					param.Name = nameStr
				}
			}
			if value, exists := paramObj["value"]; exists {
				if valueStr, ok := value.(string); ok {
					param.Value = valueStr
				}
			}
		} else {
			// 如果值是简单类型，直接作为值
			if valueStr, ok := value.(string); ok {
				param.Value = valueStr
			}
		}

		params = append(params, param)
	}

	return params
}
