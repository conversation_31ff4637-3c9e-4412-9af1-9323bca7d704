<Window x:Class="Xylia.BnsHelper.Views.AgreementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:hc="https://handyorg.github.io/handycontrol"
        mc:Ignorable="d"
        Title="用户协议"
        Width="440"
        WindowStartupLocation="CenterScreen"
        SizeToContent="Height"
        ResizeMode="NoResize"
        WindowStyle="SingleBorderWindow"
        ShowInTaskbar="True"
        Topmost="True">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 协议内容 -->
        <TextBlock Grid.Row="1" Text="{DynamicResource Application_Agreement}" TextWrapping="Wrap" FontSize="13" LineHeight="15" />

        <!-- 按钮区域 -->
        <Grid Grid.Row="2" Margin="0,10,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 提示文本 -->
            <TextBlock Grid.Row="0" Text="是否同意上述用户协议？" FontSize="14" HorizontalAlignment="Center" Margin="0,0,0,10" />

            <!-- 按钮区域 -->
            <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center">
                <!-- 取消按钮 -->
                <Button Content="取消" Width="80" Height="32" Margin="0,0,10,0" Command="{Binding CancelCommand}"/>

                <!-- 确定按钮 -->
                <Button Content="{Binding ConfirmButtonText}" Width="80" Height="32" IsEnabled="{Binding IsConfirmButtonEnabled}" Style="{StaticResource ButtonPrimary}" Command="{Binding ConfirmCommand}"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
