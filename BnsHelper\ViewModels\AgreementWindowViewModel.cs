using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Windows.Threading;

namespace Xylia.BnsHelper.ViewModels;
internal partial class AgreementWindowViewModel : ObservableObject
{
    #region Fields
    private readonly DispatcherTimer _countdownTimer;
    private int _remainingSeconds;
    #endregion

    #region Properties
    [ObservableProperty] string _confirmButtonText = "确定";
    [ObservableProperty] bool _isConfirmButtonEnabled = false;
    [ObservableProperty] bool _result = false;

    /// <summary>
    /// 窗口关闭回调
    /// </summary>
    public Action? CloseAction { get; set; }
    #endregion

    #region Constructor
    public AgreementWindowViewModel()
    {
        // 初始化倒计时定时器
        _countdownTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(1)
        };
        _countdownTimer.Tick += OnCountdownTick;
    }

    ~AgreementWindowViewModel()
    {
        StopCountdown();
    }
    #endregion

    #region Methods
    /// <summary>
    /// 开始倒计时
    /// </summary>
    public void StartCountdown()
    {
        _remainingSeconds = 10;
        UpdateConfirmButtonText();
        _countdownTimer.Start();
    }

    /// <summary>
    /// 倒计时事件处理
    /// </summary>
    private void OnCountdownTick(object? sender, EventArgs e)
    {
        _remainingSeconds--;

        if (_remainingSeconds <= 0)
        {
            // 倒计时结束，启用确定按钮
            _countdownTimer.Stop();
            IsConfirmButtonEnabled = true;
            ConfirmButtonText = "确定";
        }
        else
        {
            // 更新按钮文本
            UpdateConfirmButtonText();
        }
    }

    /// <summary>
    /// 更新确定按钮文本
    /// </summary>
    private void UpdateConfirmButtonText()
    {
        ConfirmButtonText = $"确定 ({_remainingSeconds})";
    }

    /// <summary>
    /// 停止倒计时
    /// </summary>
    public void StopCountdown()
    {
        _countdownTimer?.Stop();
    }
    #endregion

    #region Commands
    [RelayCommand]
    private void Confirm()
    {
        if (!IsConfirmButtonEnabled) return;

        Result = true;
        StopCountdown();
        CloseAction?.Invoke();
    }

    [RelayCommand]
    private void Cancel()
    {
        Result = false;
        StopCountdown();
        CloseAction?.Invoke();
    }
    #endregion
}
