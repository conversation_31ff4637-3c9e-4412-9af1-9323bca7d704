package handler

import (
	"encoding/json"
	"net/http"
	"strconv"

	gatewayService "udp-server/server/internal/gateway/service"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"

	"github.com/gorilla/mux"
)

// AdminCDKeyHandler 管理后台CDKEY处理器
type AdminCDKeyHandler struct {
	cdkeyAdminService *gatewayService.CDKeyAdminService
	authService       *service.AuthService
}

// NewAdminCDKeyHandler 创建管理后台CDKEY处理器
func NewAdminCDKeyHandler(
	cdkeyAdminService *gatewayService.CDKeyAdminService,
	authService *service.AuthService,
) *AdminCDKeyHandler {
	return &AdminCDKeyHandler{
		cdkeyAdminService: cdkeyAdminService,
		authService:       authService,
	}
}

// 处理获取CDKEY列表请求
func (h *AdminCDKeyHandler) HandleGetCDKeys(w http.ResponseWriter, r *http.Request) {
	// 获取查询参数
	page := r.URL.Query().Get("page")
	pageSize := r.URL.Query().Get("page_size")
	cdkeyType := r.URL.Query().Get("type")
	status := r.URL.Query().Get("status")
	search := r.URL.Query().Get("search")

	// 设置默认值
	if page == "" {
		page = "1"
	}
	if pageSize == "" {
		pageSize = "20"
	}

	pageInt, err := strconv.Atoi(page)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的页码", nil)
		return
	}

	pageSizeInt, err := strconv.Atoi(pageSize)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的页面大小", nil)
		return
	}

	cdkeys, err := h.cdkeyAdminService.GetCDKeys(pageInt, pageSizeInt, cdkeyType, status, search)
	if err != nil {
		logger.Error("获取CDKEY列表失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取CDKEY列表失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "success", cdkeys)
}

// 处理获取CDKEY详情请求
func (h *AdminCDKeyHandler) HandleGetCDKey(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	cdkeyID := vars["id"]

	id, err := strconv.ParseUint(cdkeyID, 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的CDKEY ID", nil)
		return
	}

	cdkey, err := h.cdkeyAdminService.GetCDKeyByID(id)
	if err != nil {
		logger.Error("获取CDKEY详情失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取CDKEY详情失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取CDKEY详情成功", cdkey)
}

// 处理创建CDKEY请求
func (h *AdminCDKeyHandler) HandleCreateCDKeys(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	// 解析请求体
	var req struct {
		Count       int    `json:"count"`
		Type        int    `json:"type"`
		RewardType  int    `json:"reward_type"`
		RewardValue int    `json:"reward_value"`
		Description string `json:"description"`
		ValidDays   int    `json:"valid_days"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析创建CDKEY请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 验证必填字段
	if req.Count <= 0 || req.Count > 5000 {
		SendJSONResponse(w, http.StatusBadRequest, "CDKEY数量必须在1-5000之间", nil)
		return
	}

	// 创建CDKEY
	cdkeys, err := h.cdkeyAdminService.CreateCDKeys(
		req.Count,
		req.Type,
		req.RewardType,
		req.RewardValue,
		req.Description,
		req.ValidDays,
	)
	if err != nil {
		logger.Error("创建CDKEY失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "创建CDKEY失败", nil)
		return
	}

	logger.Info("CDKEY创建成功: 数量=%d, 类型=%d", req.Count, req.Type)
	SendJSONResponse(w, http.StatusOK, "CDKEY创建成功", map[string]interface{}{
		"cdkeys": cdkeys,
		"count":  len(cdkeys),
	})
}

// 处理绑定CDKEY到用户请求
func (h *AdminCDKeyHandler) HandleBindCDKeyToUser(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	// 解析请求体
	var req struct {
		CDKeyCode string `json:"cdkey_code"`
		UserUID   uint64 `json:"user_uid"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析绑定CDKEY请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 验证必填字段
	if req.CDKeyCode == "" || req.UserUID == 0 {
		SendJSONResponse(w, http.StatusBadRequest, "CDKEY代码和用户UID不能为空", nil)
		return
	}

	// 绑定CDKEY到用户
	err := h.cdkeyAdminService.BindCDKeyToUser(req.CDKeyCode, req.UserUID)
	if err != nil {
		logger.Error("绑定CDKEY到用户失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "绑定CDKEY失败", nil)
		return
	}

	logger.Info("CDKEY绑定成功: 代码=%s, 用户UID=%d", req.CDKeyCode, req.UserUID)
	SendJSONResponse(w, http.StatusOK, "CDKEY绑定成功", nil)
}

// 处理删除CDKEY请求
func (h *AdminCDKeyHandler) HandleDeleteCDKey(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	vars := mux.Vars(r)
	cdkeyID := vars["id"]

	id, err := strconv.ParseUint(cdkeyID, 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的CDKEY ID", nil)
		return
	}

	// 删除CDKEY
	err = h.cdkeyAdminService.DeleteCDKey(id)
	if err != nil {
		logger.Error("删除CDKEY失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "删除CDKEY失败", nil)
		return
	}

	logger.Info("CDKEY删除成功: ID=%d", id)
	SendJSONResponse(w, http.StatusOK, "CDKEY删除成功", nil)
}

// 处理获取CDKEY使用记录请求
func (h *AdminCDKeyHandler) HandleGetCDKeyUsage(w http.ResponseWriter, r *http.Request) {
	cdkey := r.URL.Query().Get("cdkey")
	if cdkey == "" {
		SendJSONResponse(w, http.StatusBadRequest, "缺少口令码参数", nil)
		return
	}

	// 获取使用记录
	result, err := h.cdkeyAdminService.GetCDKeyUsageRecords(cdkey)
	if err != nil {
		logger.Error("获取口令码使用记录失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取使用记录失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取使用记录成功", result)
}

// 处理获取CDKEY统计信息请求
func (h *AdminCDKeyHandler) HandleGetCDKeyStats(w http.ResponseWriter, r *http.Request) {
	stats, err := h.cdkeyAdminService.GetCDKeyStats()
	if err != nil {
		logger.Error("获取CDKEY统计信息失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取CDKEY统计信息失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取CDKEY统计信息成功", stats)
}

// 处理批量删除CDKEY请求
func (h *AdminCDKeyHandler) HandleBatchDeleteCDKeys(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	// 解析请求体
	var req struct {
		CDKeyIDs []uint64 `json:"cdkey_ids"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析批量删除CDKEY请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 验证必填字段
	if len(req.CDKeyIDs) == 0 {
		SendJSONResponse(w, http.StatusBadRequest, "CDKEY ID列表不能为空", nil)
		return
	}

	// 批量删除CDKEY
	deletedCount, err := h.cdkeyAdminService.BatchDeleteCDKeys(req.CDKeyIDs)
	if err != nil {
		logger.Error("批量删除CDKEY失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "批量删除CDKEY失败", nil)
		return
	}

	logger.Info("CDKEY批量删除成功: 删除数量=%d", deletedCount)
	SendJSONResponse(w, http.StatusOK, "CDKEY批量删除成功", map[string]interface{}{
		"deleted_count": deletedCount,
	})
}
