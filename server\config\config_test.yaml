database:
  driver: mysql
  host: 127.0.0.1
  port: 3306
  username: bns
  password: 5d2481c194
  dbname: bns
  charset: utf8mb4
  parseTime: true
  loc: Local
  maxIdleConns: 5          # 减少空闲连接数
  maxOpenConns: 20         # 减少最大连接数
  connMaxLifetime: 1800    # 连接生命周期30分钟
  connMaxIdleTime: 300     # 空闲连接超时5分钟
  timeout: 10s             # 连接超时
  readTimeout: 30s         # 读取超时
  writeTimeout: 30s        # 写入超时

server:
  host: 0.0.0.0
  port: 8082               # 测试服务器使用不同端口

# HTTP服务器配置（用于Webhook）
http:
  host: 0.0.0.0
  port: 8079               # 测试服务器HTTP端口

redis:
  host: localhost
  port: 6379
  password: ""
  db: 4        # 正式服务器使用的数据库ID
  test_db: 5   # 测试服务器使用的数据库ID

# 日志配置
log:
  level: DEBUG
  console: true
  file: true               # 测试服务器启用文件日志
  file_path: "logs/test_server.log"

# 心跳配置
heartbeat:
  client_interval: "25m"    # 客户端心跳间隔：25分钟
  server_timeout: "30m"     # 服务器超时时间：30分钟
  cleanup_interval: "5m"    # 清理间隔：5分钟

# 测试服配置
test_server:
  enabled: true            # 启用测试服模式，使用test_db数据库
