﻿using Microsoft.Web.WebView2.Wpf;
using System.Windows;
using Xylia.BnsHelper.Common.Helpers;

namespace Xylia.BnsHelper.Services;
/// <summary>
/// Provides initialization logic for registering application settings, tooltips, and configuration properties.
/// </summary>
/// <remarks>This service is responsible for setting up application-wide configurations, such as night mode
/// settings,  tooltip templates for specific types, and WebView2 user data folder properties. It is intended to be used
/// during the application's startup process to ensure all necessary components are properly initialized.</remarks>
internal class InitializeService : IService
{
	public void Register()
	{
		// skin
		SettingHelper.Default.NightMode = SettingHelper.Default.NightMode;

        // configuration
        if (Application.Current.Resources["EvergreenWebView2CreationProperties"] is CoreWebView2CreationProperties properties)
		{
			properties.UserDataFolder = SettingHelper.CacheFolder;
		}
	}
}
