<hc:Window x:Class="Xylia.BnsHelper.Views.AnnouncementWindow"
           xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
           xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
           xmlns:hc="https://handyorg.github.io/handycontrol"
           Title="{DynamicResource AnnouncementWindow_Title}"
           Height="410" Width="550"
           WindowStartupLocation="CenterOwner"
           ShowInTaskbar="False"
           ResizeMode="CanResize"
           WindowStyle="None"
           Background="Transparent">

    <Window.Resources>
        <!-- 窗口控制按钮样式 -->
        <Style x:Key="WindowControlButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{DynamicResource SecondaryRegionBrush}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="{DynamicResource BorderBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 关闭按钮特殊样式 -->
        <Style x:Key="CloseButtonStyle" TargetType="Button" BasedOn="{StaticResource WindowControlButtonStyle}">
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E81123"/>
                    <Setter Property="Foreground" Value="White"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#C50E1F"/>
                    <Setter Property="Foreground" Value="White"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 公告类型颜色转换器 -->
        <Style x:Key="AnnouncementTypeStyle" TargetType="Border">
            <Setter Property="CornerRadius" Value="3"/>
            <Setter Property="Padding" Value="6,2"/>
            <Setter Property="Margin" Value="0,0,8,0"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Type}" Value="Info">
                    <Setter Property="Background" Value="#E3F2FD"/>
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Warning">
                    <Setter Property="Background" Value="#FFF3E0"/>
                    <Setter Property="BorderBrush" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Update">
                    <Setter Property="Background" Value="#E8F5E8"/>
                    <Setter Property="BorderBrush" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding Type}" Value="Maintenance">
                    <Setter Property="Background" Value="#FFEBEE"/>
                    <Setter Property="BorderBrush" Value="#F44336"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <!-- 主窗口边框 -->
    <Border Background="{DynamicResource BackgroundBrush}" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" CornerRadius="8">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 自定义标题栏 -->
            <Grid Grid.Row="0">
                <!-- 标题栏背景 -->
                <Border Background="{DynamicResource RegionBrush}" CornerRadius="8,8,0,0"/>

                <!-- 标题栏内容 -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 可拖拽的标题区域 -->
                    <Border Grid.Column="0" Background="Transparent" Padding="16,12,0,0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- 标题和信息 -->
                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                <Path Data="{StaticResource InfoGeometry}" Fill="{DynamicResource PrimaryBrush}" Width="20" Height="20" Stretch="Uniform" Margin="0,0,8,0"/>
                                <TextBlock Text="{DynamicResource AnnouncementWindow_Title}" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
                                <TextBlock Text="{Binding StatusText}" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}" VerticalAlignment="Center" Margin="12,0,0,0"/>

                                <!-- 可点击的标记已读文本 -->
                                <Button Style="{StaticResource ButtonCustom}"
                                        Background="Transparent"
                                        Margin="8,0,0,0"
                                        Command="{Binding MarkAllAsReadCommand}"
                                        Visibility="{Binding HasUnread, Converter={StaticResource Boolean2VisibilityConverter}}"
                                        ToolTip="点击标记所有公告为已读">
                                    <TextBlock Text="(全部标记为已读)" FontSize="12" Foreground="{DynamicResource PrimaryBrush}" TextDecorations="Underline" VerticalAlignment="Center"/>
                                </Button>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- 窗口控制按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Top" HorizontalAlignment="Right">
                        <Button x:Name="RefreshButton" Style="{StaticResource WindowControlButtonStyle}" Width="46" Height="26" Command="{Binding RefreshCommand}" ToolTip="刷新">
                            <Path Data="{StaticResource RefreshGeometry}" Width="13" Height="13" Stretch="Uniform" Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}" />
                        </Button>
                        <Button x:Name="MaximizeRestoreButton" Style="{StaticResource WindowControlButtonStyle}" Width="46" Height="32" Click="MaximizeRestoreButton_Click" ToolTip="最大化">
                            <Path x:Name="MaximizeRestoreIcon" Data="{StaticResource WindowMaxGeometry}" Width="10" Height="10" Stretch="Uniform" Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}" />
                        </Button>
                        <Button x:Name="CloseButton" Style="{StaticResource CloseButtonStyle}" Width="46" Height="32" Click="CloseButton_Click" ToolTip="关闭">
                            <Path Data="{StaticResource CloseGeometry}" Width="10" Height="10" Stretch="Uniform" Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}" />
                        </Button>
                    </StackPanel>
                </Grid>
            </Grid>

            <!-- 公告列表 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="16">
                <ItemsControl ItemsSource="{Binding Announcements}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border Background="{DynamicResource RegionBrush}" 
                                CornerRadius="8" 
                                Margin="0,0,0,12" 
                                Padding="16"
                                BorderThickness="1"
                                BorderBrush="{DynamicResource BorderBrush}">
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsRead}" Value="False">
                                                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryBrush}"/>
                                                <Setter Property="BorderThickness" Value="2"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>

                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- 标题和类型 -->
                                    <Grid Grid.Row="0" Margin="0,0,0,8">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                                            <Border Style="{StaticResource AnnouncementTypeStyle}" BorderThickness="1">
                                                <TextBlock Text="{Binding TypeText}" 
                                                       FontSize="10" 
                                                       FontWeight="Bold"/>
                                            </Border>
                                            <TextBlock Text="{Binding Title}" 
                                                   FontSize="14" 
                                                   FontWeight="Bold" 
                                                   VerticalAlignment="Center"/>
                                            <Ellipse Width="8" Height="8" 
                                                 Fill="{DynamicResource DangerBrush}" 
                                                 Margin="8,0,0,0"
                                                 Visibility="{Binding IsRead, Converter={StaticResource Boolean2VisibilityReConverter}}"/>
                                        </StackPanel>

                                        <TextBlock Grid.Column="1" 
                                               Text="{Binding FormattedPublishTime}" 
                                               FontSize="11" 
                                               Foreground="{DynamicResource SecondaryTextBrush}" 
                                               VerticalAlignment="Center"/>
                                    </Grid>

                                    <!-- 分隔线 -->
                                    <Separator Grid.Row="1" Margin="0,0,0,8"/>

                                    <!-- 内容 -->
                                    <TextBlock Grid.Row="2" 
                                           Text="{Binding Content}" 
                                           TextWrapping="Wrap" 
                                           LineHeight="20" 
                                           Margin="0,0,0,12"/>

                                    <!-- 操作按钮 -->
                                    <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right">
                                        <Button Content="{DynamicResource Button_MarkAsRead}"
                                            Style="{StaticResource ButtonDefault}"
                                            FontSize="11"
                                            Padding="12,4"
                                            Command="{Binding DataContext.MarkAsReadCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                            CommandParameter="{Binding Id}"
                                            Visibility="{Binding IsRead, Converter={StaticResource Boolean2VisibilityReConverter}}"/>
                                    </StackPanel>
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </Grid>
    </Border>
</hc:Window>
