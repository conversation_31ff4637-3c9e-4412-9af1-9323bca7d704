package model

import (
	"time"
)

// User 用户模型
type User struct {
	UID       uint64 `gorm:"column:uid;primaryKey" json:"uid"`      // 站点账号
	Uin       uint64 `gorm:"column:uin" json:"uin"`                 // 用户绑定的qq号
	Name      string `gorm:"column:name;size:255" json:"name"`      // 用户名
	Status    int    `gorm:"column:status;default:0" json:"status"` // 用户状态
	Email     string `gorm:"column:email;size:255" json:"email"`    // 邮箱
	Password  string `gorm:"column:password;size:255" json:"-"`     // 密码
	Token     string `gorm:"column:token;size:255" json:"token"`    // 令牌
	TokenTime int64  `gorm:"column:token_time" json:"token_time"`   // Token有效期（Unix时间戳）
	LoginTime int64  `gorm:"column:login_time" json:"login_time"`   // 登录时间（Unix时间戳）
}

// UserLog 用户日志模型
type UserLog struct {
	ID    uint   `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UID   uint64 `gorm:"column:uid;index" json:"uid"`                // 用户ID
	Type  string `gorm:"column:type;size:50;index" json:"type"`      // 日志类型
	Extra string `gorm:"column:extra;size:255" json:"extra"`         // 额外信息
	Time  string `gorm:"column:time;size:50" json:"time"`            // 时间
	Admin int64  `gorm:"column:admin;index;default:-1" json:"admin"` // 操作者ID：-1用户操作，0系统操作，>0管理员ID
	IP    string `gorm:"column:ip;size:45" json:"ip"`                // 操作者IP地址
}

// TableName 指定表名
func (User) TableName() string {
	return "user"
}

// TableName 指定表名
func (UserLog) TableName() string {
	return "user_log"
}

// ==================== 认证通信结构体 ==================== //

// 登录请求消息结构体
type LoginRequest struct {
	QQNumber          int64  // QQ号
	DeviceFingerprint string // 设备指纹（客户端计算，服务端根据此生成设备码）
}

// 登录响应消息结构体
type LoginResponse struct {
	ErrorCode            uint32 // 错误码
	ErrorMsg             string // 错误消息（仅在ErrorCode != 0时包含）
	Token                string // Token
	UIN                  string // UIN
	Name                 string // 用户名
	Email                string // 邮箱
	Status               uint32 // 状态
	Beta                 bool   // Beta标志
	Permission           uint8  // 用户权限等级
	PermissionExpiration int64  // 用户权限过期时间
	LoginTime            uint64 // 登录时间
	TokenTime            uint64 // Token时间
	PluginVersion        string // 插件版本
	PluginUrl            string // 插件下载URL
}

// 心跳响应消息结构体
type HeartbeatResponse struct {
	ErrorCode           uint32
	ErrorMsg            string
	OnlineUserCount     uint32
	ForceUpdate         bool
	AnnouncementVersion uint16
}

// 获取Token有效期
func (u *User) GetTokenTime() time.Time {
	return time.Unix(u.TokenTime, 0)
}

// 设置Token有效期
func (u *User) SetTokenTime(t time.Time) {
	u.TokenTime = t.Unix()
}

// 获取登录时间
func (u *User) GetLoginTime() time.Time {
	return time.Unix(u.LoginTime, 0)
}

// 设置登录时间
func (u *User) SetLoginTime(t time.Time) {
	u.LoginTime = t.Unix()
}
