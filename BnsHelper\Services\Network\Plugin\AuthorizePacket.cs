﻿using System.Security.Cryptography;
using Xylia.BnsHelper.Common.Extensions;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Plugin;
/// <summary>
/// 发送授权信息包
/// </summary>
internal class AuthorizePacket : IPacket
{
    public DataArchiveWriter Create()
    {
        var time = DateTimeOffset.Now.ToUnixTimeSeconds();
        var secretKey = GenerateSecretKey("1", 1);
        var signature = time.ToString().MD5Sign(secretKey);

        using var writer = new DataArchiveWriter();
        writer.Write((short)0);
        writer.Write(time);
        writer.WriteString(signature, System.Text.Encoding.Unicode);
        return writer;
    }

    public void Read(DataArchive reader)
    {
        throw new NotImplementedException();
    }

    /// <summary>
    /// 生成加密密钥
    /// </summary>
    /// <param name="version">版本号</param>
    /// <param name="permission">权限级别</param>
    internal static string GenerateSecretKey(string version, int permission)
    {
        var baseKey = $"bnszs_signkey_{version}_{permission}";
        var keyBytes = SHA256.HashData(System.Text.Encoding.Unicode.GetBytes(baseKey));
        return Convert.ToBase64String(keyBytes);
    }
}
