package service

import (
	"udp-server/server/internal/database"
	"udp-server/server/internal/model"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// 用户管理后台服务
type UserAdminService struct {
	db    *gorm.DB
	cache cache.Cache
}

// 创建用户管理后台服务
func NewUserAdminService() *UserAdminService {
	return &UserAdminService{
		db: database.GetDB(),
	}
}

// 获取用户列表
func (s *UserAdminService) GetUsers(page, limit int, status, search string) ([]model.User, error) {
	// 构建查询
	query := s.db.Model(&model.User{})

	// 添加关键词搜索
	if search != "" {
		query = query.Where("uin LIKE ? OR name LIKE ? OR email LIKE ?", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// 分页查询
	var users []model.User
	offset := (page - 1) * limit
	if err := query.Offset(offset).Limit(limit).Order("uid DESC").Find(&users).Error; err != nil {
		logger.Error("获取用户列表失败: %v", err)
		return nil, err
	}

	return users, nil
}

// 获取特定用户信息
func (s *UserAdminService) GetUser(uin uint64) (*model.User, error) {
	var user model.User
	err := s.db.Where("uin = ?", uin).First(&user).Error
	if err != nil {
		logger.Error("获取用户信息失败: %v", err)
		return nil, err
	}

	return &user, nil
}

// 踢出用户
func (s *UserAdminService) KickUser(uin uint64, reason string) error {
	// TODO: 实现踢出用户逻辑
	logger.Info("踢出用户: UID=%d, reason=%s", uin, reason)
	return nil
}

// 获取用户登录历史
func (s *UserAdminService) GetUserLoginHistory(uin uint64, page, limit int) ([]map[string]interface{}, int64, error) {
	// TODO: 实现获取用户登录历史
	logger.Info("获取用户登录历史: UID=%d, page=%d, limit=%d", uin, page, limit)
	return []map[string]interface{}{}, 0, nil
}
