using CommunityToolkit.Mvvm.ComponentModel;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Xylia.BnsHelper.Models.Api;

/// <summary>
/// 活动信息
/// </summary>
public class ActivityInfo : ObservableObject
{
    #region Fields
    [JsonProperty("iActivityId")]
    public ulong ActivityId { get; set; }

    [JsonProperty("sActivityName")]
    public string ActivityName { get; set; } = string.Empty;

    [JsonProperty("iActivityStatus")]
    public string ActivityStatus { get; set; } = string.Empty;

    [JsonProperty("sServiceType")]
    public string ServiceType { get; set; } = string.Empty;

    [JsonProperty("sServiceDepartment")]
    public string ServiceDepartment { get; set; } = string.Empty;

    [JsonProperty("sClientType")]
    public string ClientType { get; set; } = string.Empty;

    [JsonProperty("sAccountType")]
    public string AccountType { get; set; } = string.Empty;

    [JsonProperty("dtBeginTime")]
    public DateTime BeginTime { get; set; }

    [JsonProperty("dtEndTime")]
    public DateTime EndTime { get; set; }

    [JsonProperty("tOpenTime")]
    public string OpenTime { get; set; } = string.Empty;

    [JsonProperty("tCloseTime")]
    public string CloseTime { get; set; } = string.Empty;

    [JsonProperty("iTableNum")]
    public string TableNum { get; set; } = string.Empty;

    [JsonProperty("iShutdown")]
    public string Shutdown { get; set; } = string.Empty;

    [JsonProperty("sFrom")]
    public string From { get; set; } = string.Empty;

    [JsonProperty("sSDID")]
    public string SDID { get; set; } = string.Empty;

    [JsonProperty("sAMSTrusteeship")]
    public int AMSTrusteeship { get; set; }

    [JsonProperty("sAmePcUrl")]
    public string AmePcUrl { get; set; } = string.Empty;

    [JsonProperty("sAmeMobileUrl")]
    public string AmeMobileUrl { get; set; } = string.Empty;

    [JsonProperty("sServiceQQAppId")]
    public string ServiceQQAppId { get; set; } = string.Empty;

    [JsonProperty("version")]
    public ushort Version { get; set; }

    [JsonProperty("ide")]
    public ActivityIde? Ide { get; set; }

    [JsonProperty("flows")]
    public Dictionary<string, ActivityFlowDetail>? FlowDetails { get; set; }
    #endregion

    #region Properties
    [JsonIgnore]
    public bool IsActive => DateTime.Now >= BeginTime && DateTime.Now <= EndTime;

    [JsonIgnore]
    public Dictionary<ulong, ActivityFlow>? Flows => Ide?.Flows;

    /// <summary>
    /// 剩余时间描述
    /// </summary>
    [JsonIgnore]
    public string RemainingTime
    {
        get
        {
            var remaining = EndTime - DateTime.Now;
            if (remaining.TotalDays <= 0) return "已结束";
            if (remaining.TotalDays >= 365) return "长期有效";
            if (remaining.TotalDays >= 1) return $"剩余 {(int)Math.Ceiling(remaining.TotalDays)} 天";
            if (remaining.TotalHours >= 1) return $"剩余 {(int)Math.Ceiling(remaining.TotalHours)} 小时";
            
            return $"剩余 {(int)Math.Ceiling(remaining.TotalMinutes)} 分钟";
        }
    }

    /// <summary>
    /// 用户是否需要领取此活动
    /// </summary>
    [JsonIgnore]
    public bool ShouldExecute { get; set; } = false;
    #endregion
}

/// <summary>
/// 活动IDE配置
/// </summary>
public class ActivityIde
{
    [JsonProperty("iRet")]
    public int RetCode { get; set; }

    [JsonProperty("sMsg")]
    public string Message { get; set; } = string.Empty;

    [JsonProperty("sIdeUrl")]
    public string IdeUrl { get; set; } = string.Empty;

    [JsonProperty("tokens")]
    public Dictionary<string, string>? Tokens { get; set; }

    [JsonProperty("flows")]
    public Dictionary<ulong, ActivityFlow>? Flows { get; set; }

    [JsonProperty("default_tpls")]
    public List<DefaultTpl>? DefaultTpls { get; set; }

    [JsonProperty("iPaaSId")]
    public string PaaSId { get; set; } = string.Empty;

    [JsonProperty("token")]
    public string Token { get; set; } = string.Empty;
}

/// <summary>
/// 活动流程
/// </summary>
public partial class ActivityFlow : ObservableObject
{
    #region API Properties
    [JsonProperty("sIdeToken")]
    public string IdeToken { get; set; } = string.Empty;

    [JsonProperty("sAccountType")]
    public string AccountType { get; set; } = string.Empty;

    [JsonProperty("bNeedIBaseUin")]
    public string NeedIBaseUin { get; set; } = string.Empty;

    [JsonProperty("isLogin")]
    public string IsLogin { get; set; } = string.Empty;

    [JsonProperty("sName")]
    public string Name { get; set; } = string.Empty;

    [JsonProperty("iAreaChooseType")]
    public string AreaChooseType { get; set; } = string.Empty;

    [JsonProperty("sServiceType")]
    public string ServiceType { get; set; } = string.Empty;

    [JsonProperty("sIdeUrl")]
    public string IdeUrl { get; set; } = string.Empty;

    [JsonProperty("sTplType")]
    public string TplType { get; set; } = string.Empty;

    [JsonProperty("iType")]
    public string Type { get; set; } = string.Empty;

    [JsonProperty("iResId")]
    public string ResId { get; set; } = string.Empty;

    [JsonProperty("targetAppId")]
    public string TargetAppId { get; set; } = string.Empty;

    [JsonProperty("sAMSTrusteeship")]
    public string AMSTrusteeship { get; set; } = string.Empty;

    [JsonProperty("targetQQAppId")]
    public string TargetQQAppId { get; set; } = string.Empty;

    [JsonProperty("appName")]
    public string AppName { get; set; } = string.Empty;

    [JsonProperty("sGmiFlowId")]
    public string GmiFlowId { get; set; } = string.Empty;

    [JsonProperty("iCaptcha")]
    public string Captcha { get; set; } = string.Empty;

    [JsonProperty("inputParams")]
    public List<InputParam>? InputParams { get; set; }

    [JsonProperty("iCustom")]
    public string Custom { get; set; } = string.Empty;

    [JsonProperty("sAreaService")]
    public string AreaService { get; set; } = string.Empty;
    #endregion

    #region UDP Properties
    /// <summary>
    /// 分组ID（UDP通信使用）
    /// </summary>
    [JsonIgnore] public uint Group { get; set; }

    /// 流程类型（UDP通信使用）
    /// </summary>
    [JsonIgnore] public byte FlowType { get; set; }

    /// <summary>
    /// 排序顺序（UDP通信使用）
    /// </summary>
    [JsonIgnore] public int SortOrder { get; set; }
    #endregion

    #region Methods
    /// <summary>
    /// 是否需要用户输入参数
    /// </summary>
    [JsonIgnore]
    public bool NeedsUserInput => InputParams != null && InputParams.Any(x => !string.IsNullOrEmpty(x.Key));

    /// <summary>
    /// 执行结果奖励
    /// </summary>
    [ObservableProperty][JsonIgnore] ActivityFlowResponse? _reward;

    /// <summary>
    /// 构建基础请求参数
    /// </summary>
    /// <param name="flowId">流程ID</param>
    /// <returns>基础参数字典</returns>
    public Dictionary<string, string> BuildBaseParameters(ulong flowId)
    {
        return new Dictionary<string, string>
        {
            ["iChartId"] = flowId.ToString(),
            ["iSubChartId"] = flowId.ToString(),
            ["sIdeToken"] = IdeToken,
        };
    }

    /// <summary>
    /// 构建包含用户输入的完整参数
    /// </summary>
    /// <param name="flowId">流程ID</param>
    /// <param name="userInputs">用户输入的参数</param>
    /// <returns>完整参数字典</returns>
    //public Dictionary<string, string> BuildParametersWithUserInput(long flowId, IEnumerable<UserParameter> inputs)
    //{
    //    var parameters = BuildBaseParameters(flowId);

    //    foreach (var input in inputs)
    //    {
    //        if (string.IsNullOrWhiteSpace(input.Value)) continue;

    //        parameters[input.Key] = input.Value;
    //    }

    //    return parameters;
    //}
    #endregion
}

/// <summary>
/// 活动流程详情
/// </summary>
public class ActivityFlowDetail
{
    [JsonProperty("sFlowName")]
    public string FlowName { get; set; } = string.Empty;

    [JsonProperty("iNeedLogin")]
    public string NeedLogin { get; set; } = string.Empty;

    [JsonProperty("sFlowAccountType")]
    public string FlowAccountType { get; set; } = string.Empty;

    [JsonProperty("iAreaCheck")]
    public string AreaCheck { get; set; } = string.Empty;

    [JsonProperty("iNeedAreaRole")]
    public string NeedAreaRole { get; set; } = string.Empty;

    [JsonProperty("iNeedAreaRoleService")]
    public string NeedAreaRoleService { get; set; } = string.Empty;

    [JsonProperty("sAMSTrusteeship")]
    public int AMSTrusteeship { get; set; }

    [JsonProperty("sServiceType")]
    public string ServiceType { get; set; } = string.Empty;

    [JsonProperty("sAccountType")]
    public string AccountType { get; set; } = string.Empty;

    [JsonProperty("targetAppId")]
    public string TargetAppId { get; set; } = string.Empty;

    [JsonProperty("targetQQAppId")]
    public string TargetQQAppId { get; set; } = string.Empty;

    [JsonProperty("appName")]
    public object? AppName { get; set; }

    [JsonProperty("iCap")]
    public string Cap { get; set; } = string.Empty;

    [JsonProperty("sEditType")]
    public string EditType { get; set; } = string.Empty;

    [JsonProperty("mapid")]
    public int MapId { get; set; }

    [JsonProperty("functions")]
    public List<FlowFunction>? Functions { get; set; }

    /// <summary>
    /// 流程功能
    /// </summary>
    public class FlowFunction
    {
        public string? method { get; set; }
        public object? sExtModuleId { get; set; }
    }
}

/// <summary>
/// 输入参数
/// </summary>
public class InputParam
{
    [JsonProperty("key")] public string? Key;
    [JsonProperty("desc")] public string? Desc;
    [JsonProperty("value")] public string? Value;
}

/// <summary>
/// 默认模板配置
/// </summary>
public class DefaultTpl
{
    [JsonProperty("tpl")]
    public string Tpl { get; set; } = string.Empty;

    [JsonProperty("tpl_name")]
    public string TplName { get; set; } = string.Empty;

    [JsonProperty("query_map_id")]
    public string QueryMapId { get; set; } = string.Empty;

    [JsonProperty("query_map_token")]
    public string QueryMapToken { get; set; } = string.Empty;

    [JsonProperty("bind_map_id")]
    public string BindMapId { get; set; } = string.Empty;

    [JsonProperty("bind_map_token")]
    public string BindMapToken { get; set; } = string.Empty;

    [JsonProperty("sServiceType")]
    public string ServiceType { get; set; } = string.Empty;
}

/// <summary>
/// 活动流程响应
/// </summary>
public partial class ActivityFlowResponse : ObservableObject
{
    #region Fields
    [JsonProperty("ret")] public int Ret;
    [JsonProperty("iRet")] public int IRet;
    [JsonProperty("sMsg")] public string sMsg;
    [JsonProperty("sAmsSerial")] public string AmsSerial;
    [JsonProperty("jData")] public JToken jData;
    #endregion

    #region Properties
    public DateTime ClaimTime = DateTime.Now;

    public bool IsSuccess => Ret == 0 && IRet == 0;

    public string Message => sMsg;
    #endregion
}

/// <summary>
/// 角色查询结果
/// </summary>
public class QueryRoleResult
{
    public string CheckParam { get; set; } = string.Empty;
    public string Md5Str { get; set; } = string.Empty;
    public List<Creature> Roles { get; set; } = [];
}