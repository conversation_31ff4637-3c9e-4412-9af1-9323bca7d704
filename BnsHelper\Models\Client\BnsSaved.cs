﻿using System.Diagnostics;
using System.IO;
using System.Text;
using System.Xml;
using System.Xml.Serialization;
using Xylia.BnsHelper.Common.Helpers;

namespace Xylia.BnsHelper.Models;
[XmlRoot("config")]
public class BnsSaved
{
    [XmlElement("user")] public Creature[]? user;
    public IEnumerable<Creature>? User => user?
        .Where(o => BnsWorld.GetPublisher(o.world) == SettingHelper.Default.Publisher)
        .OrderByDescending(o => o.level).ThenByDescending(o => o.MasteryLevel);


    private static FileSystemWatcher? watcher;

    internal static BnsSaved? Get(Action OnChanged)
    {
        void OnFileChanged(object sender, FileSystemEventArgs e)
        {
            Debug.WriteLine($"{e.ChangeType}: {e.FullPath}");
            OnChanged?.Invoke();
        }

        var path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "BnS");
        watcher = new FileSystemWatcher(path, "User.xml");
        watcher.Changed += OnFileChanged;
        watcher.Created += OnFileChanged;
        watcher.Deleted += OnFileChanged;
        watcher.EnableRaisingEvents = true;

        return LoadData(Path.Combine(path, "User.xml"));
    }

    private static BnsSaved? LoadData(string path)
    {
        if (!File.Exists(path)) return null;

        using var fs = File.Open(path, FileMode.Open, FileAccess.ReadWrite);
        using var sr = new StreamReader(fs, Encoding.UTF8);

        var serializer = new XmlSerializer(typeof(BnsSaved));
        var result = serializer.Deserialize(sr) as BnsSaved;
        return result;
    }

    public static void Delete(int world, string name)
    {
        var path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "BnS", "User.xml");
        if (!File.Exists(path)) return;

        // 加载XML文档
        var doc = new XmlDocument();
        doc.Load(path);

        // 使用XPath查找要删除的用户节点
        var userNode = doc.SelectSingleNode($"//user[@world='{world}' and @name='{name}']");
        if (userNode == null)
        {
            Debug.WriteLine($"[BnsSaved] 未找到要删除的角色: world={world}, name={name}");
            return;
        }

        // 删除找到的节点
        userNode.ParentNode?.RemoveChild(userNode);

        // 保存修改后的XML文档
        doc.Save(path);
        Debug.WriteLine($"[BnsSaved] 成功删除角色: world={world}, name={name}");
    }
}

public class Equipment
{
    #region Fields
    [XmlAttribute("id")] public int Id;
    [XmlAttribute("name")] public string? Name;
    #endregion

    #region Methods
    public override string? ToString() => Name;
    #endregion
}
