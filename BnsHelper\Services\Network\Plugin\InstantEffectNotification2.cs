﻿using Newtonsoft.Json;
using System.Diagnostics;
using Xylia.BnsHelper.Common.Converters;
using Xylia.Preview.Data.Engine;
using static Xylia.Preview.Data.Models.BattleMessage;

namespace Xylia.BnsHelper.Services.Network.Plugin;

[JsonConverter(typeof(InstantEffectNotification2Converter))]
internal class InstantEffectNotification2 : IPacket
{
    #region Fields
    public DateTime Time;

    public ObjectTypeSeq ObjectType;
    public SkillResultTypeSeq SkillResultType;
    public EffectTypeSeq EffectType;
    private string? Message;

    public long CasterId;
    public string? CasterName;
    public long TargetId;
    public string? TargetName;
    public string? SkillName;
    public string? EffectAlias;
    public string? EffectName;
    public long Value;
    public long Value2;
    public long Value3;
    public long Value4;
    #endregion

    #region Properties
    /// <summary>
    /// 名称文本
    /// </summary>
    public string Name
    {
        get
        {
            if (!string.IsNullOrEmpty(SkillName)) return SkillName;
            if (!string.IsNullOrEmpty(EffectName)) return EffectName;

            // 处理无名效果
            if (EffectAlias != null)
            {
                if (EffectAlias.StartsWith("CardCollection_New_Purple_")) return "汲取";
                if (EffectAlias.StartsWith("CardCollection_New_Red_")) return "会心一击";
            }

            return "其他附加效果";
        }
    }

    /// <summary>
    /// 是否为召唤物技能
    /// </summary>
    public bool Summoned { get; set; }
    #endregion

    #region Methods
    public DataArchiveWriter Create() => new();

    public void Read(DataArchive reader)
    {
        Time = DateTime.Now;
        Message = reader.ReadString();
        CasterId = reader.Read<long>();
        CasterName = reader.ReadString();
        TargetId = reader.Read<long>();
        TargetName = reader.ReadString();
        SkillName = reader.ReadString();
        EffectAlias = reader.ReadString();
        EffectName = reader.ReadString();
        Value = reader.Read<long>();
        Value2 = reader.Read<long>();
        Value3 = reader.Read<long>();
        Value4 = reader.Read<long>();

        // 因为目前解析问题，只能获取到消息再转换回主键
        if (!string.IsNullOrEmpty(Message) && Mappings.TryGetValue(Message, out var record))
        {
            ObjectType = record.ObjectType;
            SkillResultType = record.SkillResultType;
            EffectType = record.EffectType;
        }

        Debug.WriteLine($"{CasterName} {CasterId} {TargetName} {ObjectType} {SkillResultType} {EffectType}     {SkillName} {EffectName}  {Value} {Value2} {Value3} {Value4}");
    }

    static readonly Dictionary<string, (ObjectTypeSeq ObjectType, SkillResultTypeSeq SkillResultType, EffectTypeSeq EffectType)> Mappings = new(StringComparer.OrdinalIgnoreCase)
    {
        // Player Attack - Hit
        ["Msg.Battle.Attack.Hit.HP.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantDamage),
        ["Msg.Battle.Attack.Hit.Attach.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.AttachDamage),
        ["Msg.Battle.Attack.Hit.Interval.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.IntervalDamage),
        ["Msg.Battle.Attack.Hit.Attach.Fail.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.AttachFailDamage),
        ["Msg.Battle.Attack.Hit.Detech.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.DetachDamage),
        ["Msg.Battle.Attack.Hit.HP.Heal"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantHp),
        ["Msg.Battle.Attack.Hit.HP.Heal.Interval"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.IntervalHp),
        ["Msg.Battle.Attack.Hit.SP.Heal"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantSp),
        ["Msg.Battle.Attack.Hit.SP.Heal.Interval"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.IntervalSp),
        ["Msg.Battle.Attack.Hit.SP.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantSpDamage),
        ["Msg.Battle.Attack.Hit.SP.Interval"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.IntervalSpDamage),
        ["Msg.Battle.Attack.HitHP.Drain"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantHpDrain),
        ["Msg.Battle.Attack.Hit.SP.Drain"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantSpDrain),
        ["Msg.Battle.Attack.HitHP.SP.Drain"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantHpSpDrain),
        ["Msg.Battle.Attack.Hit.Attach"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.Attach),
        ["Msg.Battle.Attack.Hit.Attach.Fail"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.AttachFail),
        ["Msg.Battle.Attack.Hit.Detech"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.Detach),
        ["Msg.Battle.Attack.Hit.Exhaustion"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.Exhaustion),
        ["Msg.Battle.Attack.Hit.Dead"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Hit, EffectTypeSeq.Dead),

        // Player Attack - Critical
        ["Msg.Battle.Attack.Cri.HP.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Cri, EffectTypeSeq.InstantDamage),
        ["Msg.Battle.Attack.Cri.Attach.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Cri, EffectTypeSeq.AttachDamage),
        ["Msg.Battle.Attack.Cri.Attach.Fail.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Cri, EffectTypeSeq.AttachFailDamage),
        ["Msg.Battle.Attack.Cri.Detech.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Cri, EffectTypeSeq.DetachDamage),
        ["Msg.Battle.Attack.Cri.SP.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Cri, EffectTypeSeq.InstantSpDamage),
        ["Msg.Battle.Attack.Cri.SP.Drain"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Cri, EffectTypeSeq.InstantSpDrain),
        ["Msg.Battle.Attack.CriHP.Drain"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Cri, EffectTypeSeq.InstantHpDrain),
        ["Msg.Battle.Attack.CriHP.SP.Drain"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Cri, EffectTypeSeq.InstantHpSpDrain),
        ["Msg.Battle.Attack.Cri.Attach"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Cri, EffectTypeSeq.Attach),
        ["Msg.Battle.Attack.Cri.Attach.Fail"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Cri, EffectTypeSeq.AttachFail),
        ["Msg.Battle.Attack.Cri.Detech"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Cri, EffectTypeSeq.Detach),
        ["Msg.Battle.Attack.Cri.Exhaustion"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Cri, EffectTypeSeq.Exhaustion),
        ["Msg.Battle.Attack.Cri.Dead"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Cri, EffectTypeSeq.Dead),

        // Player Attack - Parry
        ["Msg.Battle.Attack.Parry.HP.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Parry, EffectTypeSeq.InstantDamage),
        ["Msg.Battle.Attack.Parry.Attach.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Parry, EffectTypeSeq.AttachDamage),
        ["Msg.Battle.Attack.Parry.Attach.Fail.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Parry, EffectTypeSeq.AttachFailDamage),
        ["Msg.Battle.Attack.Parry.Detech.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Parry, EffectTypeSeq.DetachDamage),
        ["Msg.Battle.Attack.Parry.SP.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Parry, EffectTypeSeq.InstantSpDamage),
        ["Msg.Battle.Attack.ParryHP.Drain"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Parry, EffectTypeSeq.InstantHpDrain),
        ["Msg.Battle.Attack.Parry.SP.Drain"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Parry, EffectTypeSeq.InstantSpDrain),
        ["Msg.Battle.Attack.ParryHP.SP.Drain"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Parry, EffectTypeSeq.InstantHpSpDrain),
        ["Msg.Battle.Attack.Parry.Attach"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Parry, EffectTypeSeq.Attach),
        ["Msg.Battle.Attack.Parry.Attach.Fail"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Parry, EffectTypeSeq.AttachFail),
        ["Msg.Battle.Attack.Parry.Detech"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Parry, EffectTypeSeq.Detach),
        ["Msg.Battle.Attack.Parry.Exhaustion"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Parry, EffectTypeSeq.Exhaustion),
        ["Msg.Battle.Attack.Parry.Dead"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Parry, EffectTypeSeq.Dead),

        // Player Attack - Perfect Parry
        ["Msg.Battle.Attack.PParry.HP.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Pparry, EffectTypeSeq.InstantDamage),
        ["Msg.Battle.Attack.PParry.Attach.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Pparry, EffectTypeSeq.AttachDamage),
        ["Msg.Battle.Attack.PParry.Attach.Fail.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Pparry, EffectTypeSeq.AttachFailDamage),
        ["Msg.Battle.Attack.PParry.Detech.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Pparry, EffectTypeSeq.DetachDamage),
        ["Msg.Battle.Attack.PParry.SP.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Pparry, EffectTypeSeq.InstantSpDamage),
        ["Msg.Battle.Attack.PParryHP.Drain"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Pparry, EffectTypeSeq.InstantHpDrain),
        ["Msg.Battle.Attack.PParry.SP.Drain"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Pparry, EffectTypeSeq.InstantSpDrain),
        ["Msg.Battle.Attack.PParryHP.SP.Drain"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Pparry, EffectTypeSeq.InstantHpSpDrain),
        ["Msg.Battle.Attack.PParry.Attach"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Pparry, EffectTypeSeq.Attach),
        ["Msg.Battle.Attack.PParry.Attach.Fail"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Pparry, EffectTypeSeq.AttachFail),
        ["Msg.Battle.Attack.PParry.Detech"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Pparry, EffectTypeSeq.Detach),
        ["Msg.Battle.Attack.PParry.Exhaustion"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Pparry, EffectTypeSeq.Exhaustion),
        ["Msg.Battle.Attack.PParry.Dead"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Pparry, EffectTypeSeq.Dead),

        // Player Attack - Counter
        ["Msg.Battle.Attack.Counter.HP.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Counter, EffectTypeSeq.InstantDamage),
        ["Msg.Battle.Attack.Counter.Attach.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Counter, EffectTypeSeq.AttachDamage),
        ["Msg.Battle.Attack.Counter.Attach.Fail.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Counter, EffectTypeSeq.AttachFailDamage),
        ["Msg.Battle.Attack.Counter.Detech.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Counter, EffectTypeSeq.DetachDamage),
        ["Msg.Battle.Attack.Counter.SP.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Counter, EffectTypeSeq.InstantSpDamage),
        ["Msg.Battle.Attack.CounterHP.Drain"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Counter, EffectTypeSeq.InstantHpDrain),
        ["Msg.Battle.Attack.Counter.SP.Drain"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Counter, EffectTypeSeq.InstantSpDrain),
        ["Msg.Battle.Attack.CounterHP.SP.Drain"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Counter, EffectTypeSeq.InstantHpSpDrain),
        ["Msg.Battle.Attack.Counter.Attach"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Counter, EffectTypeSeq.Attach),
        ["Msg.Battle.Attack.Counter.Attach.Fail"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Counter, EffectTypeSeq.AttachFail),
        ["Msg.Battle.Attack.Counter.Detech"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Counter, EffectTypeSeq.Detach),
        ["Msg.Battle.Attack.Counter.Exhaustion"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Counter, EffectTypeSeq.Exhaustion),
        ["Msg.Battle.Attack.Counter.Dead"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Counter, EffectTypeSeq.Dead),

        // Player Attack - Effect (None Skill)
        ["Msg.Battle.Attack.Effect.HP.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.InstantDamage),
        ["Msg.Battle.Attack.Effect.Interval.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.IntervalDamage),
        ["Msg.Battle.Attack.Effect.Detech.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.DetachDamage),
        ["Msg.Battle.Attack.Effect.HP.Heal"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.InstantHp),
        ["Msg.Battle.Effect.HP.Heal.Interval"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.IntervalHp),
        ["Msg.Battle.Attack.Effect.SP.Heal"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.InstantSp),
        ["Msg.Battle.Attack.Effect.SP.Heal.Interval"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.IntervalSp),
        ["Msg.Battle.Attack.Effect.SP.Damage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.InstantSpDamage),
        ["Msg.Battle.Attack.Effect.SP.Interval"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.IntervalSpDamage),
        ["Msg.Battle.Attack.Effect.Attach"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.Attach),
        ["Msg.Battle.Attack.Effect.Attach.Fail"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.AttachFail),
        ["Msg.Battle.Attack.Effect.Detech"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.Detach),
        ["Msg.Battle.Attack.Effect.WallDamage"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.WallDamage),
        ["Msg.Battle.Attack.Effect.Exhaustion"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.Exhaustion),
        ["Msg.Battle.Attack.Effect.Dead"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.Dead),

        // Player Attack - Dodge
        ["Msg.Battle.Attack.Dodge.Effect.None"] = (ObjectTypeSeq.PlayerAttack, SkillResultTypeSeq.Dodge, EffectTypeSeq.NoneEffect),

        // Player Attacked - Hit
        ["Msg.Battle.Hit.HP.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantDamage),
        ["Msg.Battle.Hit.Attach.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.AttachDamage),
        ["Msg.Battle.Hit.Interval.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.IntervalDamage),
        ["Msg.Battle.Hit.Attach.Fail.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.AttachFailDamage),
        ["Msg.Battle.Hit.Detech.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.DetachDamage),
        ["Msg.Battle.Hit.HP.Heal"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantHp),
        ["Msg.Battle.Hit.HP.Heal.Interval"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.IntervalHp),
        ["Msg.Battle.Hit.SP.Heal"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantSp),
        ["Msg.Battle.Hit.SP.Heal.Interval"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.IntervalSp),
        ["Msg.Battle.Hit.SP.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantSpDamage),
        ["Msg.Battle.Hit.SP.Interval"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.IntervalSpDamage),
        ["Msg.Battle.HitHP.Drain"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantHpDrain),
        ["Msg.Battle.Hit.SP.Drain"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantSpDrain),
        ["Msg.Battle.HitHP.SP.Drain"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantHpSpDrain),
        ["Msg.Battle.Hit.Attach"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.Attach),
        ["Msg.Battle.Hit.Attach.Fail"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.AttachFail),
        ["Msg.Battle.Hit.Detech"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.Detach),
        ["Msg.Battle.Hit.Exhaustion"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.Exhaustion),
        ["Msg.Battle.Hit.Dead"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Hit, EffectTypeSeq.Dead),

        // Player Attacked - Critical
        ["Msg.Battle.Cri.HP.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Cri, EffectTypeSeq.InstantDamage),
        ["Msg.Battle.Cri.Attach.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Cri, EffectTypeSeq.AttachDamage),
        ["Msg.Battle.Cri.Attach.Fail.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Cri, EffectTypeSeq.AttachFailDamage),
        ["Msg.Battle.Cri.Detech.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Cri, EffectTypeSeq.DetachDamage),
        ["Msg.Battle.Cri.SP.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Cri, EffectTypeSeq.InstantSpDamage),
        ["Msg.Battle.CriHP.Drain"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Cri, EffectTypeSeq.InstantHpDrain),
        ["Msg.Battle.CriHP.SP.Drain"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Cri, EffectTypeSeq.InstantHpSpDrain),
        ["Msg.Battle.Cri.Attach"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Cri, EffectTypeSeq.Attach),
        ["Msg.Battle.Cri.Attach.Fail"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Cri, EffectTypeSeq.AttachFail),
        ["Msg.Battle.Cri.Detech"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Cri, EffectTypeSeq.Detach),
        ["Msg.Battle.Cri.Exhaustion"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Cri, EffectTypeSeq.Exhaustion),
        ["Msg.Battle.Cri.Dead"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Cri, EffectTypeSeq.Dead),

        // Player Attacked - Parry
        ["Msg.Battle.Parry.HP.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Parry, EffectTypeSeq.InstantDamage),
        ["Msg.Battle.Parry.Attach.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Parry, EffectTypeSeq.AttachDamage),
        ["Msg.Battle.Parry.Attach.Fail.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Parry, EffectTypeSeq.AttachFailDamage),
        ["Msg.Battle.Parry.Detech.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Parry, EffectTypeSeq.DetachDamage),
        ["Msg.Battle.Parry.SP.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Parry, EffectTypeSeq.InstantSpDamage),
        ["Msg.Battle.ParryHP.Drain"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Parry, EffectTypeSeq.InstantHpDrain),
        ["Msg.Battle.Parry.SP.Drain"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Parry, EffectTypeSeq.InstantSpDrain),
        ["Msg.Battle.ParryHP.SP.Drain"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Parry, EffectTypeSeq.InstantHpSpDrain),
        ["Msg.Battle.Parry.Attach"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Parry, EffectTypeSeq.Attach),
        ["Msg.Battle.Parry.Attach.Fail"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Parry, EffectTypeSeq.AttachFail),
        ["Msg.Battle.Parry.Detech"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Parry, EffectTypeSeq.Detach),
        ["Msg.Battle.Parry.Exhaustion"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Parry, EffectTypeSeq.Exhaustion),
        ["Msg.Battle.Parry.Dead"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Parry, EffectTypeSeq.Dead),

        // Player Attacked - Perfect Parry
        ["Msg.Battle.PParry.HP.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Pparry, EffectTypeSeq.InstantDamage),
        ["Msg.Battle.PParry.Attach.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Pparry, EffectTypeSeq.AttachDamage),
        ["Msg.Battle.PParry.Attach.Fail.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Pparry, EffectTypeSeq.AttachFailDamage),
        ["Msg.Battle.PParry.Detech.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Pparry, EffectTypeSeq.DetachDamage),
        ["Msg.Battle.PParry.SP.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Pparry, EffectTypeSeq.InstantSpDamage),
        ["Msg.Battle.PParryHP.Drain"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Pparry, EffectTypeSeq.InstantHpDrain),
        ["Msg.Battle.PParry.SP.Drain"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Pparry, EffectTypeSeq.InstantSpDrain),
        ["Msg.Battle.PParryHP.SP.Drain"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Pparry, EffectTypeSeq.InstantHpSpDrain),
        ["Msg.Battle.PParry.Attach"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Pparry, EffectTypeSeq.Attach),
        ["Msg.Battle.PParry.Attach.Fail"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Pparry, EffectTypeSeq.AttachFail),
        ["Msg.Battle.PParry.Detech"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Pparry, EffectTypeSeq.Detach),
        ["Msg.Battle.PParry.Exhaustion"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Pparry, EffectTypeSeq.Exhaustion),
        ["Msg.Battle.PParry.Dead"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Pparry, EffectTypeSeq.Dead),

        // Player Attacked - Counter
        ["Msg.Battle.Counter.HP.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Counter, EffectTypeSeq.InstantDamage),
        ["Msg.Battle.Counter.Attach.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Counter, EffectTypeSeq.AttachDamage),
        ["Msg.Battle.Counter.Attach.Fail.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Counter, EffectTypeSeq.AttachFailDamage),
        ["Msg.Battle.Counter.Detech.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Counter, EffectTypeSeq.DetachDamage),
        ["Msg.Battle.Counter.SP.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Counter, EffectTypeSeq.InstantSpDamage),
        ["Msg.Battle.CounterHP.Drain"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Counter, EffectTypeSeq.InstantHpDrain),
        ["Msg.Battle.Counter.SP.Drain"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Counter, EffectTypeSeq.InstantSpDrain),
        ["Msg.Battle.CounterHP.SP.Drain"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Counter, EffectTypeSeq.InstantHpSpDrain),
        ["Msg.Battle.Counter.Attach"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Counter, EffectTypeSeq.Attach),
        ["Msg.Battle.Counter.Attach.Fail"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Counter, EffectTypeSeq.AttachFail),
        ["Msg.Battle.Counter.Detech"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Counter, EffectTypeSeq.Detach),
        ["Msg.Battle.Counter.Exhaustion"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Counter, EffectTypeSeq.Exhaustion),
        ["Msg.Battle.Counter.Dead"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Counter, EffectTypeSeq.Dead),

        // Player Attacked - Effect (None Skill)
        ["Msg.Battle.Effect.HP.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.InstantDamage),
        ["Msg.Battle.Effect.Interval.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.IntervalDamage),
        ["Msg.Battle.Effect.Detech.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.DetachDamage),
        ["Msg.Battle.Effect.HP.Heal"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.InstantHp),
        ["Msg.Battle.Effect.HP.Heal.Interval"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.IntervalHp),
        ["Msg.Battle.Effect.SP.Heal"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.InstantSp),
        ["Msg.Battle.Effect.SP.Heal.Interval"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.IntervalSp),
        ["Msg.Battle.Effect.SP.Damage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.InstantSpDamage),
        ["Msg.Battle.Effect.SP.Interval"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.IntervalSpDamage),
        ["Msg.Battle.Effect.Attach"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.Attach),
        ["Msg.Battle.Effect.Attach.Fail"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.AttachFail),
        ["Msg.Battle.Effect.Detech"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.Detach),
        ["Msg.Battle.Effect.WallDamage"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.WallDamage),
        ["Msg.Battle.Effect.Exhaustion"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.Exhaustion),
        ["Msg.Battle.Effect.Dead"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.Dead),

        // Player Attacked - Dodge
        // 重复的键名，暂时先不处理这个
        // ["Msg.Battle.Attack.Dodge.Effect.None"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Dodge, EffectTypeSeq.NoneEffect),

        // Other - Hit
        ["Msg.Battle.Other.Hit.HP.Damage"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantDamage),
        ["Msg.Battle.Other.Hit.Attach.Damage"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.AttachDamage),
        ["Msg.Battle.Other.Hit.Interval.Damage"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.IntervalDamage),
        ["Msg.Battle.Other.Hit.Attach.Fail.Damage"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.AttachFailDamage),
        ["Msg.Battle.Other.Hit.Detech.Damage"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.DetachDamage),
        ["Msg.Battle.Other.Hit.HP.Heal"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantHp),
        ["Msg.Battle.Other.Hit.HP.Heal.Interval"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.IntervalHp),
        ["Msg.Battle.Other.Hit.SP.Heal"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantSp),
        ["Msg.Battle.Other.Hit.SP.Heal.Interval"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.IntervalSp),
        ["Msg.Battle.Other.Hit.SP.Damage"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantSpDamage),
        ["Msg.Battle.Other.Hit.SP.Interval"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.IntervalSpDamage),
        ["Msg.Battle.Other.HitHP.Drain"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantHpDrain),
        ["Msg.Battle.Other.Hit.SP.Drain"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantSpDrain),
        ["Msg.Battle.Other.HitHP.SP.Drain"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.InstantHpSpDrain),
        ["Msg.Battle.Other.Hit.Attach"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.Attach),
        ["Msg.Battle.Other.Hit.Attach.Fail"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.AttachFail),
        ["Msg.Battle.Other.Hit.Detech"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.Detach),
        ["Msg.Battle.Other.Hit.Exhaustion"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.Exhaustion),
        ["Msg.Battle.Other.Hit.Dead"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Hit, EffectTypeSeq.Dead),

        // Other - Critical
        ["Msg.Battle.Other.Cri.HP.Damage"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Cri, EffectTypeSeq.InstantDamage),
        ["Msg.Battle.Other.Cri.Attach.Damage"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Cri, EffectTypeSeq.AttachDamage),
        ["Msg.Battle.Other.Cri.Attach.Fail.Damage"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Cri, EffectTypeSeq.AttachFailDamage),
        ["Msg.Battle.Other.Cri.Detech.Damage"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Cri, EffectTypeSeq.DetachDamage),
        ["Msg.Battle.Other.Cri.SP.Damage"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Cri, EffectTypeSeq.InstantSpDamage),
        ["Msg.Battle.Other.CriHP.Drain"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Cri, EffectTypeSeq.InstantHpDrain),
        ["Msg.Battle.Other.CriHP.SP.Drain"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Cri, EffectTypeSeq.InstantHpSpDrain),
        ["Msg.Battle.Other.Cri.Attach"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Cri, EffectTypeSeq.Attach),
        ["Msg.Battle.Other.Cri.Attach.Fail"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Cri, EffectTypeSeq.AttachFail),
        ["Msg.Battle.Other.Cri.Detech"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Cri, EffectTypeSeq.Detach),
        ["Msg.Battle.Other.Cri.Exhaustion"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Cri, EffectTypeSeq.Exhaustion),
        ["Msg.Battle.Other.Cri.Dead"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.Cri, EffectTypeSeq.Dead),

        // Other - Effect (None Skill) - 部分映射
        ["Msg.Battle.Other.Effect.HP.Damage"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.InstantDamage),
        ["Msg.Battle.Other.Effect.Interval.Damage"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.IntervalDamage),
        ["Msg.Battle.Other.Effect.Detech.Damage"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.DetachDamage),
        ["Msg.Battle.Other.Effect.HP.Heal"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.InstantHp),
        ["Msg.Battle.Other.Effect.Attach"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.Attach),
        ["Msg.Battle.Other.Effect.Detech"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.Detach),
        ["Msg.Battle.Other.Effect.Exhaustion"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.Exhaustion),
        ["Msg.Battle.Other.Effect.Dead"] = (ObjectTypeSeq.Other, SkillResultTypeSeq.NoneSkill, EffectTypeSeq.Dead),

        // 常见的抵抗、闪避、反制消息
        ["Msg.Battle.Miss.Effect.None"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Miss, EffectTypeSeq.NoneEffect),
        ["Msg.Battle.Dodge.Effect.None"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Dodge, EffectTypeSeq.NoneEffect),
        ["Msg.Battle.Bounce.Effect.None"] = (ObjectTypeSeq.PlayerAttacked, SkillResultTypeSeq.Bounce, EffectTypeSeq.NoneEffect),
    };
    #endregion
}
