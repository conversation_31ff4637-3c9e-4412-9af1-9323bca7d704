﻿using System.IO;
using System.Management;
using System.Net.NetworkInformation;
using System.Text;

namespace Xylia.BnsHelper.Common.Helpers;
internal static class DeviceHelper
{
    private static SystemInfo GatherSystemInfo()
    {
        // 使用更稳定的设备信息收集方法
        var info = new SystemInfo
        {
            CPU = GetCPUInfo(),
            Memory = GetMemoryInfo(),
            Motherboard = GetMotherboardInfo(),
            Disk = GetDiskInfo(),
            MACAddress = GetMACAddress()
        };

        // 最终验证 - 如果所有信息都是默认值，生成唯一标识符
        if (IsAllDefaultValues(info))
        {
            GenerateUniqueDeviceInfo(info);
        }

        return info;
    }

    private static string GetCPUInfo()
    {
        // 方法1: WMI查询
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT Name, ProcessorId FROM Win32_Processor");
            foreach (ManagementObject obj in searcher.Get())
            {
                var name = obj["Name"]?.ToString();
                var processorId = obj["ProcessorId"]?.ToString();
                if (!string.IsNullOrEmpty(name))
                {
                    // 如果有ProcessorId，添加到名称中增加唯一性
                    return !string.IsNullOrEmpty(processorId) ? $"{name}-{processorId[..8]}" : name;
                }
            }
        }
        catch { }

        // 方法2: 环境变量
        try
        {
            var processorIdentifier = Environment.GetEnvironmentVariable("PROCESSOR_IDENTIFIER");
            var processorArchitecture = Environment.GetEnvironmentVariable("PROCESSOR_ARCHITECTURE");
            if (!string.IsNullOrEmpty(processorIdentifier))
            {
                return $"{processorIdentifier}-{processorArchitecture}";
            }
        }
        catch { }

        // 方法3: Registry查询
        try
        {
            using var key = Microsoft.Win32.Registry.LocalMachine.OpenSubKey(@"HARDWARE\DESCRIPTION\System\CentralProcessor\0");
            var processorName = key?.GetValue("ProcessorNameString")?.ToString();
            var identifier = key?.GetValue("Identifier")?.ToString();
            if (!string.IsNullOrEmpty(processorName))
            {
                return !string.IsNullOrEmpty(identifier) ? $"{processorName}-{identifier}" : processorName;
            }
        }
        catch { }

        // 最后备用方案
        return $"CPU-{Environment.ProcessorCount}Core-{Environment.MachineName}";
    }

    private static string GetMemoryInfo()
    {
        // 方法1: WMI查询
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT TotalPhysicalMemory FROM Win32_ComputerSystem");
            foreach (ManagementObject obj in searcher.Get())
            {
                if (obj["TotalPhysicalMemory"] != null)
                {
                    var totalMemory = Convert.ToUInt64(obj["TotalPhysicalMemory"]);
                    return $"{totalMemory / (1024 * 1024 * 1024)} GB";
                }
            }
        }
        catch { }

        // 方法2: Performance Counter
        try
        {
            using var pc = new System.Diagnostics.PerformanceCounter("Memory", "Available Bytes");
            var availableMemory = pc.NextValue();
            // 估算总内存（可用内存通常是总内存的一部分）
            var estimatedTotal = (long)(availableMemory * 1.5 / (1024 * 1024 * 1024));
            return $"~{estimatedTotal} GB";
        }
        catch { }

        // 方法3: GC内存信息
        try
        {
            var totalMemory = GC.GetTotalMemory(false) / (1024 * 1024);
            return $"GC-{totalMemory} MB";
        }
        catch { }

        return $"MEM-{Environment.WorkingSet / (1024 * 1024)} MB";
    }

    private static string GetMotherboardInfo()
    {
        // 方法1: WMI查询主板信息
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT Product, Manufacturer, SerialNumber FROM Win32_BaseBoard");
            foreach (ManagementObject obj in searcher.Get())
            {
                var manufacturer = obj["Manufacturer"]?.ToString();
                var product = obj["Product"]?.ToString();
                var serialNumber = obj["SerialNumber"]?.ToString();

                if (!string.IsNullOrEmpty(manufacturer) && !string.IsNullOrEmpty(product))
                {
                    var result = $"{manufacturer} {product}";
                    if (!string.IsNullOrEmpty(serialNumber) && serialNumber.Length > 4)
                    {
                        result += $"-{serialNumber[..4]}";
                    }
                    return result;
                }
            }
        }
        catch { }

        // 方法2: 计算机系统信息
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT Model, Manufacturer FROM Win32_ComputerSystem");
            foreach (ManagementObject obj in searcher.Get())
            {
                var manufacturer = obj["Manufacturer"]?.ToString();
                var model = obj["Model"]?.ToString();
                if (!string.IsNullOrEmpty(manufacturer) && !string.IsNullOrEmpty(model))
                {
                    return $"{manufacturer} {model}";
                }
            }
        }
        catch { }

        // 方法3: BIOS信息
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT Manufacturer, Version FROM Win32_BIOS");
            foreach (ManagementObject obj in searcher.Get())
            {
                var manufacturer = obj["Manufacturer"]?.ToString();
                var version = obj["Version"]?.ToString();
                if (!string.IsNullOrEmpty(manufacturer))
                {
                    return $"BIOS-{manufacturer}-{version}";
                }
            }
        }
        catch { }

        return $"MB-{Environment.MachineName}-{Environment.OSVersion.Platform}";
    }

    private static string GetDiskInfo()
    {
        // 方法1: WMI查询硬盘信息
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT Model, Size, SerialNumber FROM Win32_DiskDrive WHERE MediaType='Fixed hard disk media'");
            foreach (ManagementObject obj in searcher.Get())
            {
                var model = obj["Model"]?.ToString();
                var serialNumber = obj["SerialNumber"]?.ToString();
                var size = obj["Size"] != null ? Convert.ToUInt64(obj["Size"]) / (1024 * 1024 * 1024) : 0;

                if (!string.IsNullOrEmpty(model))
                {
                    var result = $"{model} ({size} GB)";
                    if (!string.IsNullOrEmpty(serialNumber) && serialNumber.Length > 4)
                    {
                        result += $"-{serialNumber[..4]}";
                    }
                    return result;
                }
            }
        }
        catch { }

        // 方法2: DriveInfo
        try
        {
            var drives = DriveInfo.GetDrives()
                .Where(d => d.DriveType == DriveType.Fixed && d.IsReady)
                .OrderBy(d => d.Name)
                .ToArray();

            if (drives.Length > 0)
            {
                var primaryDrive = drives[0];
                var totalSize = primaryDrive.TotalSize / (1024 * 1024 * 1024);
                var freeSpace = primaryDrive.AvailableFreeSpace / (1024 * 1024 * 1024);
                return $"Drive-{primaryDrive.Name.Replace(":\\", "")} ({totalSize}GB-{freeSpace}GB)";
            }
        }
        catch { }

        // 方法3: 逻辑磁盘信息
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT Size, FreeSpace, DeviceID FROM Win32_LogicalDisk WHERE DriveType=3");
            foreach (ManagementObject obj in searcher.Get())
            {
                var deviceId = obj["DeviceID"]?.ToString();
                var size = obj["Size"] != null ? Convert.ToUInt64(obj["Size"]) / (1024 * 1024 * 1024) : 0;
                var freeSpace = obj["FreeSpace"] != null ? Convert.ToUInt64(obj["FreeSpace"]) / (1024 * 1024 * 1024) : 0;

                if (!string.IsNullOrEmpty(deviceId))
                {
                    return $"LogicalDisk-{deviceId} ({size}GB-{freeSpace}GB)";
                }
            }
        }
        catch { }

        return $"DISK-{Environment.SystemDirectory[0]}-{Environment.MachineName}";
    }

    private static string GetMACAddress()
    {
        // 方法1: 获取物理网卡MAC地址（排除虚拟网卡）
        try
        {
            var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
                .Where(nic => nic.OperationalStatus == OperationalStatus.Up &&
                             nic.NetworkInterfaceType != NetworkInterfaceType.Loopback &&
                             nic.NetworkInterfaceType != NetworkInterfaceType.Tunnel &&
                             !nic.GetPhysicalAddress().Equals(PhysicalAddress.None) &&
                             !nic.Description.ToLower().Contains("virtual") &&
                             !nic.Description.ToLower().Contains("vmware") &&
                             !nic.Description.ToLower().Contains("hyper-v"))
                .OrderBy(nic => nic.Name)
                .ToArray();

            if (networkInterfaces.Length > 0)
            {
                var macAddress = networkInterfaces[0].GetPhysicalAddress().ToString();
                if (macAddress.Length == 12)
                {
                    return string.Join(":", Enumerable.Range(0, 6)
                        .Select(i => macAddress.Substring(i * 2, 2)));
                }
                return macAddress;
            }
        }
        catch { }

        // 方法2: WMI查询网络适配器
        try
        {
            using var searcher = new ManagementObjectSearcher("SELECT MACAddress, AdapterType FROM Win32_NetworkAdapter WHERE MACAddress IS NOT NULL AND AdapterType IS NOT NULL");
            foreach (ManagementObject obj in searcher.Get())
            {
                var macAddress = obj["MACAddress"]?.ToString();
                var adapterType = obj["AdapterType"]?.ToString();

                if (!string.IsNullOrEmpty(macAddress) &&
                    !string.IsNullOrEmpty(adapterType) &&
                    !adapterType.ToLower().Contains("virtual"))
                {
                    return macAddress.Replace("-", ":");
                }
            }
        }
        catch { }

        // 方法3: 生成基于系统信息的伪MAC地址
        try
        {
            var systemInfo = $"{Environment.MachineName}{Environment.UserName}{Environment.OSVersion}{Environment.ProcessorCount}";
            using var sha1 = System.Security.Cryptography.SHA1.Create();
            var hash = sha1.ComputeHash(System.Text.Encoding.UTF8.GetBytes(systemInfo));
            var macBytes = hash.Take(6).ToArray();
            // 设置本地管理位，确保不与真实MAC冲突
            macBytes[0] = (byte)(macBytes[0] | 0x02);
            return string.Join(":", macBytes.Select(b => b.ToString("X2")));
        }
        catch { }

        return "02:00:00:00:00:00"; // 本地管理的MAC地址格式
    }

    private static bool IsAllDefaultValues(SystemInfo info)
    {
        return (info.CPU.StartsWith("Unknown") || info.CPU.StartsWith("CPU-")) &&
               (info.Memory.StartsWith("Unknown") || info.Memory.StartsWith("MEM-") || info.Memory.StartsWith("GC-")) &&
               (info.Motherboard.StartsWith("Unknown") || info.Motherboard.StartsWith("MB-") || info.Motherboard.StartsWith("BIOS-")) &&
               (info.Disk.StartsWith("Unknown") || info.Disk.StartsWith("DISK-") || info.Disk.StartsWith("Drive-") || info.Disk.StartsWith("LogicalDisk-")) &&
               (info.MACAddress == "00:00:00:00:00:00" || info.MACAddress == "02:00:00:00:00:00");
    }

    private static void GenerateUniqueDeviceInfo(SystemInfo info)
    {
        // 生成基于多个系统属性的唯一标识符
        var uniqueId = Guid.NewGuid().ToString("N")[..8];
        var timestamp = DateTimeOffset.Now.ToUnixTimeSeconds().ToString();
        var processId = Environment.ProcessId.ToString();

        // 收集更多系统信息用于生成唯一性
        var systemData = new StringBuilder();
        systemData.Append(Environment.MachineName);
        systemData.Append(Environment.UserName);
        systemData.Append(Environment.OSVersion.ToString());
        systemData.Append(Environment.ProcessorCount);
        systemData.Append(Environment.SystemDirectory);
        systemData.Append(Environment.WorkingSet);
        systemData.Append(uniqueId);
        systemData.Append(timestamp);
        systemData.Append(processId);

        // 尝试获取更多硬件信息
        try
        {
            systemData.Append(Environment.GetEnvironmentVariable("COMPUTERNAME"));
            systemData.Append(Environment.GetEnvironmentVariable("USERDOMAIN"));
            systemData.Append(Environment.GetEnvironmentVariable("PROCESSOR_ARCHITECTURE"));
        }
        catch { }

        var combinedData = systemData.ToString();
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(combinedData));
        var hashString = Convert.ToHexString(hash).ToLower();

        // 生成唯一的设备信息
        info.CPU = $"CPU-{hashString[..8]}-{Environment.ProcessorCount}Core";
        info.Memory = $"MEM-{hashString[8..16]}-{Environment.WorkingSet / (1024 * 1024)}MB";
        info.Motherboard = $"MB-{hashString[16..24]}-{Environment.MachineName}";
        info.Disk = $"DISK-{hashString[24..32]}-{Environment.SystemDirectory[0]}";

        // 生成唯一MAC地址
        var macBytes = hash.Take(6).ToArray();
        macBytes[0] = (byte)(macBytes[0] | 0x02); // 设置本地管理位
        info.MACAddress = string.Join(":", macBytes.Select(b => b.ToString("X2")));
    }

    internal static string GenerateDeviceFingerprint()
    {
        var info = GatherSystemInfo();
        var combined = $"{info.CPU}|{info.Memory}|{info.Motherboard}|{info.Disk}|{info.MACAddress}";
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(combined));
        return Convert.ToHexString(hash).ToLower();
    }

    private class SystemInfo
    {
        public string CPU { get; set; } = string.Empty;
        public string Memory { get; set; } = string.Empty;
        public string Motherboard { get; set; } = string.Empty;
        public string Disk { get; set; } = string.Empty;
        public string MACAddress { get; set; } = string.Empty;
    }
}
