﻿using System.Windows;
using System.Windows.Controls;
using HandyControl.Controls;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Properties;
using Xylia.Preview.Common;
using Xylia.Preview.Data.Models;
using Xylia.Preview.Data.Models.Sequence;
using Xylia.Preview.UI.Controls;
using Xylia.Preview.UI.Controls.Primitives;
using Xylia.Preview.UI.Extensions;

namespace Xylia.BnsHelper.Views;
public partial class ItemMapPanel
{
	#region Constructor
	public ItemMapPanel()
	{
		InitializeComponent();

		ItemMapPanel_MapField_ScrollBar_SliderBar.SetBinding(BnsCustomRangeBaseWidget.MinimumProperty, new Binding(ItemMapPanel_MapField, "MinZoomRatio"));
		ItemMapPanel_MapField_ScrollBar_SliderBar.SetBinding(BnsCustomRangeBaseWidget.MaximumProperty, new Binding(ItemMapPanel_MapField, "MaxZoomRatio"));
		ItemMapPanel_MapField_ScrollBar_SliderBar.SetBinding(BnsCustomRangeBaseWidget.ValueProperty, new Binding(ItemMapPanel_MapField, "Ratio"));
	}
	#endregion

	#region Protected Methods
	protected override void OnInitialized(EventArgs e)
	{
		base.OnInitialized(e);

		#region Tab
		ItemMapPanel_Tab_RadioButton_10.Visibility = ItemMapPanel_Tab_RadioButton_11.Visibility =
			ItemMapPanel_Tab_RadioButton_7.Visibility = ItemMapPanel_Tab_RadioButton_9.Visibility =
			ItemMapPanel_Tab_RadioButton_12.Visibility = ItemMapPanel_Tab_RadioButton_13.Visibility = Visibility.Collapsed;
		ItemMapPanel_MapField.ViewDetailed += ItemMapPanel_MapField_ViewDetailed;

		_commands[ItemMapPanel_Tab_RadioButton_1] = "Weapon";
		_commands[ItemMapPanel_Tab_RadioButton_2] = "Necklace";
		_commands[ItemMapPanel_Tab_RadioButton_3] = "Earring";
		_commands[ItemMapPanel_Tab_RadioButton_4] = "Ring";
		_commands[ItemMapPanel_Tab_RadioButton_5] = "Bracelet";
		_commands[ItemMapPanel_Tab_RadioButton_6] = "Belt";
		_commands[ItemMapPanel_Tab_RadioButton_7] = "Soul";
		_commands[ItemMapPanel_Tab_RadioButton_8] = "Gloves";
		_commands[ItemMapPanel_Tab_RadioButton_9] = "Pet1";
		_commands[ItemMapPanel_Tab_RadioButton_10] = "SubGem1";
		_commands[ItemMapPanel_Tab_RadioButton_11] = "SubGem2";
		_commands[ItemMapPanel_Tab_RadioButton_12] = "Soul2";
		_commands[ItemMapPanel_Tab_RadioButton_13] = "Nova";
		//_commands[ItemMapPanel_Tab_RadioButton_14] = "SubGem3";

		foreach (var toggle in ItemMapPanel_Tab.Children.OfType<BnsCustomToggleButtonWidget>())
		{
			var badge = toggle.GetChild<BnsCustomLabelWidget>("Badge");
			if (badge != null) badge.Visibility = Visibility.Collapsed;
		}
		#endregion

		#region Navigation
		ItemMapPanel_NavigationList_Column_1_1_SearchHolder_ClearAll.Click += ItemMapPanel_NavigationList_SearchHolder_ClearAll_Click;
		ItemMapPanel_NavigationList_Column_1_1_SearchHolder_Navigate.Click += ItemMapPanel_NavigationList_SearchHolder_Navigate_Click;

		//ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute.Visibility = Visibility.Collapsed;
		//ItemMapPanel_NavigationList_Column_1_1_Route.Children.OfType<BnsCustomImageWidget>();
		#endregion

		#region MapField
		// SearchHolder
		ItemMapPanel_Search.TextChanged += ItemMapPanel_Search_TextChanged;
		ItemMapPanel_ClearSearchTextBtn.Visibility = Visibility.Collapsed;
		ItemMapPanel_ClearSearchTextBtn.Click += ItemMapPanel_ClearSearchTextBtn_Click;

		// ratio control
		ItemMapPanel_MapField.Children.Remove(ItemMapPanel_MapField_ScrollBar);
		ItemMapPanel_MapFieldHolder.Children.Add(ItemMapPanel_MapField_ScrollBar);

		// event
		ItemMapPanel_ControlHolder_Navigate.Click += ItemMapPanel_ControlHolder_Navigate_Clcik;
		ItemMapPanel_MapField.RoutesChanged += ItemMapPanel_MapField_RoutesChanged;
		ItemMapPanel_MapField.SetRouteThrough += ItemMapPanel_MapField_SetRouteThrough;
		#endregion

		#region Button
		//SetZOrder(ItemMapPanel_Fold, 99);
		ItemMapPanel_Fold.Unchecked += ItemMapPanel_Fold_UnChecked;
		ItemMapPanel_Fold.Checked += ItemMapPanel_Fold_Checked;
		#endregion

		InitData();
	}
	#endregion

	#region Private Methods
	/// <summary>
	/// Custom helper
	/// </summary>
	private void InitData()
	{
		// load extra data 
		var _table = Globals.GameData.Provider.GetTable("item-graph");
		_table.LoadXml(ResourceProvider.Instance.Provider.GetFiles("ItemGraphData*.xml"));

		var table = Globals.GameData.Provider.GetTable<ItemGraph>(reload: true);
		var elements = table.Elements.ToList();
		foreach (var element in elements)
		{
			switch (element)
			{
				case ItemGraph.Edge edge: edge.CreateRecipeHelper(); break;
				case ItemGraph.Seed seed: seed.CreateEdgeHelper(table); break;
			}
		}

		ItemMapPanel_MapField.Update("weapon", SettingHelper.Default.Job);
	}

	// ----- Tab ----- //
	private void ItemMapPanel_Tab_RadioButton_Checked(object sender, RoutedEventArgs e)
	{
		var source = (BnsCustomToggleButtonWidget)e.Source;
		ItemMapPanel_MapField.Update(_commands.GetValueOrDefault(source), SettingHelper.Default.Job);
	}


	// ----- MapField ----- //
	private void ItemMapPanel_Search_TextChanged(object? sender, TextChangedEventArgs? e)
	{
		//ItemMapPanel_ClearSearchTextBtn.Visibility = string.IsNullOrEmpty(ItemMapPanel_Search.Text) ? Visibility.Hidden : Visibility.Visible;

		// TODO: Search
		// wrong, correct
	}

	private void ItemMapPanel_ClearSearchTextBtn_Click(object sender, RoutedEventArgs e)
	{
		//ItemMapPanel_Search.Text = string.Empty;
	}

	private void ItemMapPanel_ControlHolder_Navigate_Clcik(object sender, RoutedEventArgs e)
	{
		// TODO: Navigate to equiped 
	}


	private void ItemMapPanel_MapField_ViewDetailed(object? sender, object source)
	{
		//new ItemGrowth2TooltipPanel { DataContext = source.To<Record>() }.Show();
	}

	private void ItemMapPanel_MapField_RoutesChanged(object? sender, ItemGraphRouteHelper[] routes)
	{
		Current?.SwitchHighlight(false);

		if (routes.Length == 0)
		{
			ItemMapPanel_NavigationList_Column_1_1_GuideText.String.LabelText = "UI.ItemGraph.Guide.NotFoundPath".GetText();
			ItemMapPanel_NavigationList_Column_1_1_Route.ItemsSource = null;
			return;
		}

		Current = routes.First();
		Current.SwitchHighlight(true);

		ItemMapPanel_NavigationList_Column_1_1_SearchHolder_Start.Text = Current.StartItem.Name;
		ItemMapPanel_NavigationList_Column_1_1_SearchHolder_Purpose.Text = Current.EndItem.Name;
		ItemMapPanel_NavigationList_Column_1_1_GuideText.String.LabelText = "UI.ItemGraph.Guide.FoundPath".GetText([null, routes.Length]);
		ItemMapPanel_NavigationList_Column_1_1_Route.ItemsSource = routes;
	}

	private void ItemMapPanel_MapField_SetRouteThrough(object? sender, BnsCustomGraphMapEdge edge)
	{
		var replace = Current != null ? Array.FindIndex(Current.Edges, x => x.Data.StartItem == edge.Data.StartItem && x.Data.EndItem == edge.Data.EndItem) : -1;
		if (replace == -1) Growl.Success("没有可以切换的目标");
		else
		{
			Current!.SwitchHighlight(false);

			Current.Edges[replace] = edge;
			Current.SwitchHighlight(true);
		}
	}


	// ----- NavigationList ----- // 
	private void ItemMapPanel_NavigationList_SearchHolder_Start_ClearBtn_Click(object sender, RoutedEventArgs e)
	{

	}

	private void ItemMapPanel_NavigationList_SearchHolder_Purpose_ClearBtn_Click(object sender, RoutedEventArgs e)
	{

	}

	private void ItemMapPanel_NavigationList_SearchHolder_ClearAll_Click(object sender, RoutedEventArgs e)
	{

	}

	private void ItemMapPanel_NavigationList_SearchHolder_Navigate_Click(object sender, RoutedEventArgs e)
	{
		// Navigate to search 
	}

	private void ItemMapPanel_NavigationList_RecentlyRoute_Click(object sender, RoutedEventArgs e)
	{
		// history
		// 아이템이름 &lt;image enablescale=&quot;true&quot; imagesetpath=&quot;00009076.Tooltip_ArrowRight&quot; scalerate=&quot;1.4&quot;/&gt; 아이템이름
	}

	private void ItemMapPanel_NavigationList_Route_Detail(object sender, RoutedEventArgs e)
	{

	}

	private void ItemMapPanel_NavigationList_Route_Initialize(object sender, DependencyPropertyChangedEventArgs e)
	{
		var route = (ItemGraphRouteHelper)e.NewValue;
		var widget = (BnsCustomImageWidget)sender;
		widget.SetExpansionText("Label", "UI.ItemGraph.PathTitle".GetText([null, route.Index + 1]));

		var Button = widget.GetChild<BnsCustomLabelButtonWidget>("Button")!;
		var MainRoute = widget.GetChild<BnsCustomColumnListWidget>("MainRoute")!;
		var Item = widget.GetChild<BnsCustomListBoxWidget>("Item")!;
		var Cost = widget.GetChild<BnsCustomLabelWidget>("Cost")!;
		var DiscountCost = widget.GetChild<BnsCustomLabelWidget>("DiscountCost")!;

		#region Button
		var flag = route.Index != 0;
		void ChangeState(object sender, EventArgs e)
		{
			flag = !flag;
			widget.SetExpansionVisibleFlag("Icon", flag);
			widget.SetExpansionVisibleFlag("Icon2", !flag);
			MainRoute.SetVisiable(flag);
			widget.InvalidateVisual();
		}

		Button.Click += ChangeState;
		ChangeState(Button, EventArgs.Empty);
		#endregion

		MainRoute.ItemsSource = route.MainRoute;
		Item.ItemsSource = route.Ingredients;
		Cost.String.LabelText = route.Money.Money;
		DiscountCost.String.LabelText = (route.Money * ItemGraphRouteHelper.Discount).Money;
	}

	private void ItemMapPanel_NavigationList_Route_InitializeRoute(object sender, DependencyPropertyChangedEventArgs e)
	{
		if (e.NewValue is not KeyValuePair<Item, string> data || data.Key is not Item item) return;

		var widget = (BnsCustomImageWidget)sender;
		var Item = widget.GetChild<BnsCustomImageWidget>("Item")!;
		Item.DataContext = item;
		Item.ToolTip = new BnsTooltipHolder();
		Item.SetExpansionImageProperties("BackgroundImage", item.BackgroundImage);
		Item.SetExpansionImageProperties("IconImage", item.FrontIcon);
		Item.SetExpansionImageProperties("UnusableImage", item.UnusableImage);
		Item.SetExpansionVisibleFlag("StackableLabel", false);
		Item.SetExpansionImageProperties("Grade_Image", null);
		Item.SetExpansionImageProperties("CanSaleItem", item.CanSaleItemImage);

		var Name = widget.GetChild<BnsCustomLabelWidget>("Name")!;
		Name.String.LabelText = item.ItemName;

		var Desc = widget.GetChild<BnsCustomLabelWidget>("Desc")!;
		Desc.SetVisiable(data.Value != null);
		Desc.String.LabelText = data.Value;
	}

	private void ItemMapPanel_NavigationList_Route_InitializeItem(object sender, DependencyPropertyChangedEventArgs e)
	{
		if (e.NewValue is not KeyValuePair<Item, int> data || data.Key is not Item item) return;

		var widget = (BnsCustomImageWidget)sender;
		widget.DataContext = item;
		widget.ToolTip = new BnsTooltipHolder();
		widget.SetExpansionImageProperties("BackgroundImage", item.BackgroundImage);
		widget.SetExpansionImageProperties("IconImage", item.FrontIcon);
		widget.SetExpansionImageProperties("UnusableImage", item.UnusableImage);
		widget.SetExpansionText("StackableLabel", data.Value.ToString());
		widget.SetExpansionImageProperties("Grade_Image", null);
		widget.SetExpansionImageProperties("CanSaleItem", item.CanSaleItemImage);
	}


	// ----- Fold ----- // 
	private void ItemMapPanel_Fold_UnChecked(object sender, RoutedEventArgs e)
	{
		ItemMapPanel_NavigationHolder.Visibility = Visibility.Visible;
	}

	private void ItemMapPanel_Fold_Checked(object sender, RoutedEventArgs e)
	{
		ItemMapPanel_NavigationHolder.Visibility = Visibility.Collapsed;
	}
	#endregion

	#region Private Fields
	private readonly Dictionary<BnsCustomToggleButtonWidget, string> _commands = [];

	private ItemGraphRouteHelper? Current;
	#endregion
}