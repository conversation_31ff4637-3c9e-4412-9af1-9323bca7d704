﻿using System;
using System.Collections.Specialized;
using System.Net;

namespace Xylia.Updater;
public class MyWebClient : WebClient
{
  /// <summary>
  ///     Response Uri after any redirects.
  /// </summary>
  internal Uri ResponseUri { get; private set; }
  public NameValueCollection Parameter { get; } = [];

  protected override WebResponse GetWebResponse(WebRequest request, IAsyncResult result)
  {
    WebResponse webResponse = base.GetWebResponse(request, result);
    ResponseUri = webResponse.ResponseUri;
    return webResponse;
  }
}
