﻿using Microsoft.Web.WebView2.Core;
using Newtonsoft.Json;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Text;
using Xylia.BnsHelper.Common.Converters;
using Xylia.BnsHelper.Models;
using Xylia.Preview.Common.Extension;

namespace Xylia.BnsHelper.Views;
// TODO: 计划将模板嵌入到本地资源
public partial class CharacterInfoPanel
{
    #region Constructor
    readonly Creature? Creature;

    public CharacterInfoPanel(Creature creature)
    {
        InitializeComponent();
        Creature = creature;
        wv.Source = new Uri($"https://tools.bnszs.com/ingame/bs/character/profile?c={creature.Name}&s={creature.world}&type=neo");
    }

    public CharacterInfoPanel(string? name, int world)
    {
        InitializeComponent();
        wv.Source = new Uri($"https://tools.bnszs.com/ingame/bs/character/profile?c={name}&s={world}");
    }
    #endregion

    #region Methods
    protected override void OnInitialized(EventArgs e)
    {
        base.OnInitialized(e);
        wv.CoreWebView2InitializationCompleted += CoreWebView2_InitializationCompleted;
        //LoadLocalTemplate();
    }

    /// <summary>
    /// 加载本地HTML模板
    /// </summary>
    private async void LoadLocalTemplate()
    {
        try
        {
            await wv.EnsureCoreWebView2Async();

            // 读取内嵌的HTML模板
            var htmlTemplate = ReadTextResource("Xylia.BnsHelper.Resources.Templates.character_profile.html");
            if (string.IsNullOrEmpty(htmlTemplate))
            {
                Debug.WriteLine("[CharacterInfoPanel] 无法读取HTML模板");
                return;
            }

            // 导航到本地HTML内容
            wv.NavigateToString(htmlTemplate);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[CharacterInfoPanel] 加载本地模板失败: {ex.Message}");
        }
    }

    private async void CoreWebView2_InitializationCompleted(object? sender, CoreWebView2InitializationCompletedEventArgs args)
    {
        if (!args.IsSuccess) return;

        wv.CoreWebView2.Settings.AreDevToolsEnabled = false;
        wv.CoreWebView2.Settings.AreDefaultContextMenusEnabled = false;
        wv.CoreWebView2.Settings.UserAgent = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.104 Safari/537.36 BnsIngameBrowser";
        wv.CoreWebView2.WebResourceRequested += CoreWebView2_WebResourceRequested;
        wv.CoreWebView2.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.XmlHttpRequest);
        wv.CoreWebView2.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.Document);
        wv.CoreWebView2.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.Stylesheet);

        wv.CoreWebView2.AddHostObjectToScript("WebObject", this);
        await wv.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync($$"""
			onmouseover = (e) => {
		    var obj = e.target; 
			if (obj.tagName != 'IMG') return;

		    obj.removeAttribute('title');

			var parent = $(obj).parent('.item-img');
			if (parent != null) chrome.webview.hostObjects.WebObject.PostMessage(parent.data('tooltip')); 
		};
		""");
    }

    private async void CoreWebView2_WebResourceRequested(object? sender, CoreWebView2WebResourceRequestedEventArgs e)
    {
        // 如果Creature不存在，使用远程响应
        if (Creature is null) return;

        object? request = null;
        var uri = e.Request.Uri;

        // 处理CSS文件请求
        if (uri.Contains("character.css"))
        {
            try
            {
                var cssContent = ReadTextResource("Xylia.BnsHelper.Resources.Templates.character.css");
                if (!string.IsNullOrEmpty(cssContent))
                {
                    var ms = new MemoryStream(Encoding.UTF8.GetBytes(cssContent));
                    e.Response = wv.CoreWebView2.Environment.CreateWebResourceResponse(ms, 200, "OK", "Content-Type: text/css; charset=utf-8");
                    return;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CharacterInfoPanel] 加载CSS文件失败: {ex.Message}");
            }
        }

        if (uri.Contains("/allowed.json"))
        {
            request = "{\"allowed\": true }";
        }
        else if (uri.Contains("/jobs.json"))
        {
            request = new object();
        }
        else if (uri.Contains("/info.json"))
        {
            request = new Info()
            {
                //account_id = new Guid("D78CA558-7925-48A4-963D-6543AAA03872"),
                //id = ********,
                clazz = Creature.Job.GetDescription(),
                class_name = NameConverter.Convert(Creature.Job)!,
                gender = "female",
                gender_name = "女",
                race = "lyn",
                race_name = "灵",
                geo_zone = Creature.GeoZone,
                geo_zone_name = "亡者森林",
                level = Creature.level,
                mastery_level = Creature.MasteryLevel,
                name = Creature.Name,
                server_id = Creature.world,
                server_name = Creature.World,
                profile_url = "http://gamepic.tw.ncsoft.com/images/null/10/%E5%92%95%E5%92%95%E9%9B%AA.jpg"
            };
        }
        else if (uri.Contains("/characters.json"))
        {
            request = new List<Character>();
        }
        else if (uri.Contains("/abilities.json"))
        {
            var base_ability = new Abilities.Ability()
            {
                attack_critical_damage_value = Creature.AttackCriticalDamageValue,
                int_attack_critical_value = Creature.AttackCriticalValue,
                int_attack_hit_value = Creature.AttackHitValue,
                int_attack_pierce_value = Creature.AttackPierceValue,
                int_attack_power_value = Creature.AttackPowerCreature,
                int_defend_critical_value = Creature.DefendCriticalValue,
                int_defend_dodge_value = Creature.DefendDodgeValue,
                int_defend_power_value = Creature.DefendPowerCreatureValue,
                int_hp_regen = Creature.HpRegen,
                int_hp_regen_combat = Creature.HpRegenCombat,
                int_max_hp = Creature.MaxHp,
            };
            var equipped_ability = new Abilities.Ability()
            {
                attack_critical_damage_value = Creature.AttackCriticalDamageValueEquip,
                int_attack_critical_value = Creature.AttackCriticalValueEquip,
                int_attack_hit_value = Creature.AttackHitValueEquip,
                int_attack_pierce_value = Creature.AttackPierceValueEquip,
                int_attack_power_value = Creature.AttackPowerEquip,
                int_defend_critical_value = Creature.DefendCriticalValueEquip,
                int_defend_dodge_value = Creature.DefendDodgeValueEquip,
                int_defend_power_value = Creature.DefendPowerEquipValue,
                int_hp_regen = Creature.HpRegenEquip,
                int_hp_regen_combat = Creature.HpRegenCombatEquip,
                int_max_hp = Creature.MaxHpEquip,
            };

            request = new Abilities()
            {
                base_ability = base_ability,
                equipped_ability = equipped_ability,
                total_ability = base_ability + equipped_ability,
            };
        }
        else if (uri.Contains("/equipments.json"))
        {
            request = new Equipments()
            {
                //hand = Creature.Hand,
                hand_appearance = Creature.HandAppearance,
                body = Creature.Body,
                eye = Creature.Eye,
                head = Creature.Head,
                ear_left = Creature.Ear,
                finger_left = Creature.Finger,
                bracelet = Creature.Waist,
                neck = Creature.Neck,
                gloves = Creature.Gloves,
                belt = Creature.Wrist,
                swift_badge = Creature.Rune1,
                soul_badge = Creature.Rune2,
                soulshield_1 = Creature.Gem1,
                soulshield_2 = Creature.Gem2,
                soulshield_3 = Creature.Gem3,
                soulshield_4 = Creature.Gem4,
                soulshield_5 = Creature.Gem5,
                soulshield_6 = Creature.Gem6,
                soulshield_7 = Creature.Gem7,
                soulshield_8 = Creature.Gem8,
                alternate_soulshield_1 = Creature.PresetGem1,
                alternate_soulshield_2 = Creature.PresetGem2,
                alternate_soulshield_3 = Creature.PresetGem3,
                alternate_soulshield_4 = Creature.PresetGem4,
                alternate_soulshield_5 = Creature.PresetGem5,
                alternate_soulshield_6 = Creature.PresetGem6,
                alternate_soulshield_7 = Creature.PresetGem7,
                alternate_soulshield_8 = Creature.PresetGem8,
            };
        }

        // rewrite request data
        if (request != null)
        {
            var ms = new MemoryStream(Encoding.UTF8.GetBytes(JsonConvert.SerializeObject(request)));
            e.Response = wv.CoreWebView2.Environment.CreateWebResourceResponse(ms, 200, "OK", "Content-Type: text/html; charset=utf-8");
        }
    }

    public void PostMessage(string meaasge)
    {
        if (meaasge is null) return;
    }

    /// <summary>
    /// 读取嵌入式文本资源
    /// </summary>
    /// <param name="resourcePath">资源路径</param>
    /// <returns>文本内容</returns>
    public static string ReadTextResource(string resourcePath)
    {
        try
        {
            using var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream(resourcePath);
            if (stream == null)
            {
                Debug.Assert(true, "资源文件不存在: " + resourcePath);
                return string.Empty;
            }

            using var reader = new StreamReader(stream, Encoding.UTF8);
            return reader.ReadToEnd();
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"读取资源文件失败: {resourcePath}, {ex.Message}");
            return string.Empty;
        }
    }
    #endregion
}
