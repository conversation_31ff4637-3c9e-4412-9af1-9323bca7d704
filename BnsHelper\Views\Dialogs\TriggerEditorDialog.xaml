<hc:Window x:Class="Xylia.BnsHelper.Views.Dialogs.TriggerEditorDialog"
           xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
           xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
           xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
           xmlns:hc="https://handyorg.github.io/handycontrol"
           Title="{Binding Title}" Width="550" SizeToContent="Height"
           WindowStartupLocation="CenterOwner"
           ResizeMode="NoResize">

    <Grid Margin="10,10,10,10">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="107*"/>
            <ColumnDefinition Width="693*"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 通用设置-->
        <Grid Grid.ColumnSpan="2" Margin="7 3">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="100"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Row="0" Grid.Column="0" Text="名称:" VerticalAlignment="Center" Margin="0,0,10,10"/>
            <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding Trigger.Name}" Margin="0,0,0,10"/>

            <TextBlock Grid.Row="3" Grid.Column="0" Text="优先级:" VerticalAlignment="Center" Margin="0,0,10,10"/>
            <hc:NumericUpDown Grid.Row="3" Grid.Column="1" Value="{Binding Trigger.Priority}" Minimum="0" Maximum="1000" Margin="0,0,0,10"/>

            <TextBlock Grid.Row="4" Grid.Column="0" Text="冷却时间:" VerticalAlignment="Center" Margin="0,0,10,10"/>
            <StackPanel Grid.Row="4" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                <hc:NumericUpDown Value="{Binding Trigger.Cooldown}" Minimum="0" Maximum="3600000" Width="120"/>
                <TextBlock Text="毫秒" VerticalAlignment="Center" Margin="5,0,0,0"/>
            </StackPanel>
        </Grid>

        <!-- 主内容区域 -->
        <TabControl Grid.Row="1" Grid.ColumnSpan="2">
            <!-- 基本设置 -->
            <TabItem Header="触发条件">
                <ScrollViewer Padding="10" VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- 触发条件类型选择 -->
                        <GroupBox Header="触发条件类型" Margin="0,0,0,15">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <RadioButton Content="消息文本匹配" IsChecked="{Binding IsMessageCondition}" Margin="0,0,15,0" />
                                <RadioButton Content="定时触发" IsChecked="{Binding IsTimeCondition}" />
                            </StackPanel>
                        </GroupBox>

                        <!-- 正则表达式条件 -->
                        <Grid Margin="0,0,0,15" Visibility="{Binding IsMessageCondition, Converter={StaticResource Boolean2VisibilityConverter}}">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBox Grid.Row="0" Text="{Binding Expression}" TextWrapping="Wrap" AcceptsReturn="True" Height="80" FontFamily="Consolas" Margin="0,0,0,10"/>
                            <TextBlock Grid.Row="1" Text="提示：使用 .* 匹配任意文本，使用 (.*) 捕获分组" VerticalAlignment="Center" Foreground="Gray"/>
                        </Grid>

                        <!-- 定时触发条件 -->
                        <Grid Margin="0,0,0,15" Visibility="{Binding IsTimeCondition, Converter={StaticResource Boolean2VisibilityConverter}}">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="80"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="触发时间:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                            <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                                <hc:NumericUpDown Value="{Binding TriggerHour}" Minimum="0" Maximum="23" Width="60" Margin="0,0,5,0"/>
                                <TextBlock Text="时" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <hc:NumericUpDown Value="{Binding TriggerMinute}" Minimum="0" Maximum="59" Width="60" Margin="0,0,5,0"/>
                                <TextBlock Text="分" VerticalAlignment="Center"/>
                            </StackPanel>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="重复模式:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                            <ComboBox Grid.Row="1" Grid.Column="1" SelectedIndex="{Binding RepeatMode}" Margin="0,0,0,10">
                                <ComboBoxItem Content="仅一次"/>
                                <ComboBoxItem Content="每天"/>
                                <ComboBoxItem Content="每周"/>
                            </ComboBox>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="星期选择:" VerticalAlignment="Center" Margin="0,0,10,10"
                                           Visibility="{Binding RepeatMode, Converter={StaticResource Enum2VisibilityConverter}, ConverterParameter=2}"/>
                            <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10"
                                            Visibility="{Binding RepeatMode, Converter={StaticResource Enum2VisibilityConverter}, ConverterParameter=2}">
                                <CheckBox Content="周一" IsChecked="{Binding IsMonday}" Margin="0,0,10,0"/>
                                <CheckBox Content="周二" IsChecked="{Binding IsTuesday}" Margin="0,0,10,0"/>
                                <CheckBox Content="周三" IsChecked="{Binding IsWednesday}" Margin="0,0,10,0"/>
                                <CheckBox Content="周四" IsChecked="{Binding IsThursday}" Margin="0,0,10,0"/>
                                <CheckBox Content="周五" IsChecked="{Binding IsFriday}" Margin="0,0,10,0"/>
                                <CheckBox Content="周六" IsChecked="{Binding IsSaturday}" Margin="0,0,10,0"/>
                                <CheckBox Content="周日" IsChecked="{Binding IsSunday}"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 动作设置 -->
            <TabItem Header="触发动作">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <ListView Grid.Row="1" ItemsSource="{Binding Trigger.Actions}" SelectedItem="{Binding SelectedAction}" 
                              ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                        <ListView.ContextMenu>
                            <ContextMenu>
                                <MenuItem Header="在此处添加动作" Command="{Binding AddActionAtCommand}"/>
                                <Separator />
                                <MenuItem Header="编辑动作" Command="{Binding EditActionCommand}"/>
                                <MenuItem Header="删除动作" Command="{Binding DeleteActionCommand}"/>
                                <MenuItem Header="测试动作" Command="{Binding ExecuteActionCommand}"/>
                                <Separator />
                                <MenuItem Header="上移" Command="{Binding MoveActionUpCommand}" IsEnabled="{Binding CanMoveActionUp}"/>
                                <MenuItem Header="下移" Command="{Binding MoveActionDownCommand}" IsEnabled="{Binding CanMoveActionDown}"/>
                            </ContextMenu>
                        </ListView.ContextMenu>
                        <ListView.View>
                            <GridView>
                                <GridView.ColumnHeaderContainerStyle>
                                    <Style TargetType="{x:Type GridViewColumnHeader}" BasedOn="{StaticResource InputElementBaseStyle}">
                                        <Setter Property="Height" Value="25" />
                                        <Setter Property="IsEnabled" Value="False"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    </Style>
                                </GridView.ColumnHeaderContainerStyle>
                                
                                <GridViewColumn Header="启用" Width="50">
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <CheckBox IsChecked="{Binding IsEnabled}" Style="{x:Null}" />
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>
                                <GridViewColumn Header="动作说明" DisplayMemberBinding="{Binding Describe}" Width="300" />
                                <GridViewColumn Header="延时(ms)" >
                                    <GridViewColumn.CellTemplate>
                                        <DataTemplate>
                                            <hc:NumericUpDown Value="{Binding Delay}" Width="110" Minimum="0" />
                                        </DataTemplate>
                                    </GridViewColumn.CellTemplate>
                                </GridViewColumn>
                            </GridView>
                        </ListView.View>
                    </ListView>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Grid.ColumnSpan="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0 7 0 -2">
            <Button Content="取消" Command="{Binding CancelCommand}" Width="80" Margin="0,0,10,0" />
            <Button Content="确定" Command="{Binding OkCommand}" Width="80" Style="{StaticResource ButtonPrimary}" />
        </StackPanel>
    </Grid>
</hc:Window>
