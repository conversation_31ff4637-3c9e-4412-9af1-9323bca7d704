// 活动管理相关功能
// ==================== 活动管理变量 ====================

// 活动管理分页变量
let currentActivitiesPage = 1;
let activitiesPageSize = 20;
let hasMoreActivities = true;

// 流程管理变量
let currentActivityId = null;
let currentActivityName = '';
let currentFlows = [];

// ==================== 活动管理核心功能 ====================

// 加载活动数据
function loadActivitiesData(resetPage = true) {
    if (resetPage) {
        currentActivitiesPage = 1;
    }
    hasMoreActivities = true;

    const statusFilter = document.getElementById('activity-status-filter').value;
    const keyword = document.getElementById('activity-search-input').value;

    const params = new URLSearchParams({
        page: currentActivitiesPage,
        page_size: activitiesPageSize
    });

    if (statusFilter) params.append('status', statusFilter);
    if (keyword) params.append('keyword', keyword);

    fetch(`/admin/api/activities?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                // 按优先级排序（从大到小）
                const sortedActivities = data.data.sort((a, b) => b.priority - a.priority);
                renderActivitiesTable(sortedActivities);
                renderActivitiesPagination(data.data.total);

                // 更新URL参数，保存当前状态
                updateURLParams();
            } else {
                showMessage('加载活动数据失败: ' + data.msg, 'error');
            }
        })
        .catch(error => {
            console.error('加载活动数据失败:', error);
            showMessage('加载活动数据失败', 'error');
        });
}

// 渲染活动表格
function renderActivitiesTable(activities) {
    const tbody = document.getElementById('activities-table-body');
    
    if (!activities || activities.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" style="padding: 40px; text-align: center; color: #718096;">暂无活动数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = activities.map(activity => `
        <tr>
            <td style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">${activity.activity_id}</td>
            <td style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">${activity.activity_name}</td>
            <td style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">
                <span style="padding: 4px 8px; border-radius: 4px; font-size: 12px; color: white; background: ${getActivityStatusColor(activity.status)};">
                    ${getActivityStatusText(activity.status)}
                </span>
            </td>
            <td style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">${activity.priority}</td>
            <td style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">${formatDateTime(activity.begin_time)}</td>
            <td style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">${formatDateTime(activity.end_time)}</td>
            <td style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">
                <button class="btn btn-sm" onclick="editActivity(${activity.activity_id})" style="margin-right: 5px;">编辑</button>
                <button class="btn btn-sm btn-info" onclick="manageFlows(${activity.activity_id}, '${activity.activity_name}')" style="margin-right: 5px;">流程</button>
                <!-- <button class="btn btn-sm btn-danger" onclick="deleteActivity(${activity.activity_id})">删除</button> -->
            </td>
        </tr>
    `).join('');
}

// 渲染活动分页
function renderActivitiesPagination(total) {
    const totalPages = Math.ceil(total / activitiesPageSize);
    const container = document.getElementById('activities-pagination');
    
    if (totalPages <= 1) {
        container.innerHTML = '';
        return;
    }
    
    let paginationHTML = '<div class="pagination">';
    
    // 上一页
    if (currentActivitiesPage > 1) {
        paginationHTML += `<button class="btn btn-sm" onclick="loadActivitiesPage(${currentActivitiesPage - 1})">上一页</button>`;
    }
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === currentActivitiesPage) {
            paginationHTML += `<button class="btn btn-sm btn-primary">${i}</button>`;
        } else {
            paginationHTML += `<button class="btn btn-sm" onclick="loadActivitiesPage(${i})">${i}</button>`;
        }
    }
    
    // 下一页
    if (currentActivitiesPage < totalPages) {
        paginationHTML += `<button class="btn btn-sm" onclick="loadActivitiesPage(${currentActivitiesPage + 1})">下一页</button>`;
    }
    
    paginationHTML += '</div>';
    container.innerHTML = paginationHTML;
}

// 加载指定页的活动数据
function loadActivitiesPage(page) {
    currentActivitiesPage = page;
    loadActivitiesData(false); // 不重置页码
}

// 更新URL参数
function updateURLParams() {
    const url = new URL(window.location);
    const statusFilter = document.getElementById('activity-status-filter').value;
    const keyword = document.getElementById('activity-search-input').value;

    // 更新URL参数
    if (currentActivitiesPage > 1) {
        url.searchParams.set('page', currentActivitiesPage);
    } else {
        url.searchParams.delete('page');
    }

    if (statusFilter) {
        url.searchParams.set('status', statusFilter);
    } else {
        url.searchParams.delete('status');
    }

    if (keyword) {
        url.searchParams.set('keyword', keyword);
    } else {
        url.searchParams.delete('keyword');
    }

    // 更新浏览器历史记录
    window.history.replaceState({}, '', url);
}

// 从URL参数恢复状态
function restoreFromURLParams() {
    const url = new URL(window.location);

    // 恢复页码
    const page = url.searchParams.get('page');
    if (page) {
        currentActivitiesPage = parseInt(page);
    }

    // 恢复状态筛选
    const status = url.searchParams.get('status');
    if (status) {
        document.getElementById('activity-status-filter').value = status;
    }

    // 恢复关键词
    const keyword = url.searchParams.get('keyword');
    if (keyword) {
        document.getElementById('activity-search-input').value = keyword;
    }
}

// 获取活动状态文本
function getActivityStatusText(status) {
    const statusMap = {
        0: '草稿',
        1: '进行中', 
        2: '已结束'
    };
    return statusMap[status] || '未知';
}

// 获取活动状态颜色
function getActivityStatusColor(status) {
    const colorMap = {
        0: '#718096',  // 灰色 - 草稿
        1: '#38a169',  // 绿色 - 进行中
        2: '#e53e3e'   // 红色 - 已结束
    };
    return colorMap[status] || '#718096';
}


// ==================== 活动CRUD操作 ====================

// 显示创建活动模态框
function showCreateActivityModal() {
    const form = document.getElementById('create-activity-form');
    if (form) {
        form.reset();
    }
    
    // 设置默认时间
    const now = new Date();
    const beginTime = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 明天
    const endTime = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30天后
    
    document.getElementById('create-activity-begin-time').value = formatDateTimeLocal(beginTime);
    document.getElementById('create-activity-end-time').value = formatDateTimeLocal(endTime);
    
    showModal('create-activity-modal');
}

// 编辑活动
function editActivity(activityId) {
    fetch(`/admin/api/activities/${activityId}`)
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                const activity = data.data;
                
                document.getElementById('edit-activity-id').value = activity.activity_id;
                document.getElementById('edit-activity-name').value = activity.activity_name;
                document.getElementById('edit-activity-description').value = activity.description || '';
                document.getElementById('edit-activity-status').value = activity.status;
                document.getElementById('edit-activity-priority').value = activity.priority;
                document.getElementById('edit-activity-begin-time').value = formatDateTimeLocal(new Date(activity.begin_time));
                document.getElementById('edit-activity-end-time').value = formatDateTimeLocal(new Date(activity.end_time));
                
                showModal('edit-activity-modal');
            } else {
                showMessage('获取活动详情失败: ' + data.msg, 'error');
            }
        })
        .catch(error => {
            console.error('获取活动详情失败:', error);
            showMessage('获取活动详情失败', 'error');
        });
}

// 删除活动
function deleteActivity(activityId) {
    if (!confirm('确定要删除这个活动吗？此操作不可恢复。')) {
        return;
    }
    
    fetch(`/admin/api/activities/${activityId}`, {
        method: 'DELETE'
    })
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                showMessage('活动删除成功', 'success');
                loadActivitiesData();
            } else {
                showMessage('删除活动失败: ' + data.msg, 'error');
            }
        })
        .catch(error => {
            console.error('删除活动失败:', error);
            showMessage('删除活动失败', 'error');
        });
}

// ==================== 工具函数 ====================

// 格式化日期时间为本地格式
function formatDateTimeLocal(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

// 格式化日期时间显示
function formatDateTime(dateStr) {
    return new Date(dateStr).toLocaleString('zh-CN');
}

// ==================== AMS链接解析功能 ====================

// 显示AMS链接导入模态框
function showImportAmsModal() {
    const form = document.getElementById('import-ams-form');
    if (form) {
        form.reset();
    }
    showModal('import-ams-modal');
}

// AMS链接解析器
class AMSParser {
    constructor(amsUrl) {
        this.amsUrl = amsUrl;
        this.parsedUrl = null;
        this.amsData = null;

        try {
            this.parsedUrl = new URL(amsUrl);
        } catch (e) {
            throw new Error('无效的AMS链接格式');
        }
    }

    // 从AMS描述文件URL获取活动数据
    async fetchAMSData() {
        try {
            // 检查是否是AMS描述文件链接
            if (this.amsUrl.includes('gmi_act.desc.js')) {
                const response = await fetch(this.amsUrl);
                const text = await response.text();

                // 解析JavaScript对象
                const jsonStr = text.trim();
                this.amsData = JSON.parse(jsonStr);
                return this.amsData;
            }

            // 如果不是描述文件，尝试构造描述文件URL
            const activityId = this.extractActivityIdFromUrl();
            if (activityId) {
                const descUrl = `https://bns.qq.com/comm-htdocs/js/ams/actDesc/291/${activityId}/gmi_act.desc.js`;
                try {
                    const response = await fetch(descUrl);
                    const text = await response.text();
                    this.amsData = JSON.parse(text.trim());
                    return this.amsData;
                } catch (e) {
                    console.warn('无法获取AMS描述文件，使用URL解析模式');
                }
            }

            return null;
        } catch (error) {
            console.warn('获取AMS数据失败:', error);
            return null;
        }
    }

    // 从URL中提取活动ID
    extractActivityIdFromUrl() {
        // 方法1: 从查询参数中提取
        const queryParams = this.parsedUrl.searchParams;
        const activityIdParams = ['activityId', 'activity_id', 'iActivityId', 'id'];
        for (const param of activityIdParams) {
            const value = queryParams.get(param);
            if (value && /^\d{6,}$/.test(value)) {
                return parseInt(value);
            }
        }

        // 方法2: 从路径中提取
        const pathMatch = this.amsUrl.match(/\/(\d{6,})\//);
        if (pathMatch) {
            return parseInt(pathMatch[1]);
        }

        // 方法3: 从整个URL中提取6位以上数字
        const numberMatch = this.amsUrl.match(/\d{6,}/);
        if (numberMatch) {
            return parseInt(numberMatch[0]);
        }

        return null;
    }

    // 提取活动ID
    extractActivityId() {
        if (this.amsData && this.amsData.iActivityId) {
            return parseInt(this.amsData.iActivityId);
        }

        const urlId = this.extractActivityIdFromUrl();
        if (urlId) {
            return urlId;
        }

        throw new Error('无法从AMS链接中提取活动ID');
    }

    // 提取活动名称
    extractActivityName() {
        if (this.amsData && this.amsData.sActivityName) {
            // 解码Unicode转义字符
            return this.amsData.sActivityName.replace(/\\u([0-9a-fA-F]{4})/g, (_, code) => {
                return String.fromCharCode(parseInt(code, 16));
            });
        }

        const queryParams = this.parsedUrl.searchParams;
        const nameParams = ['activityName', 'activity_name', 'sActivityName', 'name', 'title'];
        for (const param of nameParams) {
            const value = queryParams.get(param);
            if (value) {
                return decodeURIComponent(value);
            }
        }

        return '';
    }

    // 提取流程信息
    extractFlows() {
        const flows = [];

        // 如果有AMS数据，从中提取流程信息
        if (this.amsData && this.amsData.ide && this.amsData.ide.flows) {
            const ideFlows = this.amsData.ide.flows;
            const tokens = this.amsData.ide.tokens || {};

            let sortOrder = 1;
            for (const [flowId, flowData] of Object.entries(ideFlows)) {
                // 解码流程名称中的Unicode字符
                const flowName = flowData.sName ? flowData.sName.replace(/\\u([0-9a-fA-F]{4})/g, (_, code) => {
                    return String.fromCharCode(parseInt(code, 16));
                }) : `流程_${flowId}`;

                // 查找对应的token
                let ideToken = 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX';
                for (const [token, tokenFlowId] of Object.entries(tokens)) {
                    if (tokenFlowId === flowId) {
                        ideToken = token;
                        break;
                    }
                }

                // 构建参数对象
                const parameters = {};
                if (flowData.inputParams && Array.isArray(flowData.inputParams)) {
                    flowData.inputParams.forEach(param => {
                        if (param.key && param.key.trim()) {
                            parameters[param.key] = {
                                name: param.desc || param.key,
                                description: param.desc || '',
                                required: false,
                                default_value: ''
                            };
                        }
                    });
                }

                flows.push({
                    flow_id: flowId,
                    flow_name: flowName,
                    ide_token: ideToken,
                    account_type: flowData.sAccountType || '7',
                    flow_type: flowData.iType ? flowData.iType.toString() : '1',
                    custom: flowData.iCustom ? flowData.iCustom.toString() : '1',
                    parameters: JSON.stringify(parameters),
                    status: 1,
                    sort_order: sortOrder++
                });
            }
        }

        // 如果没有从ide.flows中获取到流程，尝试从flows字段获取
        if (flows.length === 0 && this.amsData && this.amsData.flows) {
            const flowDetails = this.amsData.flows;
            const tokens = this.amsData.ide?.tokens || {};

            let sortOrder = 1;
            for (const [flowKey, flowDetail] of Object.entries(flowDetails)) {
                // 解码流程名称中的Unicode字符
                const flowName = flowDetail.sFlowName ? flowDetail.sFlowName.replace(/\\u([0-9a-fA-F]{4})/g, (_, code) => {
                    return String.fromCharCode(parseInt(code, 16));
                }) : `流程_${flowKey}`;

                // 从mapid获取流程ID
                const flowId = flowDetail.mapid ? flowDetail.mapid.toString() : flowKey;

                // 查找对应的token
                let ideToken = 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX';
                for (const [token, tokenFlowId] of Object.entries(tokens)) {
                    if (tokenFlowId === flowId) {
                        ideToken = token;
                        break;
                    }
                }

                flows.push({
                    flow_id: flowId,
                    flow_name: flowName,
                    ide_token: ideToken,
                    account_type: flowDetail.sAccountType || '7',
                    flow_type: '1',
                    custom: '1',
                    parameters: '{}',
                    status: 1,
                    sort_order: sortOrder++
                });
            }
        }

        // 如果仍然没有流程，尝试从URL参数获取
        if (flows.length === 0) {
            const queryParams = this.parsedUrl.searchParams;

            // 方法1: 从flowIds参数提取
            const flowIds = queryParams.get('flowIds');
            if (flowIds) {
                const ids = flowIds.split(',');
                ids.forEach((id, index) => {
                    flows.push({
                        flow_id: id.trim(),
                        flow_name: `流程_${index + 1}`,
                        ide_token: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
                        account_type: '7',
                        flow_type: '1',
                        custom: '1',
                        parameters: '{}',
                        status: 1,
                        sort_order: index + 1
                    });
                });
            }

            // 方法2: 检查是否有特定的流程参数
            const commonFlows = [
                { param: 'initFlow', name: '初始化', id: '420405' },
                { param: 'queryFlow', name: '查询绑定', id: '420411' },
                { param: 'bindFlow', name: '提交绑定', id: '420412' },
                { param: 'lotteryFlow', name: '抽奖', id: '420402' },
                { param: 'rewardFlow', name: '领取奖励', id: '420403' }
            ];

            commonFlows.forEach((flowInfo) => {
                const flowId = queryParams.get(flowInfo.param);
                if (flowId) {
                    flows.push({
                        flow_id: flowId,
                        flow_name: flowInfo.name,
                        ide_token: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
                        account_type: '7',
                        flow_type: '1',
                        custom: '1',
                        parameters: '{}',
                        status: 1,
                        sort_order: flows.length + 1
                    });
                }
            });
        }

        return flows;
    }

    // 解析完整的活动信息
    async parseActivity() {
        // 首先尝试获取AMS数据
        await this.fetchAMSData();

        const activityId = this.extractActivityId();
        const activityName = this.extractActivityName() || `导入的活动_${activityId}`;
        const flows = this.extractFlows();

        // 从AMS数据中提取时间信息
        let beginTime = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(); // 默认明天
        let endTime = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(); // 默认30天后

        if (this.amsData) {
            if (this.amsData.dtBeginTime) {
                try {
                    beginTime = new Date(this.amsData.dtBeginTime).toISOString();
                } catch (e) {
                    console.warn('解析开始时间失败:', e);
                }
            }
            if (this.amsData.dtEndTime) {
                try {
                    endTime = new Date(this.amsData.dtEndTime).toISOString();
                } catch (e) {
                    console.warn('解析结束时间失败:', e);
                }
            }
        }

        return {
            activity_id: activityId,
            activity_name: activityName,
            status: 0, // 草稿状态
            priority: 5,
            begin_time: beginTime,
            end_time: endTime,
            flows: flows
        };
    }
}

// ==================== 流程管理功能 ====================

// 流程管理
function manageFlows(activityId, activityName) {
    currentActivityId = activityId;
    currentActivityName = activityName;

    document.getElementById('flow-activity-name').textContent = activityName;

    // 加载流程数据
    loadFlowsData(activityId);
    showModal('manage-flows-modal');
}

// 加载流程数据
function loadFlowsData(activityId) {
    fetch(`/admin/api/flows?activity_id=${activityId}`)
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                currentFlows = data.data || [];
                renderFlowsTable(currentFlows);
            } else {
                showMessage('加载流程数据失败: ' + data.msg, 'error');
            }
        })
        .catch(error => {
            console.error('加载流程数据失败:', error);
            showMessage('加载流程数据失败', 'error');
        });
}

// 渲染流程表格
function renderFlowsTable(flows) {
    const tbody = document.getElementById('flows-table-body');

    // 添加新建流程按钮
    addCreateFlowButton(flows && flows.length > 0);

    if (!flows || flows.length === 0) {
        tbody.innerHTML = '';
        return;
    }

    // 按sort_order排序
    const sortedFlows = [...flows].sort((a, b) => a.sort_order - b.sort_order);

    tbody.innerHTML = sortedFlows.map((flow) => `
        <tr draggable="true" data-flow-id="${flow.id}" data-sort-order="${flow.sort_order}"
            style="cursor: move; transition: background-color 0.2s ease;"
            ondragstart="handleDragStart(event)"
            ondragover="handleDragOver(event)"
            ondrop="handleDrop(event)"
            onmouseover="this.style.backgroundColor='#f8f9fa';"
            onmouseout="this.style.backgroundColor='';">
            <td style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">
                <span style="color: #718096; margin-right: 8px;">⋮⋮</span>
                <span style="font-family: monospace; font-weight: 500;">${flow.flow_id}</span>
            </td>
            <td style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">${flow.flow_name}</td>
            <td style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">
                ${flow.group && flow.group > 0 ?
                    `<span style="background: #e2e8f0; color: #4a5568; padding: 2px 8px; border-radius: 12px; font-size: 12px;">组${flow.group}</span>` :
                    '<span style="color: #a0aec0;">无分组</span>'
                }
            </td>
            <td style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">
                <button class="btn btn-sm" onclick="editFlow(${flow.id})" style="margin-right: 8px;">编辑</button>
                <button class="btn btn-sm" onclick="testFlow(${flow.id})" style="background: #f59e0b; color: white; border: none;">测试</button>
            </td>
        </tr>
    `).join('');
}

// 添加新建流程按钮
function addCreateFlowButton(hasFlows) {
    const container = document.getElementById('create-flow-container');
    if (!container) return;

    if (hasFlows) {
        // 有流程时显示+按钮
        container.innerHTML = `
            <div onclick="showCreateFlowModal()" style="
                padding: 10px;
                text-align: center;
                border: 2px dashed #e2e8f0;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.2s ease;
                background: #fafafa;
                color: #718096;
            " onmouseover="this.style.borderColor='#cbd5e0'; this.style.background='#f1f5f9'; this.style.color='#4a5568';"
               onmouseout="this.style.borderColor='#e2e8f0'; this.style.background='#fafafa'; this.style.color='#718096';">
                <div style="font-size: 20px; margin-bottom: 5px;">+</div>
                <div style="font-size: 14px;">新建流程</div>
            </div>
        `;
    } else {
        // 无流程时显示较小的点击区域
        container.innerHTML = `
            <div onclick="showCreateFlowModal()" style="
                padding: 30px 20px;
                text-align: center;
                color: #718096;
                border: 2px dashed #e2e8f0;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.2s ease;
                background: #f8f9fa;
                margin-top: 20px;
            " onmouseover="this.style.borderColor='#cbd5e0'; this.style.background='#f1f5f9';"
               onmouseout="this.style.borderColor='#e2e8f0'; this.style.background='#f8f9fa';">
                <div style="font-weight: 600; margin-bottom: 6px; font-size: 16px; color: #4a5568;">暂无流程配置</div>
                <div style="font-size: 13px; color: #a0aec0;">点击此处创建第一个流程</div>
            </div>
        `;
    }
}

// 复制当前流程
function copyCurrentFlow() {
    if (!window.currentEditingFlowId) {
        showMessage('没有可复制的流程', 'error');
        return;
    }

    const flow = currentFlows.find(f => f.id === window.currentEditingFlowId);
    if (!flow) {
        showMessage('流程不存在', 'error');
        return;
    }

    // 构建复制数据
    const copyData = {
        activity_id: currentActivityId || flow.activity_id, // 确保使用当前活动ID
        flow_id: 0, // 新流程ID由数据库自动生成
        flow_name: flow.flow_name + '_副本',
        group: flow.group || 0,
        ide_token: flow.ide_token,
        account_type: flow.account_type,
        flow_type: flow.flow_type,
        custom: flow.custom,
        status: 0, // 新复制的流程默认为禁用状态
        sort_order: flow.sort_order || 0,
        parameters: flow.parameters || '{}'
    };

    // 发送到后端创建新流程
    fetch('/admin/api/flows', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(copyData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 0) {
            showMessage('流程复制成功', 'success');
            loadFlows(currentActivityId);
        } else {
            showMessage('流程复制失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('复制流程失败:', error);
        showMessage('复制流程失败', 'error');
    });
}

// 切换流程启用状态
function toggleFlowStatus() {
    const toggleBtn = document.getElementById('toggle-flow-btn');

    // 如果是新建流程（没有currentEditingFlowId），只切换UI状态
    if (!window.currentEditingFlowId) {
        // 新建流程时的状态切换
        const currentStatus = window.currentFlowStatus || 1;
        const newStatus = currentStatus === 1 ? 0 : 1;
        window.currentFlowStatus = newStatus;

        if (newStatus === 1) {
            toggleBtn.textContent = '禁用流程';
            toggleBtn.style.background = '#ef4444';
        } else {
            toggleBtn.textContent = '启用流程';
            toggleBtn.style.background = '#10b981';
        }
        return;
    }

    const flow = currentFlows.find(f => f.id === window.currentEditingFlowId);
    if (!flow) {
        showMessage('流程不存在', 'error');
        return;
    }

    const newStatus = flow.status === 1 ? 0 : 1;
    const statusText = newStatus === 1 ? '启用' : '禁用';

    fetch(`/admin/api/flows/${flow.id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 200) {
            showMessage(`流程${statusText}成功`, 'success');

            // 更新本地数据
            flow.status = newStatus;

            // 更新按钮状态
            if (newStatus === 1) {
                toggleBtn.textContent = '禁用流程';
                toggleBtn.style.background = '#ef4444';
            } else {
                toggleBtn.textContent = '启用流程';
                toggleBtn.style.background = '#10b981';
            }

            // 刷新流程列表
            loadFlowsData(currentActivityId);
        } else {
            showMessage(`${statusText}流程失败: ` + data.msg, 'error');
        }
    })
    .catch(error => {
        console.error(`${statusText}流程失败:`, error);
        showMessage(`${statusText}流程失败`, 'error');
    });
}

// 测试当前流程
function testCurrentFlow() {
    if (!window.currentEditingFlowId) {
        showMessage('没有可测试的流程', 'error');
        return;
    }

    // 关闭编辑模态框
    closeModal('flow-edit-modal');

    // 调用现有的测试函数
    testFlow(window.currentEditingFlowId);
}

// ==================== 拖拽排序功能 ====================

let draggedElement = null;

// 拖拽开始
function handleDragStart(event) {
    draggedElement = event.target.closest('tr');
    event.dataTransfer.effectAllowed = 'move';
    event.dataTransfer.setData('text/html', draggedElement.outerHTML);
    draggedElement.style.opacity = '0.5';
}

// 拖拽经过
function handleDragOver(event) {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';

    const targetRow = event.target.closest('tr');
    if (targetRow && targetRow !== draggedElement) {
        targetRow.style.backgroundColor = '#e6f3ff';
    }
}

// 拖拽放下
function handleDrop(event) {
    event.preventDefault();

    const targetRow = event.target.closest('tr');
    if (targetRow && targetRow !== draggedElement && draggedElement) {
        const tbody = targetRow.parentNode;
        const draggedIndex = Array.from(tbody.children).indexOf(draggedElement);
        const targetIndex = Array.from(tbody.children).indexOf(targetRow);

        if (draggedIndex < targetIndex) {
            tbody.insertBefore(draggedElement, targetRow.nextSibling);
        } else {
            tbody.insertBefore(draggedElement, targetRow);
        }

        // 更新排序
        updateFlowOrder();
    }

    // 清理样式
    if (draggedElement) {
        draggedElement.style.opacity = '';
        draggedElement = null;
    }

    // 清理所有行的背景色
    const allRows = document.querySelectorAll('#flows-table-body tr');
    allRows.forEach(row => {
        row.style.backgroundColor = '';
    });
}

// 更新流程排序
function updateFlowOrder() {
    const tbody = document.getElementById('flows-table-body');
    const rows = Array.from(tbody.children);

    const updates = rows.map((row, index) => {
        const flowId = row.dataset.flowId;
        return {
            id: parseInt(flowId),
            sort_order: index + 1
        };
    });

    // 逐个更新流程排序，只发送sort_order字段
    let successCount = 0;
    let failCount = 0;

    const updatePromises = updates.map(update => {
        const flow = currentFlows.find(f => f.id === update.id);
        if (!flow) {
            failCount++;
            return Promise.resolve();
        }

        // 构造完整的更新请求，保留原有数据
        const updateData = {
            flow_name: flow.flow_name,
            ide_token: flow.ide_token,
            account_type: flow.account_type,
            flow_type: flow.flow_type,
            custom: flow.custom,
            parameters: typeof flow.parameters === 'string' ? flow.parameters : JSON.stringify(flow.parameters || {}),
            status: flow.status,
            sort_order: update.sort_order  // 只更新排序
        };

        return fetch(`/admin/api/flows/${update.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(updateData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                successCount++;
                // 更新内存中的数据
                flow.sort_order = update.sort_order;
            } else {
                failCount++;
                console.error(`更新流程 ${flow.flow_name} 排序失败:`, data.msg);
            }
        })
        .catch(error => {
            failCount++;
            console.error(`更新流程 ${flow.flow_name} 排序失败:`, error);
        });
    });

    Promise.all(updatePromises).then(() => {
        if (successCount > 0) {
            showMessage(`流程排序已更新 (成功: ${successCount}个${failCount > 0 ? `, 失败: ${failCount}个` : ''})`, 'success');
        } else {
            showMessage('更新排序失败', 'error');
            loadFlowsData(currentActivityId);
        }
    });
}

// 切换流程状态
function toggleFlowStatus(flowId) {
    const flow = currentFlows.find(f => f.id === flowId);
    if (!flow) {
        showMessage('流程不存在', 'error');
        return;
    }

    const newStatus = flow.status === 1 ? 0 : 1;

    fetch(`/admin/api/flows/${flowId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 200) {
            showMessage(`流程已${newStatus === 1 ? '启用' : '禁用'}`, 'success');
            // 重新加载流程数据
            loadFlowsData(currentActivityId);
        } else {
            showMessage('状态切换失败: ' + data.msg, 'error');
        }
    })
    .catch(error => {
        console.error('状态切换失败:', error);
        showMessage('状态切换失败', 'error');
    });
}

// 显示创建流程模态框
function showCreateFlowModal() {
    document.getElementById('flow-edit-form').reset();
    document.getElementById('flow-edit-title').textContent = '新建流程';
    document.getElementById('flow-activity-id').value = currentActivityId;
    document.getElementById('flow-db-id').value = '';

    // 清空参数列表
    clearParametersList();

    showModal('flow-edit-modal');

    // 等待模态框加载完成后再设置按钮
    setTimeout(() => {
        // 隐藏编辑相关按钮，但保留状态按钮
        const copyBtn = document.getElementById('copy-flow-btn');
        const testBtn = document.getElementById('test-flow-btn');
        const toggleBtn = document.getElementById('toggle-flow-btn');

        if (copyBtn) copyBtn.style.display = 'none';
        if (testBtn) testBtn.style.display = 'none';

        // 显示状态按钮，设置为新建流程的默认状态
        if (toggleBtn) {
            toggleBtn.style.display = 'inline-block';
            toggleBtn.textContent = '启用流程';
            toggleBtn.style.background = '#10b981';
            // 设置新建流程的默认状态为启用
            window.currentFlowStatus = 1;
        }
    }, 100);
}

// 测试流程
function testFlow(flowId) {
    const flow = currentFlows.find(f => f.id === flowId);
    if (!flow) {
        showMessage('流程不存在', 'error');
        return;
    }

    // 设置当前测试的流程ID，用于参数缓存
    window.currentTestingFlowId = flowId;

    // 检查并获取测试参数
    showSetTestTokenModal(currentActivityId, flow).then(testParams => {
        if (!testParams) {
            return; // 用户取消或设置失败
        }

        // 执行流程测试
        executeFlowTest(flowId, testParams);
    });
}

// 关闭模态框
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        // 检查是否是动态创建的模态框（通过检查是否有父元素是body且没有在HTML中预定义）
        const isDynamic = modal.parentElement === document.body &&
                         !document.querySelector(`template [id="${modalId}"]`) &&
                         !document.querySelector(`script[type="text/html"] [id="${modalId}"]`);

        if (isDynamic) {
            // 动态创建的模态框，直接移除
            modal.remove();
        } else {
            // HTML中预定义的模态框，只隐藏
            modal.style.display = 'none';
            modal.classList.remove('show');
        }
    }
}

// 显示设置测试token的模态框
function showSetTestTokenModal(activityId, flow = null) {
    return new Promise((resolve) => {
        // 默认参数
        let testParams = {
            access_token: '',
            acctype: 'qc'
        };

        // 尝试从缓存加载参数
        loadCachedParams(activityId, testParams);

        showTokenModalWithValue(activityId, testParams, resolve, flow);
    });
}

// 根据流程ID加载缓存参数
function loadCachedParams(activityId, testParams, flowId = null) {
    // accesstoken仍然按账号类型缓存，其他参数按流程ID缓存
    const tokenCacheKey = `test_token_activity_${activityId}_${testParams.acctype}`;
    const tokenCache = localStorage.getItem(tokenCacheKey);

    if (tokenCache) {
        try {
            const parsed = JSON.parse(tokenCache);
            testParams.access_token = parsed.access_token || '';
        } catch (error) {
            console.error('解析token缓存失败:', error);
        }
    }

    // 如果有流程ID，加载流程特定的参数缓存
    if (flowId) {
        const paramsCacheKey = `flow_params_${flowId}`;
        const paramsCache = localStorage.getItem(paramsCacheKey);

        if (paramsCache) {
            try {
                const parsed = JSON.parse(paramsCache);
                // 合并流程特定参数
                Object.assign(testParams, parsed);
            } catch (error) {
                console.error('解析流程参数缓存失败:', error);
            }
        }
    }
}

// 显示token模态框的辅助函数
function showTokenModalWithValue(activityId, testParams, resolve, flow = null) {
    // 根据当前账号类型加载对应的缓存
    loadCachedParams(activityId, testParams);

    const modalHtml = `
        <div class="modal-content" style="width: 600px; max-width: 90vw;">
            <button onclick="closeModal('set-test-token-modal')" class="modal-close-btn">×</button>
            <div class="modal-header">
                <h3>测试流程请求</h3>
            </div>
            <div class="modal-body">
                <div style="margin-bottom: 15px; padding: 12px; background: #fef5e7; border-radius: 6px; border-left: 4px solid #f59e0b;">
                    <p style="margin: 0; color: #92400e; font-size: 14px;">
                        <strong>说明：</strong> 设置测试流程所需的参数信息
                    </p>
                </div>
                <form id="set-test-token-form">
                    <div class="form-group">
                        <label for="test-acctype">账号类型</label>
                        <select id="test-acctype" style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;" onchange="onAccountTypeChange(${activityId})">
                            <option value="qc" ${(testParams.acctype || 'qc') === 'qc' ? 'selected' : ''}>QQ</option>
                            <option value="wx" ${(testParams.acctype || 'qc') === 'wx' ? 'selected' : ''}>微信</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="test-access-token">Access Token *</label>
                        <input type="text" id="test-access-token" value="${testParams.access_token || ''}" required
                               placeholder="请输入测试用的access_token"
                               oninput="validateAccessToken(this); updateSubmitButtonState();"
                               onblur="validateAccessToken(this); updateSubmitButtonState();"
                               style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                    </div>

                    <!-- 流程必需参数 -->
                    <div class="form-group" id="test-required-params-group" style="display: none;">
                        <div id="test-required-parameters-list">
                            <!-- 必需参数行将在这里显示 -->
                        </div>
                    </div>

                    <!-- 测试额外参数 -->
                    <div class="form-group">
                        <label>测试额外参数</label>
                        <div id="test-parameters-container">
                            <div id="test-parameters-list">
                                <!-- 额外参数行将在这里动态添加 -->
                            </div>
                        </div>
                    </div>

                    <div class="form-actions" style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                        <button type="button" id="cancel-test-token-btn" class="btn" style="background: #6b7280;">取消</button>
                        <button type="submit" class="btn">开始测试</button>
                    </div>
                </form>
            </div>
        </div>
    `;

    // 创建并显示模态框
    const modal = document.createElement('div');
    modal.id = 'set-test-token-modal';
    modal.className = 'modal';
    modal.style.display = 'flex';
    modal.innerHTML = modalHtml;
    document.body.appendChild(modal);

    // 初始化自定义参数列表
    initTestParametersList(testParams);

    // 显示流程必需参数
    if (flow) {
        showRequiredParametersForTest(flow);
    }

    // 初始化提交按钮状态
    setTimeout(() => {
        updateSubmitButtonState();
    }, 100);

    // 处理模态框关闭的函数
    const handleModalClose = function(event) {
        if (event.target === modal) {
            closeModal('set-test-token-modal');
            window.removeEventListener('click', handleModalClose);
            resolve(null);
        }
    };

    // 绑定取消按钮事件
    document.getElementById('cancel-test-token-btn').addEventListener('click', function() {
        closeModal('set-test-token-modal');
        window.removeEventListener('click', handleModalClose);
        resolve(null);
    });

    // 绑定表单提交事件
    document.getElementById('set-test-token-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const accessToken = document.getElementById('test-access-token').value.trim();
        const acctype = document.getElementById('test-acctype').value;

        if (!accessToken) {
            showMessage('请输入Access Token', 'error');
            return;
        }

        // 验证流程必需参数
        const requiredParamInputs = document.querySelectorAll('#test-required-parameters-list .test-required-param');
        const missingParams = [];

        requiredParamInputs.forEach(input => {
            const key = input.getAttribute('data-key');
            const value = input.value.trim();
            const label = input.previousElementSibling ? input.previousElementSibling.textContent.replace(' *', '') : key;

            if (!value) {
                missingParams.push(label);
                input.style.borderColor = '#ef4444';
            } else {
                input.style.borderColor = '#e2e8f0';
            }
        });

        if (missingParams.length > 0) {
            showMessage(`请填写以下必需参数: ${missingParams.join(', ')}`, 'error');
            return;
        }

        // 收集流程必需参数（重用之前查询的元素）
        const requiredParams = {};
        requiredParamInputs.forEach(input => {
            const key = input.getAttribute('data-key');
            const value = input.value.trim();
            if (key && value) {
                requiredParams[key] = value;
            }
        });

        // 收集测试额外参数（处理重复键）
        const extraParams = {};
        const usedKeys = new Set(Object.keys(requiredParams)); // 记录已使用的键
        const paramRows = document.querySelectorAll('#test-parameters-list .test-parameter-row');

        paramRows.forEach(row => {
            const keyInput = row.querySelector('.test-param-key');
            const valueInput = row.querySelector('.test-param-value');
            if (keyInput && valueInput && keyInput.value.trim() && valueInput.value.trim()) {
                const key = keyInput.value.trim();
                const value = valueInput.value.trim();

                // 检查键是否重复
                if (usedKeys.has(key)) {
                    // 如果键重复，显示警告但仍然使用最后一个值
                    console.warn(`参数键 "${key}" 重复，使用最后一个值: ${value}`);
                }

                extraParams[key] = value;
                usedKeys.add(key);
            }
        });

        // 合并所有测试参数（前端负责参数合并）
        const mergedTestParams = { ...requiredParams, ...extraParams };

        // 构建发送给后端的测试参数
        const testParams = {
            access_token: accessToken,
            acctype: acctype,
            test_params: mergedTestParams
        };

        try {
            // accesstoken按账号类型缓存
            const tokenCacheKey = `test_token_activity_${activityId}_${acctype}`;
            localStorage.setItem(tokenCacheKey, JSON.stringify({
                access_token: accessToken,
                acctype: acctype
            }));

            // 如果有流程ID，按流程ID缓存参数（保持向后兼容的格式）
            if (window.currentTestingFlowId) {
                const paramsCacheKey = `flow_params_${window.currentTestingFlowId}`;
                const flowParams = {
                    required_params: requiredParams,
                    extra_params: extraParams
                };
                localStorage.setItem(paramsCacheKey, JSON.stringify(flowParams));
            }

            closeModal('set-test-token-modal');
            window.removeEventListener('click', handleModalClose);
            resolve(testParams);
        } catch (error) {
            console.error('保存测试参数失败:', error);
            showMessage('保存测试参数失败', 'error');
        }
    });

    // 处理模态框点击外部关闭
    window.addEventListener('click', handleModalClose);
}

// 账号类型切换时的处理函数
function onAccountTypeChange(activityId) {
    const acctype = document.getElementById('test-acctype').value;
    const accessTokenInput = document.getElementById('test-access-token');

    // 根据新的账号类型加载缓存的参数
    const cacheKey = `test_params_activity_${activityId}_${acctype}`;
    const cachedParams = localStorage.getItem(cacheKey);

    if (cachedParams) {
        try {
            const parsed = JSON.parse(cachedParams);
            accessTokenInput.value = parsed.access_token || '';

            // 重新初始化额外参数列表
            initTestParametersList(parsed);

            // 如果有缓存的必需参数，恢复它们的值
            if (parsed.required_params) {
                const requiredParamInputs = document.querySelectorAll('.test-required-param');
                requiredParamInputs.forEach(input => {
                    const key = input.getAttribute('data-key');
                    if (key && parsed.required_params[key]) {
                        input.value = parsed.required_params[key];
                        validateRequiredParam(input);
                    }
                });
            }

            // 验证Access Token
            validateAccessToken(accessTokenInput);
        } catch (error) {
            console.error('解析缓存参数失败:', error);
            accessTokenInput.value = '';
            initTestParametersList({});
        }
    } else {
        // 如果没有缓存，清空输入框和参数列表
        accessTokenInput.value = '';
        initTestParametersList({});
    }
}

// 执行流程测试
function executeFlowTest(flowId, testParams) {
    const testBtn = document.querySelector(`button[onclick="testFlow(${flowId})"]`);
    const originalText = testBtn.textContent;
    testBtn.textContent = '测试中...';
    testBtn.disabled = true;

    fetch(`/admin/api/flows/${flowId}/test`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(testParams)
    })
    .then(response => response.json())
    .then(data => {
        testBtn.textContent = originalText;
        testBtn.disabled = false;

        if (data.code == 200) {
            showFlowTestResult(flowId, data.data, true);
        } else {
            showFlowTestResult(flowId, { error: data.msg }, false);
        }
    })
    .catch(error => {
        testBtn.textContent = originalText;
        testBtn.disabled = false;
        console.error('流程测试失败:', error);
        showFlowTestResult(flowId, { error: error.message }, false);
    });
}

// 显示流程测试结果
function showFlowTestResult(flowId, result, success) {
    const flow = currentFlows.find(f => f.id === flowId);
    const flowName = flow ? flow.flow_name : `流程${flowId}`;

    // 检查结果中的iRet字段，如果不等于0则认为是错误
    let isResultSuccess = success;
    if (success && result && typeof result.iRet !== 'undefined' && result.iRet !== 0) {
        isResultSuccess = false;
    }

    const modalHtml = `
        <div class="modal-content" style="width: 900px; max-width: 95vw;">
            <button onclick="closeModal('flow-test-result-modal')" class="modal-close-btn">×</button>
            <div class="modal-header">
                <h3>流程测试结果 - ${flowName}</h3>
            </div>
            <div class="modal-body">
                <pre style="background: #ffffff; padding: 15px; border-radius: 6px; border: 1px solid #e5e7eb; overflow-x: auto; font-size: 12px; margin: 0;">${JSON.stringify(result, null, 2)}</pre>

                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    ${isResultSuccess ? '<button onclick="closeModal(\'flow-test-result-modal\'); showMessage(\'测试成功，可以将活动状态改为进行中\', \'info\');" class="btn" style="background: #10b981;">确认无误</button>' : ''}
                </div>
            </div>
        </div>
    `;

    // 创建并显示模态框
    const modal = document.createElement('div');
    modal.id = 'flow-test-result-modal';
    modal.className = 'modal';
    modal.style.display = 'flex';
    modal.innerHTML = modalHtml;
    document.body.appendChild(modal);
}

// 编辑流程
function editFlow(flowId) {
    const flow = currentFlows.find(f => f.id === flowId);
    if (!flow) {
        showMessage('流程不存在', 'error');
        return;
    }

    document.getElementById('flow-edit-title').textContent = '编辑流程';
    document.getElementById('flow-activity-id').value = currentActivityId;
    document.getElementById('flow-db-id').value = flow.id;
    document.getElementById('flow-id').value = flow.flow_id;
    document.getElementById('flow-name').value = flow.flow_name;
    document.getElementById('flow-group').value = flow.group || 0;
    document.getElementById('ide-token').value = flow.ide_token;
    document.getElementById('account-type').value = flow.account_type;
    document.getElementById('flow-type').value = flow.flow_type;
    document.getElementById('custom').value = flow.custom;

    // 加载参数配置
    loadParametersFromFlow(flow);

    // 存储当前编辑的流程ID
    window.currentEditingFlowId = flow.id;

    showModal('flow-edit-modal');

    // 等待模态框加载完成后再设置按钮
    setTimeout(() => {
        // 显示编辑相关按钮
        const copyBtn = document.getElementById('copy-flow-btn');
        const toggleBtn = document.getElementById('toggle-flow-btn');
        const testBtn = document.getElementById('test-flow-btn');

        if (copyBtn) copyBtn.style.display = 'inline-block';
        if (testBtn) testBtn.style.display = 'inline-block';

        if (toggleBtn) {
            toggleBtn.style.display = 'inline-block';
            // 更新启用/禁用按钮文本和样式
            if (flow.status === 1) {
                toggleBtn.textContent = '禁用';
                toggleBtn.style.background = '#ef4444';
            } else {
                toggleBtn.textContent = '启用';
                toggleBtn.style.background = '#10b981';
            }
        }
    }, 100);
}

// 删除流程
function deleteFlow(flowId) {
    if (!confirm('确定要删除这个流程吗？此操作不可恢复。')) {
        return;
    }

    fetch(`/admin/api/flows/${flowId}`, {
        method: 'DELETE'
    })
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                showMessage('流程删除成功', 'success');
                loadFlowsData(currentActivityId);
            } else {
                showMessage('删除流程失败: ' + data.msg, 'error');
            }
        })
        .catch(error => {
            console.error('删除流程失败:', error);
            showMessage('删除流程失败', 'error');
        });
}

// ==================== 参数管理功能 ====================

// 简化的参数行添加函数
// 逻辑：checked+empty=用户输入, unchecked+value=固定值, unchecked+empty=不需要
function addParameterRow(key = '', name = '', required = false, value = '') {
    const parametersList = document.getElementById('parameters-list');
    if (!parametersList) {
        console.error('找不到parameters-list元素');
        return;
    }

    const emptyMessage = parametersList.querySelector('.parameters-empty');
    if (emptyMessage) {
        emptyMessage.remove();
    }

    // 移除现有的添加按钮
    const existingAddBtn = parametersList.querySelector('.add-parameter-btn');
    if (existingAddBtn) {
        existingAddBtn.remove();
    }

    const rowId = 'param-row-' + Date.now();
    const rowHtml = `
        <div class="parameter-row" id="${rowId}" style="display: grid; grid-template-columns: 1fr 1fr 1fr auto auto; gap: 10px; border: 1px solid #e2e8f0; border-radius: 6px; padding: 15px; margin-bottom: 10px; align-items: end;">
            <div class="form-group">
                <label>参数键</label>
                <input type="text" class="param-key" value="${key}" required>
            </div>
            <div class="form-group">
                <label>参数名称</label>
                <input type="text" class="param-name" value="${name}">
            </div>
            <div class="form-group">
                <label>参数值</label>
                <input type="text" class="param-value" value="${value}" placeholder="填写表示固定请求值，留空表示需要用户输入">
            </div>
            <div class="form-group">
                <label style="display: flex; align-items: center; gap: 5px;">
                <input type="checkbox" class="param-required" ${required ? 'checked' : ''} onchange="updateParametersJson()">
                    必填参数
                </label>
            </div>

            <button type="button" class="remove-btn" onclick="removeParameterRow('${rowId}')">删除</button>
        </div>
    `;

    parametersList.insertAdjacentHTML('beforeend', rowHtml);
    updateParametersJson();
}


// 删除参数行
function removeParameterRow(rowId) {
    const row = document.getElementById(rowId);
    if (row) {
        row.remove();
        updateParametersJson();

        const parametersList = document.getElementById('parameters-list');
        const paramRows = parametersList.querySelectorAll('.parameter-row');

        // 如果没有参数了，显示空状态
        if (paramRows.length === 0) {
            showEmptyParametersMessage();
        } else {
            // 重新添加+按钮
            addUserParameterButton();
        }
    }
}

// 显示空参数消息
function showEmptyParametersMessage() {
    const userParametersList = document.getElementById('user-parameters-list');
    if (userParametersList) {
        userParametersList.innerHTML = `
            <div class="parameters-empty" onclick="addParameterRow()" style="
                padding: 15px 10px;
                text-align: center;
                color: #718096;
                border: 2px dashed #e2e8f0;
                border-radius: 6px;
                cursor: pointer;
                transition: all 0.2s ease;
                background: #f8f9fa;
            " onmouseover="this.style.borderColor='#cbd5e0'; this.style.background='#f1f5f9';"
               onmouseout="this.style.borderColor='#e2e8f0'; this.style.background='#f8f9fa';">
                <div style="font-weight: 500; margin-bottom: 3px;">暂无参数配置</div>
            </div>
        `;
    }
}

// 清空参数列表
function clearParametersList() {
    const fixedParametersList = document.getElementById('fixed-parameters-list');
    const userParametersList = document.getElementById('user-parameters-list');

    if (fixedParametersList) {
        fixedParametersList.innerHTML = '';
    }
    if (userParametersList) {
        userParametersList.innerHTML = '';
    }

    showEmptyParametersMessage();
    updateParametersJson();
}

// 更新隐藏的JSON字段（简化版本）
function updateParametersJson() {
    const paramRows = document.querySelectorAll('#parameters-list .parameter-row');
    const parameters = {};

    paramRows.forEach(row => {
        const key = row.querySelector('.param-key').value.trim();
        const name = row.querySelector('.param-name').value.trim();
        const required = row.querySelector('.param-required') ? row.querySelector('.param-required').checked : false;
        const value = row.querySelector('.param-value') ? row.querySelector('.param-value').value.trim() : '';

        if (key) {
            // 简化逻辑：
            // checked+empty=用户输入, unchecked+value=固定值, unchecked+empty=不需要
            if (required && !value) {
                // 必填且无值：用户输入
                parameters[key] = {
                    name: name || key,
                    type: 'user_input',
                    required: true,
                    description: name || key
                };
            } else if (!required && value) {
                // 非必填但有值：固定值
                parameters[key] = {
                    name: name || key,
                    type: 'fixed',
                    value: value,
                    description: name || key
                };
            }
            // 非必填且无值：不需要此参数，不添加到parameters中
        }
    });

    document.getElementById('parameters-hidden').value = JSON.stringify(parameters);
}

// 从流程数据加载参数
function loadParametersFromFlow(flow) {
    clearParametersList();

    let parameters = {};
    if (flow.parameters) {
        if (typeof flow.parameters === 'string') {
            try {
                parameters = JSON.parse(flow.parameters);
            } catch (e) {
                console.warn('解析参数JSON失败:', e);
                parameters = {};
            }
        } else if (typeof flow.parameters === 'object') {
            parameters = flow.parameters;
        }
    }

    // 如果有参数，添加参数行
    if (Object.keys(parameters).length > 0) {
        Object.entries(parameters).forEach(([key, param]) => {
            addParameterRow(
                key,
                param.name || ''
            );
        });
    }
}

// 监听参数输入变化
function setupParameterListeners() {
    const parametersList = document.getElementById('parameters-list');
    if (parametersList) {
        parametersList.addEventListener('input', updateParametersJson);
    }
}

// ==================== AMS解析结果处理 ====================

// 显示AMS解析结果
function showAMSParseResult(parsedData) {
    closeModal('import-ams-modal');

    const modalHtml = `
        <div class="modal-content" style="width: 900px; max-width: 95vw;">
            <button onclick="closeModal('ams-result-modal')" class="modal-close-btn">×</button>
            <div class="modal-header">
                <h3>解析结果确认</h3>
            </div>
            <div class="modal-body">
                <div style="margin-bottom: 20px;">
                    <h4>活动信息</h4>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
                        <p><strong>活动ID:</strong> ${parsedData.activity_id}</p>
                        <p><strong>活动名称:</strong> ${parsedData.activity_name}</p>
                        <p><strong>开始时间:</strong> ${parsedData.begin_time}</p>
                        <p><strong>结束时间:</strong> ${parsedData.end_time}</p>
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <h4>流程信息 (${parsedData.flows.length} 个)</h4>
                    <div style="max-height: 400px; overflow-y: auto; border: 1px solid #e2e8f0; border-radius: 6px;">
                        ${parsedData.flows.map((flow) => `
                            <div style="border-bottom: 1px solid #f1f5f9; padding: 15px;">
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                                    <div><strong>流程ID:</strong> ${flow.flow_id}</div>
                                    <div><strong>流程名称:</strong> ${flow.flow_name}</div>
                                    <div><strong>账号类型:</strong> ${flow.account_type}</div>
                                    <div><strong>流程类型:</strong> ${flow.flow_type}</div>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <strong>IDE令牌:</strong>
                                    <span style="font-family: monospace; font-size: 12px; background: #f7fafc; padding: 2px 6px; border-radius: 3px;">
                                        ${flow.ide_token}
                                    </span>
                                </div>
                                ${Object.keys(flow.parameters).length > 0 ? `
                                    <div>
                                        <strong>参数 (${Object.keys(flow.parameters).length} 个):</strong>
                                        <div style="margin-top: 8px; background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px;">
                                            ${Object.entries(flow.parameters).map(([key, param]) => `
                                                <div style="margin-bottom: 5px;">
                                                    <span style="font-weight: 500; color: #2d3748;">${key}:</span>
                                                    <span style="color: #4a5568;">${param.name || param.description || '无描述'}</span>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                ` : '<div style="color: #718096; font-style: italic;">无参数</div>'}
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="form-actions" style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" onclick="closeModal('ams-result-modal')" class="btn" style="background: #6b7280;">取消</button>
                    <button type="button" onclick="confirmCreateFromAMS()" class="btn">确认创建活动草稿</button>
                </div>
            </div>
        </div>
    `;

    // 创建并显示模态框
    const modal = document.createElement('div');
    modal.id = 'ams-result-modal';
    modal.className = 'modal';
    modal.style.display = 'flex';
    modal.innerHTML = modalHtml;

    document.body.appendChild(modal);

    // 存储解析数据和原始URL供确认时使用
    window.currentAMSData = parsedData;
    window.currentAMSURL = window.lastParsedAMSURL;
}

// 显示多个AMS解析结果
function showMultipleAMSParseResult(parseResults, failedUrls) {
    closeModal('import-ams-modal');

    let contentHtml = '';

    // 成功解析的活动
    if (parseResults.length > 0) {
        contentHtml += `
            <div style="margin-bottom: 20px;">
                <h4>成功解析的活动 (${parseResults.length} 个)</h4>
                <div style="max-height: 400px; overflow-y: auto; border: 1px solid #e2e8f0; border-radius: 6px;">
                    ${parseResults.map((result) => `
                        <div style="border-bottom: 1px solid #f1f5f9; padding: 15px;">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                                <div><strong>活动ID:</strong> ${result.data.activity_id}</div>
                                <div><strong>活动名称:</strong> ${result.data.activity_name}</div>
                                <div><strong>优先级:</strong> ${result.data.priority}</div>
                                <div><strong>流程数量:</strong> ${result.data.flows.length}</div>
                            </div>
                            <div style="margin-bottom: 10px;">
                                <strong>开始时间:</strong> ${new Date(result.data.begin_time).toLocaleString('zh-CN')}
                            </div>
                            <div style="margin-bottom: 10px;">
                                <strong>结束时间:</strong> ${new Date(result.data.end_time).toLocaleString('zh-CN')}
                            </div>
                            <div style="font-size: 12px; color: #718096; word-break: break-all;">
                                <strong>链接:</strong> ${result.url}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // 解析失败的链接
    if (failedUrls.length > 0) {
        contentHtml += `
            <div style="margin-bottom: 20px;">
                <h4 style="color: #e53e3e;">解析失败的链接 (${failedUrls.length} 个)</h4>
                <div style="max-height: 200px; overflow-y: auto; border: 1px solid #fed7d7; border-radius: 6px; background: #fef5e7;">
                    ${failedUrls.map((failed) => `
                        <div style="border-bottom: 1px solid #fed7d7; padding: 10px;">
                            <div style="font-size: 12px; color: #e53e3e; margin-bottom: 5px;">
                                <strong>错误:</strong> ${failed.error}
                            </div>
                            <div style="font-size: 12px; color: #718096; word-break: break-all;">
                                <strong>链接:</strong> ${failed.url}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    const modalHtml = `
        <div class="modal-content" style="width: 900px; max-width: 95vw;">
            <button onclick="closeModal('ams-result-modal')" class="modal-close-btn">×</button>
            <div class="modal-header">
                <h3>解析结果确认</h3>
            </div>
            <div class="modal-body">
                <div style="margin-bottom: 20px; padding: 15px; background: #e6f3ff; border-radius: 6px; border-left: 4px solid #2b6cb0;">
                    <p style="margin: 0; color: #2d3748;">
                        <strong>解析完成！</strong>
                        成功解析 ${parseResults.length} 个活动${failedUrls.length > 0 ? `，${failedUrls.length} 个链接解析失败` : ''}。
                        点击确认将创建这些活动的草稿。
                    </p>
                </div>
                ${contentHtml}
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button onclick="closeModal('ams-result-modal')" class="btn" style="background: #6b7280;">取消</button>
                    <button onclick="confirmCreateMultipleFromAMS()" class="btn">确认创建 ${parseResults.length} 个活动</button>
                </div>
            </div>
        </div>
    `;

    // 创建并显示模态框
    const modal = document.createElement('div');
    modal.id = 'ams-result-modal';
    modal.className = 'modal';
    modal.style.display = 'flex';
    modal.innerHTML = modalHtml;

    document.body.appendChild(modal);

    // 存储解析数据供确认时使用
    window.currentMultipleAMSData = parseResults;
}

// 确认创建活动和流程
function confirmCreateFromAMS() {
    const parsedData = window.currentAMSData;
    if (!parsedData) {
        showMessage('解析数据丢失，请重新解析', 'error');
        return;
    }

    // 显示加载状态
    const confirmBtn = document.querySelector('#ams-result-modal button[onclick="confirmCreateFromAMS()"]');
    const originalText = confirmBtn.textContent;
    confirmBtn.textContent = '创建中...';
    confirmBtn.disabled = true;

    // 发送创建请求，使用原始URL
    const amsUrl = window.currentAMSURL;
    if (!amsUrl) {
        showMessage('AMS链接丢失，请重新解析', 'error');
        confirmBtn.textContent = originalText;
        confirmBtn.disabled = false;
        return;
    }

    fetch('/admin/api/ams/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ ams_url: amsUrl })
    })
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                showMessage(data.data.message, 'success');
                closeModal('ams-result-modal');
                loadActivitiesData();
            } else {
                showMessage('创建活动失败: ' + data.msg, 'error');
            }
        })
        .catch(error => {
            console.error('创建活动失败:', error);
            showMessage('创建活动失败', 'error');
        })
        .finally(() => {
            // 恢复按钮状态
            confirmBtn.textContent = originalText;
            confirmBtn.disabled = false;
        });
}

// 确认创建多个活动
function confirmCreateMultipleFromAMS() {
    const parseResults = window.currentMultipleAMSData;
    if (!parseResults || parseResults.length === 0) {
        showMessage('解析数据丢失，请重新解析', 'error');
        return;
    }

    // 显示加载状态
    const confirmBtn = document.querySelector('#ams-result-modal button[onclick="confirmCreateMultipleFromAMS()"]');
    const originalText = confirmBtn.textContent;
    confirmBtn.textContent = '创建中...';
    confirmBtn.disabled = true;

    // 批量创建活动
    let successCount = 0;
    let failCount = 0;
    const totalCount = parseResults.length;

    const createPromises = parseResults.map((result) => {
        return fetch('/admin/api/ams/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ ams_url: result.url })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                successCount++;
                confirmBtn.textContent = `创建中... (${successCount + failCount}/${totalCount})`;
                return { success: true, url: result.url, message: data.data.message };
            } else {
                failCount++;
                confirmBtn.textContent = `创建中... (${successCount + failCount}/${totalCount})`;
                return { success: false, url: result.url, error: data.msg };
            }
        })
        .catch(error => {
            failCount++;
            confirmBtn.textContent = `创建中... (${successCount + failCount}/${totalCount})`;
            return { success: false, url: result.url, error: error.message };
        });
    });

    Promise.all(createPromises).then(results => {
        // 恢复按钮状态
        confirmBtn.textContent = originalText;
        confirmBtn.disabled = false;

        // 显示结果
        if (successCount > 0) {
            showMessage(`成功创建 ${successCount} 个活动${failCount > 0 ? `，${failCount} 个创建失败` : ''}`, 'success');
            closeModal('ams-result-modal');
            loadActivitiesData();
        } else {
            showMessage('所有活动创建失败', 'error');
        }

        // 输出详细结果到控制台
        console.log('批量创建结果:', results);
    });
}

// ==================== 表单事件处理 ====================

// 初始化表单事件监听器
function initActivityFormEvents() {
    // 创建活动表单提交
    const createActivityForm = document.getElementById('create-activity-form');
    if (createActivityForm) {
        createActivityForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());

            // 转换时间格式和数据类型
            data.begin_time = new Date(data.begin_time).toISOString();
            data.end_time = new Date(data.end_time).toISOString();
            data.priority = parseInt(data.priority);
            data.activity_id = parseInt(data.activity_id);

            fetch('/admin/api/activities', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.code == 200) {
                        showMessage('活动创建成功', 'success');
                        closeModal('create-activity-modal');
                        loadActivitiesData();
                    } else {
                        showMessage('创建活动失败: ' + data.msg, 'error');
                    }
                })
                .catch(error => {
                    console.error('创建活动失败:', error);
                    showMessage('创建活动失败', 'error');
                });
        });
    }

    // 编辑活动表单提交
    const editActivityForm = document.getElementById('edit-activity-form');
    if (editActivityForm) {
        editActivityForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            const activityId = data.activity_id;

            // 转换时间格式和数据类型
            data.begin_time = new Date(data.begin_time).toISOString();
            data.end_time = new Date(data.end_time).toISOString();
            data.priority = parseInt(data.priority);
            data.status = parseInt(data.status);
            data.activity_id = parseInt(data.activity_id);

            fetch(`/admin/api/activities/${activityId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.code == 200) {
                        showMessage('活动更新成功', 'success');
                        closeModal('edit-activity-modal');
                        loadActivitiesData();
                    } else {
                        showMessage('更新活动失败: ' + data.msg, 'error');
                    }
                })
                .catch(error => {
                    console.error('更新活动失败:', error);
                    showMessage('更新活动失败', 'error');
                });
        });
    }

    // AMS链接导入表单提交
    const importAmsForm = document.getElementById('import-ams-form');
    if (importAmsForm) {
        importAmsForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const amsUrlsText = formData.get('ams_url');

            // 按行分割，过滤空行
            const amsUrls = amsUrlsText.split('\n')
                .map(url => url.trim())
                .filter(url => url.length > 0);

            if (amsUrls.length === 0) {
                showMessage('请输入至少一个AMS链接', 'error');
                return;
            }

            try {
                // 显示加载状态
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.textContent;
                submitBtn.textContent = `解析中... (0/${amsUrls.length})`;
                submitBtn.disabled = true;

                // 批量解析AMS链接
                const parseResults = [];
                const failedUrls = [];

                for (let i = 0; i < amsUrls.length; i++) {
                    const amsUrl = amsUrls[i];
                    submitBtn.textContent = `解析中... (${i + 1}/${amsUrls.length})`;

                    try {
                        const response = await fetch('/admin/api/ams/parse', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ ams_url: amsUrl })
                        });

                        const data = await response.json();
                        if (data.code == 200) {
                            parseResults.push({
                                url: amsUrl,
                                data: data.data
                            });
                        } else {
                            failedUrls.push({
                                url: amsUrl,
                                error: data.msg
                            });
                        }
                    } catch (error) {
                        failedUrls.push({
                            url: amsUrl,
                            error: error.message
                        });
                    }
                }

                // 恢复按钮状态
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;

                // 检查解析结果
                if (parseResults.length === 0) {
                    showMessage('所有链接解析失败', 'error');
                    if (failedUrls.length > 0) {
                        console.error('解析失败的链接:', failedUrls);
                    }
                    return;
                }

                // 显示解析结果
                showMultipleAMSParseResult(parseResults, failedUrls);

            } catch (error) {
                console.error('解析AMS链接失败:', error);
                showMessage('解析失败: ' + error.message, 'error');

                // 恢复按钮状态
                const submitBtn = this.querySelector('button[type="submit"]');
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });
    }

    // 流程编辑表单提交
    const flowEditForm = document.getElementById('flow-edit-form');
    if (flowEditForm) {
        flowEditForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // 更新参数JSON
            updateParametersJson();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            const flowDbId = data.flow_db_id;

            // 使用隐藏字段中的参数JSON
            data.parameters = data.parameters || '{}';

            // 验证JSON格式
            try {
                JSON.parse(data.parameters);
            } catch (e) {
                showMessage('参数配置JSON格式错误', 'error');
                return;
            }

            // 转换数据类型
            // 对于新建流程，使用状态按钮的状态；对于编辑流程，保持原有逻辑
            if (!isEdit && window.currentFlowStatus !== undefined) {
                data.status = window.currentFlowStatus;
            } else {
                data.status = parseInt(data.status) || 1; // 默认启用
            }
            data.sort_order = parseInt(data.sort_order);
            data.activity_id = parseInt(data.activity_id);

            const isEdit = flowDbId && flowDbId !== '';
            const url = isEdit ? `/admin/api/flows/${flowDbId}` : '/admin/api/flows';
            const method = isEdit ? 'PUT' : 'POST';

            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
                .then(response => response.json())
                .then(data => {
                    if (data.code == 200) {
                        showMessage(isEdit ? '流程更新成功' : '流程创建成功', 'success');
                        closeModal('flow-edit-modal');
                        loadFlowsData(currentActivityId);
                    } else {
                        showMessage((isEdit ? '更新' : '创建') + '流程失败: ' + data.msg, 'error');
                    }
                })
                .catch(error => {
                    console.error((isEdit ? '更新' : '创建') + '流程失败:', error);
                    showMessage((isEdit ? '更新' : '创建') + '流程失败', 'error');
                });
        });
    }
}


// ==================== 测试参数管理功能 ====================

// 初始化测试参数列表
function initTestParametersList(testParams) {
    const parametersList = document.getElementById('test-parameters-list');
    parametersList.innerHTML = '';

    // 加载已保存的自定义参数
    if (testParams.custom_params && Object.keys(testParams.custom_params).length > 0) {
        Object.entries(testParams.custom_params).forEach(([key, value]) => {
            addTestParameterRow(key, value);
        });
    } else {
        showEmptyTestParametersMessage();
    }
}

// 添加测试参数行
function addTestParameterRow(key = '', value = '') {
    const parametersList = document.getElementById('test-parameters-list');
    const emptyMessage = parametersList.querySelector('.test-parameters-empty');
    if (emptyMessage) {
        emptyMessage.remove();
    }

    // 移除现有的添加按钮
    const existingAddBtn = parametersList.querySelector('.add-test-parameter-btn');
    if (existingAddBtn) {
        existingAddBtn.remove();
    }

    const rowId = 'test-param-row-' + Date.now();
    const rowHtml = `
        <div class="test-parameter-row" id="${rowId}" style="display: flex; gap: 10px; margin-bottom: 10px; align-items: end;">
            <div style="flex: 1;">
                <input type="text" class="test-param-key" value="${key}" placeholder="参数名称"
                       onblur="checkParameterKeyDuplicate('${rowId}')"
                       style="width: 100%; padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px; font-size: 14px;">
            </div>
            <div style="flex: 2;">
                <input type="text" class="test-param-value" value="${value}" placeholder="参数值" style="width: 100%; padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px; font-size: 14px;">
            </div>
            <button type="button" onclick="removeTestParameterRow('${rowId}')" style="
                padding: 8px 12px;
                background: #ef4444;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                height: 36px;
            " onmouseover="this.style.background='#dc2626';" onmouseout="this.style.background='#ef4444';">删除</button>
        </div>
    `;

    parametersList.insertAdjacentHTML('beforeend', rowHtml);

    // 添加+按钮
    addTestParameterButton();
}

// 添加测试参数按钮
function addTestParameterButton() {
    const parametersList = document.getElementById('test-parameters-list');

    // 移除现有的添加按钮
    const existingAddBtn = parametersList.querySelector('.add-test-parameter-btn');
    if (existingAddBtn) {
        existingAddBtn.remove();
    }

    const addBtnHtml = `
        <div class="add-test-parameter-btn" onclick="addTestParameterRow()" style="
            padding: 5px;
            text-align: center;
            border: 2px dashed #e2e8f0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
            color: #718096;
        " onmouseover="this.style.borderColor='#cbd5e0'; this.style.background='#f1f5f9'; this.style.color='#4a5568';"
           onmouseout="this.style.borderColor='#e2e8f0'; this.style.background='#fafafa'; this.style.color='#718096';">
            <div style="font-size: 16px;">+</div>
        </div>
    `;
    parametersList.insertAdjacentHTML('beforeend', addBtnHtml);
}

// 删除测试参数行
function removeTestParameterRow(rowId) {
    const row = document.getElementById(rowId);
    if (row) {
        row.remove();

        const parametersList = document.getElementById('test-parameters-list');
        const paramRows = parametersList.querySelectorAll('.test-parameter-row');

        // 如果没有参数了，显示空状态
        if (paramRows.length === 0) {
            showEmptyTestParametersMessage();
        } else {
            // 重新添加+按钮
            addTestParameterButton();
        }
    }
}

// 显示空测试参数消息
function showEmptyTestParametersMessage() {
    const parametersList = document.getElementById('test-parameters-list');
    parametersList.innerHTML = `
        <div class="test-parameters-empty" onclick="addTestParameterRow()" style="
            padding: 10px;
            text-align: center;
            color: #718096;
            border: 2px dashed #e2e8f0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #f8f9fa;
        " onmouseover="this.style.borderColor='#cbd5e0'; this.style.background='#f1f5f9';"
           onmouseout="this.style.borderColor='#e2e8f0'; this.style.background='#f8f9fa';">
            <div style="font-weight: 500; margin-bottom: 3px; font-size: 14px;">暂无自定义参数</div>
            <div style="font-size: 12px; color: #a0aec0;">点击此处添加自定义参数</div>
        </div>
    `;
}

// 显示流程必需参数
function showRequiredParametersForTest(flow) {
    const container = document.getElementById('test-required-parameters-list');
    const group = document.getElementById('test-required-params-group');

    if (!container || !group || !flow) return;

    // 解析流程参数
    let parameters = {};
    if (flow.parameters) {
        if (typeof flow.parameters === 'string') {
            try {
                parameters = JSON.parse(flow.parameters);
            } catch (e) {
                console.warn('解析参数JSON失败:', e);
                parameters = {};
            }
        } else if (typeof flow.parameters === 'object') {
            parameters = flow.parameters;
        }
    }

    // 清空容器
    container.innerHTML = '';

    // 如果有参数，显示参数输入框
    if (Object.keys(parameters).length > 0) {
        Object.entries(parameters).forEach(([key, param]) => {
            const paramHtml = `
                <div class="form-group">
                    <label for="test-required-${key}">${param.name || key} *</label>
                    <input type="text" id="test-required-${key}" class="test-required-param" data-key="${key}"
                           value="${param.default_value || ''}"
                           placeholder="请输入${param.name || key}"
                           oninput="validateRequiredParam(this)"
                           onblur="validateRequiredParam(this)"
                           style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                </div>
            `;
            container.insertAdjacentHTML('beforeend', paramHtml);
        });

        group.style.display = 'block';
    } else {
        group.style.display = 'none';
    }
}

// 为特定流程加载参数
function loadParametersForFlow(flowId) {
    const flow = currentFlows.find(f => f.id === flowId);
    if (!flow) return;

    // 解析流程参数
    let parameters = {};
    if (flow.parameters) {
        if (typeof flow.parameters === 'string') {
            try {
                parameters = JSON.parse(flow.parameters);
            } catch (e) {
                console.warn('解析参数JSON失败:', e);
                parameters = {};
            }
        } else if (typeof flow.parameters === 'object') {
            parameters = flow.parameters;
        }
    }

    // 分类参数
    const fixedParams = {};
    const userParams = {};

    Object.entries(parameters).forEach(([key, param]) => {
        if (param.type === 'fixed') {
            fixedParams[key] = param;
        } else if (param.type === 'user_input') {
            userParams[key] = param;
        }
    });

    // 显示固定参数
    if (Object.keys(fixedParams).length > 0) {
        showFixedParametersForTest(fixedParams);
    }

    // 显示用户输入参数
    if (Object.keys(userParams).length > 0) {
        showUserParametersForTest(userParams);
    }
}

// 显示固定参数
function showFixedParametersForTest(fixedParams) {
    const container = document.getElementById('test-fixed-parameters-list');
    const group = document.getElementById('test-fixed-params-group');

    if (!container || !group) return;

    container.innerHTML = '';

    Object.entries(fixedParams).forEach(([key, param]) => {
        const paramHtml = `
            <div style="display: flex; gap: 10px; margin-bottom: 10px; align-items: center;">
                <div style="flex: 1;">
                    <label style="display: block; margin-bottom: 5px; font-size: 14px; color: #374151;">${param.name || key}</label>
                    <input type="text" class="test-fixed-param" data-key="${key}" value="${param.default_value || ''}" readonly
                           style="width: 100%; padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px; font-size: 14px; background: #f9fafb;">
                </div>
                <div style="flex: 0 0 auto; color: #6b7280; font-size: 12px;">固定值</div>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', paramHtml);
    });

    group.style.display = 'block';
}

// 显示用户输入参数
function showUserParametersForTest(userParams) {
    const container = document.getElementById('test-user-parameters-list');
    const group = document.getElementById('test-user-params-group');

    if (!container || !group) return;

    container.innerHTML = '';

    Object.entries(userParams).forEach(([key, param]) => {
        const paramHtml = `
            <div style="display: flex; gap: 10px; margin-bottom: 10px; align-items: center;">
                <div style="flex: 1;">
                    <label style="display: block; margin-bottom: 5px; font-size: 14px; color: #374151;">${param.name || key} ${param.required ? '*' : ''}</label>
                    <input type="text" class="test-user-param" data-key="${key}" value="${param.default_value || ''}"
                           placeholder="${param.description || '请输入' + (param.name || key)}"
                           style="width: 100%; padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px; font-size: 14px;">
                </div>
                <div style="flex: 0 0 auto; color: #6b7280; font-size: 12px;">${param.required ? '必填' : '可选'}</div>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', paramHtml);
    });

    group.style.display = 'block';
}

// 检查参数键重复
function checkParameterKeyDuplicate(currentRowId) {
    const currentRow = document.getElementById(currentRowId);
    if (!currentRow) return;

    const currentKeyInput = currentRow.querySelector('.test-param-key');
    if (!currentKeyInput) return;

    const currentKey = currentKeyInput.value.trim();
    if (!currentKey) return;

    // 检查是否与必需参数重复
    const requiredParamInputs = document.querySelectorAll('.test-required-param');
    const requiredKeys = Array.from(requiredParamInputs).map(input => input.getAttribute('data-key'));

    if (requiredKeys.includes(currentKey)) {
        showMessage(`参数键 "${currentKey}" 与流程必需参数重复，额外参数将覆盖必需参数的值`, 'warning');
        currentKeyInput.style.borderColor = '#f59e0b';
        return;
    }

    // 检查是否与其他额外参数重复
    const allParamRows = document.querySelectorAll('.test-parameter-row');
    let duplicateFound = false;

    allParamRows.forEach(row => {
        if (row.id !== currentRowId) {
            const keyInput = row.querySelector('.test-param-key');
            if (keyInput && keyInput.value.trim() === currentKey) {
                duplicateFound = true;
                keyInput.style.borderColor = '#ef4444';
            }
        }
    });

    if (duplicateFound) {
        showMessage(`参数键 "${currentKey}" 重复，将使用最后一个值`, 'warning');
        currentKeyInput.style.borderColor = '#ef4444';
    } else {
        currentKeyInput.style.borderColor = '#e2e8f0';
    }
}

// 验证Access Token
function validateAccessToken(input) {
    const value = input.value.trim();

    if (!value) {
        input.style.borderColor = '#ef4444';
        input.style.backgroundColor = '#fef2f2';
        input.title = 'Access Token 为必填参数';
    } else {
        input.style.borderColor = '#10b981';
        input.style.backgroundColor = '#f0fdf4';
        input.title = '';
    }
}

// 验证必需参数
function validateRequiredParam(input) {
    const value = input.value.trim();
    const label = input.previousElementSibling ? input.previousElementSibling.textContent.replace(' *', '') : input.getAttribute('data-key');

    if (!value) {
        input.style.borderColor = '#ef4444';
        input.style.backgroundColor = '#fef2f2';
        input.title = `${label} 为必填参数`;
    } else {
        input.style.borderColor = '#10b981';
        input.style.backgroundColor = '#f0fdf4';
        input.title = '';
    }

    // 更新提交按钮状态
    updateSubmitButtonState();
}

// 检查所有必需参数是否已填写
function checkAllRequiredParams() {
    const requiredParamInputs = document.querySelectorAll('#test-required-parameters-list .test-required-param');
    let allValid = true;

    requiredParamInputs.forEach(input => {
        if (!input.value.trim()) {
            allValid = false;
        }
    });

    return allValid;
}

// 更新提交按钮状态
function updateSubmitButtonState() {
    const submitBtn = document.querySelector('#set-test-token-form button[type="submit"]');
    const accessToken = document.getElementById('test-access-token');

    if (submitBtn && accessToken) {
        const hasAccessToken = accessToken.value.trim() !== '';
        const allRequiredValid = checkAllRequiredParams();

        if (hasAccessToken && allRequiredValid) {
            submitBtn.disabled = false;
            submitBtn.style.opacity = '1';
            submitBtn.style.cursor = 'pointer';
        } else {
            submitBtn.disabled = true;
            submitBtn.style.opacity = '0.6';
            submitBtn.style.cursor = 'not-allowed';
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initActivityFormEvents();
    setupParameterListeners();

    // 恢复URL参数状态
    restoreFromURLParams();

    // 加载数据（不重置页码）
    loadActivitiesData(false);
});