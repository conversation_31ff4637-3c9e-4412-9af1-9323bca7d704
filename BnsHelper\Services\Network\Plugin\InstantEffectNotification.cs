﻿using Xylia.BnsHelper.Models;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Plugin;
internal class InstantEffectNotification : IPacket
{
	#region Fields
	public Creature? Player;
	public int EffectId;
	#endregion

	#region Methods
	public DataArchiveWriter Create() => new DataArchiveWriter(); 

	public void Read(DataArchive reader)
	{
		Player = new Creature(reader);
		EffectId = reader.Read<int>();
	}
    #endregion
}
