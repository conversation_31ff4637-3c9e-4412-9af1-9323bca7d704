using BnsHelper.Common.Helpers;
using Newtonsoft.Json;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Windows;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Services.Network.BinaryProtocol;
using Xylia.BnsHelper.Services.Network.Service;

namespace Xylia.BnsHelper.Services;
public class AnnouncementService : IService
{
    #region Fields
    public EventHandler<int>? OnUnreadCountChanged;

    private static readonly Lazy<AnnouncementService> _instance = new(() => new AnnouncementService());
    public static AnnouncementService Instance => _instance.Value;

    private long _serverVersion = 0; // 服务端版本号
    readonly ObservableCollection<Announcement> _announcements = [];
    readonly Dictionary<uint, Announcement> _announcementCache = [];

    // 缓存保存优化
    private readonly string _cacheFilePath = Path.Combine(SettingHelper.CacheFolder, "announcements.json");
    private readonly Timer _cacheTimer;
    private volatile bool _cacheNeedsSave = false;
    #endregion

    #region Properties
    /// <summary>
    /// 所有公告（按优先级和发布时间排序）
    /// </summary>
    public IEnumerable<Announcement> Announcements => _announcements.Where(x => x.IsInValidTimeRange).OrderByDescending(o => o.Priority).ThenByDescending(a => a.PublishTime);

    /// <summary>
    /// 未读公告数量
    /// </summary>
    public int UnreadCount => _announcements.Where(x => x.IsInValidTimeRange).Count(a => !a.IsRead);
    #endregion

    #region Methods
    private AnnouncementService()
    {
        // 初始化缓存保存定时器（延迟保存，避免频繁IO）
        _cacheTimer = new Timer(SaveCacheCallback, null, Timeout.Infinite, Timeout.Infinite);
        LoadLocalCache();
    }

    public void Register()
    {
        // 注册服务，启动定时检查
    }

    /// <summary>
    /// 标记公告为已读
    /// </summary>
    public void MarkAsRead(uint announcementId)
    {
        var announcement = _announcements.FirstOrDefault(a => a.Id == announcementId);
        if (announcement != null && !announcement.IsRead)
        {
            announcement.IsRead = true;

            // 同步更新缓存中的公告
            if (_announcementCache.TryGetValue(announcementId, out Announcement? value))
            {
                value.IsRead = true;
            }

            // 标记需要保存缓存（延迟保存，避免频繁IO）
            ScheduleCacheSave();
        }
    }

    /// <summary>
    /// 标记所有公告为已读
    /// </summary>
    public void MarkAllAsRead()
    {
        bool hasChanges = false;

        foreach (var announcement in _announcements)
        {
            if (!announcement.IsRead)
            {
                announcement.IsRead = true;

                // 同步更新缓存中的公告
                if (_announcementCache.TryGetValue(announcement.Id, out Announcement? value))
                {
                    value.IsRead = true;
                }

                hasChanges = true;
            }
        }

        // 只有在有变化时才保存缓存
        if (hasChanges) ScheduleCacheSave();
    }

    /// <summary>
    /// 加载本地缓存（支持加密）
    /// </summary>
    private  void LoadLocalCache()
    {
        try
        {
            var cacheData = JsonConvert.DeserializeObject<AnnouncementCacheData>(EncryptionHelper.ReadFile(_cacheFilePath));
            if (cacheData != null)
            {
                _serverVersion = cacheData.ServerVersion;

                foreach (var announcement in cacheData.Announcements)
                {
                    _announcementCache[announcement.Id] = announcement;
                    _announcements.Add(announcement);
                }

                Debug.WriteLine($"[INFO] 加载本地缓存: 版本={_serverVersion}, 公告数量={_announcements.Count}");
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 加载本地缓存失败: {ex.Message}");
        }
        finally
        {
            OnUnreadCountChanged?.Invoke(this, UnreadCount);
        }
    }

    /// <summary>
    /// 保存本地缓存（加密）
    /// </summary>
    private async Task SaveLocalCacheAsync()
    {
        try
        {
            var cacheData = new AnnouncementCacheData
            {
                ServerVersion = _serverVersion,
                Announcements = [.. _announcementCache.Values],
                LastUpdated = DateTime.Now
            };

            await EncryptionHelper.EncryptToFileAsync(_cacheFilePath, JsonConvert.SerializeObject(cacheData));
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 保存本地缓存失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取服务端版本号
    /// </summary>
    private async Task<long?> GetVersionAsync()
    {
        try
        {
            using var session = new BnszsGateSession();
            var requestPacket = new AnnouncementVersionPacket();
            session.SendPacket(requestPacket, MessageTypes.AnnouncementVersion);

            var response = await session.WaitForResponseWithRetry(MessageTypes.AnnouncementVersionResponse, 3, 5000);
            if (response is AnnouncementVersionPacket versionResponse) return versionResponse.Version;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 获取服务端版本号异常: {ex.Message}");
        }

        return null;
    }

    /// <summary>
    /// 执行增量更新
    /// </summary>
    public async Task PerformIncrementalUpdateAsync(long newServerVersion)
    {
        try
        {
            Debug.WriteLine($"[INFO] 开始增量更新: {_serverVersion} -> {newServerVersion}");

            // 获取服务端公告ID列表和版本号
            var serverAnnouncementData = await GetAnnouncementIdsAsync();
            if (serverAnnouncementData == null)
            {
                Debug.WriteLine("[ERROR] 无法获取服务端公告ID列表");
                return;
            }

            // 转换为字典便于查找
            var serverAnnouncementDict = serverAnnouncementData.ToDictionary(kv => kv.Key, kv => kv.Value);
            var serverAnnouncementIds = serverAnnouncementDict.Keys.ToHashSet();
            var localAnnouncementIds = _announcementCache.Keys.ToHashSet();

            // 找出需要移除的公告（服务端已下线）
            var toRemove = localAnnouncementIds.Except(serverAnnouncementIds).ToList();

            // 找出需要新增的公告（本地没有）
            var toAdd = serverAnnouncementIds.Except(localAnnouncementIds).ToList();

            // 找出需要更新的公告（版本号不同）
            var toUpdate = new List<uint>();
            foreach (var localId in localAnnouncementIds.Intersect(serverAnnouncementIds))
            {
                if (serverAnnouncementDict.TryGetValue(localId, out var serverVersion))
                {
                    // 比较本地和服务端版本号
                    if (_announcementCache.TryGetValue(localId, out var localAnnouncement))
                    {
                        // 如果服务端版本号更新，则需要更新
                        if (serverVersion > localAnnouncement.Version)
                        {
                            toUpdate.Add(localId);
                            Debug.WriteLine($"[INFO] 公告 {localId} 需要更新: 本地版本={localAnnouncement.Version}, 服务端版本={serverVersion}");
                        }
                    }
                    else
                    {
                        // 本地没有该公告，标记为需要更新
                        toUpdate.Add(localId);
                        Debug.WriteLine($"[INFO] 公告 {localId} 本地不存在，标记为需要更新");
                    }
                }
            }

            Debug.WriteLine($"[INFO] 增量更新统计: 移除={toRemove.Count}, 新增={toAdd.Count}, 更新={toUpdate.Count}");

            // 处理下线公告
            foreach (var announcementId in toRemove)
            {
                await RemoveAnnouncementAsync(announcementId);
            }

            // 处理新增公告
            foreach (var announcementId in toAdd)
            {
                await FetchAnnouncementAsync(announcementId);
            }

            // 处理更新公告
            foreach (var announcementId in toUpdate)
            {
                await FetchAnnouncementAsync(announcementId);
            }

            // 更新版本号
            _serverVersion = newServerVersion;
            OnUnreadCountChanged?.Invoke(this, UnreadCount);

            // 保存缓存
            await SaveLocalCacheAsync();
            Debug.WriteLine($"[INFO] 增量更新完成，当前版本: {_serverVersion}");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 增量更新失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取服务端公告ID列表和版本号
    /// </summary>
    private async Task<IEnumerable<KeyValuePair<uint, ushort>>?> GetAnnouncementIdsAsync()
    {
        try
        {
            using var session = new BnszsGateSession();
            session.SendPacket(new AnnouncementIdsPacket(), MessageTypes.AnnouncementIds);

            var response = await session.WaitForResponseWithRetry(MessageTypes.AnnouncementIdsResponse, 3, 5000);
            if (response is AnnouncementIdsPacket packet && packet.Announcements != null) return packet.Announcements;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 获取服务端公告ID列表和版本号异常: {ex.Message}");
        }

        return null;
    }

    /// <summary>
    /// 移除公告
    /// </summary>
    private async Task RemoveAnnouncementAsync(uint announcementId)
    {
        try
        {
            Debug.WriteLine($"[INFO] 移除公告: {announcementId}");

            lock (_announcements)
            {
                _announcementCache.Remove(announcementId);

                Application.Current.Dispatcher.Invoke(() =>
                {
                    var toRemove = _announcements.FirstOrDefault(a => a.Id == announcementId);
                    if (toRemove != null) _announcements.Remove(toRemove);
                });
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 移除公告失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取并添加单个公告
    /// </summary>
    private async Task FetchAnnouncementAsync(uint announcementId)
    {
        try
        {
            Debug.WriteLine($"[INFO] 获取公告详情: {announcementId}");

            using var session = new BnszsGateSession();
            session.SendPacket(new AnnouncementDetailPacket { AnnouncementId = announcementId }, MessageTypes.AnnouncementDetail);

            var response = await session.WaitForResponseWithRetry(MessageTypes.AnnouncementDetailResponse, 3, 5000);
            if (response is AnnouncementDetailPacket detailResponse)
            {
                var announcement = detailResponse.Announcement;
                if (announcement is null) return;

                // 检查缓存中是否已有该公告，如果有则保持其已读状态
                if (_announcementCache.TryGetValue(announcementId, out Announcement? value))
                {
                    announcement.IsRead = value.IsRead;
                }

                // 新公告默认为未读状态（IsRead默认为false）
                lock (_announcements)
                {
                    _announcementCache[announcementId] = announcement;

                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        // 移除旧版本（如果存在）
                        var existing = _announcements.FirstOrDefault(a => a.Id == announcementId);
                        if (existing != null) _announcements.Remove(existing);

                        // 添加新版本
                        _announcements.Add(announcement);
                    });
                }

                Debug.WriteLine($"[INFO] 成功添加公告: {announcement.Title}");
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 获取公告详情异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 手动刷新公告（供外部调用）
    /// </summary>
    public async Task RefreshAnnouncementsAsync()
    {
        try
        {
            Debug.WriteLine("[INFO] 手动刷新公告...");

            // 获取服务端版本号
            var serverVersion = await GetVersionAsync();
            if (serverVersion == null)
            {
                Debug.WriteLine("[WARNING] 无法获取服务端版本号");
                return;
            }

            Debug.WriteLine($"[INFO] 版本比较: 本地={_serverVersion}, 服务端={serverVersion}");

            // 版本号相同，无需更新
            if (_serverVersion == serverVersion)
            {
                Debug.WriteLine("[INFO] 版本号相同，无需更新");
                return;
            }

            // 版本号不同，执行增量更新
            await PerformIncrementalUpdateAsync(serverVersion.Value);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 手动刷新公告失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 调度缓存保存（延迟保存，避免频繁IO）
    /// </summary>
    private void ScheduleCacheSave()
    {
        _cacheNeedsSave = true;
        _cacheTimer.Change(2000, Timeout.Infinite); // 2秒后保存

        // 委托通知公告更新
        OnUnreadCountChanged?.Invoke(this, UnreadCount);
    }

    /// <summary>
    /// 缓存保存回调
    /// </summary>
    private void SaveCacheCallback(object? state)
    {
        if (_cacheNeedsSave)
        {
            _cacheNeedsSave = false;
            _ = Task.Run(SaveLocalCacheAsync);
        }
    }

    /// <summary>
    /// 本地缓存数据结构
    /// </summary>
    private class AnnouncementCacheData
    {
        public long ServerVersion { get; set; }
        public List<Announcement> Announcements { get; set; } = [];
        public DateTime LastUpdated { get; set; }
    }
    #endregion
}