﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Interactivity;
using System.Windows.Media.Imaging;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Models;
public struct Team(TeamMember[] members)
{
    public IEnumerable<TeamMember> Author { get; } = members.Where(m => m.Type == 0);
    public IEnumerable<TeamMember> Friends { get; } = members.Where(m => m.Type == 1);
}

public struct TeamMember
{
    #region Fields
    public string Name { get; set; }
    public long Uin { get; set; }
    public string Slogan { get; set; }
    public string Strengths { get; set; }
    public string Url { get; set; }
    public byte Type { get; set; }
    #endregion

    #region Constructor
    public TeamMember(DataArchive reader)
    {
        Name = reader.ReadString();
        Uin = reader.Read<long>();
        Slogan = reader.ReadString();
        Strengths = reader.ReadString();
        Url = reader.ReadString();
        Type = reader.Read<byte>(); // 0=author, 1=friends
    }
    #endregion

    #region Methods
    public readonly BitmapFrame? HeadImg
    {
        get
        {
            try
            {
                return BitmapFrame.Create(new Uri($"https://q1.qlogo.cn/g?b=qq&nk={Uin}&s=100"), BitmapCreateOptions.None, BitmapCacheOption.Default);
            }
            catch
            {
                return null;
            }
        }
    }

    public readonly IRelayCommand OpenLinkCommand => new RelayCommand(OpenLink);

    readonly void OpenLink()
    {
        // check url
        if (!string.IsNullOrEmpty(Url)) new OpenLinkCommand().Execute(Url);
    }
    #endregion
}
