package model

import (
	"time"

	"udp-server/server/pkg/utils"
)

// ==================== 模型 ==================== //

// 签到抽奖活动模型
type Lucky struct {
	ID         uint      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name       string    `gorm:"column:name;size:255;not null" json:"name"`           // 活动名称
	IsActivity bool      `gorm:"column:is_activity;default:0" json:"is_activity"`     // 是否为活动状态
	IsContinue bool      `gorm:"column:is_continue;default:0" json:"is_continue"`     // 是否重置奖励点数
	Free       int       `gorm:"column:free;default:1" json:"free"`                   // 每日免费次数
	StartTime  time.Time `gorm:"column:startTime;not null" json:"start_time"`         // 开始时间
	EndTime    time.Time `gorm:"column:endTime;not null" json:"end_time"`             // 结束时间
	UpdateTime time.Time `gorm:"column:updateTime;autoUpdateTime" json:"update_time"` // 最后更新时间
}

// TableName 指定表名
func (Lucky) TableName() string {
	return "bns_lucky"
}

// 检查活动是否正在进行中
func (l *Lucky) IsActive() bool {
	now := time.Now()
	return l.IsActivity && now.After(l.StartTime) && now.Before(l.EndTime)
}

// 用户签到记录模型
type UserDraw struct {
	UID      uint64    `gorm:"column:uid;primaryKey" json:"uid"`           // 用户ID (复合主键)
	Schedule uint      `gorm:"column:schedule;primaryKey" json:"schedule"` // 活动ID (复合主键)
	Extra    int       `gorm:"column:extra;default:0" json:"extra"`        // 额外次数
	Day      int       `gorm:"column:day;default:1" json:"day"`            // 总签到天数
	Number   int       `gorm:"column:number;default:0" json:"number"`      // 总签到次数
	Today    int       `gorm:"column:today;default:0" json:"today"`        // 今日签到次数
	Point    int       `gorm:"column:point;default:1" json:"point"`        // 奖励点数
	Time     time.Time `gorm:"column:time;autoCreateTime" json:"time"`     // 最后签到时间
}

// TableName 指定表名
func (UserDraw) TableName() string {
	return "user_draw"
}

// 检查是否为今天签到
func (u *UserDraw) IsToday() bool {
	return utils.IsToday(u.Time)
}

// 检查是否为昨天签到
func (u *UserDraw) IsYesterday() bool {
	return utils.IsYesterday(u.Time)
}

// 奖励配置模型
type LuckyReward struct {
	ID           uint      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Activity     uint      `gorm:"column:activity;not null;index" json:"activity"`        // 活动ID
	IsUse        bool      `gorm:"column:is_use;default:1" json:"is_use"`                 // 是否启用
	IsResetPoint bool      `gorm:"column:is_reset_point;default:0" json:"is_reset_point"` // 是否重置点数
	Point        int       `gorm:"column:point;default:1" json:"point"`                   // 所需点数
	Reward       string    `gorm:"column:reward;size:255" json:"reward"`                  // 奖励内容（CDKey等）
	Text         string    `gorm:"column:text;type:text" json:"text"`                     // 奖励描述文本
	Type         string    `gorm:"column:type;size:50;default:'random'" json:"type"`      // 奖励类型：random/guard
	Weight       int       `gorm:"column:weight;default:0" json:"weight"`                 // 权重
	Limit        int       `gorm:"column:limit;default:0" json:"limit"`                   // 限制次数，0为无限制
	LimitCurrent int       `gorm:"column:limit_current;default:0" json:"limit_current"`   // 当前已使用次数
	Admin        *int      `gorm:"column:admin" json:"admin"`                             // 管理员ID
	CreateTime   time.Time `gorm:"column:create_time;autoCreateTime" json:"create_time"`  // 创建时间
	UpdateTime   time.Time `gorm:"column:update_time;autoUpdateTime" json:"update_time"`  // 更新时间
}

// TableName 指定表名
func (LuckyReward) TableName() string {
	return "bns_lucky_reward"
}

// 检查奖励是否可用
func (r *LuckyReward) IsAvailable() bool {
	return r.IsUse && (r.Limit == 0 || r.LimitCurrent < r.Limit)
}

// 用户抽奖结果记录
type UserDrawResult struct {
	ID     uint   `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Reward uint   `gorm:"column:reward;not null;index" json:"reward"` // 奖励ID
	UID    uint64 `gorm:"column:uid;not null;index" json:"uid"`       // 用户ID
}

// TableName 指定表名
func (UserDrawResult) TableName() string {
	return "user_draw_result"
}

// ==================== 抽奖通信模型 ==================== //

// 抽奖活动公示信息
type LuckyRewardList struct {
	ActivityID   uint              `json:"activity_id"`
	ActivityName string            `json:"activity_name"`
	Description  string            `json:"description"`
	StartTime    time.Time         `json:"start_time"`
	EndTime      time.Time         `json:"end_time"`
	Rewards      []LuckyRewardInfo `json:"rewards"`
}

// 抽奖活动公示奖励信息
type LuckyRewardInfo struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	Weight      int    `json:"weight"`
	Point       int    `json:"point"`
	Description string `json:"description"`
}

// 抽奖状态响应
type LuckyStatusResponse struct {
	UID          uint64    `json:"uid"`
	ActivityID   uint      `json:"activity_id"`
	ActivityName string    `json:"activity_name"`
	Day          int       `json:"day"`
	Point        int       `json:"point"`
	Number       int       `json:"number"`
	Today        int       `json:"today"`
	Extra        int       `json:"extra"`
	LastSignTime time.Time `json:"last_sign_time"`
	// 客户端期望的字段
	AvailableCount uint32 `json:"available_count"` // 今日可用次数
}

// 抽奖响应
type LuckyDrawResponse struct {
	Message              string // 奖励文本
	Day                  int    // 总天数
	Point                int    // 当前积分
	RewardName           string // 奖励名称
	Permission           *uint8 // 更新后的权限级别（仅在权限有变化时包含）
	PermissionExpiration *int64 // 更新后的权限过期时间（仅在权限有变化时包含）
}

// 已签到错误类型
type AlreadySignedInError struct {
	Message string
}

func (e *AlreadySignedInError) Error() string {
	return "already signed in today"
}
