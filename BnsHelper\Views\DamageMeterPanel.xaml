﻿<Window x:Class="Xylia.BnsHelper.Views.DamageMeterPanel"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008" mc:Ignorable="d"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
		xmlns:hc="https://handyorg.github.io/handycontrol" 
		xmlns:helper="clr-namespace:Xylia.BnsHelper.Common.Helpers"
        xmlns:local="clr-namespace:Xylia.BnsHelper.Views"
		Background="Transparent" Topmost="True" ShowInTaskbar="False" Title="{DynamicResource DamageMeterPanel_Name}"
		WindowStyle="None" ResizeMode="NoResize" SizeToContent="WidthAndHeight">

    <WindowChrome.WindowChrome>
        <WindowChrome GlassFrameThickness="-1" CornerRadius="20" />
    </WindowChrome.WindowChrome>

    <Window.Resources>
        <Style TargetType="ToggleButton" BasedOn="{StaticResource ToggleButtonTransparent}">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="Foreground" Value="White" />
            <Setter Property="FontFamily" Value="{DynamicResource SegoeAssets}" />
            <Setter Property="FontSize" Value="16" />
            <Setter Property="MinWidth" Value="35" />
            <Setter Property="IsHitTestVisible" Value="{Binding IsHitTestVisible}" />
        </Style>

        <Style TargetType="ToolTip">
            <Setter Property="Padding" Value="5 4" />
            <Setter Property="Background" Value="{DynamicResource SecondaryRegionBrush}" />
            <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}" />
            <Setter Property="BorderThickness" Value="1" />
            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}" />
            <Setter Property="FontSize" Value="13.5" />
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToolTip">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" 
                                Padding="{TemplateBinding Padding}" CornerRadius="4">
                            <ContentPresenter />
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <!-- Main Content -->
        <DockPanel>
            <!-- Top Header -->
            <Grid Margin="0 5" DockPanel.Dock="Top" WindowChrome.IsHitTestVisibleInChrome="True">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!-- Left Side: Target and Time -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <!-- Target Selector Button -->
                    <Button x:Name="TargetButton" Background="Transparent" BorderBrush="Transparent" Foreground="White"
						FontWeight="Bold" FontSize="14" Padding="2 1" Margin="0 0 8 0" Click="OnTargetButtonClick" Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
												BorderBrush="{TemplateBinding BorderBrush}"
												BorderThickness="1"
												CornerRadius="3"
												Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#20FFFFFF" />
                                                    <Setter Property="BorderBrush" Value="#40FFFFFF" />
                                                </Trigger>
                                                <Trigger Property="IsPressed" Value="True">
                                                    <Setter Property="Background" Value="#30FFFFFF" />
                                                    <Setter Property="BorderBrush" Value="#60FFFFFF" />
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <StackPanel.Resources>
                                    <Style TargetType="TextBlock">
                                        <!-- 设置默认前景色为白色，避免频道切换时的闪烁 -->
                                        <Setter Property="Foreground" Value="White" />
                                        <Style.Triggers>
                                            <!-- 只有在明确的暂停状态时才显示红色 -->
                                            <DataTrigger Binding="{Binding Status}" Value="Pause">
                                                <Setter Property="Foreground" Value="Red" />
                                                <Setter Property="FontWeight" Value="Bold" />
                                                <Setter Property="ToolTip" Value="{DynamicResource DamageMeterPanel_InPause}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Status}" Value="PauseAuto">
                                                <Setter Property="Foreground" Value="Red" />
                                                <Setter Property="FontWeight" Value="Bold" />
                                                <Setter Property="ToolTip" Value="{DynamicResource DamageMeterPanel_InPause}" />
                                            </DataTrigger>
                                            <!-- Work和Wait状态都使用白色，避免闪烁 -->
                                            <DataTrigger Binding="{Binding Status}" Value="Work">
                                                <Setter Property="Foreground" Value="White" />
                                                <Setter Property="ToolTip" Value="{DynamicResource DamageMeterPanel_InWork}" />
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Status}" Value="Wait">
                                                <Setter Property="Foreground" Value="White" />
                                                <Setter Property="ToolTip" Value="{DynamicResource DamageMeterPanel_InWork}" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </StackPanel.Resources>

                                <TextBlock Text="▼" FontSize="10" Margin="0 0 5 0" VerticalAlignment="Center" Opacity="0.7" />
                                <TextBlock Text="{Binding CurrentTargetName}" MaxWidth="115" TextTrimming="CharacterEllipsis" />
                                <TextBlock Text="{Binding CurrentTargetSeconds, Converter={StaticResource SecondsDisplayConverter}}" FontSize="13" Margin="5 0 0 0" VerticalAlignment="Center" />
                            </StackPanel>
                        </Button.Content>
                        <Button.ContextMenu>
                            <ContextMenu x:Name="TargetContextMenu" ItemsSource="{Binding TargetOptions}"
									 Background="{DynamicResource RegionBrush}"
									 BorderBrush="{DynamicResource BorderBrush}">
                                <ContextMenu.Resources>
                                    <Style TargetType="MenuItem">
                                        <Setter Property="Background" Value="Transparent" />
                                        <Setter Property="Padding" Value="8 4" />
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="MenuItem">
                                                    <Border Background="{TemplateBinding Background}" BorderBrush="Transparent" BorderThickness="1" Padding="{TemplateBinding Padding}">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="*" />
                                                                <ColumnDefinition Width="Auto" />
                                                            </Grid.ColumnDefinitions>
                                                            <TextBlock Grid.Column="0" Text="{Binding Name}"
																   FontWeight="Bold"
																   Foreground="{DynamicResource PrimaryTextBrush}"
																   MaxWidth="180"
																   TextTrimming="CharacterEllipsis" />
                                                            <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="20 0 0 0">
                                                                <TextBlock Text="{Binding TotalDamage,StringFormat={}{0:N0}}" FontSize="10" Foreground="{StaticResource PrimaryTextBrush}" />
                                                            </StackPanel>
                                                        </Grid>
                                                    </Border>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsHighlighted" Value="True">
                                                            <Setter Property="Background" Value="{DynamicResource SecondaryRegionBrush}" />
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </ContextMenu.Resources>
                                <ContextMenu.ItemTemplate>
                                    <DataTemplate>
                                        <ContentPresenter Content="{Binding}" />
                                    </DataTemplate>
                                </ContextMenu.ItemTemplate>
                                <ContextMenu.ItemContainerStyle>
                                    <Style TargetType="MenuItem" BasedOn="{StaticResource {x:Type MenuItem}}">
                                        <Setter Property="Command" Value="{Binding DataContext.SelectTargetCommand, RelativeSource={RelativeSource AncestorType=ContextMenu}}" />
                                        <Setter Property="CommandParameter" Value="{Binding}" />
                                    </Style>
                                </ContextMenu.ItemContainerStyle>
                            </ContextMenu>
                        </Button.ContextMenu>
                    </Button>

                    <!-- Status and Time -->
                    <TextBlock Text="{Binding Players.TimeSpan,Mode=OneWay,Converter={StaticResource TimeConverter}}" VerticalAlignment="Center" FontWeight="Bold" Visibility="Collapsed">
                        <TextBlock.InputBindings>
                            <MouseBinding MouseAction="RightClick" Command="{Binding SwitchStautsCommand}" />
                        </TextBlock.InputBindings>
                        <TextBlock.Style>
                            <Style TargetType="TextBlock">
                                <!-- 设置默认前景色为白色 -->
                                <Setter Property="Foreground" Value="White" />
                                <Style.Triggers>
                                    <!-- 只有在明确的暂停状态时才显示红色 -->
                                    <DataTrigger Binding="{Binding Status}" Value="Pause">
                                        <Setter Property="Foreground" Value="Red" />
                                        <Setter Property="FontWeight" Value="Bold" />
                                        <Setter Property="ToolTip" Value="{DynamicResource DamageMeterPanel_InPause}" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Status}" Value="PauseAuto">
                                        <Setter Property="Foreground" Value="Red" />
                                        <Setter Property="FontWeight" Value="Bold" />
                                        <Setter Property="ToolTip" Value="{DynamicResource DamageMeterPanel_InPause}" />
                                    </DataTrigger>
                                    <!-- Work和Wait状态都使用白色 -->
                                    <DataTrigger Binding="{Binding Status}" Value="Work">
                                        <Setter Property="Foreground" Value="White" />
                                        <Setter Property="ToolTip" Value="{DynamicResource DamageMeterPanel_InWork}" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Status}" Value="Wait">
                                        <Setter Property="Foreground" Value="White" />
                                        <Setter Property="ToolTip" Value="{DynamicResource DamageMeterPanel_InWork}" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                </StackPanel>

                <!-- Right Side: Control Buttons -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0 0 -8 0">
                    <ToggleButton IsChecked="{Binding Page,Converter={StaticResource Enum2BoolConverter},ConverterParameter=2}" ToolTip="{DynamicResource DamageMeterPanel_History}">
                        <TextBlock Style="{x:Null}" Text="&#xE713;" />
                    </ToggleButton>

                    <ToggleButton x:Name="Lock" IsChecked="{Binding IsHitTestVisible}" IsHitTestVisible="True" ToolTip="{DynamicResource DamageMeterPanel_Lock}">
                        <StackPanel>
                            <TextBlock Style="{x:Null}" Tag="Lock" Text="&#xE785;" Visibility="{Binding IsChecked,ElementName=Lock,Converter={StaticResource Boolean2VisibilityConverter}}" Foreground="White" />
                            <TextBlock Style="{x:Null}" Tag="Lock" Text="&#xE72E;" Visibility="{Binding IsChecked,ElementName=Lock,Converter={StaticResource Boolean2VisibilityReConverter}}" Foreground="{StaticResource PrimaryBrush}" />
                        </StackPanel>
                    </ToggleButton>

                    <ToggleButton IsChecked="{Binding Page,Converter={StaticResource Enum2BoolConverter},ConverterParameter=1}" ToolTip="{DynamicResource DamageMeterPanel_Record}" Visibility="Collapsed">
                        <TextBlock Style="{x:Null}" Text="&#xE712;" />
                    </ToggleButton>

                    <ToggleButton Click="OnExit" ToolTip="{DynamicResource DamageMeterPanel_Exit}">
                        <TextBlock Style="{x:Null}" Text="&#xE711;" />
                    </ToggleButton>
                </StackPanel>
            </Grid>

            <!-- Main Content Area -->
            <StackPanel MinWidth="280" Grid.Row="1">
                <!-- Boss Timer Display -->
                <ItemsControl ItemsSource="{Binding BossTimers}" Background="Transparent" Margin="0,0,0,5"
                              Visibility="{Binding BossTimers.Count, Converter={StaticResource Number2VisibilityConverter}, ConverterParameter=1}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Border CornerRadius="4" Margin="0,1" Padding="8,4">
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Setter Property="Background" Value="{DynamicResource SecondaryRegionBrush}" />
                                        <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <Style.Triggers>
                                            <!-- 已刷新状态的样式 -->
                                            <DataTrigger Binding="{Binding State}" Value="Finished">
                                                <Setter Property="Background" Value="#2D4A2D" />
                                                <Setter Property="BorderBrush" Value="#4CAF50" />
                                                <Setter Property="Opacity" Value="0.8" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>

                                <Border.ContextMenu>
                                    <ContextMenu>
                                        <MenuItem Header="{DynamicResource DamageMeter_ClearSingleTimer}"
                                                  Command="{Binding DataContext.ClearSingleBossTimerCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                  CommandParameter="{Binding}" />
                                        <MenuItem Header="{DynamicResource DamageMeter_ClearAllTimers}"
                                                  Command="{Binding DataContext.ClearAllBossTimersCommand, RelativeSource={RelativeSource AncestorType=Window}}" />
                                    </ContextMenu>
                                </Border.ContextMenu>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0"
                                               Text="{Binding DisplayText}"
                                               FontSize="12"
                                               VerticalAlignment="Center">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}" />
                                                <Style.Triggers>
                                                    <!-- 已刷新状态的文字颜色 -->
                                                    <DataTrigger Binding="{Binding State}" Value="Finished">
                                                        <Setter Property="Foreground" Value="#81C784" />
                                                        <Setter Property="FontWeight" Value="Bold" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>

                                    <!-- Warning icon for Ominous Power -->
                                    <TextBlock Grid.Column="1"
                                               Text="⚠"
                                               Foreground="Orange"
                                               FontSize="14"
                                               VerticalAlignment="Center"
                                               Visibility="{Binding HasExclamationMark, Converter={StaticResource Boolean2VisibilityConverter}}" />
                                </Grid>
                            </Border>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>

                <!-- Player List -->
                <ListBox x:Name="PlayerHolder" ItemsSource="{Binding Players.View}" Background="Transparent" Visibility="{Binding Page,Converter={StaticResource Enum2VisibilityConverter},ConverterParameter=0}">
                    <ListBox.ContextMenu>
                        <ContextMenu Background="{DynamicResource RegionBrush}" BorderBrush="{DynamicResource BorderBrush}"
                                     DataContext="{Binding PlacementTarget.DataContext, RelativeSource={RelativeSource Self}}">
                            <MenuItem Header="伤害输出" Command="{Binding SetStatisticsTypeCommand}" CommandParameter="Damage" IsCheckable="True" IsChecked="{Binding StatisticsType, Converter={StaticResource Enum2BoolConverter}, ConverterParameter=0}">
                                <MenuItem.Icon>
                                    <TextBlock Text="⚔" FontSize="14" />
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="承受伤害" Command="{Binding SetStatisticsTypeCommand}" CommandParameter="DamageTaken" IsCheckable="True" IsChecked="{Binding StatisticsType, Converter={StaticResource Enum2BoolConverter}, ConverterParameter=1}">
                                <MenuItem.Icon>
                                    <TextBlock Text="🛡" FontSize="14" />
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="治疗统计" Command="{Binding SetStatisticsTypeCommand}" CommandParameter="Healing" IsCheckable="True" IsChecked="{Binding StatisticsType, Converter={StaticResource Enum2BoolConverter}, ConverterParameter=2}">
                                <MenuItem.Icon>
                                    <TextBlock Text="♥" FontSize="14" />
                                </MenuItem.Icon>
                            </MenuItem>
                            <MenuItem Header="重置数据" Command="{Binding ResetDataCommand}">
                                <MenuItem.Icon>
                                    <TextBlock Text="🔄" FontSize="14" />
                                </MenuItem.Icon>
                            </MenuItem>
                        </ContextMenu>
                    </ListBox.ContextMenu>
                    <ListBox.ItemContainerStyle>
                        <Style TargetType="{x:Type ListBoxItem}">
                            <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                            <Setter Property="Focusable" Value="False" />
                            <Setter Property="Background" Value="Transparent" />
                            <Setter Property="BorderBrush" Value="Transparent" />
                            <Setter Property="BorderThickness" Value="0" />
                            <Setter Property="Foreground" Value="White" />
                            <Setter Property="MinHeight" Value="30" />
                            <Setter Property="Margin" Value="0,0,0,1" />
                            <Setter Property="ToolTip">
                                <Setter.Value>
                                    <ToolTip>
                                        <ToolTip.Content>
                                            <local:DamageMeterTooltipPanel />
                                        </ToolTip.Content>
                                    </ToolTip>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="ListBoxItem">
                                        <Border x:Name="Bd" CornerRadius="{Binding Path=(hc:BorderElement.CornerRadius),RelativeSource={RelativeSource TemplatedParent}}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Background="{TemplateBinding Background}" SnapsToDevicePixels="true">
                                            <hc:SimplePanel>
                                                <Rectangle x:Name="Rect" Fill="{DynamicResource PrimaryBrush}" HorizontalAlignment="Left" RadiusX="2" RadiusY="2">
                                                    <Rectangle.Width>
                                                        <MultiBinding Converter="{StaticResource RatioConverter}">
                                                            <Binding Path="DamageRate2" />
                                                            <Binding Path="ActualWidth" ElementName="Bd" />
                                                        </MultiBinding>
                                                    </Rectangle.Width>
                                                </Rectangle>
                                                <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                                            </hc:SimplePanel>
                                        </Border>

                                        <ControlTemplate.Triggers>
                                            <DataTrigger Binding="{Binding Path=Self}" Value="True">
                                                <Setter TargetName="Rect" Property="Fill" Value="{DynamicResource TitleBrush}" />
                                            </DataTrigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </ListBox.ItemContainerStyle>
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Grid Margin="5 3" IsHitTestVisible="{Binding DataContext.IsHitTestVisible, RelativeSource={RelativeSource AncestorType=ListBox}}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <Grid.InputBindings>
                                    <MouseBinding MouseAction="LeftClick" Command="{Binding ToWorldCommand}" />
                                </Grid.InputBindings>

                                <Image x:Name="PART_Job" Width="20" Height="20" Margin="3 0 5 0" Source="{Binding Job,Converter={StaticResource ImageSourceSelector}}" VerticalAlignment="Center" />
                                <TextBlock Grid.Column="1" Text="{Binding Name}" VerticalAlignment="Center" />
                                <TextBlock Grid.Column="2" Text="{Binding DamageRate,Mode=OneWay,StringFormat={}{0:P1}}" Visibility="{Binding Mode,Converter={StaticResource Boolean2VisibilityConverter}}" HorizontalAlignment="Right" VerticalAlignment="Center" />
                                <TextBlock Grid.Column="2" Text="{Binding PerSecondDisplayText,Mode=OneWay}" Visibility="{Binding Mode,Converter={StaticResource Boolean2VisibilityReConverter}}" HorizontalAlignment="Right" VerticalAlignment="Center" />
                            </Grid>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>

                <!-- History Page -->
                <TreeView x:Name="HistoryHolder" Background="Transparent" ItemsSource="{Binding History}"
                          Visibility="{Binding Page,Converter={StaticResource Enum2VisibilityConverter},ConverterParameter=2}"
                          MaxHeight="{Binding Source={x:Static SystemParameters.MaximizedPrimaryScreenHeight},Converter={StaticResource RatioConverter},ConverterParameter='.6'}"
                          MouseDoubleClick="HistoryHolder_MouseDoubleClick">
                    <TreeView.Resources>
                        <helper:BindingProxy x:Key="Proxy" Data="{Binding }" />

                        <ContextMenu x:Key="HistoryGroupMenu">
                            <MenuItem Header="{DynamicResource DamageMeterPanel_GroupMode}" IsCheckable="True" IsChecked="{Binding Data.GroupMode,Source={StaticResource Proxy}}" />
                            <MenuItem Header="{DynamicResource Text.DeleteAll}" Command="{Binding DeleteAllCommand}" />
                        </ContextMenu>
                        <ContextMenu x:Key="HistoryMenu">
                            <MenuItem Header="{DynamicResource Text.Delete}" Command="{Binding DeleteCommand}" />
                        </ContextMenu>
                    </TreeView.Resources>
                    <TreeView.ItemTemplate>
                        <HierarchicalDataTemplate ItemsSource="{Binding Path=.}">
                            <DockPanel ContextMenu="{StaticResource HistoryGroupMenu}">
                                <TextBlock Text="{Binding Name,Converter={StaticResource NameConverter},ConverterParameter=zone}" Foreground="White" />
                                <TextBlock Text="{Binding Count,StringFormat=(0)}" DockPanel.Dock="Right" Foreground="White" Margin="5 0" />
                            </DockPanel>

                            <HierarchicalDataTemplate.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel ContextMenu="{StaticResource HistoryMenu}" Margin="-12 0">
                                        <TextBlock Text="{Binding Name}" Foreground="White"  />
                                    </StackPanel>
                                </DataTemplate>
                            </HierarchicalDataTemplate.ItemTemplate>
                        </HierarchicalDataTemplate>
                    </TreeView.ItemTemplate>
                </TreeView>
            </StackPanel>
        </DockPanel>

        <!-- Growl容器用于显示活动提醒 -->
        <StackPanel hc:Growl.GrowlParent="True" hc:Growl.Token="DamageMeterGrowl" Margin="0,5,0,0" Panel.ZIndex="99" />
    </Grid>
</Window>