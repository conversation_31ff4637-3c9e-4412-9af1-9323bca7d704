package service

import (
	"fmt"
	"strconv"
	"strings"

	"gorm.io/gorm"
	"udp-server/server/internal/gateway/model"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"
)

type AdminPermissionService struct {
	db                *gorm.DB
	permissionService *service.PermissionService
}

// 创建管理员权限服务实例
func NewAdminPermissionService(db *gorm.DB, permissionService *service.PermissionService) *AdminPermissionService {
	return &AdminPermissionService{
		db:                db,
		permissionService: permissionService,
	}
}

// 检查管理员是否有指定权限
func (s *AdminPermissionService) HasPermission(adminUID uint64, permission string) (bool, error) {
	// 获取管理员的power字段
	powerString, err := s.getAdminPower(adminUID)
	if err != nil {
		return false, err
	}

	// 解析power字符串为权限ID列表
	powerIDs := s.parsePowerString(powerString)

	// 检查是否为超级管理员（包含权限ID 20）
	if s.containsPowerID(powerIDs, 20) {
		return true, nil
	}

	// 根据权限键查找对应的权限ID
	adminItem := model.GetAdminItemByPermission(permission)
	if adminItem == nil {
		return false, fmt.Errorf("未知权限: %s", permission)
	}

	// 检查是否包含该权限ID
	return s.containsPowerID(powerIDs, int(adminItem.ID)), nil
}

// 检查是否为超级管理员
func (s *AdminPermissionService) IsSuperAdmin(adminUID uint64) (bool, error) {
	// 获取管理员的power字段
	powerString, err := s.getAdminPower(adminUID)
	if err != nil {
		return false, err
	}

	// 解析power字符串为权限ID列表
	powerIDs := s.parsePowerString(powerString)

	// 检查是否包含超级管理员权限ID 20
	return s.containsPowerID(powerIDs, 20), nil
}

// 获取管理员的power字段
func (s *AdminPermissionService) getAdminPower(adminUID uint64) (string, error) {
	var admin struct {
		Power string `gorm:"column:power"`
	}

	err := s.db.Table("bns_useradmin").
		Select("power").
		Where("uid = ?", adminUID).
		First(&admin).Error

	if err != nil {
		return "", fmt.Errorf("获取管理员权限失败: %v", err)
	}

	return admin.Power, nil
}

// 解析power字符串
func (s *AdminPermissionService) parsePowerString(powerString string) []int {
	if powerString == "" {
		return []int{}
	}

	parts := strings.Split(powerString, ",")
	powerIDs := make([]int, 0, len(parts))

	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		if id, err := strconv.Atoi(part); err == nil {
			powerIDs = append(powerIDs, id)
		}
	}

	return powerIDs
}

// 检查权限ID列表是否包含指定ID
func (s *AdminPermissionService) containsPowerID(powerIDs []int, targetID int) bool {
	for _, id := range powerIDs {
		if id == targetID {
			return true
		}
	}
	return false
}

// 获取管理员的所有权限
func (s *AdminPermissionService) GetAdminPermissions(adminUID uint64) ([]string, error) {
	// 获取管理员的power字段
	powerString, err := s.getAdminPower(adminUID)
	if err != nil {
		return nil, err
	}

	// 解析power字符串为权限ID列表
	powerIDs := s.parsePowerString(powerString)

	// 将权限ID转换为权限键
	permissions := make([]string, 0, len(powerIDs))
	for _, powerID := range powerIDs {
		if adminItem := model.GetAdminItemByID(uint64(powerID)); adminItem != nil {
			permissions = append(permissions, adminItem.Permission)
		}
	}

	return permissions, nil
}

// 获取管理员的权限列表
func (s *AdminPermissionService) GetAdminPowerIDs(adminUID uint64) ([]int, error) {
	// 获取管理员的power字段
	powerString, err := s.getAdminPower(adminUID)
	if err != nil {
		return nil, err
	}

	// 解析power字符串为权限ID列表
	return s.parsePowerString(powerString), nil
}

// 更新管理员权限（通过更新power字段）
func (s *AdminPermissionService) UpdateAdminPermissions(adminUID uint64, permissions []string) error {
	// 将权限键转换为权限ID
	powerIDs := make([]int, 0, len(permissions))
	for _, permission := range permissions {
		if adminItem := model.GetAdminItemByPermission(permission); adminItem != nil {
			powerIDs = append(powerIDs, int(adminItem.ID))
		}
	}

	// 构建power字符串
	powerString := s.buildPowerString(powerIDs)

	// 更新管理员的power字段
	err := s.db.Table("bns_useradmin").
		Where("uid = ?", adminUID).
		Update("power", powerString).Error

	if err != nil {
		return fmt.Errorf("更新管理员权限失败: %v", err)
	}

	return nil
}

// 构建power字符串
func (s *AdminPermissionService) buildPowerString(powerIDs []int) string {
	if len(powerIDs) == 0 {
		return ""
	}

	parts := make([]string, len(powerIDs))
	for i, id := range powerIDs {
		parts[i] = strconv.Itoa(id)
	}

	return strings.Join(parts, ",")
}

// 初始化权限项表
func (s *AdminPermissionService) InitializeAdminItems() error {
	logger.Info("开始初始化权限项表...")

	// 获取默认权限项定义
	items := model.GetDefaultAdminItems()

	for _, item := range items {
		// 检查权限项是否已存在
		var count int64
		err := s.db.Model(&model.AdminItem{}).
			Where("id = ?", item.ID).
			Count(&count).Error

		if err != nil {
			logger.Error("检查权限项失败: ID=%d, Error=%v", item.ID, err)
			continue
		}

		if count > 0 {
			// 权限项已存在，更新信息
			err = s.db.Model(&model.AdminItem{}).
				Where("id = ?", item.ID).
				Updates(map[string]interface{}{
					"permission": item.Permission,
					"name":       item.Name,
				}).Error

			if err != nil {
				logger.Error("更新权限项失败: ID=%d, Error=%v", item.ID, err)
			}
		} else {
			// 创建新权限项
			err = s.db.Create(&item).Error
			if err != nil {
				logger.Error("创建权限项失败: ID=%d, Error=%v", item.ID, err)
			} else {
				logger.Info("创建权限项成功: ID=%d, Permission=%s", item.ID, item.Permission)
			}
		}
	}

	logger.Info("权限项表初始化完成")
	return nil
}
