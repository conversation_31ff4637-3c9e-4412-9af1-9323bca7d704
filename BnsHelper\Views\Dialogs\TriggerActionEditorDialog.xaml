<hc:Window x:Class="Xylia.BnsHelper.Views.Dialogs.TriggerActionEditorDialog"
           xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
           xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
           xmlns:hc="https://handyorg.github.io/handycontrol"
           xmlns:triggers="clr-namespace:Xylia.BnsHelper.Models.Triggers"
           Title="{Binding Title}" Width="450" SizeToContent="Height"
           WindowStartupLocation="CenterOwner"
           ResizeMode="NoResize">

    <hc:Window.Resources>
        <!-- 音频播放动作模板 -->
        <DataTemplate DataType="{x:Type triggers:AudioPlayAction}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="音频文件:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding AudioPath}" Margin="0,0,5,10" IsReadOnly="True" ToolTip="{Binding AudioPath}"/>
                <Button Grid.Row="0" Grid.Column="2" Command="{Binding DataContext.BrowseAudioFileCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                        Width="70" Margin="0,0,5,10" Style="{StaticResource ButtonPrimary}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📁" FontSize="12" Margin="0,0,3,0"/>
                        <TextBlock Text="浏览"/>
                    </StackPanel>
                </Button>
                <Button Grid.Row="0" Grid.Column="3" Command="{Binding DataContext.TestPlayAudioCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                        Width="70" Margin="0,0,0,10" Style="{StaticResource ButtonSuccess}">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="▶️" FontSize="12" Margin="0,0,3,0"/>
                        <TextBlock Text="测试"/>
                    </StackPanel>
                </Button>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="音量:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                    <hc:NumericUpDown Value="{Binding Volume}" Minimum="0" Maximum="100" Width="80"/>
                    <TextBlock Text="%" VerticalAlignment="Center" Margin="5,0,0,0"/>
                </StackPanel>

                <CheckBox Grid.Row="2" Grid.Column="1" Content="循环播放" IsChecked="{Binding Loop}" Margin="0,0,0,10"/>
            </Grid>
        </DataTemplate>

        <!-- TTS语音播报动作模板 -->
        <DataTemplate DataType="{x:Type triggers:TTSAction}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="播报文本:" VerticalAlignment="Top" Margin="0,5,10,10"/>
                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding Text}" TextWrapping="Wrap" AcceptsReturn="True" Height="60" Margin="0,0,0,10"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="语音速度:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                    <hc:NumericUpDown Value="{Binding Rate}" Minimum="0" Maximum="10" Width="80"/>
                    <TextBlock Text="(0-10)" VerticalAlignment="Center" Margin="5,0,0,0"/>
                </StackPanel>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="音量:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                    <hc:NumericUpDown Value="{Binding Volume}" Minimum="0" Maximum="100" Width="80"/>
                    <TextBlock Text="%" VerticalAlignment="Center" Margin="5,0,0,0"/>
                </StackPanel>

                <TextBlock Grid.Row="3" Grid.Column="0" Text="语音名称:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <ComboBox Grid.Row="3" Grid.Column="1" ItemsSource="{Binding Voices}" Text="{Binding Voice}" Margin="0,0,0,10" />
            </Grid>
        </DataTemplate>

        <!-- 日志记录动作模板 -->
        <DataTemplate DataType="{x:Type triggers:LogAction}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="日志消息:" VerticalAlignment="Top" Margin="0,5,10,10"/>
                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding Message}" TextWrapping="Wrap" AcceptsReturn="True" Height="60" Margin="0,0,0,10"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="日志级别:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <ComboBox Grid.Row="1" Grid.Column="1" Width="120" HorizontalAlignment="Left" Margin="0,0,0,10" SelectedIndex="{Binding Level,Converter={StaticResource Enum2NumberConverter}}" >
                    <ComboBoxItem Content="调试" />
                    <ComboBoxItem Content="信息" />
                    <ComboBoxItem Content="警告" />
                    <ComboBoxItem Content="错误" />
                </ComboBox>
            </Grid>
        </DataTemplate>

        <!-- 系统通知动作模板 -->
        <DataTemplate DataType="{x:Type triggers:NotificationAction}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="通知标题:" VerticalAlignment="Center" Margin="0,0,10,10" Visibility="Collapsed" />
                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding Title}" Margin="0,0,0,10" Visibility="Collapsed" />

                <TextBlock Grid.Row="1" Grid.Column="0" Text="通知消息:" VerticalAlignment="Top" Margin="0,5,10,10"/>
                <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Message}" TextWrapping="Wrap" AcceptsReturn="True" Height="60" Margin="0,0,0,10"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="图标路径:" VerticalAlignment="Center" Margin="0,0,10,10" Visibility="Collapsed" />
                <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Icon}" Margin="0,0,0,10" Visibility="Collapsed" />

                <TextBlock Grid.Row="3" Grid.Column="0" Text="显示时长:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <StackPanel Grid.Row="3" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                    <hc:NumericUpDown Value="{Binding Duration}" Minimum="1000" Maximum="30000" Width="80"/>
                    <TextBlock Text="毫秒" VerticalAlignment="Center" Margin="5,0,0,0"/>
                </StackPanel>
            </Grid>
        </DataTemplate>

        <!-- 游戏内消息动作模板 -->
        <DataTemplate DataType="{x:Type triggers:SendGameMessageAction}">
            <StackPanel>
                <TextBox hc:TitleElement.Title="消息内容" Text="{Binding Message}" TextWrapping="Wrap" AcceptsReturn="True" Height="60" Margin="0,0,0,10" Style="{StaticResource TextBoxExtend}" />
                <CheckBox IsChecked="{Binding Headline}" Content="使用横幅" />
            </StackPanel>
        </DataTemplate>

        <!-- 鼠标操作动作模板 -->
        <DataTemplate DataType="{x:Type triggers:MouseAction}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="操作类型:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <ComboBox Grid.Row="0" Grid.Column="1" Width="120" HorizontalAlignment="Left" Margin="0,0,0,10" SelectedIndex="{Binding Operation,Converter={StaticResource Enum2NumberConverter}}">
                    <ComboBoxItem Content="左键点击" Tag="LeftClick"/>
                    <ComboBoxItem Content="右键点击" Tag="RightClick"/>
                    <ComboBoxItem Content="移动" Tag="Move"/>
                    <ComboBoxItem Content="滚轮" Tag="Scroll"/>
                </ComboBox>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="坐标位置:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="X:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <hc:NumericUpDown Value="{Binding X}" Minimum="0" Maximum="3840" Width="80" Margin="0,0,10,0"/>
                    <TextBlock Text="Y:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <hc:NumericUpDown Value="{Binding Y}" Minimum="0" Maximum="2160" Width="80"/>
                </StackPanel>

                <CheckBox Grid.Row="2" Grid.Column="1" Content="相对坐标" IsChecked="{Binding Relative}" Margin="0,0,0,10"/>

                <TextBlock Grid.Row="3" Grid.Column="0" Text="点击次数:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <hc:NumericUpDown Grid.Row="3" Grid.Column="1" Value="{Binding ClickCount}" Minimum="1" Maximum="10" Width="80" HorizontalAlignment="Left" Margin="0,0,0,10"/>
            </Grid>
        </DataTemplate>

        <!-- 键盘输入动作模板 -->
        <DataTemplate DataType="{x:Type triggers:KeyboardInputAction}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="按键序列:" VerticalAlignment="Top" Margin="0,5,10,10" />
                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding Keys}" TextWrapping="Wrap" Margin="0,0,0,10" />

                <TextBlock Grid.Row="1" Grid.Column="0" Text="按键延时:" VerticalAlignment="Center" Margin="0,0,10,10" />
                <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                    <hc:NumericUpDown Value="{Binding KeyDelay}" Minimum="0" Maximum="5000" Width="80"/>
                    <TextBlock Text="毫秒" VerticalAlignment="Center" Margin="5,0,0,0"/>
                </StackPanel>
            </Grid>
        </DataTemplate>

        <!-- 文本显示动作模板 -->
        <DataTemplate DataType="{x:Type triggers:TextDisplayAction}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="唯一标识:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding UniqueId}" Margin="0,0,0,10"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="显示文本:" VerticalAlignment="Top" Margin="0,5,10,10"/>
                <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Text}" TextWrapping="Wrap" AcceptsReturn="True" Height="60" Margin="0,0,0,10"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="显示时长:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                    <hc:NumericUpDown Value="{Binding DurationMs}" Minimum="0" Maximum="60000" Width="80"/>
                    <TextBlock Text="毫秒 (0=永久)" VerticalAlignment="Center" Margin="5,0,0,0"/>
                </StackPanel>

                <TextBlock Grid.Row="3" Grid.Column="0" Text="字体大小:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <hc:NumericUpDown Grid.Row="3" Grid.Column="1" Value="{Binding FontSize}" Minimum="8" Maximum="72" Width="80" Margin="0,0,0,10"/>

                <TextBlock Grid.Row="4" Grid.Column="0" Text="文本颜色:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding TextColor}" Margin="0,0,0,10"/>

                <TextBlock Grid.Row="5" Grid.Column="0" Text="背景颜色:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <TextBox Grid.Row="5" Grid.Column="1" Text="{Binding BackgroundColor}" Margin="0,0,0,10"/>

                <TextBlock Grid.Row="6" Grid.Column="0" Text="显示位置:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <StackPanel Grid.Row="6" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="X:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <hc:NumericUpDown Value="{Binding PositionX}" Minimum="0" Maximum="3840" Width="80" Margin="0,0,10,0"/>
                    <TextBlock Text="Y:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <hc:NumericUpDown Value="{Binding PositionY}" Minimum="0" Maximum="2160" Width="80"/>
                </StackPanel>
            </Grid>
        </DataTemplate>

        <!-- 计数器动作模板 -->
        <DataTemplate DataType="{x:Type triggers:CounterAction}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="计数器名称:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding CounterName}" Margin="0,0,0,10"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="操作类型:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <ComboBox Grid.Row="1" Grid.Column="1" Width="120" HorizontalAlignment="Left" Margin="0,0,0,10" SelectedIndex="{Binding Operation,Converter={StaticResource Enum2NumberConverter}}">
                    <ComboBoxItem Content="递增" Tag="Increment"/>
                    <ComboBoxItem Content="递减" Tag="Decrement"/>
                    <ComboBoxItem Content="重置" Tag="Reset"/>
                    <ComboBoxItem Content="设置" Tag="Set"/>
                </ComboBox>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="变化值:" VerticalAlignment="Center" Margin="0,0,10,10"/>
                <hc:NumericUpDown Grid.Row="2" Grid.Column="1" Value="{Binding Value}" Minimum="-1000" Maximum="1000" Width="80" HorizontalAlignment="Left" Margin="0,0,0,10"/>
            </Grid>
        </DataTemplate>
    </hc:Window.Resources>


    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 动作特定设置 -->
        <ContentPresenter Grid.Row="0" Content="{Binding Action}" Margin="10 5" />

        <!-- 按钮区域 -->
        <Border Grid.Row="1" Background="{DynamicResource RegionBrush}" Padding="10" 
                BorderBrush="{DynamicResource BorderBrush}" BorderThickness="0,1,0,0" Margin="-10,10,-10,-10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button Content="取消" Command="{Binding CancelCommand}" Width="80" Margin="0,0,10,0"/>
                <Button Content="确定" Command="{Binding OkCommand}" Style="{StaticResource ButtonPrimary}" Width="80" />
            </StackPanel>
        </Border>
    </Grid>
</hc:Window>