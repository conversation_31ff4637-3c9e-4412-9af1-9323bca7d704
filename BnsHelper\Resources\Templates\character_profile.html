<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{CHARACTER_NAME}} - 角色资料</title>
    <link rel="stylesheet" href="https://down.qq.com/bns/static/ingameWeb/ingame/bns/ue4/character/css/character.css">
    <link rel="stylesheet" href="character.css">
    <style>
        .header,.header:after{
          border-bottom: 0;
        }
        .charater-view{
          overflow:inherit;
        }
        .profileimg{
          cursor:pointer;
        }
        .compare{
          z-index: 10002;
        }
        {{CUSTOM_STYLES}}
    </style>
</head>
<body>
    {{CUSTOM_HTML_TOP}}
    
    <div id="container" class="container hiddensearch">
        {{BGM_HTML}}
        <img class="background-image" src="{{BACKGROUND_IMAGE}}" style="opacity:0.25; height: 100%; position: absolute; width: 100%; z-index: 0;">
        
        <div class="search-wrap">
            <!-- 搜索区域 -->
            <div class="character-search-wrap">
                <!-- 职业过滤器 -->
                <button class="btn-filter">过滤器</button>
                <div class="layer job-filter-list">
                    <ul></ul>
                </div>
                
                <form id="characterAutoSuggest" class="character-search" method="get" action="" onsubmit="return false" role="search">
                    <input type="search" value="" autocomplete="off" data-name="suggest_input" placeholder="请输入角色名称" />
                    <button value="X" title="删除" data-name="suggest_delete" style="display: none;"></button>
                    <button value="GO" title="搜索" data-name="suggest_submit"></button>
                    <div data-name="suggest_wrap" style="display:none;">
                        <div data-name="suggest_list" style="display:none;">
                            <div data-name="suggest_scroll">
                                <ul></ul>
                            </div>
                        </div>
                        <div data-name="suggest_recent" style="display:none;">
                            <div data-name="suggest_recent_scroll">
                                <ul></ul>
                                <div data-name="suggest_recent-none">最近没有进行搜索</div>
                            </div>
                            <div data-name="suggest_recent-del">
                                <button type="button" style="display:none;">删除所有记录</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- 搜索结果 -->
            <section class="result-list-wrap">
                <div class="messagebox messagebox-first">
                    <h2>请先搜索角色</h2>
                </div>
                <div class="messagebox messagebox-noresult" style="display: none;">
                    <h2>没有搜索结果</h2>
                    <p class="description">请检查后再尝试搜索</p>
                </div>
                <div class="result-count" style="display: block;">搜索结果
                    <strong class="count">0</strong>个
                </div>
                <div class="character-list" style="display: block;"></div>
                <div class="ui-pagination" style="display: block;">
                    <ul></ul>
                </div>
                
                <button class="btn-recent-character pos-bottom">最近浏览角色</button>
                <div class="layer recent-list">
                    <button class="btn-recent-character">最近浏览角色</button>
                    <div class="character-list" style="display: block;"></div>
                    <div class="ui-pagination" style="display: block;">
                        <ul style="display: none;">
                            <li class="current">1</li>
                        </ul>
                    </div>
                </div>
            </section>
        </div>
        
        <div class="contents-wrap">
            <div class="loading" style="display: block;">
                <span class="loader">
                    <img src="//down.qq.com/bns/static/ingameWeb/ingame/bns/character_v2/loading.gif" alt="" />
                </span>
                <span class="text">数据加载中</span>
            </div>
            
            <!-- 上方角色信息 -->
            <section class="header"></section>
            
            <!-- 角色照片/八卦牌信息 -->
            <section class="info-character">
                <div class="charater-view">
                    <div class="photo"></div>
                </div>
                
                <div class="gem-wrap">
                    <input id="gemTab1" type="radio" name="tab" checked="checked">
                    <input id="gemTab2" type="radio" name="tab">
                    <label for="gemTab1">装备八卦牌</label>
                    <span class="bar"></span>
                    <label for="gemTab2">备用八卦牌</label>
                    <div class="gem-icon-bg" id="gem-icon-default">
                        <div class="gem-icon"></div>
                    </div>
                    <div class="gem-icon-bg" id="gem-icon-spare">
                        <div class="gem-icon"></div>
                    </div>
                </div>
            </section>
            
            <!-- 装备信息 -->
            <section class="info-item">
                <div class="item-wrap">
                    <div class="weapon-wrap">
                        <div class="icon">
                            <p class="item-img"></p>
                            <div class="quality">
                                <span class="bar" style="width:0px"></span>
                                <span class="text">0 / 0</span>
                            </div>
                        </div>
                        <div class="item-name">
                            <span class="grade_none">武器</span>
                        </div>
                        <div class="enchant">
                            <div class="enchant-usable1"></div>
                        </div>
                    </div>
                    
                    <!-- 饰品信息 -->
                    <div class="accessory-wrap">
                        <div class="ring">
                            <div class="item-img"></div>
                            <div class="item-name">
                                <span class="grade_none">戒指</span>
                            </div>
                        </div>
                        <div class="earring">
                            <div class="item-img"></div>
                            <div class="item-name">
                                <span class="grade_none">耳环</span>
                            </div>
                        </div>
                        <div class="necklace">
                            <div class="item-img"></div>
                            <div class="item-name">
                                <span class="grade_none">项链</span>
                            </div>
                        </div>
                        <div class="bracelet">
                            <div class="item-img"></div>
                            <div class="item-name">
                                <span class="grade_none">手镯</span>
                            </div>
                        </div>
                        <div class="belt">
                            <div class="item-img"></div>
                            <div class="item-name">
                                <span class="grade_none">腰带</span>
                            </div>
                        </div>
                        <div class="gloves">
                            <div class="item-img"></div>
                            <div class="item-name">
                                <span class="grade_none">手套</span>
                            </div>
                        </div>
                        <div class="soul">
                            <div class="item-img"></div>
                            <div class="item-name">
                                <span class="grade_none">魂</span>
                            </div>
                        </div>
                        <div class="soul-2">
                            <div class="item-img"></div>
                            <div class="item-name">
                                <span class="grade_none">灵</span>
                            </div>
                        </div>
                        <div class="guard">
                            <div class="item-img"></div>
                            <div class="item-name">
                                <span class="grade_none">守护石</span>
                            </div>
                        </div>
                        <div class="nova">
                            <div class="item-img"></div>
                            <div class="item-name">
                                <span class="grade_none">星</span>
                            </div>
                        </div>
                        <div class="singongpae">
                            <div class="item-img"></div>
                            <div class="item-name">
                                <span class="grade_none">神功牌</span>
                            </div>
                        </div>
                        <div class="rune">
                            <div class="item-img"></div>
                            <div class="item-name">
                                <span class="grade_none">秘功牌</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
    
    <script src="https://down.qq.com/bns/static/ingameWeb/ingame/bns/ue4/character/js/jquery-1.11.1.min.js"></script>
    <script src="https://down.qq.com/bns/static/ingameWeb/ingame/bns/ue4/character/js/character.js"></script>
    <script>
        // 初始化应用
        var app = new nc.bns.ingamechar({
            apiDomain: '',
            staticPath: 'https://down.qq.com/bns/static/ingameWeb/ingame/bns/ue4/character',
            profileImgPath: '',
            isAllowed: true
        });
        
        // 角色数据
        var characterData = {{CHARACTER_DATA}};
        
        {{CUSTOM_SCRIPTS}}
    </script>
    
    {{CUSTOM_HTML_BOTTOM}}
</body>
</html>
