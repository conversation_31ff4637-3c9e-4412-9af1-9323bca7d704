﻿<s:Grid x:Class="Xylia.BnsHelper.Views.ItemMapPanel"
	xmlns="https://github.com/xyliaup/bns-preview-tools"
	xmlns:s="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	xmlns:hc="https://handyorg.github.io/handycontrol"
	Width="1200" s:TextElement.Foreground="{s:DynamicResource PrimaryTextBrush}">
    <s:Grid.RowDefinitions>
        <s:RowDefinition Height="40" />
        <s:RowDefinition Height="700" />
    </s:Grid.RowDefinitions>
    <s:Grid.ColumnDefinitions>
        <s:ColumnDefinition Width="Auto" />
        <s:ColumnDefinition Width="*" />
    </s:Grid.ColumnDefinitions>
    <s:Grid.Resources>
        <s:Style TargetType="s:ToolTip" BasedOn="{s:StaticResource ToolTipBaseStyle}" />

        <s:Style TargetType="BnsCustomGraphMapEdge">
            <s:Style.Triggers>
                <s:Trigger Property="Highlight" Value="True">
                    <s:Setter Property="Effect">
                        <s:Setter.Value>
                            <s:DropShadowEffect ShadowDepth="0" Color="Yellow" />
                        </s:Setter.Value>
                    </s:Setter>
                </s:Trigger>
            </s:Style.Triggers>
        </s:Style>
    </s:Grid.Resources>

    <BnsCustomRadioButtonWidget Name="ItemMapPanel_Tab" s:Grid.ColumnSpan="99" BnsCustomToggleButtonWidget.Checked="ItemMapPanel_Tab_RadioButton_Checked">
        <BnsCustomToggleButtonWidget Name="ItemMapPanel_Tab_RadioButton_13" LayoutData.Offsets="552.0117 0 45.000977 40" MetaData="tooltip=UI.ButtonTooltip.Nova">
            <BnsCustomToggleButtonWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_Tab_RadioButton_9" Offset1="1" />
            </BnsCustomToggleButtonWidget.HorizontalResizeLink>
            <BnsCustomToggleButtonWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" />
            </BnsCustomToggleButtonWidget.VerticalResizeLink>
            <BnsCustomToggleButtonWidget.String>
                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Tab_12.Normal_Tab_12" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" bWordWrap="true" />
            </BnsCustomToggleButtonWidget.String>
            <BnsCustomToggleButtonWidget.NormalImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="0 116" ImageUVSize="18 11" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="0.3" />
            </BnsCustomToggleButtonWidget.NormalImageProperty>
            <BnsCustomToggleButtonWidget.ExpansionComponentList>
                <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="160 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="0.5" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
            </BnsCustomToggleButtonWidget.ExpansionComponentList>
            <BnsCustomLabelWidget Name="ItemMapPanel_Tab_RadioButton_13_Badge" LayoutData.Anchors="1 0 1 0" LayoutData.Offsets="4 -8 20.000977 20">
                <BnsCustomLabelWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="-4" />
                </BnsCustomLabelWidget.HorizontalResizeLink>
                <BnsCustomLabelWidget.VerticalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Offset1="-8" />
                </BnsCustomLabelWidget.VerticalResizeLink>
                <BnsCustomLabelWidget.String>
                    <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_10.Normal_10" LabelText="0" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBound="0 -2" Padding="6 0" />
                </BnsCustomLabelWidget.String>
                <BnsCustomLabelWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bShow="true" ExpansionName="background">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="170 29" ImageUVSize="20 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomLabelWidget.ExpansionComponentList>
            </BnsCustomLabelWidget>
        </BnsCustomToggleButtonWidget>
        <BnsCustomToggleButtonWidget Name="ItemMapPanel_Tab_RadioButton_9" LayoutData.Offsets="506.01074 0 45.000977 40" MetaData="tooltip=UI.ButtonTooltip.Pet">
            <BnsCustomToggleButtonWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_Tab_RadioButton_12" Offset1="1" />
            </BnsCustomToggleButtonWidget.HorizontalResizeLink>
            <BnsCustomToggleButtonWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" />
            </BnsCustomToggleButtonWidget.VerticalResizeLink>
            <BnsCustomToggleButtonWidget.String>
                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Tab_12.Normal_Tab_12" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" bWordWrap="true" />
            </BnsCustomToggleButtonWidget.String>
            <BnsCustomToggleButtonWidget.NormalImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="0 116" ImageUVSize="18 11" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="0.3" />
            </BnsCustomToggleButtonWidget.NormalImageProperty>
            <BnsCustomToggleButtonWidget.ExpansionComponentList>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="512 64" ImageUVSize="64 64" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="0.3" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="Icon_c" WidgetSubState="Expansion_WidgetSubState_Checked">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="512 64" ImageUVSize="64 64" EnableDrawImage="true" EnableResourceSize="true" EnableDrawColor="true" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="5A70A0" Opacity="0.7" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
            </BnsCustomToggleButtonWidget.ExpansionComponentList>
            <BnsCustomLabelWidget Name="ItemMapPanel_Tab_RadioButton_9_Badge" LayoutData.Anchors="1 0 1 0" LayoutData.Offsets="4 -8 20.000977 20">
                <BnsCustomLabelWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="-4" />
                </BnsCustomLabelWidget.HorizontalResizeLink>
                <BnsCustomLabelWidget.VerticalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Offset1="-8" />
                </BnsCustomLabelWidget.VerticalResizeLink>
                <BnsCustomLabelWidget.String>
                    <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_10.Normal_10" LabelText="0" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBound="0 -2" Padding="6 0" />
                </BnsCustomLabelWidget.String>
                <BnsCustomLabelWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bShow="true" ExpansionName="background">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="170 29" ImageUVSize="20 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomLabelWidget.ExpansionComponentList>
            </BnsCustomLabelWidget>
        </BnsCustomToggleButtonWidget>
        <BnsCustomToggleButtonWidget Name="ItemMapPanel_Tab_RadioButton_12" LayoutData.Offsets="460.00977 0 45.000977 40" MetaData="tooltip=UI.ButtonTooltip.soul-2">
            <BnsCustomToggleButtonWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_Tab_RadioButton_7" Offset1="1" />
            </BnsCustomToggleButtonWidget.HorizontalResizeLink>
            <BnsCustomToggleButtonWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" />
            </BnsCustomToggleButtonWidget.VerticalResizeLink>
            <BnsCustomToggleButtonWidget.String>
                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Tab_12.Normal_Tab_12" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" bWordWrap="true" />
            </BnsCustomToggleButtonWidget.String>
            <BnsCustomToggleButtonWidget.NormalImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="0 116" ImageUVSize="18 11" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="0.3" />
            </BnsCustomToggleButtonWidget.NormalImageProperty>
            <BnsCustomToggleButtonWidget.ExpansionComponentList>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="960 704" ImageUVSize="64 64" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="0.3" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="Icon_c" WidgetSubState="Expansion_WidgetSubState_Checked">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="960 704" ImageUVSize="64 64" EnableDrawImage="true" EnableResourceSize="true" EnableDrawColor="true" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="5A70A0" Opacity="0.7" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
            </BnsCustomToggleButtonWidget.ExpansionComponentList>
            <BnsCustomLabelWidget Name="ItemMapPanel_Tab_RadioButton_12_Badge" LayoutData.Anchors="1 0 1 0" LayoutData.Offsets="4 -8 20.000977 20">
                <BnsCustomLabelWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="-4" />
                </BnsCustomLabelWidget.HorizontalResizeLink>
                <BnsCustomLabelWidget.VerticalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Offset1="-8" />
                </BnsCustomLabelWidget.VerticalResizeLink>
                <BnsCustomLabelWidget.String>
                    <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_10.Normal_10" LabelText="0" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBound="0 -2" Padding="6 0" />
                </BnsCustomLabelWidget.String>
                <BnsCustomLabelWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bShow="true" ExpansionName="background">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="170 29" ImageUVSize="20 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomLabelWidget.ExpansionComponentList>
            </BnsCustomLabelWidget>
        </BnsCustomToggleButtonWidget>
        <BnsCustomToggleButtonWidget Name="ItemMapPanel_Tab_RadioButton_7" LayoutData.Offsets="414.0088 0 45.000977 40" MetaData="tooltip=UI.ButtonTooltip.Soul">
            <BnsCustomToggleButtonWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_Tab_RadioButton_8" Offset1="1" />
            </BnsCustomToggleButtonWidget.HorizontalResizeLink>
            <BnsCustomToggleButtonWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" />
            </BnsCustomToggleButtonWidget.VerticalResizeLink>
            <BnsCustomToggleButtonWidget.String>
                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Tab_12.Normal_Tab_12" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" bWordWrap="true" />
            </BnsCustomToggleButtonWidget.String>
            <BnsCustomToggleButtonWidget.NormalImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="0 116" ImageUVSize="18 11" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="0.3" />
            </BnsCustomToggleButtonWidget.NormalImageProperty>
            <BnsCustomToggleButtonWidget.ExpansionComponentList>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="448 64" ImageUVSize="64 64" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="0.3" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="Icon_c" WidgetSubState="Expansion_WidgetSubState_Checked">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="448 64" ImageUVSize="64 64" EnableDrawImage="true" EnableResourceSize="true" EnableDrawColor="true" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="5A70A0" Opacity="0.7" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
            </BnsCustomToggleButtonWidget.ExpansionComponentList>
            <BnsCustomLabelWidget Name="ItemMapPanel_Tab_RadioButton_7_Badge" LayoutData.Anchors="1 0 1 0" LayoutData.Offsets="4 -8 20.000977 20">
                <BnsCustomLabelWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="-4" />
                </BnsCustomLabelWidget.HorizontalResizeLink>
                <BnsCustomLabelWidget.VerticalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Offset1="-8" />
                </BnsCustomLabelWidget.VerticalResizeLink>
                <BnsCustomLabelWidget.String>
                    <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_10.Normal_10" LabelText="0" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBound="0 -2" Padding="6 0" />
                </BnsCustomLabelWidget.String>
                <BnsCustomLabelWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bShow="true" ExpansionName="background">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="170 29" ImageUVSize="20 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomLabelWidget.ExpansionComponentList>
            </BnsCustomLabelWidget>
        </BnsCustomToggleButtonWidget>
        <BnsCustomToggleButtonWidget Name="ItemMapPanel_Tab_RadioButton_8" LayoutData.Offsets="368.0078 0 45.000977 40" MetaData="tooltip=UI.ButtonTooltip.Gloves">
            <BnsCustomToggleButtonWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_Tab_RadioButton_6" Offset1="1" />
            </BnsCustomToggleButtonWidget.HorizontalResizeLink>
            <BnsCustomToggleButtonWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" />
            </BnsCustomToggleButtonWidget.VerticalResizeLink>
            <BnsCustomToggleButtonWidget.String>
                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Tab_12.Normal_Tab_12" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" bWordWrap="true" />
            </BnsCustomToggleButtonWidget.String>
            <BnsCustomToggleButtonWidget.NormalImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="0 116" ImageUVSize="18 11" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="0.3" />
            </BnsCustomToggleButtonWidget.NormalImageProperty>
            <BnsCustomToggleButtonWidget.ExpansionComponentList>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="254 519" ImageUVSize="64 56" EnableDrawImage="true" EnableResourceSize="true" EnableDrawColor="False" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="6D7380" Offset="0 3" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="C_Icon" WidgetSubState="Expansion_WidgetSubState_Checked">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="254 519" ImageUVSize="64 56" EnableDrawImage="true" EnableResourceSize="true" EnableDrawColor="true" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="5A70A0" Offset="0 3" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
            </BnsCustomToggleButtonWidget.ExpansionComponentList>
            <BnsCustomLabelWidget Name="ItemMapPanel_Tab_RadioButton_8_Badge" LayoutData.Anchors="1 0 1 0" LayoutData.Offsets="4 -8 20.000977 20">
                <BnsCustomLabelWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="-4" />
                </BnsCustomLabelWidget.HorizontalResizeLink>
                <BnsCustomLabelWidget.VerticalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Offset1="-8" />
                </BnsCustomLabelWidget.VerticalResizeLink>
                <BnsCustomLabelWidget.String>
                    <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_10.Normal_10" LabelText="0" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBound="0 -2" Padding="6 0" />
                </BnsCustomLabelWidget.String>
                <BnsCustomLabelWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bShow="true" ExpansionName="background">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="170 29" ImageUVSize="20 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomLabelWidget.ExpansionComponentList>
            </BnsCustomLabelWidget>
        </BnsCustomToggleButtonWidget>
        <BnsCustomToggleButtonWidget Name="ItemMapPanel_Tab_RadioButton_6" LayoutData.Offsets="322.00684 0 45.000977 40" MetaData="tooltip=UI.ButtonTooltip.Belt">
            <BnsCustomToggleButtonWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_Tab_RadioButton_5" Offset1="1" />
            </BnsCustomToggleButtonWidget.HorizontalResizeLink>
            <BnsCustomToggleButtonWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" />
            </BnsCustomToggleButtonWidget.VerticalResizeLink>
            <BnsCustomToggleButtonWidget.String>
                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Tab_12.Normal_Tab_12" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" bWordWrap="true" />
            </BnsCustomToggleButtonWidget.String>
            <BnsCustomToggleButtonWidget.NormalImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="0 116" ImageUVSize="18 11" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="0.3" />
            </BnsCustomToggleButtonWidget.NormalImageProperty>
            <BnsCustomToggleButtonWidget.ExpansionComponentList>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="387 72" ImageUVSize="64 55" EnableDrawImage="true" EnableResourceSize="true" EnableDrawColor="False" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="6D7380" Offset="0 3" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="C_Icon" WidgetSubState="Expansion_WidgetSubState_Checked">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="387 72" ImageUVSize="64 55" EnableDrawImage="False" EnableResourceSize="true" EnableDrawColor="true" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="5C72A0" Offset="0 3" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
            </BnsCustomToggleButtonWidget.ExpansionComponentList>
            <BnsCustomLabelWidget Name="ItemMapPanel_Tab_RadioButton_6_Badge" LayoutData.Anchors="1 0 1 0" LayoutData.Offsets="4 -8 20.000977 20">
                <BnsCustomLabelWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="-4" />
                </BnsCustomLabelWidget.HorizontalResizeLink>
                <BnsCustomLabelWidget.VerticalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Offset1="-8" />
                </BnsCustomLabelWidget.VerticalResizeLink>
                <BnsCustomLabelWidget.String>
                    <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_10.Normal_10" LabelText="0" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBound="0 -2" Padding="6 0" />
                </BnsCustomLabelWidget.String>
                <BnsCustomLabelWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bShow="true" ExpansionName="background">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="170 29" ImageUVSize="20 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomLabelWidget.ExpansionComponentList>
            </BnsCustomLabelWidget>
        </BnsCustomToggleButtonWidget>
        <BnsCustomToggleButtonWidget Name="ItemMapPanel_Tab_RadioButton_5" LayoutData.Offsets="276.00586 0 45.000977 40" MetaData="tooltip=UI.ButtonTooltip.Bracelet">
            <BnsCustomToggleButtonWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_Tab_RadioButton_4" Offset1="1" />
            </BnsCustomToggleButtonWidget.HorizontalResizeLink>
            <BnsCustomToggleButtonWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" />
            </BnsCustomToggleButtonWidget.VerticalResizeLink>
            <BnsCustomToggleButtonWidget.String>
                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Tab_12.Normal_Tab_12" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" bWordWrap="true" />
            </BnsCustomToggleButtonWidget.String>
            <BnsCustomToggleButtonWidget.NormalImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="0 116" ImageUVSize="18 11" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="0.3" />
            </BnsCustomToggleButtonWidget.NormalImageProperty>
            <BnsCustomToggleButtonWidget.ExpansionComponentList>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="318 74" ImageUVSize="66 50" EnableDrawImage="true" EnableResourceSize="true" EnableDrawColor="False" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="6D7380" Offset="0 3" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="C_Icon" WidgetSubState="Expansion_WidgetSubState_Checked">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="318 74" ImageUVSize="66 50" EnableDrawImage="true" EnableResourceSize="true" EnableDrawColor="true" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="5C72A0" Offset="0 3" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
            </BnsCustomToggleButtonWidget.ExpansionComponentList>
            <BnsCustomLabelWidget Name="ItemMapPanel_Tab_RadioButton_5_Badge" LayoutData.Anchors="1 0 1 0" LayoutData.Offsets="4 -8 20.000916 20">
                <BnsCustomLabelWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="-4" />
                </BnsCustomLabelWidget.HorizontalResizeLink>
                <BnsCustomLabelWidget.VerticalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Offset1="-8" />
                </BnsCustomLabelWidget.VerticalResizeLink>
                <BnsCustomLabelWidget.String>
                    <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_10.Normal_10" LabelText="0" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBound="0 -2" Padding="6 0" />
                </BnsCustomLabelWidget.String>
                <BnsCustomLabelWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bShow="true" ExpansionName="background">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="170 29" ImageUVSize="20 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomLabelWidget.ExpansionComponentList>
            </BnsCustomLabelWidget>
        </BnsCustomToggleButtonWidget>
        <BnsCustomToggleButtonWidget Name="ItemMapPanel_Tab_RadioButton_4" LayoutData.Offsets="230.00488 0 45.000977 40" MetaData="tooltip=UI.ButtonTooltip.Ring">
            <BnsCustomToggleButtonWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_Tab_RadioButton_3" Offset1="1" />
            </BnsCustomToggleButtonWidget.HorizontalResizeLink>
            <BnsCustomToggleButtonWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" />
            </BnsCustomToggleButtonWidget.VerticalResizeLink>
            <BnsCustomToggleButtonWidget.String>
                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Tab_12.Normal_Tab_12" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" bWordWrap="true" />
            </BnsCustomToggleButtonWidget.String>
            <BnsCustomToggleButtonWidget.NormalImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="0 116" ImageUVSize="18 11" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="0.3" />
            </BnsCustomToggleButtonWidget.NormalImageProperty>
            <BnsCustomToggleButtonWidget.ExpansionComponentList>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="262 72" ImageUVSize="51 54" EnableDrawImage="true" EnableResourceSize="true" EnableDrawColor="False" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="6D7380" Offset="0 3" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="C_Icon" WidgetSubState="Expansion_WidgetSubState_Checked">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="262 72" ImageUVSize="51 54" EnableDrawImage="true" EnableResourceSize="true" EnableDrawColor="true" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="5C72A0" Offset="0 3" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
            </BnsCustomToggleButtonWidget.ExpansionComponentList>
            <BnsCustomLabelWidget Name="ItemMapPanel_Tab_RadioButton_4_Badge" LayoutData.Anchors="1 0 1 0" LayoutData.Offsets="4 -8 20.000977 20">
                <BnsCustomLabelWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="-4" />
                </BnsCustomLabelWidget.HorizontalResizeLink>
                <BnsCustomLabelWidget.VerticalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Offset1="-8" />
                </BnsCustomLabelWidget.VerticalResizeLink>
                <BnsCustomLabelWidget.String>
                    <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_10.Normal_10" LabelText="0" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBound="0 -2" Padding="6 0" />
                </BnsCustomLabelWidget.String>
                <BnsCustomLabelWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bShow="true" ExpansionName="background">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="170 29" ImageUVSize="20 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomLabelWidget.ExpansionComponentList>
            </BnsCustomLabelWidget>
        </BnsCustomToggleButtonWidget>
        <BnsCustomToggleButtonWidget Name="ItemMapPanel_Tab_RadioButton_3" LayoutData.Offsets="184.0039 0 45.000977 40" MetaData="tooltip=UI.ButtonTooltip.Earring">
            <BnsCustomToggleButtonWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_Tab_RadioButton_2" Offset1="1" />
            </BnsCustomToggleButtonWidget.HorizontalResizeLink>
            <BnsCustomToggleButtonWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" />
            </BnsCustomToggleButtonWidget.VerticalResizeLink>
            <BnsCustomToggleButtonWidget.String>
                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Tab_12.Normal_Tab_12" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" bWordWrap="true" />
            </BnsCustomToggleButtonWidget.String>
            <BnsCustomToggleButtonWidget.NormalImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="0 116" ImageUVSize="18 11" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="0.3" />
            </BnsCustomToggleButtonWidget.NormalImageProperty>
            <BnsCustomToggleButtonWidget.ExpansionComponentList>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="195 69" ImageUVSize="57 59" EnableDrawImage="true" EnableResourceSize="true" EnableDrawColor="False" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="6D7380" Offset="0 3" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="C_Icon" WidgetSubState="Expansion_WidgetSubState_Checked">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="195 69" ImageUVSize="57 59" EnableDrawImage="true" EnableResourceSize="true" EnableDrawColor="true" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="5C72A0" Offset="0 3" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
            </BnsCustomToggleButtonWidget.ExpansionComponentList>
            <BnsCustomLabelWidget Name="ItemMapPanel_Tab_RadioButton_3_Badge" LayoutData.Anchors="1 0 1 0" LayoutData.Offsets="4 -8 20.000977 20">
                <BnsCustomLabelWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="-4" />
                </BnsCustomLabelWidget.HorizontalResizeLink>
                <BnsCustomLabelWidget.VerticalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Offset1="-8" />
                </BnsCustomLabelWidget.VerticalResizeLink>
                <BnsCustomLabelWidget.String>
                    <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_10.Normal_10" LabelText="0" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBound="0 -2" Padding="6 0" />
                </BnsCustomLabelWidget.String>
                <BnsCustomLabelWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bShow="true" ExpansionName="background">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="170 29" ImageUVSize="20 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomLabelWidget.ExpansionComponentList>
            </BnsCustomLabelWidget>
        </BnsCustomToggleButtonWidget>
        <BnsCustomToggleButtonWidget Name="ItemMapPanel_Tab_RadioButton_2" LayoutData.Offsets="138.00294 0 45.00096 40" MetaData="tooltip=UI.ButtonTooltip.Necklace">
            <BnsCustomToggleButtonWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_Tab_RadioButton_11" Offset1="1" />
            </BnsCustomToggleButtonWidget.HorizontalResizeLink>
            <BnsCustomToggleButtonWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" />
            </BnsCustomToggleButtonWidget.VerticalResizeLink>
            <BnsCustomToggleButtonWidget.String>
                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Tab_12.Normal_Tab_12" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" bWordWrap="true" />
            </BnsCustomToggleButtonWidget.String>
            <BnsCustomToggleButtonWidget.NormalImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="0 116" ImageUVSize="18 11" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="0.3" />
            </BnsCustomToggleButtonWidget.NormalImageProperty>
            <BnsCustomToggleButtonWidget.ExpansionComponentList>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="127 70" ImageUVSize="63 59" EnableDrawImage="true" EnableResourceSize="true" EnableDrawColor="False" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="6D7380" Offset="0 3" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="C_Icon" WidgetSubState="Expansion_WidgetSubState_Checked">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_PictogramIcon.BNSR_PictogramIcon" ImageUV="127 70" ImageUVSize="63 59" EnableDrawImage="true" EnableResourceSize="true" EnableDrawColor="true" GrayWeightValue="0.15" ImageScale="0.5" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="5C72A0" Offset="0 3" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
            </BnsCustomToggleButtonWidget.ExpansionComponentList>
            <BnsCustomLabelWidget Name="ItemMapPanel_Tab_RadioButton_2_Badge" LayoutData.Anchors="1 0 1 0" LayoutData.Offsets="4 -8 20.000961 20">
                <BnsCustomLabelWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="-4" />
                </BnsCustomLabelWidget.HorizontalResizeLink>
                <BnsCustomLabelWidget.VerticalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Offset1="-8" />
                </BnsCustomLabelWidget.VerticalResizeLink>
                <BnsCustomLabelWidget.String>
                    <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_10.Normal_10" LabelText="0" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBound="0 -2" Padding="6 0" />
                </BnsCustomLabelWidget.String>
                <BnsCustomLabelWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bShow="true" ExpansionName="background">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="170 29" ImageUVSize="20 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomLabelWidget.ExpansionComponentList>
            </BnsCustomLabelWidget>
        </BnsCustomToggleButtonWidget>
        <BnsCustomToggleButtonWidget Name="ItemMapPanel_Tab_RadioButton_11" LayoutData.Offsets="92.00197 0 45.000977 40" MetaData="tooltip=UI.ButtonTooltip.SubGem2">
            <BnsCustomToggleButtonWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_Tab_RadioButton_10" Offset1="1" />
            </BnsCustomToggleButtonWidget.HorizontalResizeLink>
            <BnsCustomToggleButtonWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" />
            </BnsCustomToggleButtonWidget.VerticalResizeLink>
            <BnsCustomToggleButtonWidget.String>
                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Tab_12.Normal_Tab_12" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" bWordWrap="true" />
            </BnsCustomToggleButtonWidget.String>
            <BnsCustomToggleButtonWidget.NormalImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="0 116" ImageUVSize="18 11" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="0.3" />
            </BnsCustomToggleButtonWidget.NormalImageProperty>
            <BnsCustomToggleButtonWidget.ExpansionComponentList>
                <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="480 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="0.5" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
            </BnsCustomToggleButtonWidget.ExpansionComponentList>
            <BnsCustomLabelWidget Name="ItemMapPanel_Tab_RadioButton_11_Badge" LayoutData.Anchors="1 0 1 0" LayoutData.Offsets="4 -8 20.000977 20">
                <BnsCustomLabelWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="-4" />
                </BnsCustomLabelWidget.HorizontalResizeLink>
                <BnsCustomLabelWidget.VerticalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Offset1="-8" />
                </BnsCustomLabelWidget.VerticalResizeLink>
                <BnsCustomLabelWidget.String>
                    <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_10.Normal_10" LabelText="0" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBound="0 -2" Padding="6 0" />
                </BnsCustomLabelWidget.String>
                <BnsCustomLabelWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bShow="true" ExpansionName="background">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="170 29" ImageUVSize="20 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomLabelWidget.ExpansionComponentList>
            </BnsCustomLabelWidget>
        </BnsCustomToggleButtonWidget>
        <BnsCustomToggleButtonWidget Name="ItemMapPanel_Tab_RadioButton_10" LayoutData.Offsets="46.000977 0 45.00099 40" MetaData="tooltip=UI.ButtonTooltip.SubGem1">
            <BnsCustomToggleButtonWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_Tab_RadioButton_1" Offset1="1" />
            </BnsCustomToggleButtonWidget.HorizontalResizeLink>
            <BnsCustomToggleButtonWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" />
            </BnsCustomToggleButtonWidget.VerticalResizeLink>
            <BnsCustomToggleButtonWidget.String>
                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Tab_12.Normal_Tab_12" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" bWordWrap="true" />
            </BnsCustomToggleButtonWidget.String>
            <BnsCustomToggleButtonWidget.NormalImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="0 116" ImageUVSize="18 11" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="0.3" />
            </BnsCustomToggleButtonWidget.NormalImageProperty>
            <BnsCustomToggleButtonWidget.ExpansionComponentList>
                <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="448 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="0.5" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
            </BnsCustomToggleButtonWidget.ExpansionComponentList>
            <BnsCustomLabelWidget Name="ItemMapPanel_Tab_RadioButton_10_Badge" LayoutData.Anchors="1 0 1 0" LayoutData.Offsets="4 -8 20.000977 20">
                <BnsCustomLabelWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="-4" />
                </BnsCustomLabelWidget.HorizontalResizeLink>
                <BnsCustomLabelWidget.VerticalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Offset1="-8" />
                </BnsCustomLabelWidget.VerticalResizeLink>
                <BnsCustomLabelWidget.String>
                    <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_10.Normal_10" LabelText="0" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBound="0 -2" Padding="6 0" />
                </BnsCustomLabelWidget.String>
                <BnsCustomLabelWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bShow="true" ExpansionName="background">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="170 29" ImageUVSize="20 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomLabelWidget.ExpansionComponentList>
            </BnsCustomLabelWidget>
        </BnsCustomToggleButtonWidget>
        <BnsCustomToggleButtonWidget Name="ItemMapPanel_Tab_RadioButton_1" LayoutData.Offsets="0 0 45.000977 40" MetaData="tooltip=UI.ButtonTooltip.Weapon">
            <BnsCustomToggleButtonWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" />
            </BnsCustomToggleButtonWidget.HorizontalResizeLink>
            <BnsCustomToggleButtonWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" />
            </BnsCustomToggleButtonWidget.VerticalResizeLink>
            <BnsCustomToggleButtonWidget.String>
                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Tab_12.Normal_Tab_12" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" bWordWrap="true" />
            </BnsCustomToggleButtonWidget.String>
            <BnsCustomToggleButtonWidget.NormalImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="0 116" ImageUVSize="18 11" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="0.3" />
            </BnsCustomToggleButtonWidget.NormalImageProperty>
            <BnsCustomToggleButtonWidget.ExpansionComponentList>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_TabIcon_R/ItemTransform_Weapon_1.ItemTransform_Weapon_1" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="0.9" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Offset="0 2" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
                <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="C_Icon" WidgetSubState="Expansion_WidgetSubState_Checked">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_TabIcon_R/ItemTransform_Weapon.ItemTransform_Weapon" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="0.9" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Offset="0 2" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
            </BnsCustomToggleButtonWidget.ExpansionComponentList>
            <BnsCustomLabelWidget Name="ItemMapPanel_Tab_RadioButton_1_Badge" LayoutData.Anchors="1 0 1 0" LayoutData.Offsets="4 -8 20.000977 20">
                <BnsCustomLabelWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="-4" />
                </BnsCustomLabelWidget.HorizontalResizeLink>
                <BnsCustomLabelWidget.VerticalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Offset1="-8" />
                </BnsCustomLabelWidget.VerticalResizeLink>
                <BnsCustomLabelWidget.String>
                    <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_10.Normal_10" LabelText="0" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBound="0 -2" Padding="6 0" />
                </BnsCustomLabelWidget.String>
                <BnsCustomLabelWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bShow="true" ExpansionName="background">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="170 29" ImageUVSize="20 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomLabelWidget.ExpansionComponentList>
            </BnsCustomLabelWidget>
        </BnsCustomToggleButtonWidget>
    </BnsCustomRadioButtonWidget>
    <BnsCustomImageWidget Name="ItemMapPanel_NavigationHolder" s:Grid.Row="1">
        <BnsCustomColumnListWidget Name="ItemMapPanel_NavigationList" LayoutData.Anchors="0 0 1 1">
            <BnsCustomColumnListWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" />
            </BnsCustomColumnListWidget.HorizontalResizeLink>
            <BnsCustomColumnListWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" />
            </BnsCustomColumnListWidget.VerticalResizeLink>
            <BnsCustomImageWidget Name="ItemMapPanel_NavigationList_Column_1_1" LayoutData.Offsets="0 0 317 0">
                <BnsCustomImageWidget Name="ItemMapPanel_NavigationList_Column_1_1_SearchHolder" LayoutData.Offsets="0 0 317 147">
                    <BnsCustomImageWidget.ExpansionComponentList>
                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="Purpose" MetaData="textref=UI.ItemMap.Purpose">
                            <UBnsCustomExpansionComponent.ImageProperty>
                                <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                            </UBnsCustomExpansionComponent.ImageProperty>
                            <UBnsCustomExpansionComponent.StringProperty>
                                <StringProperty LabelText="목표#" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" ClippingBound="37 25" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Opacity="1" TextScale="1" AnimScale="1" />
                            </UBnsCustomExpansionComponent.StringProperty>
                        </UBnsCustomExpansionComponent>
                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="Start" MetaData="textref=UI.ItemMap.Start">
                            <UBnsCustomExpansionComponent.ImageProperty>
                                <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                            </UBnsCustomExpansionComponent.ImageProperty>
                            <UBnsCustomExpansionComponent.StringProperty>
                                <StringProperty LabelText="시작#" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" ClippingBound="37 65" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Opacity="1" TextScale="1" AnimScale="1" />
                            </UBnsCustomExpansionComponent.StringProperty>
                        </UBnsCustomExpansionComponent>
                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="PurposeIcon">
                            <UBnsCustomExpansionComponent.ImageProperty>
                                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIButtonIcon.BNSR_UIButtonIcon" ImageUV="160 320" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="0.7" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Offset="12 23" Opacity="1" />
                            </UBnsCustomExpansionComponent.ImageProperty>
                        </UBnsCustomExpansionComponent>
                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="StartIcon">
                            <UBnsCustomExpansionComponent.ImageProperty>
                                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIButtonIcon.BNSR_UIButtonIcon" ImageUV="128 320" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="0.7" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Offset="12 63" Opacity="1" />
                            </UBnsCustomExpansionComponent.ImageProperty>
                        </UBnsCustomExpansionComponent>
                    </BnsCustomImageWidget.ExpansionComponentList>
                    <BnsCustomImageWidget Name="ItemMapPanel_NavigationList_Column_1_1_SearchHolder_HorizontalLine" LayoutData.Anchors="0.5 1 0.5 1" LayoutData.Offsets="0 0 282 1">
                        <BnsCustomImageWidget.HorizontalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                        </BnsCustomImageWidget.HorizontalResizeLink>
                        <BnsCustomImageWidget.VerticalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" />
                        </BnsCustomImageWidget.VerticalResizeLink>
                        <BnsCustomImageWidget.BaseImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="199 61" ImageUVSize="5 1" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" Opacity="0.1" />
                        </BnsCustomImageWidget.BaseImageProperty>
                    </BnsCustomImageWidget>
                    <BnsCustomLabelButtonWidget Name="ItemMapPanel_NavigationList_Column_1_1_SearchHolder_ClearAll" LayoutData.Anchors="0.5 0 0.5 0" LayoutData.Offsets="-5 104 110 0" MetaData="textref=UI.ItemMap.ClearAll;tooltip=UI.ItemMap.ClearAllTooltip">
                        <BnsCustomLabelButtonWidget.HorizontalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" Offset1="-5" />
                        </BnsCustomLabelButtonWidget.HorizontalResizeLink>
                        <BnsCustomLabelButtonWidget.VerticalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Offset1="104" />
                        </BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomLabelButtonWidget.String>
                            <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Button_12.Normal_Button_12" LabelText="모두 지우기#" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" />
                        </BnsCustomLabelButtonWidget.String>
                        <BnsCustomLabelButtonWidget.NormalImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUVSize="15 40" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
                        </BnsCustomLabelButtonWidget.NormalImageProperty>
                    </BnsCustomLabelButtonWidget>
                    <BnsCustomLabelButtonWidget Name="ItemMapPanel_NavigationList_Column_1_1_SearchHolder_Navigate" LayoutData.Offsets="265 58 34 0">
                        <BnsCustomLabelButtonWidget.HorizontalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_SearchHolder_Start" Offset1="5" />
                        </BnsCustomLabelButtonWidget.HorizontalResizeLink>
                        <BnsCustomLabelButtonWidget.VerticalResizeLink>
                            <BnsCustomResizeLink Offset1="58" />
                        </BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomLabelButtonWidget.String>
                            <StringProperty SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBound="0 20" ClippingBoundFace_Vertical="WidgetFaceFace_Bottom" />
                        </BnsCustomLabelButtonWidget.String>
                        <BnsCustomLabelButtonWidget.NormalImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="41 118" ImageUVSize="15 16" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
                        </BnsCustomLabelButtonWidget.NormalImageProperty>
                        <BnsCustomLabelButtonWidget.ExpansionComponentList>
                            <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                                <UBnsCustomExpansionComponent.ImageProperty>
                                    <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="54 232" ImageUVSize="20 20" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                                </UBnsCustomExpansionComponent.ImageProperty>
                            </UBnsCustomExpansionComponent>
                            <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Icon_d">
                                <UBnsCustomExpansionComponent.ImageProperty>
                                    <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="54 232" ImageUVSize="20 20" EnableDrawImage="true" EnableResourceSize="true" EnableResourceGray="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                                </UBnsCustomExpansionComponent.ImageProperty>
                            </UBnsCustomExpansionComponent>
                            <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Icon_AO" WidgetState="BNSCustomWidgetState_ActiveOnly">
                                <UBnsCustomExpansionComponent.ImageProperty>
                                    <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="54 232" ImageUVSize="20 20" EnableDrawImage="true" EnableResourceSize="true" EnableResourceGray="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                                </UBnsCustomExpansionComponent.ImageProperty>
                            </UBnsCustomExpansionComponent>
                        </BnsCustomLabelButtonWidget.ExpansionComponentList>
                    </BnsCustomLabelButtonWidget>
                    <hc:TextBox Name="ItemMapPanel_NavigationList_Column_1_1_SearchHolder_Start" Width="190" LayoutData.Offsets="68 15 192 31" />
                    <hc:TextBox Name="ItemMapPanel_NavigationList_Column_1_1_SearchHolder_Purpose" Width="190" LayoutData.Offsets="68 57 192 31" />
                </BnsCustomImageWidget>
                <BnsCustomLabelWidget Name="ItemMapPanel_NavigationList_Column_1_1_GuideText" LayoutData.Offsets="2 147 317 40" AutoResizeVertical="False">
                    <BnsCustomLabelWidget.HorizontalResizeLink>
                        <BnsCustomResizeLink Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                    </BnsCustomLabelWidget.HorizontalResizeLink>
                    <BnsCustomLabelWidget.VerticalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_SearchHolder" />
                    </BnsCustomLabelWidget.VerticalResizeLink>
                    <BnsCustomLabelWidget.String>
                        <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_14.Normal_14" LabelText="请浏览路径。" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" Padding="10 10" />
                    </BnsCustomLabelWidget.String>
                </BnsCustomLabelWidget>
                <BnsCustomImageWidget Name="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute" LayoutData.Anchors="0.5 0 0.5 0" LayoutData.Offsets="0 187 283 408" Visibility="Collapsed">
                    <BnsCustomImageWidget.HorizontalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                    </BnsCustomImageWidget.HorizontalResizeLink>
                    <BnsCustomImageWidget.VerticalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_GuideText" />
                    </BnsCustomImageWidget.VerticalResizeLink>
                    <BnsCustomImageWidget.BaseImageProperty>
                        <ImageProperty EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                    </BnsCustomImageWidget.BaseImageProperty>
                    <BnsCustomImageWidget.ExpansionComponentList>
                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="Text" MetaData="textref=UI.ItemMap.RecentlyInfo">
                            <UBnsCustomExpansionComponent.ImageProperty>
                                <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                            </UBnsCustomExpansionComponent.ImageProperty>
                            <UBnsCustomExpansionComponent.StringProperty>
                                <StringProperty LabelText="최근 경로#" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" ClippingBound="15 15" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Opacity="1" TextScale="1" AnimScale="1" />
                            </UBnsCustomExpansionComponent.StringProperty>
                        </UBnsCustomExpansionComponent>
                    </BnsCustomImageWidget.ExpansionComponentList>
                    <BnsCustomLabelButtonWidget Name="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_1" LayoutData.Offsets="0 40 283 35">
                        <BnsCustomLabelButtonWidget.VerticalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_Dummy" />
                        </BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomLabelButtonWidget.String>
                            <StringProperty LabelText="아이템이름 &lt;image enablescale=&quot;true&quot; imagesetpath=&quot;00009076.Tooltip_ArrowRight&quot; scalerate=&quot;1.4&quot;/&gt; 아이템이름" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" ClippingBound="10 0" />
                        </BnsCustomLabelButtonWidget.String>
                        <BnsCustomLabelButtonWidget.NormalImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="20 116" ImageUVSize="18 18" EnableDrawImage="true" EnableDrawColor="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="5E687D" Opacity="0.2" />
                        </BnsCustomLabelButtonWidget.NormalImageProperty>
                    </BnsCustomLabelButtonWidget>
                    <BnsCustomLabelButtonWidget Name="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_2" LayoutData.Offsets="0 77 283 35">
                        <BnsCustomLabelButtonWidget.VerticalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_1" Offset1="2" />
                        </BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomLabelButtonWidget.String>
                            <StringProperty LabelText="아이템이름 &lt;image enablescale=&quot;true&quot; imagesetpath=&quot;00009076.Tooltip_ArrowRight&quot; scalerate=&quot;1.4&quot;/&gt; 아이템이름" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" ClippingBound="10 0" />
                        </BnsCustomLabelButtonWidget.String>
                        <BnsCustomLabelButtonWidget.NormalImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="20 116" ImageUVSize="18 18" EnableDrawImage="true" EnableDrawColor="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="5E687D" Opacity="0.2" />
                        </BnsCustomLabelButtonWidget.NormalImageProperty>
                    </BnsCustomLabelButtonWidget>
                    <BnsCustomLabelButtonWidget Name="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_3" LayoutData.Offsets="0 114 283 35.00003">
                        <BnsCustomLabelButtonWidget.VerticalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_2" Offset1="2" />
                        </BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomLabelButtonWidget.String>
                            <StringProperty LabelText="아이템이름 &lt;image enablescale=&quot;true&quot; imagesetpath=&quot;00009076.Tooltip_ArrowRight&quot; scalerate=&quot;1.4&quot;/&gt; 아이템이름" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" ClippingBound="10 0" />
                        </BnsCustomLabelButtonWidget.String>
                        <BnsCustomLabelButtonWidget.NormalImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="20 116" ImageUVSize="18 18" EnableDrawImage="true" EnableDrawColor="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="5E687D" Opacity="0.2" />
                        </BnsCustomLabelButtonWidget.NormalImageProperty>
                    </BnsCustomLabelButtonWidget>
                    <BnsCustomLabelButtonWidget Name="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_4" LayoutData.Offsets="0 151.00003 283 35">
                        <BnsCustomLabelButtonWidget.VerticalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_3" Offset1="2" />
                        </BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomLabelButtonWidget.String>
                            <StringProperty LabelText="아이템이름 &lt;image enablescale=&quot;true&quot; imagesetpath=&quot;00009076.Tooltip_ArrowRight&quot; scalerate=&quot;1.4&quot;/&gt; 아이템이름" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" ClippingBound="10 0" />
                        </BnsCustomLabelButtonWidget.String>
                        <BnsCustomLabelButtonWidget.NormalImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="20 116" ImageUVSize="18 18" EnableDrawImage="true" EnableDrawColor="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="5E687D" Opacity="0.2" />
                        </BnsCustomLabelButtonWidget.NormalImageProperty>
                    </BnsCustomLabelButtonWidget>
                    <BnsCustomLabelButtonWidget Name="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_5" LayoutData.Offsets="0 188.00003 283 35">
                        <BnsCustomLabelButtonWidget.VerticalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_4" Offset1="2" />
                        </BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomLabelButtonWidget.String>
                            <StringProperty LabelText="아이템이름 &lt;image enablescale=&quot;true&quot; imagesetpath=&quot;00009076.Tooltip_ArrowRight&quot; scalerate=&quot;1.4&quot;/&gt; 아이템이름" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" ClippingBound="10 0" />
                        </BnsCustomLabelButtonWidget.String>
                        <BnsCustomLabelButtonWidget.NormalImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="20 116" ImageUVSize="18 18" EnableDrawImage="true" EnableDrawColor="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="5E687D" Opacity="0.2" />
                        </BnsCustomLabelButtonWidget.NormalImageProperty>
                    </BnsCustomLabelButtonWidget>
                    <BnsCustomLabelButtonWidget Name="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_6" LayoutData.Offsets="0 225.00003 283 35">
                        <BnsCustomLabelButtonWidget.VerticalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_5" Offset1="2" />
                        </BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomLabelButtonWidget.String>
                            <StringProperty LabelText="아이템이름 &lt;image enablescale=&quot;true&quot; imagesetpath=&quot;00009076.Tooltip_ArrowRight&quot; scalerate=&quot;1.4&quot;/&gt; 아이템이름" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" ClippingBound="10 0" />
                        </BnsCustomLabelButtonWidget.String>
                        <BnsCustomLabelButtonWidget.NormalImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="20 116" ImageUVSize="18 18" EnableDrawImage="true" EnableDrawColor="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="5E687D" Opacity="0.2" />
                        </BnsCustomLabelButtonWidget.NormalImageProperty>
                    </BnsCustomLabelButtonWidget>
                    <BnsCustomLabelButtonWidget Name="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_7" LayoutData.Offsets="0 262.00003 283 35">
                        <BnsCustomLabelButtonWidget.VerticalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_6" Offset1="2" />
                        </BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomLabelButtonWidget.String>
                            <StringProperty LabelText="아이템이름 &lt;image enablescale=&quot;true&quot; imagesetpath=&quot;00009076.Tooltip_ArrowRight&quot; scalerate=&quot;1.4&quot;/&gt; 아이템이름" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" ClippingBound="10 0" />
                        </BnsCustomLabelButtonWidget.String>
                        <BnsCustomLabelButtonWidget.NormalImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="20 116" ImageUVSize="18 18" EnableDrawImage="true" EnableDrawColor="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="5E687D" Opacity="0.2" />
                        </BnsCustomLabelButtonWidget.NormalImageProperty>
                    </BnsCustomLabelButtonWidget>
                    <BnsCustomLabelButtonWidget Name="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_8" LayoutData.Offsets="0 299.00003 283 35">
                        <BnsCustomLabelButtonWidget.VerticalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_7" Offset1="2" />
                        </BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomLabelButtonWidget.String>
                            <StringProperty LabelText="아이템이름 &lt;image enablescale=&quot;true&quot; imagesetpath=&quot;00009076.Tooltip_ArrowRight&quot; scalerate=&quot;1.4&quot;/&gt; 아이템이름" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" ClippingBound="10 0" />
                        </BnsCustomLabelButtonWidget.String>
                        <BnsCustomLabelButtonWidget.NormalImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="20 116" ImageUVSize="18 18" EnableDrawImage="true" EnableDrawColor="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="5E687D" Opacity="0.2" />
                        </BnsCustomLabelButtonWidget.NormalImageProperty>
                    </BnsCustomLabelButtonWidget>
                    <BnsCustomLabelButtonWidget Name="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_9" LayoutData.Offsets="0 336.00003 283 35">
                        <BnsCustomLabelButtonWidget.VerticalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_8" Offset1="2" />
                        </BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomLabelButtonWidget.String>
                            <StringProperty LabelText="아이템이름 &lt;image enablescale=&quot;true&quot; imagesetpath=&quot;00009076.Tooltip_ArrowRight&quot; scalerate=&quot;1.4&quot;/&gt; 아이템이름" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" ClippingBound="10 0" />
                        </BnsCustomLabelButtonWidget.String>
                        <BnsCustomLabelButtonWidget.NormalImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="20 116" ImageUVSize="18 18" EnableDrawImage="true" EnableDrawColor="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="5E687D" Opacity="0.2" />
                        </BnsCustomLabelButtonWidget.NormalImageProperty>
                    </BnsCustomLabelButtonWidget>
                    <BnsCustomLabelButtonWidget Name="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_10" LayoutData.Offsets="0 373.00003 283 35">
                        <BnsCustomLabelButtonWidget.VerticalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_9" Offset1="2" />
                        </BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomLabelButtonWidget.String>
                            <StringProperty LabelText="아이템이름 &lt;image enablescale=&quot;true&quot; imagesetpath=&quot;00009076.Tooltip_ArrowRight&quot; scalerate=&quot;1.4&quot;/&gt; 아이템이름" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" ClippingBound="10 0" />
                        </BnsCustomLabelButtonWidget.String>
                        <BnsCustomLabelButtonWidget.NormalImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="20 116" ImageUVSize="18 18" EnableDrawImage="true" EnableDrawColor="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="5E687D" Opacity="0.2" />
                        </BnsCustomLabelButtonWidget.NormalImageProperty>
                    </BnsCustomLabelButtonWidget>
                    <BnsCustomImageWidget Name="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute_Dummy" LayoutData.Offsets="0 40 200 0">
                        <BnsCustomImageWidget.VerticalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Offset1="40" />
                        </BnsCustomImageWidget.VerticalResizeLink>
                        <BnsCustomImageWidget.BaseImageProperty>
                            <ImageProperty EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                        </BnsCustomImageWidget.BaseImageProperty>
                    </BnsCustomImageWidget>
                </BnsCustomImageWidget>
                <BnsCustomColumnListWidget Name="ItemMapPanel_NavigationList_Column_1_1_Route" LayoutData.Anchors="0.5 0 0.5 0" LayoutData.Offsets="0 595 290 0">
                    <BnsCustomColumnListWidget.HorizontalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                    </BnsCustomColumnListWidget.HorizontalResizeLink>
                    <BnsCustomColumnListWidget.VerticalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_RecentlyRoute" />
                    </BnsCustomColumnListWidget.VerticalResizeLink>
                    <BnsCustomColumnListWidget.ItemTemplate>
                        <WidgetTemplate>
                            <BnsCustomImageWidget Name="ItemMapPanel_NavigationList_Column_1_1_Route_1" LayoutData.Offsets="0 0 283 0" DataContextChanged="ItemMapPanel_NavigationList_Route_Initialize" Margin="0 2" BorderThickness=".5" BorderBrush="Black">
                                <BnsCustomImageWidget.ExpansionComponentList>
                                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="false" ExpansionName="Selected">
                                        <UBnsCustomExpansionComponent.ImageProperty>
                                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="51 66" ImageUVSize="50 50" EnableDrawImage="true" EnableResourceSize="false" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                                        </UBnsCustomExpansionComponent.ImageProperty>
                                    </UBnsCustomExpansionComponent>
                                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="Label">
                                        <UBnsCustomExpansionComponent.ImageProperty>
                                            <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                                        </UBnsCustomExpansionComponent.ImageProperty>
                                        <UBnsCustomExpansionComponent.StringProperty>
                                            <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_14.Normal_14" LabelText="경로 1" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" ClippingBound="15 15" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Opacity="1" TextScale="1" AnimScale="1" />
                                        </UBnsCustomExpansionComponent.StringProperty>
                                    </UBnsCustomExpansionComponent>
                                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                                        <UBnsCustomExpansionComponent.ImageProperty>
                                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="193 39" ImageUVSize="14 8" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Offset="-10 15" Opacity="1" />
                                        </UBnsCustomExpansionComponent.ImageProperty>
                                    </UBnsCustomExpansionComponent>
                                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Icon2" bVisibleFlag="False">
                                        <UBnsCustomExpansionComponent.ImageProperty>
                                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="201 21" ImageUVSize="12 18" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Offset="-10 15" Opacity="1" />
                                        </UBnsCustomExpansionComponent.ImageProperty>
                                    </UBnsCustomExpansionComponent>
                                </BnsCustomImageWidget.ExpansionComponentList>
                                <BnsCustomLabelButtonWidget Name="ItemMapPanel_NavigationList_Column_1_1_Route_1_Button" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 0 0 45">
                                    <BnsCustomLabelButtonWidget.HorizontalResizeLink>
                                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" />
                                    </BnsCustomLabelButtonWidget.HorizontalResizeLink>
                                    <BnsCustomLabelButtonWidget.NormalImageProperty>
                                        <ImageProperty EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                                    </BnsCustomLabelButtonWidget.NormalImageProperty>
                                </BnsCustomLabelButtonWidget>
                                <BnsCustomColumnListWidget Name="ItemMapPanel_NavigationList_Column_1_1_Route_1_MainRoute" LayoutData.Offsets="15 50 256 0">
                                    <BnsCustomColumnListWidget.VerticalResizeLink>
                                        <BnsCustomResizeLink bEnable="true" Offset1="50" />
                                    </BnsCustomColumnListWidget.VerticalResizeLink>
                                    <BnsCustomColumnListWidget.BaseImageProperty>
                                        <ImageProperty EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                                    </BnsCustomColumnListWidget.BaseImageProperty>
                                    <BnsCustomColumnListWidget.ItemTemplate>
                                        <WidgetTemplate>
                                            <BnsCustomImageWidget Name="ItemMapPanel_NavigationList_Column_1_1_Route_1_MainRoute_1" LayoutData.Offsets="0 0 256 0" DataContextChanged="ItemMapPanel_NavigationList_Route_InitializeRoute">
                                                <BnsCustomImageWidget Name="ItemMapPanel_NavigationList_Column_1_1_Route_1_MainRoute_1_Item" LayoutData.Offsets="3 3 35 35">
                                                    <BnsCustomImageWidget.BaseImageProperty>
                                                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="916 875" ImageUVSize="52 52" EnableDrawImage="true" EnableSkinColor="true" ImageScale="1" Opacity="1" />
                                                    </BnsCustomImageWidget.BaseImageProperty>
                                                    <BnsCustomImageWidget.ExpansionComponentList>
                                                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage">
                                                            <UBnsCustomExpansionComponent.ImageProperty>
                                                                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="137 5" ImageUVSize="38 38" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="4 4" Opacity="2" />
                                                            </UBnsCustomExpansionComponent.ImageProperty>
                                                        </UBnsCustomExpansionComponent>
                                                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage">
                                                            <UBnsCustomExpansionComponent.ImageProperty>
                                                                <ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
                                                            </UBnsCustomExpansionComponent.ImageProperty>
                                                        </UBnsCustomExpansionComponent>
                                                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage">
                                                            <UBnsCustomExpansionComponent.ImageProperty>
                                                                <ImageProperty ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                                                            </UBnsCustomExpansionComponent.ImageProperty>
                                                        </UBnsCustomExpansionComponent>
                                                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel">
                                                            <UBnsCustomExpansionComponent.ImageProperty>
                                                                <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                                                            </UBnsCustomExpansionComponent.ImageProperty>
                                                            <UBnsCustomExpansionComponent.StringProperty>
                                                                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
                                                            </UBnsCustomExpansionComponent.StringProperty>
                                                        </UBnsCustomExpansionComponent>
                                                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image">
                                                            <UBnsCustomExpansionComponent.ImageProperty>
                                                                <ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
                                                            </UBnsCustomExpansionComponent.ImageProperty>
                                                        </UBnsCustomExpansionComponent>
                                                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="MouseOverImage" WidgetState="BNSCustomWidgetState_Active">
                                                            <UBnsCustomExpansionComponent.ImageProperty>
                                                                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="51 931" ImageUVSize="48 48" EnableDrawImage="true" EnableResourceSize="false" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                                                            </UBnsCustomExpansionComponent.ImageProperty>
                                                        </UBnsCustomExpansionComponent>
                                                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="MousePressImage" WidgetState="BNSCustomWidgetState_Pressed">
                                                            <UBnsCustomExpansionComponent.ImageProperty>
                                                                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="1 931" ImageUVSize="48 48" EnableDrawImage="true" EnableResourceSize="false" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                                                            </UBnsCustomExpansionComponent.ImageProperty>
                                                        </UBnsCustomExpansionComponent>
                                                    </BnsCustomImageWidget.ExpansionComponentList>
                                                </BnsCustomImageWidget>
                                                <BnsCustomLabelWidget Name="ItemMapPanel_NavigationList_Column_1_1_Route_1_MainRoute_1_Name" LayoutData.Offsets="40 11 215 35">
                                                    <BnsCustomLabelWidget.HorizontalResizeLink>
                                                        <BnsCustomResizeLink bEnable="true" Offset1="45" />
                                                    </BnsCustomLabelWidget.HorizontalResizeLink>
                                                    <BnsCustomLabelWidget.String>
                                                        <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="ItemName" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" />
                                                    </BnsCustomLabelWidget.String>
                                                </BnsCustomLabelWidget>
                                                <BnsCustomLabelWidget Name="ItemMapPanel_NavigationList_Column_1_1_Route_1_MainRoute_1_Desc" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 35 0 50" AutoResizeVertical="False">
                                                    <BnsCustomLabelWidget.HorizontalResizeLink>
                                                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" />
                                                    </BnsCustomLabelWidget.HorizontalResizeLink>
                                                    <BnsCustomLabelWidget.VerticalResizeLink>
                                                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_Route_1_MainRoute_1_Item" Offset1="4" />
                                                    </BnsCustomLabelWidget.VerticalResizeLink>
                                                    <BnsCustomLabelWidget.String>
                                                        <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Item Desc&lt;br/&gt;Item Desc" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" ClippingBound="30 -5" Padding="0 10" />
                                                    </BnsCustomLabelWidget.String>
                                                    <BnsCustomImageWidget Name="ItemMapPanel_NavigationList_Column_1_1_Route_1_MainRoute_1_Desc_Arrow" LayoutData.Anchors="0 0 0 1" LayoutData.Offsets="3 0 28 0">
                                                        <BnsCustomImageWidget.HorizontalResizeLink>
                                                            <BnsCustomResizeLink bEnable="true" Offset1="3" />
                                                        </BnsCustomImageWidget.HorizontalResizeLink>
                                                        <BnsCustomImageWidget.VerticalResizeLink>
                                                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" />
                                                        </BnsCustomImageWidget.VerticalResizeLink>
                                                        <BnsCustomImageWidget.ExpansionComponentList>
                                                            <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Arrow_100">
                                                                <UBnsCustomExpansionComponent.ImageProperty>
                                                                    <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="631 863" ImageUVSize="19 63" EnableDrawImage="true" EnableResourceSize="false" EnableDrawColor="true" GrayWeightValue="0.15" ImageScale="0.8" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="22A031" Opacity="1.2" SperateImageType="BNS_SperateImageType_3Frame" SperateType="BNS_UIORIENT_Vertical">
                                                                        <ImageProperty.CoordinatesArray>
                                                                            <TextureCoordinate U="631" V="863" UL="19" VL="5" />
                                                                            <TextureCoordinate U="631" V="868" UL="19" VL="1" />
                                                                            <TextureCoordinate U="631" V="906" UL="19" VL="20" />
                                                                        </ImageProperty.CoordinatesArray>
                                                                    </ImageProperty>
                                                                </UBnsCustomExpansionComponent.ImageProperty>
                                                            </UBnsCustomExpansionComponent>
                                                            <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Arrow_Randome">
                                                                <UBnsCustomExpansionComponent.ImageProperty>
                                                                    <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="631 863" ImageUVSize="19 63" EnableDrawImage="true" EnableResourceSize="false" EnableDrawColor="true" GrayWeightValue="0.15" ImageScale="0.8" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="386087" Opacity="1.2" SperateImageType="BNS_SperateImageType_3Frame" SperateType="BNS_UIORIENT_Vertical">
                                                                        <ImageProperty.CoordinatesArray>
                                                                            <TextureCoordinate U="631" V="863" UL="19" VL="5" />
                                                                            <TextureCoordinate U="631" V="868" UL="19" VL="1" />
                                                                            <TextureCoordinate U="631" V="906" UL="19" VL="20" />
                                                                        </ImageProperty.CoordinatesArray>
                                                                    </ImageProperty>
                                                                </UBnsCustomExpansionComponent.ImageProperty>
                                                            </UBnsCustomExpansionComponent>
                                                        </BnsCustomImageWidget.ExpansionComponentList>
                                                    </BnsCustomImageWidget>
                                                </BnsCustomLabelWidget>
                                            </BnsCustomImageWidget>
                                        </WidgetTemplate>
                                    </BnsCustomColumnListWidget.ItemTemplate>
                                </BnsCustomColumnListWidget>
                                <BnsCustomImageWidget Name="ItemMapPanel_NavigationList_Column_1_1_Route_1_HorizontalLine" LayoutData.Anchors="0.5 0 0.5 0" LayoutData.Offsets="0 84 259 1" BorderThickness=".1" BorderBrush="Black">
                                    <BnsCustomImageWidget.HorizontalResizeLink>
                                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                                    </BnsCustomImageWidget.HorizontalResizeLink>
                                    <BnsCustomImageWidget.VerticalResizeLink>
                                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_Route_1_MainRoute" Offset1="3" />
                                    </BnsCustomImageWidget.VerticalResizeLink>
                                    <BnsCustomImageWidget.BaseImageProperty>
                                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="199 61" ImageUVSize="5 1" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" Opacity="1" />
                                    </BnsCustomImageWidget.BaseImageProperty>
                                </BnsCustomImageWidget>
                                <BnsCustomListBoxWidget Name="ItemMapPanel_NavigationList_Column_1_1_Route_1_Item" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 0 0 45">
                                    <BnsCustomListBoxWidget.HorizontalResizeLink>
                                        <BnsCustomResizeLink bEnable="True" Offset1="15" />
                                    </BnsCustomListBoxWidget.HorizontalResizeLink>
                                    <BnsCustomListBoxWidget.VerticalResizeLink>
                                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_Route_1_HorizontalLine" Offset1="4" />
                                    </BnsCustomListBoxWidget.VerticalResizeLink>
                                    <BnsCustomListBoxWidget.ItemsPanel>
                                        <WidgetTemplate>
                                            <WarpBox LayoutData.Anchors="0 0 1 0" InnerSlotPadding="5 5" />
                                        </WidgetTemplate>
                                    </BnsCustomListBoxWidget.ItemsPanel>
                                    <BnsCustomListBoxWidget.ItemTemplate>
                                        <WidgetTemplate>
                                            <BnsCustomImageWidget LayoutData.Offsets="0 0 40 40" DataContextChanged="ItemMapPanel_NavigationList_Route_InitializeItem" Foreground="White">
                                                <BnsCustomImageWidget.ExpansionComponentList>
                                                    <UBnsCustomExpansionComponent bShow="true" ExpansionName="BackgroundImage">
                                                        <UBnsCustomExpansionComponent.ImageProperty>
                                                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="0 66" ImageUVSize="48 48" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="0.5" />
                                                        </UBnsCustomExpansionComponent.ImageProperty>
                                                    </UBnsCustomExpansionComponent>
                                                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage">
                                                        <UBnsCustomExpansionComponent.ImageProperty>
                                                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Icon/Acc_Bangle_Legendry_2-1Phase.Acc_Bangle_Legendry_2-1Phase" ImageUVSize="64 64" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
                                                        </UBnsCustomExpansionComponent.ImageProperty>
                                                    </UBnsCustomExpansionComponent>
                                                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage">
                                                        <UBnsCustomExpansionComponent.ImageProperty>
                                                            <ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                                                        </UBnsCustomExpansionComponent.ImageProperty>
                                                    </UBnsCustomExpansionComponent>
                                                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel">
                                                        <UBnsCustomExpansionComponent.StringProperty>
                                                            <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_12.Normal_Out_12" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
                                                        </UBnsCustomExpansionComponent.StringProperty>
                                                    </UBnsCustomExpansionComponent>
                                                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image">
                                                        <UBnsCustomExpansionComponent.ImageProperty>
                                                            <ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
                                                        </UBnsCustomExpansionComponent.ImageProperty>
                                                    </UBnsCustomExpansionComponent>
                                                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem">
                                                        <UBnsCustomExpansionComponent.ImageProperty>
                                                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
                                                        </UBnsCustomExpansionComponent.ImageProperty>
                                                    </UBnsCustomExpansionComponent>
                                                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="MouseOverImage" WidgetState="BNSCustomWidgetState_Active">
                                                        <UBnsCustomExpansionComponent.ImageProperty>
                                                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="51 66" ImageUVSize="50 50" EnableDrawImage="true" EnableResourceSize="false" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                                                        </UBnsCustomExpansionComponent.ImageProperty>
                                                    </UBnsCustomExpansionComponent>
                                                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="MousePressImage" WidgetState="BNSCustomWidgetState_Pressed">
                                                        <UBnsCustomExpansionComponent.ImageProperty>
                                                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="51 66" ImageUVSize="50 50" EnableDrawImage="true" EnableResourceSize="false" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                                                        </UBnsCustomExpansionComponent.ImageProperty>
                                                    </UBnsCustomExpansionComponent>
                                                </BnsCustomImageWidget.ExpansionComponentList>
                                            </BnsCustomImageWidget>
                                        </WidgetTemplate>
                                    </BnsCustomListBoxWidget.ItemTemplate>
                                </BnsCustomListBoxWidget>
                                <BnsCustomLabelWidget Name="ItemMapPanel_NavigationList_Column_1_1_Route_1_Cost" LayoutData.Offsets="15 171 255 20">
                                    <BnsCustomLabelWidget.HorizontalResizeLink>
                                        <BnsCustomResizeLink bEnable="true" Offset1="15" />
                                    </BnsCustomLabelWidget.HorizontalResizeLink>
                                    <BnsCustomLabelWidget.VerticalResizeLink>
                                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_Route_1_Item" Offset1="10" />
                                    </BnsCustomLabelWidget.VerticalResizeLink>
                                    <BnsCustomLabelWidget.String>
                                        <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="00금00은00동" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Center" />
                                    </BnsCustomLabelWidget.String>
                                    <BnsCustomLabelWidget.ExpansionComponentList>
                                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="CostLabel" MetaData="textref=UI.ItemGrowth.Common.Money.none">
                                            <UBnsCustomExpansionComponent.ImageProperty>
                                                <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                                            </UBnsCustomExpansionComponent.ImageProperty>
                                            <UBnsCustomExpansionComponent.StringProperty>
                                                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="基本費用" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Opacity="1" TextScale="1" AnimScale="1" />
                                            </UBnsCustomExpansionComponent.StringProperty>
                                        </UBnsCustomExpansionComponent>
                                    </BnsCustomLabelWidget.ExpansionComponentList>
                                </BnsCustomLabelWidget>
                                <BnsCustomLabelWidget Name="ItemMapPanel_NavigationList_Column_1_1_Route_1_DiscountCost" LayoutData.Offsets="15 191 255 20" Margin="0 0 0 10">
                                    <BnsCustomLabelWidget.HorizontalResizeLink>
                                        <BnsCustomResizeLink bEnable="true" Offset1="15" />
                                    </BnsCustomLabelWidget.HorizontalResizeLink>
                                    <BnsCustomLabelWidget.VerticalResizeLink>
                                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationList_Column_1_1_Route_1_Cost" />
                                    </BnsCustomLabelWidget.VerticalResizeLink>
                                    <BnsCustomLabelWidget.String>
                                        <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="00금00은00동" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Center" />
                                    </BnsCustomLabelWidget.String>
                                    <BnsCustomLabelWidget.ExpansionComponentList>
                                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="DiscountCostLabel" MetaData="textref=UI.ItemGrowth.Common.Money.Guild">
                                            <UBnsCustomExpansionComponent.ImageProperty>
                                                <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                                            </UBnsCustomExpansionComponent.ImageProperty>
                                            <UBnsCustomExpansionComponent.StringProperty>
                                                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="할인비용" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Opacity="1" TextScale="1" AnimScale="1" />
                                            </UBnsCustomExpansionComponent.StringProperty>
                                        </UBnsCustomExpansionComponent>
                                    </BnsCustomLabelWidget.ExpansionComponentList>
                                </BnsCustomLabelWidget>
                            </BnsCustomImageWidget>
                        </WidgetTemplate>
                    </BnsCustomColumnListWidget.ItemTemplate>
                </BnsCustomColumnListWidget>
            </BnsCustomImageWidget>
            <BnsCustomScrollBarWidget Name="ItemMapPanel_NavigationList_ScrollBar" LayoutData.Anchors="1 0 1 1" LayoutData.Offsets="0 0 8 0">
                <BnsCustomScrollBarWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" />
                </BnsCustomScrollBarWidget.HorizontalResizeLink>
                <BnsCustomScrollBarWidget.VerticalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" />
                </BnsCustomScrollBarWidget.VerticalResizeLink>
                <BnsCustomSliderBarWidget Name="ItemMapPanel_NavigationList_ScrollBar_SliderBar" LayoutData.Anchors="0.5 0 0.5 1" LayoutData.Offsets="0 1 8 1">
                    <BnsCustomSliderBarWidget.HorizontalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                    </BnsCustomSliderBarWidget.HorizontalResizeLink>
                    <BnsCustomSliderBarWidget.VerticalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="1" Offset2="1" />
                    </BnsCustomSliderBarWidget.VerticalResizeLink>
                    <BnsCustomLabelButtonWidget Name="ItemMapPanel_NavigationList_ScrollBar_SliderBar_Marker" LayoutData.Anchors="0.5 0 0.5 0" LayoutData.Offsets="0 0 4 62.999756">
                        <BnsCustomLabelButtonWidget.HorizontalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                        </BnsCustomLabelButtonWidget.HorizontalResizeLink>
                        <BnsCustomLabelButtonWidget.NormalImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="142 30" ImageUVSize="4 13" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" Opacity="1" />
                        </BnsCustomLabelButtonWidget.NormalImageProperty>
                    </BnsCustomLabelButtonWidget>
                </BnsCustomSliderBarWidget>
                <BnsCustomLabelButtonWidget Name="ItemMapPanel_NavigationList_ScrollBar_DecrementButton" LayoutData.Anchors="0.5 0 0.5 0">
                    <BnsCustomLabelButtonWidget.HorizontalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                    </BnsCustomLabelButtonWidget.HorizontalResizeLink>
                    <BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomResizeLink bEnable="true" />
                    </BnsCustomLabelButtonWidget.VerticalResizeLink>
                    <BnsCustomLabelButtonWidget.NormalImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="28 167" ImageUVSize="19 18" EnableDrawImage="true" EnableSkinColor="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                    </BnsCustomLabelButtonWidget.NormalImageProperty>
                    <BnsCustomLabelButtonWidget.ExpansionComponentList>
                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Normal">
                            <UBnsCustomExpansionComponent.ImageProperty>
                                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="344 568" ImageUVSize="11 9" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                            </UBnsCustomExpansionComponent.ImageProperty>
                        </UBnsCustomExpansionComponent>
                    </BnsCustomLabelButtonWidget.ExpansionComponentList>
                </BnsCustomLabelButtonWidget>
                <BnsCustomLabelButtonWidget Name="ItemMapPanel_NavigationList_ScrollBar_IncrementButton" LayoutData.Anchors="0.5 1 0.5 1">
                    <BnsCustomLabelButtonWidget.HorizontalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                    </BnsCustomLabelButtonWidget.HorizontalResizeLink>
                    <BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" />
                    </BnsCustomLabelButtonWidget.VerticalResizeLink>
                    <BnsCustomLabelButtonWidget.NormalImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="28 167" ImageUVSize="19 18" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                    </BnsCustomLabelButtonWidget.NormalImageProperty>
                    <BnsCustomLabelButtonWidget.ExpansionComponentList>
                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Down">
                            <UBnsCustomExpansionComponent.ImageProperty>
                                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="344 578" ImageUVSize="11 8" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                            </UBnsCustomExpansionComponent.ImageProperty>
                        </UBnsCustomExpansionComponent>
                    </BnsCustomLabelButtonWidget.ExpansionComponentList>
                </BnsCustomLabelButtonWidget>
            </BnsCustomScrollBarWidget>
        </BnsCustomColumnListWidget>
    </BnsCustomImageWidget>
    <BnsCustomImageWidget Name="ItemMapPanel_MapFieldHolder" s:Grid.Row="1" s:Grid.Column="1">
        <BnsCustomImageWidget.HorizontalResizeLink>
            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINKNRESIZE_RIGHT_AND_LEFT" LinkWidgetName1="ItemMapPanel_NavigationHolder" LinkWidgetName2="ItemMapPanel_RightDummy" Offset1="4" />
        </BnsCustomImageWidget.HorizontalResizeLink>
        <BnsCustomImageWidget.VerticalResizeLink>
            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="15" />
        </BnsCustomImageWidget.VerticalResizeLink>
        <BnsCustomLabelButtonWidget Name="ItemMapPanel_ControlHolder_Navigate" LayoutData.Anchors="1 0 1 0" LayoutData.Offsets="-20 0 22 22">
            <BnsCustomLabelButtonWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="20" />
            </BnsCustomLabelButtonWidget.HorizontalResizeLink>
            <BnsCustomLabelButtonWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" Offset1="220" />
            </BnsCustomLabelButtonWidget.VerticalResizeLink>
            <BnsCustomLabelButtonWidget.String>
                <StringProperty SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBound="0 20" ClippingBoundFace_Vertical="WidgetFaceFace_Bottom" />
            </BnsCustomLabelButtonWidget.String>
            <BnsCustomLabelButtonWidget.NormalImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="41 118" ImageUVSize="15 16" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
            </BnsCustomLabelButtonWidget.NormalImageProperty>
            <BnsCustomLabelButtonWidget.ExpansionComponentList>
                <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="54 232" ImageUVSize="20 20" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="0.7" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
            </BnsCustomLabelButtonWidget.ExpansionComponentList>
        </BnsCustomLabelButtonWidget>
        <BnsCustomImageWidget Name="ItemMapPanel_MapFieldTopImage" LayoutData.Anchors="0 0 1 1" LayoutData.Offsets="-1 -1 -1 -1">
            <BnsCustomImageWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="-1" Offset2="-1" />
            </BnsCustomImageWidget.HorizontalResizeLink>
            <BnsCustomImageWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="-1" Offset2="-1" />
            </BnsCustomImageWidget.VerticalResizeLink>
            <BnsCustomImageWidget.BaseImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="612 711" ImageUVSize="61 56" EnableDrawImage="true" EnableSkinColor="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="0.3">
                    <ImageProperty.CoordinatesArray>
                        <TextureCoordinate U="612" V="711" UL="30" VL="27" />
                        <TextureCoordinate U="642" V="711" UL="1" VL="27" />
                        <TextureCoordinate U="643" V="711" UL="30" VL="27" />
                        <TextureCoordinate U="612" V="738" UL="30" VL="1" />
                        <TextureCoordinate U="642" V="738" UL="1" VL="1" />
                        <TextureCoordinate U="643" V="738" UL="30" VL="1" />
                        <TextureCoordinate U="612" V="739" UL="30" VL="28" />
                        <TextureCoordinate U="642" V="739" UL="1" VL="28" />
                        <TextureCoordinate U="643" V="739" UL="30" VL="28" />
                    </ImageProperty.CoordinatesArray>
                </ImageProperty>
            </BnsCustomImageWidget.BaseImageProperty>
        </BnsCustomImageWidget>
        <BnsCustomGraphMapWidget Name="ItemMapPanel_MapField" LayoutData.Anchors="0 0 1 1">
            <BnsCustomGraphMapWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" />
            </BnsCustomGraphMapWidget.HorizontalResizeLink>
            <BnsCustomGraphMapWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" />
            </BnsCustomGraphMapWidget.VerticalResizeLink>
            <BnsCustomImageWidget Name="ItemMapPanel_MapField_NodeTemplate" LayoutData.Offsets="0 56 50 70">
                <BnsCustomImageWidget.BaseImageProperty>
                    <ImageProperty EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                </BnsCustomImageWidget.BaseImageProperty>
                <BnsCustomImageWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bShow="true" ExpansionName="Node_SubGroupImage">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement_01.BNSR_UIElement_01" ImageUV="551 353" ImageUVSize="40 40" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Offset="4 9" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Node_BackImage">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="931 713" ImageUVSize="52 52" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Node_BackImage2">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="693 726" ImageUVSize="16 16" EnableResourceSize="true" EnableDrawColor="true" GrayWeightValue="0.15" ImageScale="2.6" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="000000" Offset="0 5" Opacity="0.5" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Node_Icon">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_Icon1_R/Attach_010006_All_2.Attach_010006_All_2" ImageUVSize="128 128" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="0.313" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Offset="0 5" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Node_ViaImage">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement_01.BNSR_UIElement_01" ImageUV="464 411" ImageUVSize="44 45" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Offset="0 3" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Node_PurposeImage">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement_01.BNSR_UIElement_01" ImageUV="464 411" ImageUVSize="44 45" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Offset="0 3" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Node_StartImage">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement_01.BNSR_UIElement_01" ImageUV="464 411" ImageUVSize="44 45" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Offset="0 3" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="Node_ItemName">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                        <UBnsCustomExpansionComponent.StringProperty>
                            <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_12.Normal_Out_12" LabelText="ItemName" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" ClippingBound="0 75" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="0 -25" Opacity="1" TextScale="1" AnimScale="1" />
                        </UBnsCustomExpansionComponent.StringProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" bVisibleFlag="False" ExpansionName="Node_EquipedImage">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement_01.BNSR_UIElement_01" ImageUV="400 411" ImageUVSize="61 54" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Offset="-10 -1" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Node_OverImage" WidgetState="BNSCustomWidgetState_Active">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="876 715" ImageUVSize="52 52" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Offset="0 -0.5" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Node_OverImage2">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="876 715" ImageUVSize="52 52" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Offset="0 -0.5" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Node_PressedImage" WidgetState="BNSCustomWidgetState_Pressed">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="876 715" ImageUVSize="52 52" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Offset="0 -0.5" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Node_SearchedImage">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement_01.BNSR_UIElement_01" ImageUV="511 411" ImageUVSize="44 44" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Offset="0 3" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Node_PossessionImage">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIButtonIcon.BNSR_UIButtonIcon" ImageUV="230 326" ImageUVSize="20 21" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Bottom" TintColor="FFFFFF" Offset="-6 -16" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomImageWidget.ExpansionComponentList>
            </BnsCustomImageWidget>
            <BnsCustomImageWidget Name="ItemMapPanel_MapField_HorizontalRulerItemTemplate" LayoutData.Offsets="96 128 180 65">
                <BnsCustomImageWidget.BaseImageProperty>
                    <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_10.GameUI_New_Scene_10" ImageUV="274 342" ImageUVSize="175 45" EnableDrawColor="true" ImageScale="1" TintColor="000000" Offset="7 0" Opacity="1" />
                </BnsCustomImageWidget.BaseImageProperty>
                <BnsCustomImageWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="TreeTitle">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                        <UBnsCustomExpansionComponent.StringProperty>
                            <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_14.Normal_Out_14" LabelText="속성" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBound="0 -7" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Opacity="1" TextScale="1" AnimScale="1" />
                        </UBnsCustomExpansionComponent.StringProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomImageWidget.ExpansionComponentList>
            </BnsCustomImageWidget>
            <BnsCustomImageWidget Name="ItemMapPanel_MapField_VerticalRulerItemTemplate" LayoutData.Offsets="0 56 32 32">
                <BnsCustomImageWidget.BaseImageProperty>
                    <ImageProperty ImageUV="611 359" ImageUVSize="32 32" EnableResourceSize="true" ImageScale="1" Opacity="1" />
                </BnsCustomImageWidget.BaseImageProperty>
                <BnsCustomImageWidget Name="ItemMapPanel_MapField_VerticalRulerItemTemplate_Main" LayoutData.Offsets="0 0 32 26">
                    <BnsCustomImageWidget.BaseImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="611 359" ImageUVSize="32 32" EnableResourceSize="true" ImageScale="1" Opacity="1" />
                    </BnsCustomImageWidget.BaseImageProperty>
                    <BnsCustomImageWidget.ExpansionComponentList>
                        <UBnsCustomExpansionComponent bShow="true" ExpansionName="Main_Level">
                            <UBnsCustomExpansionComponent.ImageProperty>
                                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement_01.BNSR_UIElement_01" ImageUV="556 395" ImageUVSize="26 26" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                            </UBnsCustomExpansionComponent.ImageProperty>
                        </UBnsCustomExpansionComponent>
                        <UBnsCustomExpansionComponent bShow="true" ExpansionName="Main_MasteryLevel">
                            <UBnsCustomExpansionComponent.ImageProperty>
                                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement_01.BNSR_UIElement_01" ImageUV="481 383" ImageUVSize="26 26" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                            </UBnsCustomExpansionComponent.ImageProperty>
                        </UBnsCustomExpansionComponent>
                        <UBnsCustomExpansionComponent bShow="true" ExpansionName="Main_MyLevel">
                            <UBnsCustomExpansionComponent.ImageProperty>
                                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement_01.BNSR_UIElement_01" ImageUV="508 383" ImageUVSize="26 26" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                            </UBnsCustomExpansionComponent.ImageProperty>
                        </UBnsCustomExpansionComponent>
                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="Main_Count">
                            <UBnsCustomExpansionComponent.ImageProperty>
                                <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                            </UBnsCustomExpansionComponent.ImageProperty>
                            <UBnsCustomExpansionComponent.StringProperty>
                                <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_12.Normal_Out_12" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Opacity="1" TextScale="1" AnimScale="1" />
                            </UBnsCustomExpansionComponent.StringProperty>
                        </UBnsCustomExpansionComponent>
                    </BnsCustomImageWidget.ExpansionComponentList>
                </BnsCustomImageWidget>
                <BnsCustomLabelWidget Name="ItemMapPanel_MapField_VerticalRulerItemTemplate_Sub" LayoutData.Anchors="0 0.5 0 0.5" LayoutData.Offsets="-0.00079345703 0 31.002228 20.001007">
                    <BnsCustomLabelWidget.HorizontalResizeLink>
                        <BnsCustomResizeLink Offset1="30" />
                    </BnsCustomLabelWidget.HorizontalResizeLink>
                    <BnsCustomLabelWidget.VerticalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                    </BnsCustomLabelWidget.VerticalResizeLink>
                    <BnsCustomLabelWidget.String>
                        <StringProperty fontset="/Game/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_12.Normal_Out_12" LabelText="999" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" />
                    </BnsCustomLabelWidget.String>
                    <BnsCustomLabelWidget.ExpansionComponentList>
                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Sub_Icon">
                            <UBnsCustomExpansionComponent.ImageProperty>
                                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="555 201" ImageUVSize="13 13" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                            </UBnsCustomExpansionComponent.ImageProperty>
                        </UBnsCustomExpansionComponent>
                    </BnsCustomLabelWidget.ExpansionComponentList>
                </BnsCustomLabelWidget>
            </BnsCustomImageWidget>
            <BnsCustomScrollBarWidget Name="ItemMapPanel_MapField_ScrollBar" LayoutData.Anchors="1 0 1 0" LayoutData.Offsets="-20 20 22 196">
                <BnsCustomScrollBarWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="20" />
                </BnsCustomScrollBarWidget.HorizontalResizeLink>
                <BnsCustomScrollBarWidget.VerticalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Offset1="20" />
                </BnsCustomScrollBarWidget.VerticalResizeLink>
                <BnsCustomSliderBarWidget Name="ItemMapPanel_MapField_ScrollBar_SliderBar" LayoutData.Anchors="0.5 0 0.5 0" LayoutData.Offsets="0 0 8 140" bReverseDirection="True" SliderStepValue="0.1">
                    <BnsCustomSliderBarWidget.HorizontalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                    </BnsCustomSliderBarWidget.HorizontalResizeLink>
                    <BnsCustomSliderBarWidget.VerticalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_MapField_ScrollBar_IncrementButton" Offset1="5" />
                    </BnsCustomSliderBarWidget.VerticalResizeLink>
                    <BnsCustomLabelButtonWidget Name="ItemMapPanel_MapField_ScrollBar_SliderBar_Marker" LayoutData.Anchors="0.5 0 0.5 0" LayoutData.Offsets="0 0 13 13">
                        <BnsCustomLabelButtonWidget.HorizontalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                        </BnsCustomLabelButtonWidget.HorizontalResizeLink>
                        <BnsCustomLabelButtonWidget.VerticalResizeLink>
                            <BnsCustomResizeLink Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                        </BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomLabelButtonWidget.NormalImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="0 166" ImageUVSize="20 20" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                        </BnsCustomLabelButtonWidget.NormalImageProperty>
                    </BnsCustomLabelButtonWidget>
                </BnsCustomSliderBarWidget>
                <BnsCustomLabelButtonWidget Name="ItemMapPanel_MapField_ScrollBar_IncrementButton" LayoutData.Anchors="0.5 0 0.5 0" LayoutData.Offsets="0 0 22 22" MetaData="tooltip=UI.ButtonTooltip.Minus:widget_face_right">
                    <BnsCustomLabelButtonWidget.HorizontalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                    </BnsCustomLabelButtonWidget.HorizontalResizeLink>
                    <BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomResizeLink bEnable="true" />
                    </BnsCustomLabelButtonWidget.VerticalResizeLink>
                    <BnsCustomLabelButtonWidget.NormalImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="41 118" ImageUVSize="15 16" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
                    </BnsCustomLabelButtonWidget.NormalImageProperty>
                    <BnsCustomLabelButtonWidget.ExpansionComponentList>
                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                            <UBnsCustomExpansionComponent.ImageProperty>
                                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIButtonIcon.BNSR_UIButtonIcon" ImageUV="9 168" ImageUVSize="14 14" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="0.7" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                            </UBnsCustomExpansionComponent.ImageProperty>
                        </UBnsCustomExpansionComponent>
                    </BnsCustomLabelButtonWidget.ExpansionComponentList>
                </BnsCustomLabelButtonWidget>
                <BnsCustomLabelButtonWidget Name="ItemMapPanel_MapField_ScrollBar_DecrementButton" LayoutData.Anchors="0.5 1 0.5 1" LayoutData.Offsets="0 0 22 22" MetaData="tooltip=UI.ButtonTooltip.Plus:widget_face_right">
                    <BnsCustomLabelButtonWidget.HorizontalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                    </BnsCustomLabelButtonWidget.HorizontalResizeLink>
                    <BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" />
                    </BnsCustomLabelButtonWidget.VerticalResizeLink>
                    <BnsCustomLabelButtonWidget.NormalImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="41 118" ImageUVSize="15 16" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
                    </BnsCustomLabelButtonWidget.NormalImageProperty>
                    <BnsCustomLabelButtonWidget.ExpansionComponentList>
                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                            <UBnsCustomExpansionComponent.ImageProperty>
                                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIButtonIcon.BNSR_UIButtonIcon" ImageUV="41 175" ImageUVSize="15 2" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="0.7" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                            </UBnsCustomExpansionComponent.ImageProperty>
                        </UBnsCustomExpansionComponent>
                    </BnsCustomLabelButtonWidget.ExpansionComponentList>
                </BnsCustomLabelButtonWidget>
            </BnsCustomScrollBarWidget>
        </BnsCustomGraphMapWidget>
    </BnsCustomImageWidget>

    <BnsCustomImageWidget Name="ItemMapPanel_SearchHolder" LayoutData.Offsets="623 98 310 0" Visibility="Collapsed">
        <BnsCustomImageWidget.HorizontalResizeLink>
            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_LEFT" LinkWidgetName1="ItemMapPanel_JobComboBox" Offset1="5" />
        </BnsCustomImageWidget.HorizontalResizeLink>
        <BnsCustomImageWidget.VerticalResizeLink>
            <BnsCustomResizeLink bEnable="true" Offset1="98" />
        </BnsCustomImageWidget.VerticalResizeLink>
        <BnsCustomImageWidget.BaseImageProperty>
            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="128 0" ImageUVSize="18 27" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="0.5" />
        </BnsCustomImageWidget.BaseImageProperty>
        <BnsCustomImageWidget.ExpansionComponentList>
            <UBnsCustomExpansionComponent bShow="true" ExpansionName="Wrong">
                <UBnsCustomExpansionComponent.ImageProperty>
                    <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="20 116" ImageUVSize="18 18" EnableDrawImage="true" EnableDrawColor="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FF0000" Opacity="0.5" />
                </UBnsCustomExpansionComponent.ImageProperty>
            </UBnsCustomExpansionComponent>
            <UBnsCustomExpansionComponent bShow="true" ExpansionName="Correct">
                <UBnsCustomExpansionComponent.ImageProperty>
                    <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="20 116" ImageUVSize="18 18" EnableDrawImage="true" EnableDrawColor="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="54ACFF" Opacity="0.5" />
                </UBnsCustomExpansionComponent.ImageProperty>
            </UBnsCustomExpansionComponent>
        </BnsCustomImageWidget.ExpansionComponentList>
        <BnsCustomEditBoxWidget Name="ItemMapPanel_Search" LayoutData.Anchors="0 0.5 1 0.5" LayoutData.Offsets="28 0 23 24">
            <BnsCustomEditBoxWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="28" Offset2="23" />
            </BnsCustomEditBoxWidget.HorizontalResizeLink>
            <BnsCustomEditBoxWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
            </BnsCustomEditBoxWidget.VerticalResizeLink>
            <BnsCustomEditBoxWidget.String>
                <StringProperty SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" ClippingBound="0 -1" MaxCharacters="31" />
            </BnsCustomEditBoxWidget.String>
        </BnsCustomEditBoxWidget>
        <BnsCustomToggleButtonWidget Name="ItemMapPanel_ClearSearchTextBtn" LayoutData.Anchors="1 0.5 1 0.5" LayoutData.Offsets="-7 0 15 15">
            <BnsCustomToggleButtonWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" Offset1="7" />
            </BnsCustomToggleButtonWidget.HorizontalResizeLink>
            <BnsCustomToggleButtonWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
            </BnsCustomToggleButtonWidget.VerticalResizeLink>
            <BnsCustomToggleButtonWidget.NormalImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="156 50" ImageUVSize="18 18" EnableDrawImage="true" ImageScale="1" Opacity="0.5" />
            </BnsCustomToggleButtonWidget.NormalImageProperty>
        </BnsCustomToggleButtonWidget>
        <BnsCustomImageWidget Name="ItemMapPanel_Search_Image" LayoutData.Anchors="0 0.5 0 0.5" LayoutData.Offsets="5.0026855 0 19 19">
            <BnsCustomImageWidget.VerticalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
            </BnsCustomImageWidget.VerticalResizeLink>
            <BnsCustomImageWidget.BaseImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="235 23" ImageUVSize="15 15" EnableDrawImage="true" EnableResourceSize="true" ImageScale="1" Opacity="0.3" />
            </BnsCustomImageWidget.BaseImageProperty>
        </BnsCustomImageWidget>
    </BnsCustomImageWidget>
    <BnsCustomToggleButtonWidget Name="ItemMapPanel_Fold" s:Grid.Row="1" s:Grid.Column="1" Width="18" Height="48" MetaData="tooltip=UI.ItemMap.OpenClose" HorizontalAlignment="Left" Margin="-10 0">
        <BnsCustomToggleButtonWidget.HorizontalResizeLink>
            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemMapPanel_NavigationHolder" Offset1="-9" />
        </BnsCustomToggleButtonWidget.HorizontalResizeLink>
        <BnsCustomToggleButtonWidget.VerticalResizeLink>
            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" Offset1="30" />
        </BnsCustomToggleButtonWidget.VerticalResizeLink>
        <BnsCustomToggleButtonWidget.NormalImageProperty>
            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="41 118" ImageUVSize="15 16" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
        </BnsCustomToggleButtonWidget.NormalImageProperty>
        <BnsCustomToggleButtonWidget.ExpansionComponentList>
            <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="Fold">
                <UBnsCustomExpansionComponent.ImageProperty>
                    <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="203 23" ImageUVSize="8 14" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Offset="-1 0" Opacity="1" />
                </UBnsCustomExpansionComponent.ImageProperty>
            </UBnsCustomExpansionComponent>
            <UBnsCustomExpansionComponent bEnableSubState="true" bPostExpansitonRender="true" bShow="true" ExpansionName="Spread" WidgetSubState="Expansion_WidgetSubState_Checked">
                <UBnsCustomExpansionComponent.ImageProperty>
                    <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="213 23" ImageUVSize="8 14" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Offset="1 0" Opacity="1" />
                </UBnsCustomExpansionComponent.ImageProperty>
            </UBnsCustomExpansionComponent>
        </BnsCustomToggleButtonWidget.ExpansionComponentList>
    </BnsCustomToggleButtonWidget>
    <BnsCustomComboBoxWidget Name="ItemMapPanel_JobComboBox" LayoutData.Anchors="1 0 1 0" LayoutData.Offsets="-12 98 150 0" Visibility="Collapsed">
        <BnsCustomToggleButtonWidget Name="ItemMapPanel_JobComboBox_Button" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 6.1035156E-05 0 31">
            <BnsCustomToggleButtonWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" />
            </BnsCustomToggleButtonWidget.HorizontalResizeLink>
            <BnsCustomToggleButtonWidget.NormalImageProperty>
                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="22 42" ImageUVSize="20 20" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
            </BnsCustomToggleButtonWidget.NormalImageProperty>
            <BnsCustomToggleButtonWidget.ExpansionComponentList>
                <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Icon">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="193 39" ImageUVSize="14 8" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Offset="-10 0" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
            </BnsCustomToggleButtonWidget.ExpansionComponentList>
        </BnsCustomToggleButtonWidget>
        <BnsCustomLabelWidget Name="ItemMapPanel_JobComboBox_Label" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 6.1035156E-05 0 31">
            <BnsCustomLabelWidget.HorizontalResizeLink>
                <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" />
            </BnsCustomLabelWidget.HorizontalResizeLink>
            <BnsCustomLabelWidget.String>
                <StringProperty LabelText="직업" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" Padding="20 0" />
            </BnsCustomLabelWidget.String>
            <BnsCustomLabelWidget.ExpansionComponentList>
                <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="SelectedLabel">
                    <UBnsCustomExpansionComponent.ImageProperty>
                        <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                    </UBnsCustomExpansionComponent.ImageProperty>
                </UBnsCustomExpansionComponent>
            </BnsCustomLabelWidget.ExpansionComponentList>
        </BnsCustomLabelWidget>
        <BnsCustomListCtrlWidget Name="ItemMapPanel_JobComboBox_List" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 31 0 334">
            <BnsCustomImageWidget Name="ItemMapPanel_JobComboBox_List_ListContainer_1" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 0 9 33">
                <BnsCustomImageWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset2="9" />
                </BnsCustomImageWidget.HorizontalResizeLink>
                <BnsCustomImageWidget.BaseImageProperty>
                    <ImageProperty ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                </BnsCustomImageWidget.BaseImageProperty>
                <BnsCustomImageWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="MouseOver" WidgetState="BNSCustomWidgetState_Active">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Pressed" WidgetState="BNSCustomWidgetState_Pressed">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="JobLabel" MetaData=" ">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                        <UBnsCustomExpansionComponent.StringProperty>
                            <StringProperty LabelText="직업" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBound="10 0" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="0 -5" Opacity="1" TextScale="1" AnimScale="1" />
                        </UBnsCustomExpansionComponent.StringProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomImageWidget.ExpansionComponentList>
            </BnsCustomImageWidget>
            <BnsCustomImageWidget Name="ItemMapPanel_JobComboBox_List_ListContainer_2" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 33 9 33">
                <BnsCustomImageWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset2="9" />
                </BnsCustomImageWidget.HorizontalResizeLink>
                <BnsCustomImageWidget.BaseImageProperty>
                    <ImageProperty ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                </BnsCustomImageWidget.BaseImageProperty>
                <BnsCustomImageWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="MouseOver" WidgetState="BNSCustomWidgetState_Active">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Pressed" WidgetState="BNSCustomWidgetState_Pressed">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="JobLabel" MetaData=" ">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                        <UBnsCustomExpansionComponent.StringProperty>
                            <StringProperty LabelText="직업" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBound="10 0" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="0 -5" Opacity="1" TextScale="1" AnimScale="1" />
                        </UBnsCustomExpansionComponent.StringProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomImageWidget.ExpansionComponentList>
            </BnsCustomImageWidget>
            <BnsCustomImageWidget Name="ItemMapPanel_JobComboBox_List_ListContainer_3" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 66 9 33">
                <BnsCustomImageWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset2="9" />
                </BnsCustomImageWidget.HorizontalResizeLink>
                <BnsCustomImageWidget.BaseImageProperty>
                    <ImageProperty ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                </BnsCustomImageWidget.BaseImageProperty>
                <BnsCustomImageWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="MouseOver" WidgetState="BNSCustomWidgetState_Active">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Pressed" WidgetState="BNSCustomWidgetState_Pressed">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="JobLabel" MetaData=" ">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                        <UBnsCustomExpansionComponent.StringProperty>
                            <StringProperty LabelText="직업" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBound="10 0" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="0 -5" Opacity="1" TextScale="1" AnimScale="1" />
                        </UBnsCustomExpansionComponent.StringProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomImageWidget.ExpansionComponentList>
            </BnsCustomImageWidget>
            <BnsCustomImageWidget Name="ItemMapPanel_JobComboBox_List_ListContainer_4" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 99 9 33">
                <BnsCustomImageWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset2="9" />
                </BnsCustomImageWidget.HorizontalResizeLink>
                <BnsCustomImageWidget.BaseImageProperty>
                    <ImageProperty ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                </BnsCustomImageWidget.BaseImageProperty>
                <BnsCustomImageWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="MouseOver" WidgetState="BNSCustomWidgetState_Active">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Pressed" WidgetState="BNSCustomWidgetState_Pressed">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="JobLabel" MetaData=" ">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                        <UBnsCustomExpansionComponent.StringProperty>
                            <StringProperty LabelText="직업" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBound="10 0" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="0 -5" Opacity="1" TextScale="1" AnimScale="1" />
                        </UBnsCustomExpansionComponent.StringProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomImageWidget.ExpansionComponentList>
            </BnsCustomImageWidget>
            <BnsCustomImageWidget Name="ItemMapPanel_JobComboBox_List_ListContainer_5" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 132 9 33">
                <BnsCustomImageWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset2="9" />
                </BnsCustomImageWidget.HorizontalResizeLink>
                <BnsCustomImageWidget.BaseImageProperty>
                    <ImageProperty ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                </BnsCustomImageWidget.BaseImageProperty>
                <BnsCustomImageWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="MouseOver" WidgetState="BNSCustomWidgetState_Active">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Pressed" WidgetState="BNSCustomWidgetState_Pressed">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="JobLabel" MetaData=" ">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                        <UBnsCustomExpansionComponent.StringProperty>
                            <StringProperty LabelText="직업" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBound="10 0" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="0 -5" Opacity="1" TextScale="1" AnimScale="1" />
                        </UBnsCustomExpansionComponent.StringProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomImageWidget.ExpansionComponentList>
            </BnsCustomImageWidget>
            <BnsCustomImageWidget Name="ItemMapPanel_JobComboBox_List_ListContainer_6" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 165 9 33">
                <BnsCustomImageWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset2="9" />
                </BnsCustomImageWidget.HorizontalResizeLink>
                <BnsCustomImageWidget.BaseImageProperty>
                    <ImageProperty ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                </BnsCustomImageWidget.BaseImageProperty>
                <BnsCustomImageWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="MouseOver" WidgetState="BNSCustomWidgetState_Active">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Pressed" WidgetState="BNSCustomWidgetState_Pressed">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="JobLabel" MetaData=" ">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                        <UBnsCustomExpansionComponent.StringProperty>
                            <StringProperty LabelText="직업" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBound="10 0" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="0 -5" Opacity="1" TextScale="1" AnimScale="1" />
                        </UBnsCustomExpansionComponent.StringProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomImageWidget.ExpansionComponentList>
            </BnsCustomImageWidget>
            <BnsCustomImageWidget Name="ItemMapPanel_JobComboBox_List_ListContainer_7" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 198 9 33">
                <BnsCustomImageWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset2="9" />
                </BnsCustomImageWidget.HorizontalResizeLink>
                <BnsCustomImageWidget.BaseImageProperty>
                    <ImageProperty ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                </BnsCustomImageWidget.BaseImageProperty>
                <BnsCustomImageWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="MouseOver" WidgetState="BNSCustomWidgetState_Active">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Pressed" WidgetState="BNSCustomWidgetState_Pressed">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="JobLabel" MetaData=" ">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                        <UBnsCustomExpansionComponent.StringProperty>
                            <StringProperty LabelText="직업" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBound="10 0" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="0 -5" Opacity="1" TextScale="1" AnimScale="1" />
                        </UBnsCustomExpansionComponent.StringProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomImageWidget.ExpansionComponentList>
            </BnsCustomImageWidget>
            <BnsCustomImageWidget Name="ItemMapPanel_JobComboBox_List_ListContainer_8" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 231 9 33">
                <BnsCustomImageWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset2="9" />
                </BnsCustomImageWidget.HorizontalResizeLink>
                <BnsCustomImageWidget.BaseImageProperty>
                    <ImageProperty ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                </BnsCustomImageWidget.BaseImageProperty>
                <BnsCustomImageWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="MouseOver" WidgetState="BNSCustomWidgetState_Active">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Pressed" WidgetState="BNSCustomWidgetState_Pressed">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="JobLabel" MetaData=" ">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                        <UBnsCustomExpansionComponent.StringProperty>
                            <StringProperty LabelText="직업" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBound="10 0" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="0 -5" Opacity="1" TextScale="1" AnimScale="1" />
                        </UBnsCustomExpansionComponent.StringProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomImageWidget.ExpansionComponentList>
            </BnsCustomImageWidget>
            <BnsCustomImageWidget Name="ItemMapPanel_JobComboBox_List_ListContainer_9" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 264 9 33">
                <BnsCustomImageWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset2="9" />
                </BnsCustomImageWidget.HorizontalResizeLink>
                <BnsCustomImageWidget.BaseImageProperty>
                    <ImageProperty ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                </BnsCustomImageWidget.BaseImageProperty>
                <BnsCustomImageWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="MouseOver" WidgetState="BNSCustomWidgetState_Active">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Pressed" WidgetState="BNSCustomWidgetState_Pressed">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="JobLabel" MetaData=" ">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                        <UBnsCustomExpansionComponent.StringProperty>
                            <StringProperty LabelText="직업" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBound="10 0" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="0 -5" Opacity="1" TextScale="1" AnimScale="1" />
                        </UBnsCustomExpansionComponent.StringProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomImageWidget.ExpansionComponentList>
            </BnsCustomImageWidget>
            <BnsCustomImageWidget Name="ItemMapPanel_JobComboBox_List_ListContainer_10" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 297 9 33">
                <BnsCustomImageWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset2="9" />
                </BnsCustomImageWidget.HorizontalResizeLink>
                <BnsCustomImageWidget.BaseImageProperty>
                    <ImageProperty ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                </BnsCustomImageWidget.BaseImageProperty>
                <BnsCustomImageWidget.ExpansionComponentList>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="MouseOver" WidgetState="BNSCustomWidgetState_Active">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Pressed" WidgetState="BNSCustomWidgetState_Pressed">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="205 0" ImageUVSize="11 20" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                    </UBnsCustomExpansionComponent>
                    <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="JobLabel" MetaData=" ">
                        <UBnsCustomExpansionComponent.ImageProperty>
                            <ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                        </UBnsCustomExpansionComponent.ImageProperty>
                        <UBnsCustomExpansionComponent.StringProperty>
                            <StringProperty LabelText="직업" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBound="10 0" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="0 -5" Opacity="1" TextScale="1" AnimScale="1" />
                        </UBnsCustomExpansionComponent.StringProperty>
                    </UBnsCustomExpansionComponent>
                </BnsCustomImageWidget.ExpansionComponentList>
            </BnsCustomImageWidget>
            <BnsCustomScrollBarWidget Name="ItemMapPanel_JobComboBox_List_ScrollBar" LayoutData.Anchors="1 0 1 1" LayoutData.Offsets="0 0 8 0">
                <BnsCustomScrollBarWidget.HorizontalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" />
                </BnsCustomScrollBarWidget.HorizontalResizeLink>
                <BnsCustomScrollBarWidget.VerticalResizeLink>
                    <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" />
                </BnsCustomScrollBarWidget.VerticalResizeLink>
                <BnsCustomSliderBarWidget Name="ItemMapPanel_JobComboBox_List_ScrollBar_SliderBar" LayoutData.Anchors="0.5 0 0.5 1" LayoutData.Offsets="0 1 8 1">
                    <BnsCustomSliderBarWidget.HorizontalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                    </BnsCustomSliderBarWidget.HorizontalResizeLink>
                    <BnsCustomSliderBarWidget.VerticalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="1" Offset2="1" />
                    </BnsCustomSliderBarWidget.VerticalResizeLink>
                    <BnsCustomLabelButtonWidget Name="ItemMapPanel_JobComboBox_List_ScrollBar_SliderBar_Marker" LayoutData.Anchors="0.5 0 0.5 0" LayoutData.Offsets="0 -6.1035156E-05 4 62.999695">
                        <BnsCustomLabelButtonWidget.HorizontalResizeLink>
                            <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                        </BnsCustomLabelButtonWidget.HorizontalResizeLink>
                        <BnsCustomLabelButtonWidget.NormalImageProperty>
                            <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="142 30" ImageUVSize="4 13" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" Opacity="1" />
                        </BnsCustomLabelButtonWidget.NormalImageProperty>
                    </BnsCustomLabelButtonWidget>
                </BnsCustomSliderBarWidget>
                <BnsCustomLabelButtonWidget Name="ItemMapPanel_JobComboBox_List_ScrollBar_DecrementButton" LayoutData.Anchors="0.5 0 0.5 0" LayoutData.Offsets="0 -0.00018310547 0 0">
                    <BnsCustomLabelButtonWidget.HorizontalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                    </BnsCustomLabelButtonWidget.HorizontalResizeLink>
                    <BnsCustomLabelButtonWidget.NormalImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="28 167" ImageUVSize="19 18" EnableDrawImage="true" EnableSkinColor="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                    </BnsCustomLabelButtonWidget.NormalImageProperty>
                    <BnsCustomLabelButtonWidget.ExpansionComponentList>
                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Normal">
                            <UBnsCustomExpansionComponent.ImageProperty>
                                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="344 568" ImageUVSize="11 9" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                            </UBnsCustomExpansionComponent.ImageProperty>
                        </UBnsCustomExpansionComponent>
                    </BnsCustomLabelButtonWidget.ExpansionComponentList>
                </BnsCustomLabelButtonWidget>
                <BnsCustomLabelButtonWidget Name="ItemMapPanel_JobComboBox_List_ScrollBar_IncrementButton" LayoutData.Anchors="0.5 1 0.5 1">
                    <BnsCustomLabelButtonWidget.HorizontalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
                    </BnsCustomLabelButtonWidget.HorizontalResizeLink>
                    <BnsCustomLabelButtonWidget.VerticalResizeLink>
                        <BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" />
                    </BnsCustomLabelButtonWidget.VerticalResizeLink>
                    <BnsCustomLabelButtonWidget.NormalImageProperty>
                        <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="28 167" ImageUVSize="19 18" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
                    </BnsCustomLabelButtonWidget.NormalImageProperty>
                    <BnsCustomLabelButtonWidget.ExpansionComponentList>
                        <UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Down">
                            <UBnsCustomExpansionComponent.ImageProperty>
                                <ImageProperty BaseImageTexture="/Game/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="344 578" ImageUVSize="11 8" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
                            </UBnsCustomExpansionComponent.ImageProperty>
                        </UBnsCustomExpansionComponent>
                    </BnsCustomLabelButtonWidget.ExpansionComponentList>
                </BnsCustomLabelButtonWidget>
            </BnsCustomScrollBarWidget>
        </BnsCustomListCtrlWidget>
    </BnsCustomComboBoxWidget>
</s:Grid>