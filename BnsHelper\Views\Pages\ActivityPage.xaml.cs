﻿using Microsoft.Web.WebView2.Core;
using System.Diagnostics;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.ViewModels.Pages;

namespace Xylia.BnsHelper.Views.Pages;
public partial class ActivityPage 
{
    #region Constructor
    public ActivityPage()
    {
        InitializeComponent();
        DataContext = new ActivityPageViewModel();

        SkinHelpers.OnDayNightChanged += OnDayNightChanged;
        WebView.CoreWebView2InitializationCompleted += WebView_CoreWebView2InitializationCompleted;
    }
    #endregion

    #region Methods
    protected override void OnInitialized(EventArgs e)
    {
        base.OnInitialized(e);
        OnDayNightChanged(this, SkinHelpers.IsNight());
    }

    private void WebView_CoreWebView2InitializationCompleted(object? sender, CoreWebView2InitializationCompletedEventArgs args)
    {
        if (!args.IsSuccess) return;

        WebView.CoreWebView2.Settings.AreDevToolsEnabled = false;
        WebView.CoreWebView2.Settings.UserAgent = "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.104 Safari/537.36 BnsIngameBrowser";
        WebView.CoreWebView2.NewWindowRequested += WebView_CoreWebView2NewWindowRequested;
    }

    private void WebView_CoreWebView2NewWindowRequested(object? sender, CoreWebView2NewWindowRequestedEventArgs args)
    {
        args.Handled = true;
        Process.Start("explorer.exe", args.Uri.ToString());
    }

    private void OnDayNightChanged(object? sender, bool nigth)
    {
        WebView.Source = new Uri($"https://www.bnszs.com/activity.html?s={SettingHelper.Default.Publisher}&t={(nigth ? "1" : "0")}");
    }
    #endregion
}
