using System.Diagnostics;
using Xylia.BnsHelper.Models;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Service;

/// <summary>
/// 公告版本查询包
/// </summary>
internal class AnnouncementVersionPacket : IPacket
{
    #region Fields
    public uint ErrorCode;
    public string? ErrorMessage;
    public ushort? Version;    
    #endregion

    #region Methods
    public DataArchiveWriter Create()
    {
        return new DataArchiveWriter();
    }

    public void Read(DataArchive reader)
    {
        ErrorCode = reader.Read<uint>();
        if (ErrorCode == 0)
        {
            Version = reader.Read<ushort>();
        }
        else
        {
            Version = null;
            ErrorMessage = reader.ReadString();
            Debug.WriteLine($"[ERROR] 获取版本号失败: {ErrorMessage}");
        }
    }
    #endregion
}

/// <summary>
/// 公告列表查询包
/// </summary>
internal class AnnouncementIdsPacket : IPacket
{
    #region Fields
    public uint ErrorCode;
    public string? ErrorMessage;

    public KeyValuePair<uint, ushort>[]? Announcements;  // 公告列表
    #endregion

    #region Methods
    public DataArchiveWriter Create()
    {
        return new DataArchiveWriter();
    }

    public void Read(DataArchive reader)
    {
        ErrorCode = reader.Read<uint>();
        if (ErrorCode == 0)
        {
            Announcements = new KeyValuePair<uint, ushort>[reader.Read<short>()];

            for (int i = 0; i < Announcements.Length; i++)
            {
                Announcements[i] = new KeyValuePair<uint, ushort>(
                    reader.Read<uint>(),   // 公告ID
                    reader.Read<ushort>() // 版本号
                );
            }
        }
        else
        {
            ErrorMessage = reader.ReadString();
            Debug.WriteLine($"[ERROR] 获取公告ID列表失败: {ErrorMessage}");
        }
    }
    #endregion
}

/// <summary>
/// 公告详情请求数据包
/// </summary>
internal class AnnouncementDetailPacket : IPacket
{
    #region Fields
    public uint AnnouncementId;

    public uint ErrorCode;
    public string? ErrorMessage;
    public Announcement? Announcement;
    #endregion

    #region Methods
    public DataArchiveWriter Create()
    {
        var writer = new DataArchiveWriter();
        writer.Write(AnnouncementId);
        return writer;
    }

    public void Read(DataArchive reader)
    {
        ErrorCode = reader.Read<uint>();
        if (ErrorCode == 0)
        {
            Announcement = new Announcement(reader);
        }
        else
        {
            ErrorMessage = reader.ReadString();
            Debug.WriteLine($"[ERROR] 获取公告详情失败: {ErrorMessage}");
        }
    }
    #endregion
}
