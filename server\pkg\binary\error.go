package binary

// 错误码定义
const (
	ErrorCodeSuccess          = 0
	ErrorCodeServerError      = 1000 // 服务器错误
	ErrorCodeInvalidRequest   = 1001 // 无效请求
	ErrorCodeInvalidQQ        = 1002 // 无效QQ号
	ErrorCodeInvalidDevice    = 1003 // 无效设备
	ErrorCodeUnauthorized     = 1004 // 未授权/账号被封禁
	ErrorCodeTokenExpired     = 1005 // Token过期
	ErrorCodeNotFound         = 1006 // 资源不存在
	ErrorCodeRateLimit        = 1007 // 频率限制
	ErrorCodeTimestampInvalid = 1008 // 时间戳无效（需要更新设备时间）
)

// 签到专用错误码
const (
	LuckyDrawErrorCodeSuccess       = 0 // 成功
	LuckyDrawErrorCodeAlreadySigned = 1 // 已签到
	LuckyDrawErrorCodeNoActivity    = 2 // 暂无活动
	LuckyDrawErrorCodeDeviceLimit   = 3 // 设备限制
	LuckyDrawErrorCodeServerError   = 4 // 服务器错误
)

// 错误码到消息的映射
var ErrorMessages = map[uint32]string{
	ErrorCodeSuccess:          "Success",
	ErrorCodeServerError:      "Internal server error",
	ErrorCodeInvalidRequest:   "Invalid request",
	ErrorCodeInvalidQQ:        "Invalid QQ number",
	ErrorCodeInvalidDevice:    "Invalid device information",
	ErrorCodeUnauthorized:     "Unauthorized access",
	ErrorCodeTokenExpired:     "Token expired",
	ErrorCodeNotFound:         "Resource not found",
	ErrorCodeRateLimit:        "Rate limit exceeded",
	ErrorCodeTimestampInvalid: "Invalid timestamp",
}

// ProtocolError 协议错误
type ProtocolError struct {
	Code    uint32
	Message string
}

// NewProtocolError 创建新的协议错误
func NewProtocolError(code uint32, message string) error {
	return &ProtocolError{
		Code:    code,
		Message: message,
	}
}

// 实现接口
func (e *ProtocolError) Error() string {
	return e.Message
}

// GetErrorMessage 根据错误码获取错误消息
func GetErrorMessage(code uint32) string {
	if msg, exists := ErrorMessages[code]; exists {
		return msg
	}
	return "Unknown error"
}

// SecurityError 安全验证错误
type SecurityError struct {
	Code    uint32
	Message string
}

func (e *SecurityError) Error() string {
	return e.Message
}

// NewSecurityError 创建安全验证错误
func NewSecurityError(code uint32, message string) *SecurityError {
	return &SecurityError{
		Code:    code,
		Message: message,
	}
}
