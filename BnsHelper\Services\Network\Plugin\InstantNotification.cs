﻿using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using Xylia.Preview.Data.Engine;
using static Xylia.Preview.Data.Models.ChatChannelOption;

namespace Xylia.BnsHelper.Services.Network.Plugin;
internal class InstantNotification : IPacket
{
    #region Fields
    public string? Text;
    public CategorySeq Category;
    public bool Headline;

    public string? TrimedText => Trim(Text);
    #endregion

    #region Methods
    public DataArchiveWriter Create()
    {
        var writer = new DataArchiveWriter();
        writer.Write((short)1);
        writer.WriteString(Text, Encoding.Unicode);
        writer.Write((byte)Category);
        writer.Write(Headline);
        return writer;
    }

    public void Read(DataArchive reader)
    {
        Text = reader.ReadString();
        Category = (CategorySeq)reader.Read<byte>();
    }

    public static string? Trim(string? text)
    {
        if (text is null) return null;

        // remove tags
        var CopyTxt = WebUtility.HtmlDecode(text);
        CopyTxt = new Regex(@"<\s*br\s*/\s*>").Replace(CopyTxt, "\n");
        return new Regex(@"<.*?>").Replace(CopyTxt, "");
    }
    #endregion
}

public enum PluginPacketType
{
    Authorize = 0,
    InstantNotification = 1,
    QueryWorld = 11,
    QueryQuote = 12,
}