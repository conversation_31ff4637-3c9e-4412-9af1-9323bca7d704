﻿<hc:Window x:Class="Xylia.BnsHelper.Views.MainWindow"
		xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
		xmlns:hc="https://handyorg.github.io/handycontrol"
		xmlns:helper="clr-namespace:Xylia.BnsHelper.Common.Helpers"
		xmlns:system="clr-namespace:System;assembly=mscorlib"
		MinHeight="450" MinWidth="600" ResizeMode="CanMinimize" SizeToContent="WidthAndHeight"
		WindowStartupLocation="CenterScreen" Loaded="OnLoaded">

    <hc:Window.Title>
        <MultiBinding StringFormat="{}{0} {2}">
            <Binding Source="{x:Static helper:VersionHelper.ProductName}" />
            <Binding Source="{x:Static helper:VersionHelper.Version}" />
            <Binding Path="Setting.Server.Latency" FallbackValue="" TargetNullValue="" />
        </MultiBinding>
    </hc:Window.Title>

    <hc:Window.Resources>
        <system:String x:Key="Game">/Resources/Images/game.ico</system:String>
        <ImageSource x:Key="Head">pack://application:,,,/Resources/Images/head.png</ImageSource>
        <ImageSource x:Key="Logo">pack://application:,,,/Resources/Images/logo.png</ImageSource>
    </hc:Window.Resources>

    <!-- Commands -->
    <hc:Window.InputBindings>
        <KeyBinding Key="A" Modifiers="Ctrl" Command="hc:ControlCommands.PushMainWindow2Top" />
        <KeyBinding Key="B" Modifiers="Ctrl" Command="{Binding OpenDamageMeterPanelCommand}" />
        <KeyBinding Key="R" Modifiers="Ctrl" Command="{Binding RandomHammerCommand}" />
        <KeyBinding Key="F4" Modifiers="Alt" Command="{Binding ShutdownAppCommand}" />
    </hc:Window.InputBindings>

    <hc:Window.NonClientAreaContent>
        <Grid>
            <Menu HorizontalAlignment="Right" VerticalAlignment="Center">
                <!-- 菜单按钮 -->
                <MenuItem ToolTip="{DynamicResource Text.More}" Style="{StaticResource TopLevelMenuItemStyle}">
                    <MenuItem.Header>
                        <Path Fill="{DynamicResource PrimaryTextBrush}" Width="12" Height="12" Stretch="Uniform" Data="M3 6h18v1.5H3V6zm0 5h18v1.5H3v-1.5zm0 5h18v1.5H3v-1.5z" />
                    </MenuItem.Header>

                    <MenuItem Header="{DynamicResource MainWindow_OpenLink2}" Command="hc:ControlCommands.OpenLink" CommandParameter="https://bns.qq.com/neo/">
                        <MenuItem.Icon>
                            <Image Source="{Binding Source={StaticResource Game},Converter={StaticResource ImageSelector},ConverterParameter=16}" />
                        </MenuItem.Icon>
                    </MenuItem>
                    <MenuItem Header="{DynamicResource MainWindow_OpenLink1}" Command="hc:ControlCommands.OpenLink" CommandParameter="https://www.bnszs.com/">
                        <MenuItem.Icon>
                            <Image Source="{StaticResource Logo}" Width="16" Height="16" />
                        </MenuItem.Icon>
                    </MenuItem>

                    <!-- 用户信息 -->
                    <MenuItem Visibility="{Binding IsLogin,Converter={StaticResource Boolean2VisibilityConverter}}">
                        <MenuItem.Icon>
                            <Viewbox Width="16" Height="16">
                                <Path Data="{StaticResource UserGeometry}" Fill="{DynamicResource PrimaryBrush}" />
                            </Viewbox>
                        </MenuItem.Icon>
                        <MenuItem.Header>
                            <TextBlock>
								<Run Text="{DynamicResource MainWindow_OnlineUserCount}" />
								<Run Text="{Binding UserCount}" />
                            </TextBlock>
                        </MenuItem.Header>
                    </MenuItem>

                    <!-- 帮助 -->
                    <MenuItem Header="{DynamicResource Help_Name}" Command="hc:ControlCommands.OpenLink" CommandParameter="https://docs.qq.com/doc/p/966f4638d107a5e2bb7261489ef5e1779df33f14" />
                </MenuItem>
                
                <!-- 公告按钮 -->
                <MenuItem Style="{StaticResource TopLevelMenuItemStyle}" ToolTip="{DynamicResource AnnouncementWindow_Title}" Click="OpenAnnouncement">
                    <MenuItem.Header>
                        <hc:Badge Status="Dot" Style="{StaticResource BadgeDanger}" ShowBadge="{Binding HasUnreadAnnouncements}">
                            <Path Fill="{DynamicResource PrimaryTextBrush}" Width="12" Height="12" Stretch="Uniform" Data="M433.38 334.75C405.38 308.25 384.004 280.375 384.004 185.875C384.004 106.25 320.627 41.375 240 33.5V16C240 7.125 232.875 0 224 0S208 7.125 208 16V33.5C127.373 41.375 63.996 106.25 63.996 185.875C63.996 280.375 42.619 308.25 14.619 334.75C0.619 348.125 -3.756 368.25 3.369 386C10.619 404.25 28.119 416 47.994 416H400.005C419.88 416 437.38 404.25 444.63 386C451.755 368.25 447.38 348.125 433.38 334.75ZM400.005 384H47.994C33.744 384 26.619 367.5 36.619 358C71.496 324.75 95.996 287.625 95.996 185.875C95.996 118.5 153.248 64 224 64S352.004 118.5 352.004 185.875C352.004 287.25 376.254 324.625 411.38 358C421.38 367.625 414.13 384 400.005 384ZM272.295 448C264.857 448 257.783 452.146 255.244 458.879C250.603 471.176 238.334 480 223.994 480S197.384 471.176 192.744 458.879C190.205 452.146 183.129 448 175.693 448H175.691C165.078 448 157.48 457.998 160.765 467.717C169.431 493.371 194.525 512 223.994 512C253.461 512 278.554 493.371 287.222 467.717C290.505 457.998 282.91 448 272.295 448Z" />
                        </hc:Badge>
                    </MenuItem.Header>
                </MenuItem>

                <!-- 设置按钮 -->
                <MenuItem Style="{StaticResource TopLevelMenuItemStyle}" ToolTip="{DynamicResource Settings_Name}" Click="OpenSetting">
                    <MenuItem.Header>
                        <StackPanel>
                            <Path Data="{StaticResource SettingGeometry}" Width="20" Height="12" Stretch="Uniform" Fill="{DynamicResource PrimaryBrush}" Visibility="{Binding IsSettingPage,Converter={StaticResource Boolean2VisibilityConverter}}" />
                            <Path Data="{StaticResource SettingGeometry}" Width="20" Height="12" Stretch="Uniform" Fill="{DynamicResource PrimaryTextBrush}" Visibility="{Binding IsSettingPage,Converter={StaticResource Boolean2VisibilityReConverter}}" />
                        </StackPanel>
                    </MenuItem.Header>
                </MenuItem>
            </Menu>

            <!-- NotifyIcon -->
            <hc:NotifyIcon Token="NotifyIcon" IsBlink="{Binding NotifyIconIsBlink}" Visibility="{Binding Setting.UseNotifyIcon,Converter={StaticResource Boolean2VisibilityConverter}}">
                <hc:NotifyIcon.ContextMenu>
                    <ContextMenu>
                        <MenuItem Command="hc:ControlCommands.PushMainWindow2Top" Header="{DynamicResource Application_Push}" InputGestureText="Ctrl + A" />
                        <MenuItem Command="{Binding OpenDamageMeterPanelCommand}" Header="{DynamicResource Application_PushACT}" InputGestureText="Ctrl + B" />
                        <MenuItem Command="{Binding RandomHammerCommand}" Header="{DynamicResource Application_RandomHammer}" InputGestureText="Ctrl + R" />
                        <MenuItem Command="{Binding ShutdownAppCommand}" Header="{DynamicResource Application_Exit}" />
                    </ContextMenu>
                </hc:NotifyIcon.ContextMenu>
                <hc:Interaction.Triggers>
                    <hc:EventTrigger EventName="Click">
                        <hc:EventToCommand Command="hc:ControlCommands.PushMainWindow2Top" />
                    </hc:EventTrigger>
                </hc:Interaction.Triggers>
            </hc:NotifyIcon>
        </Grid>
    </hc:Window.NonClientAreaContent>

    <!-- Content -->
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="105" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Slider -->
        <Grid Grid.RowSpan="99">
            <Grid.RowDefinitions>
                <RowDefinition Height="80" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!-- Login -->
            <Button Grid.Row="0" Command="{Binding LoginCommand}" Style="{StaticResource ButtonCustom}">
                <Grid Width="60" Height="60">
                    <!-- 主头像 -->
                    <Image Source="{Binding User.HeadImg, Mode=OneWay, FallbackValue={StaticResource Head}}"
                           Width="50" Height="50" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Image.Clip>
                            <RectangleGeometry RadiusX="25" RadiusY="25" Rect="0,0,50,50" />
                        </Image.Clip>
                        <Image.ContextMenu>
                            <ContextMenu Visibility="{Binding IsLogin, Converter={StaticResource Boolean2VisibilityConverter}}">
                                <!-- 权限级别 -->
                                <MenuItem IsEnabled="False">
                                    <MenuItem.Icon>
                                        <Path Data="{StaticResource UserGeometry}" Fill="{DynamicResource PrimaryBrush}" Width="16" Height="16"/>
                                    </MenuItem.Icon>
                                    <MenuItem.Header>
                                        <TextBlock>
                                            <Run Text="{Binding User.PermissionText,Mode=OneWay}" /> 
                                            <Run Text="{Binding User.ExpirationText,Mode=OneWay}" />
                                        </TextBlock>
                                    </MenuItem.Header>
                                </MenuItem>

                                <!-- 活动提示 -->
                                <MenuItem IsEnabled="False"
                                         Visibility="{Binding User.ShowActivityNotice, Converter={StaticResource Boolean2VisibilityConverter}}"
                                         Foreground="Orange" FontWeight="Bold">
                                    <MenuItem.Icon>
                                        <Path Data="{StaticResource InfoGeometry}" Fill="Orange" Width="16" Height="16"/>
                                    </MenuItem.Icon>
                                    <MenuItem.Header>
                                        <TextBlock>
                                            <Run Text="{DynamicResource MainWindow_BattleStatsPromo}" />
                                            <LineBreak />
                                            <Run Text="{DynamicResource MainWindow_SignInRestore}" />
                                        </TextBlock>
                                    </MenuItem.Header>
                                </MenuItem>

                                <Separator />

                                <!-- 总签到次数 -->
                                <MenuItem IsEnabled="False">
                                    <MenuItem.Header>
                                        <TextBlock>
                                            <Run Text="{DynamicResource MainWindow_TotalSignIn}"/>
                                            <Run Text="{Binding User.TotalSignCount, Mode=OneWay}" FontWeight="Bold"/>
                                        </TextBlock>
                                    </MenuItem.Header>
                                    <MenuItem.Icon>
                                        <Path Data="{StaticResource ChartBarGeometry}" Fill="{DynamicResource PrimaryBrush}" Width="16" Height="16"/>
                                    </MenuItem.Icon>
                                </MenuItem>

                                <!-- 签到按钮 -->
                                <MenuItem Command="{Binding SignInCommand}">
                                    <MenuItem.Header>
                                        <TextBlock Text="{Binding User.SignInStatusText,Mode=OneWay}" />
                                    </MenuItem.Header>
                                    <MenuItem.Icon>
                                        <Path Data="{StaticResource CalendarGeometry}" Fill="{DynamicResource PrimaryBrush}" Width="16" Height="16"/>
                                    </MenuItem.Icon>
                                </MenuItem>

                                <!-- 口令码 -->
                                <MenuItem Command="{Binding ActivateCDKeyCommand}" Header="{DynamicResource MainWindow_ActivateCDKey}">
                                    <MenuItem.Icon>
                                        <Path Data="{StaticResource SettingGeometry}" Fill="{DynamicResource PrimaryBrush}" Width="16" Height="16"/>
                                    </MenuItem.Icon>
                                </MenuItem>

                                <Separator/>

                                <!-- 退出账号 -->
                                <MenuItem Command="{Binding LoginCommand}" Header="{DynamicResource MainWindow_Logout}" Foreground="{StaticResource DangerBrush}">
                                    <MenuItem.Icon>
                                        <Path Data="{StaticResource UserGeometry}" Fill="{DynamicResource DangerBrush}" Width="16" Height="16"/>
                                    </MenuItem.Icon>
                                </MenuItem>
                            </ContextMenu>
                        </Image.ContextMenu>
                    </Image>

                    <!-- 权限标识 -->
                    <Grid Width="18" Height="18" HorizontalAlignment="Right" VerticalAlignment="Bottom" Margin="0,0,5,5">
                        <Grid.Style>
                            <Style TargetType="Grid">
                                <Setter Property="Visibility" Value="Collapsed" />
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding User.Permission, Mode=OneWay}" Value="1">
                                        <Setter Property="Visibility" Value="Visible" />
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding User.Permission, Mode=OneWay}" Value="2">
                                        <Setter Property="Visibility" Value="Visible" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Grid.Style>

                        <!-- 外层阴影圆 -->
                        <Ellipse Fill="#40000000" Width="18" Height="18" />
                        <Ellipse Fill="White" Width="16" Height="16" />

                        <!-- 权限颜色圆 -->
                        <Ellipse Width="15" Height="15">
                            <Ellipse.Style>
                                <Style TargetType="Ellipse">
                                    <Setter Property="Fill">
                                        <Setter.Value>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                <GradientStop Color="#FFD700" Offset="0" />
                                                <GradientStop Color="#FFA500" Offset="1" />
                                            </LinearGradientBrush>
                                        </Setter.Value>
                                    </Setter>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding User.Permission, Mode=OneWay}" Value="1">
                                            <Setter Property="Fill">
                                                <Setter.Value>
                                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                        <GradientStop Color="#FFD700" Offset="0" />
                                                        <GradientStop Color="#FFA500" Offset="1" />
                                                    </LinearGradientBrush>
                                                </Setter.Value>
                                            </Setter>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding User.Permission, Mode=OneWay}" Value="2">
                                            <Setter Property="Fill">
                                                <Setter.Value>
                                                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                        <GradientStop Color="#9932CC" Offset="0" />
                                                        <GradientStop Color="#6A0DAD" Offset="1" />
                                                    </LinearGradientBrush>
                                                </Setter.Value>
                                            </Setter>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Ellipse.Style>
                        </Ellipse>

                        <!-- V字符标识 -->
                        <TextBlock Text="V" Foreground="White" FontSize="10" FontWeight="Bold" FontFamily="Arial"
                                   HorizontalAlignment="Center" VerticalAlignment="Center" Margin="0,0,0,1">
                            <TextBlock.Effect>
                                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="0.5" BlurRadius="1" Opacity="0.8" />
                            </TextBlock.Effect>
                        </TextBlock>
                    </Grid>
                </Grid>
            </Button>

            <!-- Navigation -->
            <ListBox Grid.Row="2" x:Name="Navigation" Background="{DynamicResource SecondaryRegionBrush}" BorderThickness="0" SelectionChanged="Navigation_SelectionChanged">
                <ListBox.ItemContainerStyle>
                    <Style TargetType="ListBoxItem" BasedOn="{StaticResource ListBoxItemCustom}">
                        <Setter Property="Cursor" Value="Hand" />
                        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
                        <Setter Property="Margin" Value="0 0 1 2" />
                    </Style>
                </ListBox.ItemContainerStyle>
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <Border Name="BorderRoot" MinHeight="38" Background="Transparent">
                            <hc:SimplePanel>
                                <Rectangle x:Name="Rect" Width="3" HorizontalAlignment="Left" />
                                <DockPanel Margin="13 0 0 0">
                                    <hc:Badge Status="Dot" BadgeMargin="0,7,-5,0" Style="{StaticResource BadgeDanger}" ShowBadge="{Binding IsNew}">
                                        <Path x:Name="PresenterIcon" Fill="{DynamicResource PrimaryTextBrush}" Data="{Binding Icon}" Width="18" Height="18" Stretch="Uniform" />
                                    </hc:Badge>
                                    <TextBlock x:Name="PresenterHeader" Text="{Binding Name}" Foreground="{DynamicResource PrimaryTextBrush}" FontSize="13" VerticalAlignment="Center" Margin="7 0" />
                                </DockPanel>
                            </hc:SimplePanel>
                        </Border>
                        <DataTemplate.Triggers>
                            <DataTrigger Binding="{Binding IsSelected,RelativeSource={RelativeSource AncestorType=ListBoxItem}}" Value="True">
                                <Setter TargetName="BorderRoot" Property="Background" Value="{DynamicResource BorderBrush}" />
                                <Setter TargetName="Rect" Property="Fill" Value="{DynamicResource PrimaryBrush}" />
                                <Setter TargetName="PresenterIcon" Property="Fill" Value="{DynamicResource PrimaryBrush}" />
                                <Setter TargetName="PresenterHeader" Property="Foreground" Value="{DynamicResource PrimaryBrush}" />
                            </DataTrigger>
                            <MultiTrigger>
                                <MultiTrigger.Conditions>
                                    <Condition Property="IsMouseOver" Value="True" />
                                    <Condition Property="IsEnabled" Value="True"/>
                                </MultiTrigger.Conditions>
                                <Setter TargetName="BorderRoot" Property="Background" Value="{DynamicResource BorderBrush}" />
                            </MultiTrigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="BorderRoot" Property="TextElement.Foreground" Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}" />
                            </Trigger>
                        </DataTemplate.Triggers>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
        </Grid>

        <!-- Content -->
        <Frame Grid.Column="1" x:Name="Presenter" BorderThickness="0" NavigationUIVisibility="Hidden" />

        <StackPanel Grid.Column="1" hc:Growl.GrowlParent="True" HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,0,0,10" Panel.ZIndex="99" />
    </Grid>
</hc:Window>