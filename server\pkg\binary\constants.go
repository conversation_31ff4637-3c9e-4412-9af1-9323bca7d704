package binary

// 协议常量定义
const (
	// 协议魔数和版本
	ProtocolMagic   = 0xDA
	ProtocolVersion = 0x01

	// 消息头大小
	HeaderSize = 16 // 固定16字节

	// 标志位定义
	FlagNeedResponse = 0x01 // 需要响应
	FlagCompressed   = 0x02 // 已压缩
	FlagEncrypted    = 0x04 // 已加密

	// 限制常量
	MaxMessageSize = 65536 // 最大消息大小 64KB
)

// 消息类型定义
const (
	MsgTypeLogin             = 0x01 // 登录请求
	MsgTypeLoginResponse     = 0x81 // 登录响应
	MsgTypeHeartbeat         = 0x02 // 心跳请求
	MsgTypeHeartbeatResponse = 0x82 // 心跳响应
	MsgTypeLogout            = 0x03 // 登出请求
	MsgTypeLogoutResponse    = 0x83 // 登出响应

	MsgTypeLuckyDraw             = 0x06 // 签到抽奖请求
	MsgTypeLuckyDrawResponse     = 0x86 // 签到抽奖响应
	MsgTypeLuckyStatus           = 0x07 // 获取签到状态
	MsgTypeLuckyStatusResponse   = 0x87 // 签到状态响应
	MsgTypeCDKeyActivate         = 0x08 // 口令码激活请求
	MsgTypeCDKeyActivateResponse = 0x88 // 口令码激活响应

	MsgTypeActivityVersion         = 0x0A // 活动版本请求
	MsgTypeActivityVersionResponse = 0x8A // 活动版本响应
	MsgTypeActivityList            = 0x0B // 活动列表请求
	MsgTypeActivityListResponse    = 0x8B // 活动列表响应
	MsgTypeActivityDetail          = 0x0C // 活动详情请求
	MsgTypeActivityDetailResponse  = 0x8C // 活动详情响应

	// 扩展功能 (0x20-0x2F)
	MsgTypeUpdateConfig                = 0x20 // 获取更新配置请求
	MsgTypeUpdateConfigResponse        = 0xA0 // 更新配置响应
	MsgTypeAnnouncementVersion         = 0x21 // 获取公告版本请求
	MsgTypeAnnouncementVersionResponse = 0xA1 // 公告版本响应
	MsgTypeAnnouncementIds             = 0x22 // 获取公告ID列表请求
	MsgTypeAnnouncementIdsResponse     = 0xA2 // 公告ID列表响应
	MsgTypeAnnouncementDetail          = 0x23 // 获取公告详情请求
	MsgTypeAnnouncementDetailResponse  = 0xA3 // 公告详情响应
	MsgTypeGetTeamInfo                 = 0x24 // 获取团队信息请求
	MsgTypeGetTeamInfoResponse         = 0xA4 // 团队信息响应
)

// 消息类型到字符串的映射
var MsgTypeNames = map[uint8]string{
	MsgTypeLogin:                   "Login",
	MsgTypeLoginResponse:           "LoginResponse",
	MsgTypeHeartbeat:               "Heartbeat",
	MsgTypeHeartbeatResponse:       "HeartbeatResponse",
	MsgTypeLogout:                  "Logout",
	MsgTypeLogoutResponse:          "LogoutResponse",
	MsgTypeLuckyDraw:               "LuckyDraw",
	MsgTypeLuckyDrawResponse:       "LuckyDrawResponse",
	MsgTypeLuckyStatus:             "LuckyStatus",
	MsgTypeLuckyStatusResponse:     "LuckyStatusResponse",
	MsgTypeCDKeyActivate:           "CDKeyActivate",
	MsgTypeCDKeyActivateResponse:   "CDKeyActivateResponse",
	MsgTypeActivityVersion:         "ActivityVersion",
	MsgTypeActivityVersionResponse: "ActivityVersionResponse",
	MsgTypeActivityList:            "GetActivityList",
	MsgTypeActivityListResponse:    "GetActivityListResponse",
	MsgTypeActivityDetail:          "ActivityDetail",
	MsgTypeActivityDetailResponse:  "ActivityDetailResponse",

	MsgTypeUpdateConfig:                "UpdateConfig",
	MsgTypeUpdateConfigResponse:        "UpdateConfigResponse",
	MsgTypeAnnouncementVersion:         "AnnouncementVersion",
	MsgTypeAnnouncementVersionResponse: "AnnouncementVersionResponse",
	MsgTypeAnnouncementIds:             "AnnouncementIds",
	MsgTypeAnnouncementIdsResponse:     "AnnouncementIdsResponse",
	MsgTypeAnnouncementDetail:          "AnnouncementDetail",
	MsgTypeAnnouncementDetailResponse:  "AnnouncementDetailResponse",
	MsgTypeGetTeamInfo:                 "GetTeamInfo",
	MsgTypeGetTeamInfoResponse:         "GetTeamInfoResponse",
}

// IsResponseType 检查是否为响应类型
func IsResponseType(msgType uint8) bool {
	return (msgType & 0x80) != 0
}

// GetResponseType 获取对应的响应类型
func GetResponseType(requestType uint8) uint8 {
	return requestType | 0x80
}

// GetRequestType 获取对应的请求类型
func GetRequestType(responseType uint8) uint8 {
	return responseType & 0x7F
}
