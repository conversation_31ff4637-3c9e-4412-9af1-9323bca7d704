using BnsHelper.Common.Helpers;
using Newtonsoft.Json;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using Xylia.BnsHelper.Models;

namespace Xylia.BnsHelper.Common.Helpers;

/// <summary>
/// 服务器配置帮助类
/// </summary>
internal class ServerConfigHelper : IDisposable
{
    private ServerConfigCollection? _serverConfig;
    private readonly HttpClient _httpClient = new() { Timeout = TimeSpan.FromSeconds(10) };

    /// <summary>
    /// 远程服务器配置URL
    /// </summary>
    private const string RemoteConfigUrl = "https://bnszs-1251192097.cos.ap-shanghai.myqcloud.com/servers.json";

    private string LocalConfig { get; } = Path.Combine(SettingHelper.CacheFolder, "servers.json");

    /// <summary>
    /// 获取服务器配置
    /// </summary>
    /// <returns></returns>
    public ServerConfig GetServerConfig() => (_serverConfig ??= LoadServerConfig()).GetServer(VersionHelper.IsTest);

    /// <summary>
    /// 加载服务器配置
    /// </summary>
    /// <param name="forceRemoteLoad">是否强制从远程加载</param>
    /// <returns>服务器配置集合</returns>
    private ServerConfigCollection LoadServerConfig(bool forceRemoteLoad = false)
    {
        // 如果不强制远程加载，优先尝试从本地缓存加载
        if (!forceRemoteLoad)
        {
            var localConfig = LoadLocalServerConfig();
            if (localConfig != null)
            {
                Debug.WriteLine("[INFO] 使用本地缓存的服务器配置");
                return localConfig;
            }
        }

        // 尝试从远程加载
        var remoteConfig = LoadRemoteServerConfig();
        if (remoteConfig != null)
        {
            SaveConfigToLocal(remoteConfig);
            return remoteConfig;
        }

        // 都失败时使用默认配置
        return new ServerConfigCollection(new ServerConfig() { ip = "tools.bnszs.com", port = 8081 });
    }

    /// <summary>
    /// 从远程URL加载服务器配置
    /// </summary>
    /// <returns>服务器配置集合，失败时返回null</returns>
    private ServerConfigCollection? LoadRemoteServerConfig()
    {
        try
        {
            Debug.WriteLine("[INFO] 正在从远程加载服务器配置...");

            var response = _httpClient.GetStringAsync(RemoteConfigUrl).ConfigureAwait(false).GetAwaiter().GetResult();
            var config = JsonConvert.DeserializeObject<ServerConfigCollection>(response);
            if (config != null && config.Servers.Count != 0)
            {
                Debug.WriteLine($"[INFO] 成功加载远程服务器配置，共 {config.Servers.Count} 个服务器");
                return config;
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 从远程加载服务器配置失败: {ex.Message}");
        }

        return null;
    }

    /// <summary>
    /// 从本地文件加载服务器配置
    /// </summary>
    /// <returns>服务器配置集合，失败时返回null</returns>
    private ServerConfigCollection? LoadLocalServerConfig()
    {
        try
        {
            var configPath = LocalConfig;
            if (!File.Exists(configPath)) return null;

            // 检查文件修改时间，如果超过30天则认为缓存过期
            var fileInfo = new FileInfo(configPath);
            var cacheAge = DateTime.Now - fileInfo.LastWriteTime;
            if (cacheAge.TotalDays > 30)
            {
                Debug.WriteLine($"[INFO] 本地缓存已过期 ({cacheAge.TotalDays:F1} 天)，将从远程重新加载");
                return null;
            }

            // 智能读取文件（自动处理加密/解密）
            var config = JsonConvert.DeserializeObject<ServerConfigCollection>(EncryptionHelper.ReadFile(configPath));
            if (config != null && config.Servers.Count != 0)
            {
                Debug.WriteLine($"[INFO] 使用本地缓存配置 (缓存时间: {cacheAge.TotalDays:F1} 天)");
                return config;
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 从本地加载服务器配置失败: {ex.Message}");
        }

        return null;
    }

    /// <summary>
    /// 保存配置到本地文件作为缓存
    /// </summary>
    /// <param name="config">服务器配置</param>
    private async void SaveConfigToLocal(ServerConfigCollection config)
    {
        try
        {
            await EncryptionHelper.EncryptToFileAsync(LocalConfig, JsonConvert.SerializeObject(config));
            Debug.WriteLine("[INFO] 服务器配置已加密缓存到本地");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 保存服务器配置到本地失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 当会话连接失败时调用，尝试重新加载配置
    /// </summary>
    public void OnConnectionFailed()
    {
        Debug.WriteLine("重新加载配置信息");
        _serverConfig = LoadServerConfig(true);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}
