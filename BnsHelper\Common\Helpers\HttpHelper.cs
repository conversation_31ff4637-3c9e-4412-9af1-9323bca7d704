﻿using RestSharp;
using System.IO;
using System.Net.Http;

namespace Xylia.BnsHelper.Common.Helpers;
internal static class HttpHelper
{
    public static async Task<Stream> DownloadAsync(string? url)
    {
        ArgumentNullException.ThrowIfNull(url);

        var client = new HttpClient();
        client.DefaultRequestHeaders.Add("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.63 Safari/537.36");

        var response = await client.GetAsync(url);
        response.EnsureSuccessStatusCode();

        return await response.Content.ReadAsStreamAsync();
    }

    /// <summary>
    /// 解析Set-Cookie头
    /// </summary>
    /// <param name="headers">HTTP响应头集合</param>
    /// <returns>解析后的Cookie字典</returns>
    public static Dictionary<string, string> ParseCookies(IEnumerable<KeyValuePair<string, string>> headers)
    {
        var cookies = new Dictionary<string, string>();

        foreach (var header in headers.Where(x => x.Key.Equals("Set-Cookie", StringComparison.OrdinalIgnoreCase)))
        {
            var cookieString = header.Value;

            // 解析单个Set-Cookie头，格式如：name=value; Path=/; Domain=.qq.com; Expires=...
            // 需要正确处理包含逗号的日期格式，不能简单用逗号分割
            var parsedCookie = ParseSingleSetCookie(cookieString);
            if (parsedCookie.HasValue)
            {
                var (key, value) = parsedCookie.Value;
                // 只有当键不存在或者新值不为空时才更新（避免空值覆盖有效值）
                if (!string.IsNullOrEmpty(key) && (!cookies.ContainsKey(key) || !string.IsNullOrEmpty(value)))
                {
                    cookies[key] = value;
                }
            }
        }

        return cookies;
    }

    /// <summary>
    /// 解析单个Set-Cookie头
    /// </summary>
    /// <param name="setCookieValue">Set-Cookie头的值</param>
    /// <returns>解析出的cookie键值对，如果解析失败返回null</returns>
    private static (string key, string value)? ParseSingleSetCookie(string setCookieValue)
    {
        if (string.IsNullOrWhiteSpace(setCookieValue))
            return null;

        // 找到第一个分号的位置，分号前是name=value，分号后是属性
        var semicolonIndex = setCookieValue.IndexOf(';');
        var nameValuePart = semicolonIndex >= 0
            ? setCookieValue[..semicolonIndex].Trim()
            : setCookieValue.Trim();

        // 解析name=value部分
        var equalIndex = nameValuePart.IndexOf('=');
        if (equalIndex <= 0) // 等号不存在或在开头
            return null;

        var key = nameValuePart.Substring(0, equalIndex).Trim();
        var value = nameValuePart.Substring(equalIndex + 1).Trim();

        return string.IsNullOrEmpty(key) ? null : (key, value);
    }

    /// <summary>
    /// 获取RestSharp响应中的Location头
    /// </summary>
    /// <param name="response">RestSharp响应对象</param>
    /// <returns>Location头的值，如果不存在则返回null</returns>
    public static string? GetHeader(this RestResponse response, string name)
    {
        return response.Headers?.FirstOrDefault(h => h.Name?.Equals(name, StringComparison.OrdinalIgnoreCase) == true)?.Value;
    }
}