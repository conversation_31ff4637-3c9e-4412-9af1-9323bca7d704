package handler

import (
	"fmt"
	"net"

	"udp-server/server/internal/service"
	"udp-server/server/pkg/binary"
	"udp-server/server/pkg/logger"
)

// 公告处理器
type AnnouncementHandler struct {
	announcementService *service.AnnouncementService
	authService         *service.AuthService
}

// 创建公告处理器实例
func NewAnnouncementHandler(announcementService *service.AnnouncementService, authService *service.AuthService) *AnnouncementHandler {
	return &AnnouncementHandler{
		announcementService: announcementService,
		authService:         authService,
	}
}

// 处理公告版本请求
func (h *AnnouncementHandler) HandleAnnouncementVersionRequest(msg *binary.Message, remoteAddr *net.UDPAddr) (*binary.BinaryWriter, error) {
	logger.Debug("处理公告版本请求，来自: %s", remoteAddr.IP.String())

	// 获取公告版本
	currentVersion := h.announcementService.GetCurrentVersion()

	// 创建响应
	writer := binary.NewBinaryWriter()
	writer.WriteUint32(binary.ErrorCodeSuccess)
	writer.WriteUint16(currentVersion)
	return writer, nil
}

// 处理公告ID列表请求
func (h *AnnouncementHandler) HandleAnnouncementIdsRequest(msg *binary.Message, remoteAddr *net.UDPAddr) (*binary.BinaryWriter, error) {
	logger.Debug("处理公告ID列表请求，来自: %s", remoteAddr.IP.String())

	// 获取活跃公告ID列表
	announcements, err := h.announcementService.GetAnnouncements(1)
	if err != nil {
		logger.Error("获取公告ID列表失败: %v", err)
		return nil, fmt.Errorf("获取公告ID列表失败")
	}

	// 创建响应
	writer := binary.NewBinaryWriter()
	writer.WriteUint32(binary.ErrorCodeSuccess)
	writer.WriteUint16(uint16(len(announcements))) // 列表数量
	for _, announcement := range announcements {
		writer.WriteUint32(announcement.ID)      // 公告ID
		writer.WriteUint16(announcement.Version) // 版本号
	}

	return writer, nil
}

// 获取公告详情请求
func (h *AnnouncementHandler) HandleAnnouncementDetailRequest(msg *binary.Message, remoteAddr *net.UDPAddr) (*binary.BinaryWriter, error) {
	logger.Debug("处理公告详情请求，来自: %s", remoteAddr.IP.String())

	// 获取请求的公告编号
	reader := binary.NewBinaryReader(msg.Body)
	id, err := reader.ReadUint32()
	if err != nil {
		return nil, fmt.Errorf("获取公告详情失败: %w", err)
	}

	// 获取公告详情
	announcement, err := h.announcementService.GetAnnouncement(id)
	if err != nil || announcement == nil {
		logger.Error("获取公告详情失败: ID=%d, Error=%v", id, err)
		return nil, fmt.Errorf("获取公告详情失败")
	}

	// 创建响应
	writer := binary.NewBinaryWriter()
	writer.WriteUint32(binary.ErrorCodeSuccess)

	// 公告实例
	writer.WriteUint32(announcement.ID)
	writer.WriteString(announcement.Title)
	writer.WriteString(announcement.Content)
	writer.WriteUint8(announcement.Type)
	writer.WriteUint8(announcement.Priority)
	writer.WriteInt64(announcement.UpdatedAt.Unix())
	writer.WriteUint16(announcement.Version)

	if announcement.StartTime == nil {
		writer.WriteInt64(0)
	} else {
		writer.WriteInt64(announcement.StartTime.Unix())
	}

	if announcement.EndTime == nil {
		writer.WriteInt64(0)
	} else {
		writer.WriteInt64(announcement.EndTime.Unix())
	}

	return writer, nil
}
