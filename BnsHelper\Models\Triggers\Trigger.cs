using CommunityToolkit.Mvvm.ComponentModel;
using Newtonsoft.Json;
using System.Collections.ObjectModel;

namespace Xylia.BnsHelper.Models.Triggers;

/// <summary>
/// 触发器
/// </summary>
public partial class Trigger : ObservableObject, ITriggerNode
{
    #region Fields
    [ObservableProperty] string _name = "";
    [ObservableProperty] bool _isEnabled = true;
    [ObservableProperty] int _priority = 0;
    [ObservableProperty] int _cooldown = 0;
    [ObservableProperty] ObservableCollection<TriggerCondition> _conditions = [];
    [ObservableProperty] ObservableCollection<TriggerAction> _actions = [];

    // 运行时属性
    [ObservableProperty][property: JsonIgnore] int _executionCount;
    [ObservableProperty][property: JsonIgnore] DateTime? _lastExecutionTime;
    [ObservableProperty][property: JsonIgnore] string? _lastError;
    [ObservableProperty][property: JsonIgnore] bool _hasError;
    [JsonIgnore] Dictionary<string, object> LastMatchVariables = [];    // 最后匹配的变量
    #endregion

    #region Properties
    [JsonIgnore] public TriggerNodeType NodeType => TriggerNodeType.Trigger;

    /// <summary>
    /// 当前状态
    /// </summary>
    [JsonIgnore]
    public string Status
    {
        get
        {
            if (!IsEnabled) return "已禁用";
            if (!CanExecute()) return "冷却中";
            return "等待中";
        }
    }

    /// <summary>
    /// 状态颜色
    /// </summary>
    [JsonIgnore]
    public string StatusColor
    {
        get
        {
            if (!IsEnabled) return "Gray";
            if (!CanExecute()) return "Orange";
            return "Green";
        }
    }
    #endregion

    #region Methods
    /// <summary>
    /// 检查是否可以执行
    /// </summary>
    public bool CanExecute()
    {
        if (!IsEnabled) return false;

        if (Cooldown > 0 && LastExecutionTime.HasValue)
        {
            var elapsed = DateTime.Now - LastExecutionTime.Value;
            return elapsed.TotalMilliseconds >= Cooldown;
        }

        return true;
    }

    /// <summary>
    /// 检查是否匹配
    /// </summary>
    public bool IsMatch(string message)
    {
        if (!IsEnabled || string.IsNullOrEmpty(message)) return false;

        // 清空上次的变量
        LastMatchVariables.Clear();

        // 如果有条件，使用条件系统
        if (Conditions.Count > 0)
        {
            var triggerEvent = new GameLogEvent(message);
            var variables = new Dictionary<string, object>();

            // 所有启用的条件都必须满足
            var result = Conditions.All(condition => condition.Evaluate(triggerEvent, variables));
            if (result) LastMatchVariables = new Dictionary<string, object>(variables);
            return result;
        }

        // 如果没有正则表达式，检查是否包含触发器名称
        return !string.IsNullOrEmpty(Name) && message.Contains(Name, StringComparison.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 执行触发器
    /// </summary>
    public async Task ExecuteAsync(string message)
    {
        if (!CanExecute()) return;

        try
        {
            // 初始化统计信息
            HasError = false;
            ExecutionCount++;
            LastExecutionTime = DateTime.Now;
            LastError = null;

            // 创建执行上下文，使用IsMatch时提取的变量
            var context = new TriggerExecutionContext
            {
                Message = message,
                Trigger = this,
                Variables = new Dictionary<string, object>(LastMatchVariables)
            };

            foreach (var action in Actions.Where(a => a.IsEnabled))
            {
                await action.ExecuteAsync(context);
            }
        }
        catch (Exception ex)
        {
            LastError = ex.Message;
            HasError = true;
        }
    }

    /// <summary>
    /// 重置触发器执行状态
    /// </summary>
    public void Reset()
    {
        LastExecutionTime = null;
        ExecutionCount = 0;
        LastError = null;
    }

    /// <summary>
    /// 克隆触发器
    /// </summary>
    public ITriggerNode Clone()
    {
        var clone = new Trigger
        {
            Name = $"{Name} (副本)",
            IsEnabled = IsEnabled,
            Priority = Priority,
            Cooldown = Cooldown,
            Conditions = new(Conditions.Select(a => a.Clone())),
            Actions = new(Actions.Select(a => a.Clone()))
        };

        return clone;
    }
    #endregion
}
