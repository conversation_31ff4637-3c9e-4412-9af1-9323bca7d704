namespace Xylia.BnsHelper.Services.Network.BinaryProtocol;

/// <summary>
/// Binary protocol constants
/// </summary>
internal static class Constants
{
    // Protocol magic and version
    public const byte ProtocolMagic = 0xDA;
    public const byte ProtocolVersion = 0x01;

    // Message header size
    public const int HeaderSize = 16; // Fixed 16 bytes

    // Flag definitions
    public const byte FlagNeedResponse = 0x01; // Need response
    public const byte FlagCompressed = 0x02;   // Compressed
    public const byte FlagEncrypted = 0x04;    // Encrypted

    // Limits
    public const int MaxMessageSize = 65536;   // Max message size 64KB
}

/// <summary>
/// Message type definitions
/// </summary>
internal static class MessageTypes
{
    public const byte Login = 0x01;                        // Login request
    public const byte LoginResponse = 0x81;                // Login response
    public const byte Heartbeat = 0x02;                    // Heartbeat request
    public const byte HeartbeatResponse = 0x82;            // Heartbeat response
    public const byte Logout = 0x03;                       // Logout request
    public const byte LogoutResponse = 0x83;               // Logout response
    public const byte GetDeviceHistory = 0x04;             // Get device history
    public const byte GetDeviceHistoryResponse = 0x84;     // Device history response
    public const byte GetActiveDevices = 0x05;             // Get active devices
    public const byte GetActiveDevicesResponse = 0x85;     // Active devices response
    public const byte LuckyDraw = 0x06;                    // Lucky draw (sign-in) request
    public const byte LuckyDrawResponse = 0x86;            // Lucky draw response
    public const byte LuckyStatus = 0x07;                  // Get sign-in status request
    public const byte LuckyStatusResponse = 0x87;          // Sign-in status response
    public const byte CDKeyActivate = 0x08;                // CDKEY activate request
    public const byte CDKeyActivateResponse = 0x88;        // CDKEY activate response

    public const byte ActivityVersion = 0x0A;              // Get activity version request
    public const byte ActivityVersionResponse = 0x8A;      // Activity version response
    public const byte ActivityList = 0x0B;              // Get activity list request
    public const byte ActivityListResponse = 0x8B;      // Get activity list response
    public const byte ActivityDetail = 0x0C;               // Get activity detail request
    public const byte ActivityDetailResponse = 0x8C;       // Activity detail response

    public const byte UpdateConfig = 0x20;                 // Get update config request
    public const byte UpdateConfigResponse = 0xA0;         // Update config response
    public const byte AnnouncementVersion = 0x21;          // Get announcement version request
    public const byte AnnouncementVersionResponse = 0xA1;  // Announcement version response
    public const byte AnnouncementIds = 0x22;              // Get announcement IDs request
    public const byte AnnouncementIdsResponse = 0xA2;      // Announcement IDs response
    public const byte AnnouncementDetail = 0x23;           // Get announcement detail request
    public const byte AnnouncementDetailResponse = 0xA3;   // Announcement detail response
    public const byte GetTeamInfo = 0x24;                  // Get team info request
    public const byte GetTeamInfoResponse = 0xA4;          // Get team info response
}
