package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"
	"udp-server/server/pkg/logger"

	"github.com/redis/go-redis/v9"
)

// Redis缓存实现
type redisCache struct {
	client         *redis.Client
	ctx            context.Context
	healthTicker   *time.Ticker
	healthStopChan chan struct{}
	healthMutex    sync.Mutex
	healthRunning  bool
}

// NewRedisCache 创建Redis缓存
func NewRedisCache(host string, port int, password string, db int) (Cache, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", host, port),
		Password: password,
		DB:       db,
	})

	// 测试连接
	ctx := context.Background()
	_, err := client.Ping(ctx).Result()
	if err != nil {
		return nil, err
	}

	return &redisCache{
		client:         client,
		ctx:            ctx,
		healthStopChan: make(chan struct{}),
	}, nil
}

// Get 从Redis获取缓存值
func (r *redisCache) Get(key string, value interface{}) error {
	data, err := r.client.Get(r.ctx, key).Bytes()
	if err != nil {
		if err == redis.Nil {
			return ErrCacheMiss
		}
		return err
	}

	return json.Unmarshal(data, value)
}

// Set 设置Redis缓存值
func (r *redisCache) Set(key string, value interface{}, expiration time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}

	return r.client.Set(r.ctx, key, data, expiration).Err()
}

// Delete 删除Redis缓存值
func (r *redisCache) Delete(key string) error {
	return r.client.Del(r.ctx, key).Err()
}

// Close 关闭Redis连接
func (r *redisCache) Close() error {
	return r.client.Close()
}

// Increment 增加键的值
func (r *redisCache) Increment(key string, value int64) (int64, error) {
	return r.client.IncrBy(r.ctx, key, value).Result()
}

// Expire 设置键的过期时间
func (r *redisCache) Expire(key string, expiration time.Duration) error {
	return r.client.Expire(r.ctx, key, expiration).Err()
}

// HealthCheck Redis健康检查
func (r *redisCache) HealthCheck() error {
	const healthCheckKey = "health_check_test"
	const healthCheckValue = "test_value"

	// 测试写入
	if err := r.client.Set(r.ctx, healthCheckKey, healthCheckValue, 10*time.Second).Err(); err != nil {
		return fmt.Errorf("Redis健康检查失败 - 写入测试: %v", err)
	}

	// 测试读取
	readValue, err := r.client.Get(r.ctx, healthCheckKey).Result()
	if err != nil {
		return fmt.Errorf("Redis健康检查失败 - 读取测试: %v", err)
	}

	// 验证数据一致性
	if readValue != healthCheckValue {
		return fmt.Errorf("Redis健康检查失败 - 数据不一致: 期望 '%s', 实际 '%s'", healthCheckValue, readValue)
	}

	// 清理测试数据
	if err := r.client.Del(r.ctx, healthCheckKey).Err(); err != nil {
		logger.Warn("Redis健康检查 - 清理测试数据失败: %v", err)
	}

	return nil
}

// StartHealthMonitor 启动Redis健康监控
func (r *redisCache) StartHealthMonitor(interval time.Duration) error {
	r.healthMutex.Lock()
	defer r.healthMutex.Unlock()

	if r.healthRunning {
		return fmt.Errorf("健康监控已在运行")
	}

	r.healthTicker = time.NewTicker(interval)
	r.healthRunning = true

	go func() {
		logger.Info("Redis健康检查已启动，检查间隔: %v", interval)

		for {
			select {
			case <-r.healthTicker.C:
				if err := r.HealthCheck(); err != nil {
					logger.Error("Redis健康检查失败: %v", err)
				}
			case <-r.healthStopChan:
				return
			}
		}
	}()

	return nil
}

// StopHealthMonitor 停止Redis健康监控
func (r *redisCache) StopHealthMonitor() error {
	r.healthMutex.Lock()
	defer r.healthMutex.Unlock()

	if !r.healthRunning {
		return nil
	}

	if r.healthTicker != nil {
		r.healthTicker.Stop()
		r.healthTicker = nil
	}

	close(r.healthStopChan)
	r.healthStopChan = make(chan struct{}) // 重新创建channel以便下次使用
	r.healthRunning = false

	logger.Info("Redis健康监控已停止")
	return nil
}
