package service

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
	"gorm.io/gorm"

	"udp-server/server/internal/database"
	"udp-server/server/internal/model"

	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"
)

// StatsService 统计服务
type StatsService struct {
	startTime    time.Time
	db           *gorm.DB
	cache        cache.Cache
	authService  *AuthService
	heartbeatSvc *HeartbeatService
	riskService  *RiskControlService
}

// NewStatsService 创建统计服务
func NewStatsService(db *gorm.DB, cache cache.Cache, authService *AuthService, heartbeatSvc *HeartbeatService, riskService *RiskControlService) *StatsService {
	return &StatsService{
		startTime:    time.Now(),
		db:           db,
		cache:        cache,
		authService:  authService,
		heartbeatSvc: heartbeatSvc,
		riskService:  riskService,
	}
}

// SaveCurrentStats 保存当前统计数据到历史记录
func (s *StatsService) SaveCurrentStats() error {
	now := time.Now()

	// 更新风控配置（每小时执行一次）
	if s.riskService != nil {
		if err := s.riskService.UpdateConfig(); err != nil {
			logger.Error("更新风控配置失败: %v", err)
		}
	}

	// 检查是否为测试服务器，不需要将统计信息保存到数据库
	isTestServer := viper.GetBool("test_server.enabled")
	if isTestServer {
		return nil
	}

	// 直接获取统计信息，避免不必要的类型转换
	onlineUsers := s.authService.GetOnlineUsers()
	authActiveUsers := len(s.authService.GetTokens())
	authTotalTokens := len(s.authService.GetTokens())
	heartbeatActive := s.heartbeatSvc.GetOnlineUserCount()
	heartbeatTotal := s.heartbeatSvc.GetTotalDeviceCount()
	updateRequestCount := s.getUpdateRequestCount()

	// 创建历史记录
	statsHistory := &model.OnlineStatsHistory{
		OnlineCount:        len(onlineUsers),
		AuthActiveUsers:    authActiveUsers,
		AuthTotalTokens:    authTotalTokens,
		HeartbeatActive:    heartbeatActive,
		HeartbeatTotal:     heartbeatTotal,
		UpdateRequestCount: updateRequestCount,
		Timestamp:          now.Unix(),
	}

	// 保存到数据库
	if err := s.db.Create(statsHistory).Error; err != nil {
		return fmt.Errorf("保存统计历史记录失败: %v", err)
	}

	logger.Info("已保存统计历史记录: 在线用户=%d, 认证用户=%d, 心跳设备=%d, 更新请求=%d",
		statsHistory.OnlineCount, statsHistory.AuthActiveUsers, statsHistory.HeartbeatActive, statsHistory.UpdateRequestCount)
	return nil
}

// GetHistoryStats 获取历史统计数据
func (s *StatsService) GetHistoryStats(period string, limit int) ([]model.OnlineStatsHistory, error) {
	var stats []model.OnlineStatsHistory
	var interval time.Duration

	// 根据周期确定查询间隔
	switch period {
	case "hour":
		interval = time.Hour
	case "day":
		interval = 24 * time.Hour
	default:
		interval = time.Hour
	}

	// 计算开始时间
	startTime := time.Now().Add(-time.Duration(limit) * interval).Unix()

	// 查询历史数据
	err := s.db.Where("timestamp >= ?", startTime).
		Order("timestamp ASC").
		Limit(limit).
		Find(&stats).Error

	if err != nil {
		return nil, fmt.Errorf("查询历史统计数据失败: %v", err)
	}

	return stats, nil
}

// GetHistoryStatsGrouped 获取按时间分组的历史统计数据
func (s *StatsService) GetHistoryStatsGrouped(period string, limit int) ([]model.OnlineStatsHistory, error) {
	var stats []model.OnlineStatsHistory
	var groupBy string
	var interval time.Duration

	// 根据周期确定分组方式和间隔
	switch period {
	case "hour":
		// 按小时分组，取每小时的平均值
		groupBy = "FROM_UNIXTIME(timestamp, '%Y-%m-%d %H:00:00')"
		interval = time.Hour
	case "day":
		// 按天分组，取每天的平均值
		groupBy = "FROM_UNIXTIME(timestamp, '%Y-%m-%d')"
		interval = 24 * time.Hour
	default:
		groupBy = "FROM_UNIXTIME(timestamp, '%Y-%m-%d %H:00:00')"
		interval = time.Hour
	}

	// 计算开始时间
	startTime := time.Now().Add(-time.Duration(limit) * interval).Unix()

	// 查询分组统计数据
	err := s.db.Select(fmt.Sprintf(`
		ROUND(AVG(online_count)) as online_count,
		ROUND(AVG(auth_active_users)) as auth_active_users,
		ROUND(AVG(auth_total_tokens)) as auth_total_tokens,
		ROUND(AVG(heartbeat_active)) as heartbeat_active,
		ROUND(AVG(heartbeat_total)) as heartbeat_total,
		ROUND(AVG(update_request_count)) as update_request_count,
		UNIX_TIMESTAMP(%s) as timestamp
	`, groupBy)).
		Where("timestamp >= ?", startTime).
		Group(groupBy).
		Order("timestamp ASC").
		Limit(limit).
		Find(&stats).Error

	if err != nil {
		return nil, fmt.Errorf("查询分组历史统计数据失败: %v", err)
	}

	return stats, nil
}

// GetReviewUsersCount 获取审核状态用户数量
func (s *StatsService) GetReviewUsersCount() (int64, error) {
	var count int64
	err := s.db.Table("user").Where("status = ?", 3).Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("查询审核用户数量失败: %v", err)
	}
	return count, nil
}

// CleanupOldStats 清理过期的统计数据
func (s *StatsService) CleanupOldStats() error {
	// 检查是否为测试服务器
	isTestServer := viper.GetBool("test_server.enabled")
	if isTestServer {
		return nil
	}

	// 保留最近30天的数据
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30).Unix()

	result := s.db.Where("timestamp < ?", thirtyDaysAgo).Delete(&model.OnlineStatsHistory{})
	if result.Error != nil {
		return fmt.Errorf("清理过期统计数据失败: %v", result.Error)
	}

	if result.RowsAffected > 0 {
		logger.Info("已清理过期统计数据: 删除记录数=%d", result.RowsAffected)
	}

	return nil
}

// StartStatsCollector 启动统计数据收集器
func (s *StatsService) StartStatsCollector() {
	// 检查是否为测试服务器模式
	isTestServer := viper.GetBool("test_server.enabled")

	// 每小时保存一次统计数据
	ticker := time.NewTicker(1 * time.Hour)

	go func() {
		defer ticker.Stop()

		for range ticker.C {
			if err := s.SaveCurrentStats(); err != nil {
				logger.Error("保存统计数据失败: %v", err)
			}
		}
	}()

	if isTestServer {
		logger.Info("统计数据收集器已启动（测试服务器模式：不写入 online_stats_history 表）")
	} else {
		logger.Info("统计数据收集器已启动，每小时保存一次数据到 online_stats_history 表")
	}
}

// StartStatsCleanup 启动统计数据清理任务
func (s *StatsService) StartStatsCleanup() {
	// 检查是否为测试服务器模式
	isTestServer := viper.GetBool("test_server.enabled")

	// 每天凌晨3点清理过期数据
	now := time.Now()
	nextCleanup := time.Date(now.Year(), now.Month(), now.Day()+1, 3, 0, 0, 0, now.Location())
	if now.Hour() < 3 {
		nextCleanup = time.Date(now.Year(), now.Month(), now.Day(), 3, 0, 0, 0, now.Location())
	}

	ticker := time.NewTicker(24 * time.Hour)

	go func() {
		defer ticker.Stop()

		// 等待到第一次执行时间
		time.Sleep(time.Until(nextCleanup))

		if isTestServer {
			logger.Info("启动统计数据清理任务（测试服务器模式：不清理 online_stats_history 表）")
		} else {
			logger.Info("启动统计数据清理任务，每天凌晨3点执行")
		}

		for range ticker.C {
			if isTestServer {
				logger.Info("执行统计数据清理任务（测试服务器模式：跳过清理）")
			} else {
				logger.Info("开始执行统计数据清理任务")
				if err := s.CleanupOldStats(); err != nil {
					logger.Error("统计数据清理任务失败: %v", err)
				} else {
					logger.Info("统计数据清理任务完成")
				}
			}
		}
	}()
}

// getUpdateRequestCount 获取今日更新请求统计数量
func (s *StatsService) getUpdateRequestCount() int {
	if s.cache == nil {
		return 0
	}

	today := time.Now().Format("2006-01-02")
	cacheKey := fmt.Sprintf("update_request_count:%s", today)

	// 从Redis获取今日更新请求数量
	var updateRequestCount int64
	if err := s.cache.Get(cacheKey, &updateRequestCount); err == nil {
		logger.Debug("今日更新请求统计: %d 次", updateRequestCount)
		return int(updateRequestCount)
	} else {
		logger.Debug("未找到今日更新请求统计数据")
		return 0
	}
}

// GetStatsForAdmin 获取管理后台统计信息
func (s *StatsService) GetStatsForAdmin(dateStr string) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 解析日期
	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		return nil, fmt.Errorf("日期格式错误: %v", err)
	}

	// 获取在线用户统计
	onlineUsers := s.authService.GetOnlineUsers()
	stats["online_users"] = len(onlineUsers)

	// 获取总用户数（从数据库直接查询）
	var totalUsers int64
	database.GetDB().Model(&model.User{}).Count(&totalUsers)
	stats["total_users"] = totalUsers

	// 获取今日活跃用户数（从缓存或数据库获取）
	dailyActiveKey := fmt.Sprintf("daily_active_users:%s", dateStr)
	var dailyActive int64
	if err := s.cache.Get(dailyActiveKey, &dailyActive); err == nil {
		stats["daily_active"] = dailyActive
	} else {
		// 从数据库查询今日登录的用户数
		var count int64
		startOfDay := date
		endOfDay := date.Add(24 * time.Hour)
		s.db.Model(&model.User{}).
			Where("login_time >= ? AND login_time < ?", startOfDay.Unix(), endOfDay.Unix()).
			Count(&count)
		stats["daily_active"] = count
	}

	// 获取公告数量
	var announcementCount int64
	s.db.Model(&model.Announcement{}).Where("status = ?", 1).Count(&announcementCount)
	stats["announcements_count"] = announcementCount

	// 获取当日登录统计
	loginCacheKey := fmt.Sprintf("daily_login_stats:%s", dateStr)
	var loginStats map[string]interface{}
	if err := s.cache.Get(loginCacheKey, &loginStats); err == nil {
		stats["login_stats"] = loginStats
	} else {
		stats["login_stats"] = map[string]interface{}{
			"total_logins": 0,
			"unique_users": 0,
		}
	}

	// 获取当日更新请求统计
	updateCacheKey := fmt.Sprintf("daily_update_requests:%s", dateStr)
	var updateRequestCount int64
	if err := s.cache.Get(updateCacheKey, &updateRequestCount); err == nil {
		stats["update_requests"] = updateRequestCount
	} else {
		stats["update_requests"] = 0
	}

	// 获取风控事件统计
	stats["risk_events"] = 0

	// 获取心跳统计
	if s.heartbeatSvc != nil {
		heartbeatStats := map[string]interface{}{
			"total_heartbeats":   0,
			"active_connections": s.heartbeatSvc.GetOnlineUserCount(),
		}
		stats["heartbeat_stats"] = heartbeatStats
	}

	// 系统状态信息
	stats["server_start_time"] = time.Now().Add(-24 * time.Hour).Format("2006-01-02 15:04:05") // 临时数据
	stats["uptime"] = "24小时"                                                                   // 临时数据
	stats["memory_usage"] = "256MB / 1GB"                                                      // 临时数据
	stats["db_status"] = "connected"                                                           // 临时数据

	stats["date"] = dateStr
	stats["generated_at"] = time.Now().Format("2006-01-02 15:04:05")

	return stats, nil
}

// GetStartTime 获取服务器启动时间
func (s *StatsService) GetStartTime() time.Time {
	return s.startTime
}

// GetUptime 获取服务器运行时间
func (s *StatsService) GetUptime() time.Duration {
	return time.Since(s.startTime)
}

// GetStats 获取统计信息
func (s *StatsService) GetStats() (map[string]interface{}, error) {
	// TODO: 实现获取统计信息
	stats := map[string]interface{}{
		"total_users":  0,
		"online_users": 0,
		"total_events": 0,
		"today_events": 0,
	}
	return stats, nil
}

// GetRealtimeData 获取实时数据
func (s *StatsService) GetRealtimeData() (map[string]interface{}, error) {
	// TODO: 实现获取实时数据
	data := map[string]interface{}{
		"cpu_usage":          0.0,
		"memory_usage":       0.0,
		"online_users":       0,
		"active_connections": 0,
	}
	return data, nil
}

// GetServerStatus 获取服务器状态
func (s *StatsService) GetServerStatus() (map[string]interface{}, error) {
	// TODO: 实现获取服务器状态
	status := map[string]interface{}{
		"status":       "running",
		"uptime":       "0h 0m",
		"version":      "1.0.0",
		"last_restart": time.Now().Format("2006-01-02 15:04:05"),
	}
	return status, nil
}

// GetOnlineUsers 获取在线用户
func (s *StatsService) GetOnlineUsers() ([]map[string]interface{}, error) {
	// TODO: 实现获取在线用户
	users := []map[string]interface{}{}
	return users, nil
}

// GetSystemResources 获取系统资源
func (s *StatsService) GetSystemResources() (map[string]interface{}, error) {
	// TODO: 实现获取系统资源
	resources := map[string]interface{}{
		"cpu_usage":    0.0,
		"memory_usage": 0.0,
		"disk_usage":   0.0,
		"network_io": map[string]interface{}{
			"in":  0,
			"out": 0,
		},
	}
	return resources, nil
}

// GetErrorStats 获取错误统计
func (s *StatsService) GetErrorStats(period string) (map[string]interface{}, error) {
	// TODO: 实现获取错误统计
	logger.Info("获取错误统计: period=%s", period)
	stats := map[string]interface{}{
		"total_errors": 0,
		"error_rate":   0.0,
		"top_errors":   []map[string]interface{}{},
	}
	return stats, nil
}

// GetAPIStats 获取API调用统计
func (s *StatsService) GetAPIStats(period string) (map[string]interface{}, error) {
	// TODO: 实现获取API调用统计
	logger.Info("获取API调用统计: period=%s", period)
	stats := map[string]interface{}{
		"total_calls":   0,
		"success_rate":  0.0,
		"average_time":  0.0,
		"top_endpoints": []map[string]interface{}{},
	}
	return stats, nil
}
