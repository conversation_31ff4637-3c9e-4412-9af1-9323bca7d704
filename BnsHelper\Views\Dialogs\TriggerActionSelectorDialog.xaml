<hc:Window x:Class="Xylia.BnsHelper.Views.Dialogs.TriggerActionSelectorDialog"
           xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
           xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
           xmlns:hc="https://handyorg.github.io/handycontrol"
           Title="选择动作类型" Width="500" Height="400"
           WindowStartupLocation="CenterOwner"
           ResizeMode="CanResize">

    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="请选择要添加的动作类型" FontSize="14" FontWeight="Bold" Margin="0,0,0,15"/>

        <!-- 动作类型列表 -->
        <Border Grid.Row="1" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1">
            <ListBox ItemsSource="{Binding ActionTypes}" SelectedItem="{Binding SelectedActionType}" ScrollViewer.HorizontalScrollBarVisibility="Disabled">
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <Border Padding="10" Background="Transparent">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- 图标 -->
                                <TextBlock Grid.Column="0" Text="{Binding Icon}" FontSize="24" VerticalAlignment="Center" Margin="0,0,15,0" Foreground="{DynamicResource PrimaryTextBrush}" />

                                <!-- 信息 -->
                                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                    <TextBlock Text="{Binding Name}" Foreground="{DynamicResource PrimaryTextBrush}" FontWeight="Bold" FontSize="14" />
                                    <TextBlock Text="{Binding Description}" Foreground="{DynamicResource SecondaryTextBrush}" FontSize="12" TextWrapping="Wrap" Margin="0,2,0,0"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ListBox.ItemTemplate>
                <ListBox.ItemContainerStyle>
                    <Style TargetType="ListBoxItem">
                        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                        <Setter Property="Margin" Value="0"/>
                        <Setter Property="Padding" Value="0"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{DynamicResource SecondaryRegionBrush}"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="{DynamicResource PrimaryBrush}"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </ListBox.ItemContainerStyle>
            </ListBox>
        </Border>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0 7 0 -2">
            <Button Content="取消" Command="{Binding CancelCommand}" Width="80" Margin="0,0,10,0" />
            <Button Content="确定" Command="{Binding OkCommand}" Width="80" Style="{StaticResource ButtonPrimary}" />
        </StackPanel>
    </Grid>
</hc:Window>
