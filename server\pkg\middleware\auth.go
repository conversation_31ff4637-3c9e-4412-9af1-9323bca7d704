package middleware

import (
	"udp-server/server/internal/model"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"
)

// AuthMiddleware 认证中间件
type AuthMiddleware struct {
	authService *service.AuthService
}

// NewAuthMiddleware 创建认证中间件
func NewAuthMiddleware(authService *service.AuthService) *AuthMiddleware {
	return &AuthMiddleware{
		authService: authService,
	}
}

// AuthenticatedHandler 需要认证的处理器函数类型
type AuthenticatedHandler func(user *model.User, args ...interface{}) ([]byte, error)