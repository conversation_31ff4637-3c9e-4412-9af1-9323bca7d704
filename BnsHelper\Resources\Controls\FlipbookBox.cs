﻿using HandyControl.Controls;
using System.Diagnostics;
using System.Windows;
using System.Windows.Threading;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.Preview.Data.Engine.DatData;

namespace Xylia.BnsHelper.Resources;
internal class FlipbookBox : GifImage
{
    const string FlipbookL = $"pack://application:,,,/Resources/Images/Flipbook.gif";
    const string FlipbookN = $"pack://application:,,,/Resources/Images/Neo_Flipbook.gif";

    public FlipbookBox()
    {
        SettingHelper.Default.GameDirectoryChanged += (s, e) => GetResource();
        GetResource();

        // 监听可见性变化以重启动画
        IsVisibleChanged += OnVisibilityChanged;
    }

    private void GetResource()
    {
        try
        {
            Uri = new Uri(SettingHelper.Default.Publisher >= EPublisher.ZNCS ? FlipbookN : FlipbookL);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"FlipbookBox 初始化失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 可见性变化时重启动画
    /// </summary>
    private void OnVisibilityChanged(object sender, DependencyPropertyChangedEventArgs e)
    {
        if (IsVisible)
        {
            RestartAnimation();
        }
    }

    /// <summary>
    /// 重启GIF动画
    /// </summary>
    public void RestartAnimation()
    {
        try
        {
            var currentUri = Uri;
            if (currentUri != null)
            {
                // 通过重新设置Uri来重启动画
                Uri = null;

                // 使用Dispatcher延迟执行，确保UI更新完成
                Dispatcher.BeginInvoke(() => { Uri = currentUri; }, DispatcherPriority.Loaded);
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"FlipbookBox 重启动画失败: {ex.Message}");
        }
    }
}
