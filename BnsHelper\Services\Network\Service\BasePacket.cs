using System.Text;
using Xylia.BnsHelper.Resources;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Service;

/// <summary>
/// 服务包的基类，提供通用的响应字段和处理逻辑
/// </summary>
internal abstract class BasePacket : IPacket
{
    #region Fields
    /// <summary>
    /// 会话请求Token，用于标识当前会话
    /// </summary>
    public string? Token;

    // 基本响应字段
    public uint ErrorCode { get; set; }
    public string? ErrorMessage { get; set; }
    #endregion

    #region Methods
    /// <summary>
    /// Reads data from the specified <see cref="DataArchive"/> and populates the response or error details.
    /// </summary>
    /// <remarks>If the operation is successful, the method reads the response data. If an error occurs,  it
    /// sets the <c>ErrorCode</c> and <c>ErrorMessage</c> properties to indicate the failure.</remarks>
    /// <param name="reader">The <see cref="DataArchive"/> instance from which to read the data.</param>
    public void Read(DataArchive reader)
    {
        try
        {
            ErrorCode = reader.Read<uint>();
            if (ErrorCode == 0) ReadResponse(reader);
            else if (Enum.IsDefined(typeof(ErrorCode), ErrorCode))
            {
                ErrorMessage = StringHelper.Get("Exception_" + ErrorCode);
            }
            else
            {
                ErrorMessage = reader.ReadString();
            }
        }
        catch (Exception ex)
        {
            ErrorCode = 500;
            ErrorMessage = ex.Message;
        }
    }

    /// <summary>
    /// Reads and processes a response from the specified data archive.
    /// </summary>
    /// <remarks>This method is intended to be overridden in derived classes to implement custom response reading
    /// logic.</remarks>
    /// <param name="reader">The <see cref="DataArchive"/> instance from which the response is read. Must not be <see langword="null"/>.</param>
    protected virtual void ReadResponse(DataArchive reader)
    {

    }

    /// <summary>
    /// Creates a new instance of <see cref="DataArchiveWriter"/> and initializes it with the current token.
    /// </summary>
    /// <remarks>The created <see cref="DataArchiveWriter"/> is preconfigured to write the token using UTF-8
    /// encoding. This method is useful for generating a writer that is ready to archive data with the associated
    /// token.</remarks>
    /// <returns>A new instance of <see cref="DataArchiveWriter"/> initialized with the current token.</returns>
    public virtual DataArchiveWriter Create()
    {
        var writer = new DataArchiveWriter();
        writer.WriteString(Token, Encoding.UTF8);
        return writer;
    }
    #endregion
}

public enum ErrorCode : uint
{
    Success = 0,
    ServerError = 1000,
    InvalidRequest = 1001,
    InvalidQQ = 1002,
    InvalidDevice = 1003,
    Unauthorized = 1004,
    TokenExpired = 1005,
    NotFound = 1006,
    RateLimit = 1007,
    TimestampInvalid = 1008,
}
