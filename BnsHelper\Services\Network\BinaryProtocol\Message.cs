using System.IO;

namespace Xylia.BnsHelper.Services.Network.BinaryProtocol;

/// <summary>
/// Binary protocol message with integrated header
/// </summary>
internal class Message
{
    #region Fields
    public byte Magic { get; set; } = Constants.ProtocolMagic;
    public byte Version { get; set; } = Constants.ProtocolVersion;
    public byte MsgType { get; set; }
    public byte Flags { get; set; }
    public uint Length { get; set; }
    public ulong Timestamp { get; set; }
    public byte[]? Body { get; set; }
    #endregion

    #region Constructors
    /// <summary>
    /// Create a new message
    /// </summary>
    public Message(byte msgType, byte flags , byte[] body)
    {
        MsgType = msgType;
        Flags = flags;
        Timestamp = (ulong)DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        Body = body;
    }

    /// <summary>
    /// Create message from decoded data
    /// </summary>
    public Message(byte[] data)
    {
        if (data.Length < Constants.HeaderSize)
        {
            throw new ArgumentException($"Insufficient data for message header: need {Constants.HeaderSize} bytes, got {data.Length}");
        }

        Magic = data[0];
        Version = data[1];
        MsgType = data[2];
        Flags = data[3];

        // Big-endian decoding for length
        Length = ((uint)data[4] << 24) | ((uint)data[5] << 16) | ((uint)data[6] << 8) | data[7];

        // Big-endian decoding for timestamp
        Timestamp = ((ulong)data[8] << 56) | ((ulong)data[9] << 48) | ((ulong)data[10] << 40) | ((ulong)data[11] << 32) |
                           ((ulong)data[12] << 24) | ((ulong)data[13] << 16) | ((ulong)data[14] << 8) | data[15];

        // Validate magic number and version
        if (Magic != Constants.ProtocolMagic)
        {
            throw new InvalidDataException($"Invalid protocol magic: expected 0x{Constants.ProtocolMagic:X2}, got 0x{Magic:X2}");
        }

        if (Version != Constants.ProtocolVersion)
        {
            throw new InvalidDataException($"Unsupported protocol version: expected 0x{Constants.ProtocolVersion:X2}, got 0x{Version:X2}");
        }

        if (data.Length < Length)
        {
            throw new ArgumentException($"Insufficient data for complete message: need {Length} bytes, got {data.Length}");
        }

        var bodyLength = (int)Length - Constants.HeaderSize;
        if (bodyLength > 0)
        {
            Body = new byte[bodyLength];
            Array.Copy(data, Constants.HeaderSize, Body, 0, bodyLength);
        }

        ArgumentNullException.ThrowIfNull(Body);
    }
    #endregion

    #region Methods
    /// <summary>
    /// 编码消息并加密
    /// </summary>
    /// <param name="message">要编码的消息</param>
    /// <param name="xorKey">加密密钥</param>
    /// <returns>编码并加密后的字节流</returns>
    public byte[] EncodeWithEncryption(byte[]? xorKey)
    {
        var data = Encode();
        if (data.Length > Constants.HeaderSize && xorKey?.Length > 0)
        {
            // Encrypt only the body part
            for (int i = Constants.HeaderSize; i < data.Length; i++)
            {
                data[i] ^= xorKey[(i - Constants.HeaderSize) % xorKey.Length];
            }

            // Set encryption flag
            data[3] |= Constants.FlagEncrypted;
        }

        return data;
    }

    /// <summary>
    /// 编码消息并压缩和加密
    /// </summary>
    /// <param name="xorKey">加密密钥</param>
    /// <returns>编码、压缩并加密后的字节流</returns>
    public byte[] EncodeWithCompressionAndEncryption(byte[]? xorKey)
    {
        var data = Encode();

        // 先压缩
        if (data.Length > Constants.HeaderSize)
        {
            data = Compression.CompressMessageBody(data);
        }

        // 再加密
        if (data.Length > Constants.HeaderSize && xorKey?.Length > 0)
        {
            // Encrypt only the body part
            for (int i = Constants.HeaderSize; i < data.Length; i++)
            {
                data[i] ^= xorKey[(i - Constants.HeaderSize) % xorKey.Length];
            }

            // Set encryption flag
            data[3] |= Constants.FlagEncrypted;
        }

        return data;
    }

    /// <summary>
    /// 编码消息为字节流
    /// </summary>
    /// <param name="message">要编码的消息</param>
    /// <returns>编码后的字节流</returns>
    public byte[] Encode()
    {
        uint bodyLength = (uint)(this.Body?.Length ?? 0);
        var totalLength = Constants.HeaderSize + bodyLength;

        if (totalLength > Constants.MaxMessageSize)
        {
            throw new InvalidOperationException($"Message too large: {totalLength} bytes (max: {Constants.MaxMessageSize})");
        }

        this.Length = totalLength;

        var buffer = new byte[totalLength];
        this.EncodeHeader(buffer);

        if (this.Body != null)
        {
            Array.Copy(this.Body, 0, buffer, Constants.HeaderSize, this.Body.Length);
        }

        return buffer;
    }

    /// <summary>
    /// 编码消息头
    /// </summary>
    /// <param name="message">消息对象</param>
    /// <param name="buffer">目标缓冲区</param>
    private void EncodeHeader(byte[] buffer)
    {
        buffer[0] = this.Magic;
        buffer[1] = this.Version;
        buffer[2] = this.MsgType;
        buffer[3] = this.Flags;

        // Big-endian encoding for length (4 bytes)
        buffer[4] = (byte)(this.Length >> 24);
        buffer[5] = (byte)(this.Length >> 16);
        buffer[6] = (byte)(this.Length >> 8);
        buffer[7] = (byte)this.Length;

        // Big-endian encoding for timestamp (8 bytes)
        buffer[8] = (byte)(this.Timestamp >> 56);
        buffer[9] = (byte)(this.Timestamp >> 48);
        buffer[10] = (byte)(this.Timestamp >> 40);
        buffer[11] = (byte)(this.Timestamp >> 32);
        buffer[12] = (byte)(this.Timestamp >> 24);
        buffer[13] = (byte)(this.Timestamp >> 16);
        buffer[14] = (byte)(this.Timestamp >> 8);
        buffer[15] = (byte)this.Timestamp;
    }

    /// <summary>
    /// 解码消息并解密
    /// </summary>
    /// <param name="data">加密的字节流数据</param>
    /// <param name="xorKey">解密密钥</param>
    /// <returns>解码后的消息</returns>
    public static Message DecodeWithDecryption(byte[] data, byte[]? xorKey)
    {
        if (data.Length < Constants.HeaderSize)
        {
            throw new ArgumentException($"Insufficient data for message header: {data.Length} bytes");
        }

        var processedData = data;

        // 先解密（如果需要）
        var isEncrypted = (processedData[3] & Constants.FlagEncrypted) != 0;
        if (isEncrypted && xorKey?.Length > 0)
        {
            var decryptedData = new byte[processedData.Length];
            Array.Copy(processedData, decryptedData, processedData.Length);

            // Decrypt only the body part (skip header)
            for (int i = Constants.HeaderSize; i < decryptedData.Length; i++)
            {
                decryptedData[i] ^= xorKey[(i - Constants.HeaderSize) % xorKey.Length];
            }

            // Clear encryption flag
            decryptedData[3] &= unchecked((byte)~Constants.FlagEncrypted);
            processedData = decryptedData;
        }

        // 再解压缩（如果需要）
        var isCompressed = (processedData[3] & Constants.FlagCompressed) != 0;
        if (isCompressed)
        {
            processedData = Compression.DecompressMessageBody(processedData);
        }

        return new Message(processedData);
    }
    #endregion
}
