package handler

import (
	"encoding/json"
	"net/http"
	"strconv"

	"github.com/gorilla/mux"
	"gorm.io/gorm"

	"udp-server/server/internal/database"
	"udp-server/server/internal/gateway/service"
	"udp-server/server/internal/model"
	coreService "udp-server/server/internal/service"
	"udp-server/server/pkg/logger"
)

type ActivityListResponse struct {
	Activities []model.Activity `json:"activities"`
	Total      int64            `json:"total"`
	Page       int              `json:"page"`
	PageSize   int              `json:"page_size"`
}

type CreateActivityRequest struct {
	ActivityId   uint64 `json:"activity_id" binding:"required"`
	ActivityName string `json:"activity_name" binding:"required"`
	Status       int8   `json:"status"`
	Priority     int8   `json:"priority"`
	BeginTime    string `json:"begin_time" binding:"required"`
	EndTime      string `json:"end_time" binding:"required"`
}

type UpdateActivityRequest struct {
	ActivityName string `json:"activity_name"`
	Status       int8   `json:"status"`
	Priority     int8   `json:"priority"`
	BeginTime    string `json:"begin_time"`
	EndTime      string `json:"end_time"`
}

// AdminActivityHandler 活动管理后台处理器
type AdminActivityHandler struct {
	activityService      *coreService.ActivityService
	activityAdminService *service.ActivityAdminService
	db                   *gorm.DB
}

// 创建活动管理后台处理器
func NewAdminActivityHandler(activityService *coreService.ActivityService, activityAdminService *service.ActivityAdminService) *AdminActivityHandler {
	return &AdminActivityHandler{
		activityService:      activityService,
		activityAdminService: activityAdminService,
		db:                   database.GetDB(),
	}
}

// ==================== 活动管理 ==================== //

// 获取活动列表
func (h *AdminActivityHandler) GetActivityList(w http.ResponseWriter, r *http.Request) {
	// 构建查询
	query := h.db.Model(&model.Activity{})

	// 状态筛选
	if statusStr := r.URL.Query().Get("status"); statusStr != "" {
		if status, err := strconv.Atoi(statusStr); err == nil {
			query = query.Where("status = ?", status)
		}
	}

	// 关键词搜索
	keyword := r.URL.Query().Get("keyword")
	if keyword != "" {
		query = query.Where("activity_name LIKE ?", "%"+keyword+"%")
	}

	// 分页查询
	page := 0
	if pageStr := r.URL.Query().Get("page"); pageStr != "" {
		page, _ = strconv.Atoi(pageStr)
	}

	pagesize := 0
	if pageSizeStr := r.URL.Query().Get("page_size"); pageSizeStr != "" {
		pagesize, _ = strconv.Atoi(pageSizeStr)
	}

	var activities []model.Activity
	offset := (page - 1) * pagesize
	if err := query.Offset(offset).Limit(pagesize).Order("created_at DESC").Find(&activities).Error; err != nil {
		SendJSONResponse(w, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "success", activities)
}

// 获取特定活动详情
func (h *AdminActivityHandler) GetActivityById(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	vars := mux.Vars(r)

	activityIdStr, ok := vars["id"]
	if !ok {
		SendJSONResponse(w, http.StatusBadRequest, "缺少活动ID", nil)
		return
	}

	activityId, err := strconv.ParseUint(activityIdStr, 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的活动ID", nil)
		return
	}

	// 调用服务
	activity, err := h.activityAdminService.GetActivityById(ctx, activityId)
	if err != nil {
		logger.Error("获取活动详情失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取活动详情失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "success", activity)
}

// 创建活动
func (h *AdminActivityHandler) CreateActivity(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var req service.CreateActivityRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "请求参数格式错误", nil)
		return
	}

	// 调用服务
	activity, err := h.activityAdminService.CreateActivity(ctx, &req)
	if err != nil {
		logger.Error("创建活动失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "创建活动失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusCreated, "success", activity)
}

// 更新活动
func (h *AdminActivityHandler) UpdateActivity(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	vars := mux.Vars(r)

	activityIdStr, ok := vars["id"]
	if !ok {
		SendJSONResponse(w, http.StatusBadRequest, "缺少活动ID", nil)
		return
	}

	activityId, err := strconv.ParseUint(activityIdStr, 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的活动ID", nil)
		return
	}

	var req service.UpdateActivityRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "请求参数格式错误", nil)
		return
	}

	// 调用服务
	activity, err := h.activityAdminService.UpdateActivity(ctx, activityId, &req)
	if err != nil {
		logger.Error("更新活动失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "更新活动失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "success", activity)
}

// 删除活动
func (h *AdminActivityHandler) DeleteActivity(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	vars := mux.Vars(r)

	activityIdStr, ok := vars["id"]
	if !ok {
		SendJSONResponse(w, http.StatusBadRequest, "缺少活动ID", nil)
		return
	}

	activityId, err := strconv.ParseUint(activityIdStr, 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的活动ID", nil)
		return
	}

	// 调用服务
	err = h.activityAdminService.DeleteActivity(ctx, activityId)
	if err != nil {
		logger.Error("删除活动失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "删除活动失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "删除成功", nil)
}

// ==================== 流程管理 ==================== //

// 创建流程
func (h *AdminActivityHandler) CreateFlow(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// 先解析为通用的map来处理parameters字段的类型转换
	var reqMap map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&reqMap); err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "请求参数格式错误", nil)
		return
	}

	// 处理parameters字段：如果是对象，转换为JSON字符串
	if params, exists := reqMap["parameters"]; exists {
		switch v := params.(type) {
		case map[string]interface{}:
			// 如果是对象，转换为JSON字符串
			if paramsBytes, err := json.Marshal(v); err == nil {
				reqMap["parameters"] = string(paramsBytes)
			} else {
				reqMap["parameters"] = "{}"
			}
		case string:
			// 如果已经是字符串，保持不变
		default:
			// 其他类型，设置为空对象
			reqMap["parameters"] = "{}"
		}
	} else {
		// 如果没有parameters字段，设置为空对象
		reqMap["parameters"] = "{}"
	}

	// 重新序列化并解析为UpdateFlowRequest
	reqBytes, _ := json.Marshal(reqMap)
	var req service.UpdateFlowRequest
	if err := json.Unmarshal(reqBytes, &req); err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "请求参数格式错误", nil)
		return
	}

	// 转换为ActivityFlow模型
	flow := &model.ActivityFlow{
		ActivityID:  req.ActivityID,
		FlowId:      req.FlowID,
		FlowName:    req.FlowName,
		Group:       req.Group,
		IdeToken:    req.IdeToken,
		AccountType: byte(req.AccountType),
		FlowType:    byte(req.FlowType),
		Custom:      byte(req.Custom),
		Parameters:  req.Parameters,
		Status:      byte(req.Status),
		SortOrder:   req.SortOrder,
	}

	// 调用服务
	createdFlow, err := h.activityAdminService.CreateFlow(ctx, flow)
	if err != nil {
		logger.Error("创建流程失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "创建流程失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusCreated, "创建成功", createdFlow)
}

// 根据活动ID获取流程列表
func (h *AdminActivityHandler) GetFlowsByActivityId(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// 获取参数
	activityId, err := strconv.ParseUint(r.URL.Query().Get("activity_id"), 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的活动ID", nil)
		return
	}

	// 调用服务
	activity, err := h.activityAdminService.GetActivityById(ctx, activityId)
	if err != nil {
		logger.Error("获取流程列表失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取流程列表失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "success", activity.Flows)
}

// 更新流程
func (h *AdminActivityHandler) UpdateFlow(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	vars := mux.Vars(r)

	flowIdStr, ok := vars["id"]
	if !ok {
		SendJSONResponse(w, http.StatusBadRequest, "缺少流程ID", nil)
		return
	}

	flowId, err := strconv.ParseUint(flowIdStr, 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的流程ID", nil)
		return
	}

	// 先解析为通用的map来检测请求类型
	var reqMap map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&reqMap); err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "请求参数格式错误", nil)
		return
	}

	var flow *model.ActivityFlow

	// 检查是否只更新状态（只有status字段）
	if len(reqMap) == 1 {
		if statusVal, exists := reqMap["status"]; exists {
			if status, ok := statusVal.(float64); ok {
				// 只更新状态
				flow, err = h.activityAdminService.UpdateFlowStatus(ctx, flowId, int8(status))
			} else {
				SendJSONResponse(w, http.StatusBadRequest, "状态值格式错误", nil)
				return
			}
		} else {
			SendJSONResponse(w, http.StatusBadRequest, "未知的单字段更新请求", nil)
			return
		}
	} else {
		// 完整更新，处理parameters字段类型转换
		if params, exists := reqMap["parameters"]; exists {
			switch v := params.(type) {
			case map[string]interface{}:
				// 如果是对象，转换为JSON字符串
				if paramsBytes, err := json.Marshal(v); err == nil {
					reqMap["parameters"] = string(paramsBytes)
				} else {
					reqMap["parameters"] = "{}"
				}
			case string:
				// 如果已经是字符串，保持不变
			default:
				// 其他类型，设置为空对象
				reqMap["parameters"] = "{}"
			}
		}

		// 重新解析为UpdateFlowRequest
		reqBytes, _ := json.Marshal(reqMap)
		var req service.UpdateFlowRequest
		if err := json.Unmarshal(reqBytes, &req); err != nil {
			SendJSONResponse(w, http.StatusBadRequest, "请求参数格式错误", nil)
			return
		}

		// 转换为ActivityFlow模型
		updateFlow := &model.ActivityFlow{
			FlowName:   req.FlowName,
			Status:     byte(req.Status),
			IdeToken:   req.IdeToken,
			Parameters: req.Parameters,
		}
		flow, err = h.activityAdminService.UpdateFlow(ctx, flowId, updateFlow)
	}
	if err != nil {
		logger.Error("更新流程失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "更新流程失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "更新成功", map[string]interface{}{
		"data": flow,
	})
}

// 删除流程
func (h *AdminActivityHandler) DeleteFlow(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	vars := mux.Vars(r)

	flowIdStr, ok := vars["id"]
	if !ok {
		SendJSONResponse(w, http.StatusBadRequest, "缺少流程ID", nil)
		return
	}

	flowId, err := strconv.ParseUint(flowIdStr, 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的流程ID", nil)
		return
	}

	// 调用服务
	err = h.activityAdminService.DeleteFlow(ctx, flowId)
	if err != nil {
		logger.Error("删除流程失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "删除流程失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "删除成功", nil)
}

// TestFlow 测试流程
func (h *AdminActivityHandler) TestFlow(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	vars := mux.Vars(r)
	flowIDStr := vars["id"]

	flowID, err := strconv.ParseUint(flowIDStr, 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的流程ID", nil)
		return
	}

	var req struct {
		AccessToken string            `json:"access_token"`
		AccType     string            `json:"acctype"`
		TestParams  map[string]string `json:"test_params"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "请求参数格式错误", nil)
		return
	}

	if req.AccessToken == "" {
		SendJSONResponse(w, http.StatusBadRequest, "Access Token不能为空", nil)
		return
	}

	// 根据账号类型自动设置固定参数
	var openID, appID string
	switch req.AccType {
	case "wx":
		// 微信账号
		openID = "oDj4vwoj83EIEWBzjsoO8iGQMikM"
		appID = "wxfa0c35392d06b82f"
	default:
		// 默认QQ号
		openID = "E1A933F9ABEB7DFCFD7EFE8A8BD160DD"
		appID = "101491592"
	}

	result, err := h.activityAdminService.TestFlowWithParams(ctx, flowID, req.AccessToken, openID, appID, req.AccType, req.TestParams)
	if err != nil {
		logger.Error("测试流程失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "测试流程失败: "+err.Error(), nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "success", result)
}

// ==================== AMS解析 ==================== //

// 解析AMS链接
func (h *AdminActivityHandler) ParseAMS(w http.ResponseWriter, r *http.Request) {
	var req struct {
		AMSURL string `json:"ams_url"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "请求参数格式错误", nil)
		return
	}

	if req.AMSURL == "" {
		SendJSONResponse(w, http.StatusBadRequest, "AMS链接不能为空", nil)
		return
	}

	// 调用服务
	result, err := h.activityAdminService.FetchAMSData(req.AMSURL)
	if err != nil {
		logger.Error("解析AMS链接失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, err.Error(), nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "AMS链接解析成功", result)
}

// 创建活动和流程
func (h *AdminActivityHandler) CreateActivityAndFlows(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var req struct {
		AMSURL string `json:"ams_url"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "请求参数格式错误", nil)
		return
	}

	if req.AMSURL == "" {
		SendJSONResponse(w, http.StatusBadRequest, "AMS链接不能为空", nil)
		return
	}

	// 调用服务
	result, err := h.activityAdminService.CreateActivityAndFlows(ctx, req.AMSURL)
	if err != nil {
		logger.Error("创建活动和流程失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, err.Error(), nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "AMS链接解析并创建成功", result)
}
