﻿using NAudio.Wave;
using NAudio.Wave.SampleProviders;
using System.IO;
using System.Reflection;

namespace Xylia.BnsHelper.Common.Helpers;
internal class AudioSteamReader : WaveStream, ISampleProvider
{
    #region Fields
    private WaveStream readerStream;
	private readonly SampleChannel sampleChannel;
	private readonly int destBytesPerSample;
	private readonly int sourceBytesPerSample;
	private readonly long length;
	private readonly object lockObject;

	/// <summary>
	/// WaveFormat of this stream
	/// </summary>
	public override WaveFormat WaveFormat => sampleChannel.WaveFormat;

	/// <summary>
	/// Length of this stream (in bytes)
	/// </summary>
	public override long Length => length;

	/// <summary>
	/// Position of this stream (in bytes)
	/// </summary>
	public override long Position
	{
		get
		{
			return SourceToDest(readerStream.Position);
		}
		set
		{
			lock (lockObject)
			{
				readerStream.Position = DestToSource(value);
			}
		}
	}

	/// <summary>
	/// Gets or Sets the Volume of this AudioFileReader. 1.0f is full volume
	/// </summary>
	public float Volume
	{
		get
		{
			return sampleChannel.Volume;
		}
		set
		{
			sampleChannel.Volume = value;
		}
	}
    #endregion

    #region Constructor
    /// <summary>
    /// Initializes a new instance of AudioFileReader
    /// </summary>
    /// <param name="stream"></param>
    /// <param name="extension"></param>
    public AudioSteamReader(Stream stream, string extension = "mp3")
	{
		lockObject = new object();
		CreateReaderStream(stream, extension);
		sourceBytesPerSample = readerStream.WaveFormat.BitsPerSample / 8 * readerStream.WaveFormat.Channels;
		sampleChannel = new SampleChannel(readerStream, forceStereo: false);
		destBytesPerSample = 4 * sampleChannel.WaveFormat.Channels;
		length = SourceToDest(readerStream.Length);
	}
    #endregion

    #region Methods
    /// <summary>
    /// Creates the reader stream, supporting all filetypes in the core NAudio library,	and ensuring we are in PCM format
    /// </summary>
    /// <param name="stream"></param>
    /// <param name="extension"></param>
    private void CreateReaderStream(Stream stream, string extension)
	{
		if (extension.Equals("wav", StringComparison.OrdinalIgnoreCase))
		{
			readerStream = new WaveFileReader(stream);
			if (readerStream.WaveFormat.Encoding != WaveFormatEncoding.Pcm && readerStream.WaveFormat.Encoding != WaveFormatEncoding.IeeeFloat)
			{
				readerStream = WaveFormatConversionStream.CreatePcmStream(readerStream);
				readerStream = new BlockAlignReductionStream(readerStream);
			}
		}
		else if (extension.Equals("mp3", StringComparison.OrdinalIgnoreCase))
		{
			readerStream = new Mp3FileReader(stream);
		}
		else if (extension.Equals("aiff", StringComparison.OrdinalIgnoreCase) || extension.Equals("aif", StringComparison.OrdinalIgnoreCase))
		{
			readerStream = new AiffFileReader(stream);
		}
		else
		{
			throw new NotSupportedException("Sound is not supported!");
		}
	}

	/// <summary>
	/// Reads from this wave stream
	/// </summary>
	/// <param name="buffer">Audio buffer</param>
	/// <param name="offset">Offset into buffer</param>
	/// <param name="count">Number of bytes required</param>
	/// <returns>Number of bytes read</returns>
	public override int Read(byte[] buffer, int offset, int count)
	{
		WaveBuffer waveBuffer = new WaveBuffer(buffer);
		int count2 = count / 4;
		return Read(waveBuffer.FloatBuffer, offset / 4, count2) * 4;
	}

	/// <summary>
	/// Reads audio from this sample provider
	/// </summary>
	/// <param name="buffer">Sample buffer</param>
	/// <param name="offset">Offset into sample buffer</param>
	/// <param name="count">Number of samples required</param>
	/// <returns><Number of samples read/returns>
	public int Read(float[] buffer, int offset, int count)
	{
		lock (lockObject)
		{
			return sampleChannel.Read(buffer, offset, count);
		}
	}

	/// <summary>
	/// Helper to convert source to dest bytes
	/// </summary>
	/// <param name="sourceBytes"></param>
	/// <returns></returns>
	private long SourceToDest(long sourceBytes)
	{
		return destBytesPerSample * (sourceBytes / sourceBytesPerSample);
	}

	/// <summary>
	///  Helper to convert dest to source bytes
	/// </summary>
	/// <param name="destBytes"></param>
	/// <returns></returns>
	private long DestToSource(long destBytes)
	{
		return sourceBytesPerSample * (destBytes / destBytesPerSample);
	}

	/// <summary>
	/// Disposes this AudioFileReader
	/// </summary>
	protected override void Dispose(bool disposing)
	{
		if (disposing && readerStream != null)
		{
			readerStream.Dispose();
			readerStream = null;
		}

		base.Dispose(disposing);
	}
    #endregion

    #region Static Methods
    /// <summary>
    /// Plays an audio file embedded as a resource in the assembly.
    /// </summary>
    /// <remarks>The method retrieves the audio file as a stream from the assembly's embedded resources,
    /// initializes a playback device,  and plays the audio asynchronously. If the specified resource path does not
    /// exist, the method returns without playing any audio.</remarks>
    /// <param name="path">The resource path of the audio file within the assembly.</param>
    /// <param name="volume">The playback volume, ranging from 0.0 (silent) to 1.0 (full volume). The default is 0.4.</param>
    /// <returns>A task that represents the asynchronous operation. The task completes when the audio playback finishes.</returns>
    public static async Task Play(string path, float volume = 0.4f)
    {
        using var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream(path);
        if (stream is null) return;

        await Play(stream, volume);
    }

    public static async Task Play(Stream stream, float volume = 0.4f)
	{
        // use volumn sample 
        using var audioFile = new AudioSteamReader(stream);
        var _volumeProvider = new VolumeSampleProvider(audioFile) { Volume = volume };

        using var outputDevice = new WaveOutEvent();
        outputDevice.Init(_volumeProvider);
        outputDevice.Play();

        while (outputDevice.PlaybackState == PlaybackState.Playing)
        {
            await Task.Delay(1000);
        }
    }
    #endregion
}
