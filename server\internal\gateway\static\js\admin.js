// 当前显示的部分
let currentSection = 'dashboard';

// 分页变量
let currentRiskPage = 1;
let currentUsersPage = 1;
let currentCDKeysPage = 1;

// 分页状态变量（用于判断是否还有更多数据）
let hasMoreUsers = true;
let hasMoreCDKeys = true;
let hasMoreRisk = true;

// 全局权限相关函数
function getPermissionText(permission) {
    const permissions = {
        0: '普通用户',
        1: '高级用户',
        2: '超级用户',
        3: '特级用户'
    };
    return permissions[permission] || '未知';
}

function getPermissionColor(permission) {
    const colors = {
        0: '#718096',  // 灰色 - 普通用户
        1: '#3182ce',  // 蓝色 - 高级用户
        2: '#d69e2e',  // 橙色 - 超级用户
        3: '#e53e3e'   // 红色 - 特级用户
    };
    return colors[permission] || '#718096';
}

// 显示消息提示
function showMessage(message, type = 'info') {
    // 创建消息元素
    const messageEl = document.createElement('div');
    messageEl.className = `message message-${type}`;
    messageEl.textContent = message;

    // 添加样式
    messageEl.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        padding: 16px 24px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        max-width: 500px;
        min-width: 300px;
        text-align: center;
        box-shadow: 0 8px 24px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
        font-size: 16px;
    `;

    // 根据类型设置颜色
    switch(type) {
        case 'success':
            messageEl.style.background = '#38a169';
            break;
        case 'error':
            messageEl.style.background = '#e53e3e';
            break;
        case 'warning':
            messageEl.style.background = '#d69e2e';
            break;
        default:
            messageEl.style.background = '#3182ce';
    }

    // 添加到页面
    document.body.appendChild(messageEl);

    // 3秒后自动移除
    setTimeout(() => {
        messageEl.style.opacity = '0';
        messageEl.style.transform = 'translate(-50%, -50%) scale(0.8)';
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 300);
    }, 3000);
}

// 显示指定部分
function showSection(sectionName) {
    // 隐藏所有部分
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
    });
    
    // 更新导航状态
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // 显示指定部分
    document.getElementById(sectionName).classList.add('active');

    // 找到对应的导航项并激活
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        const onclick = item.getAttribute('onclick');
        if (onclick && onclick.includes(`showSection('${sectionName}')`)) {
            item.classList.add('active');
        }
    });

    currentSection = sectionName;

    // 更新导航栏active状态
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
        if (item.getAttribute('href') === `#${sectionName}`) {
            item.classList.add('active');
        }
    });

    // 加载对应数据
    loadSectionData(sectionName);
}

// 处理hash变化
function handleHashChange() {
    const hash = window.location.hash.substring(1); // 去掉#
    const section = hash || 'dashboard';
    showSection(section);
}

// 从hash恢复导航状态
function restoreSectionFromHash() {
    handleHashChange();
}

// 加载部分数据
function loadSectionData(sectionName) {
    switch(sectionName) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'announcements':
            loadAnnouncementsData();
            break;
        case 'users':
            loadUsersData();
            break;
        case 'cdkeys':
            loadCDKeysData();
            break;
        case 'admins':
            loadAdminsData();
            break;
        case 'risk':
            loadRiskData();
            break;
        case 'updates':
            loadUpdatesData();
            break;
        case 'activity':
            loadActivitiesData();
            break;
    }
}

// 加载仪表板数据
function loadDashboardData() {
    fetch('/admin/api/stats')
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                const stats = data.data;
                document.getElementById('auth-users').textContent = stats.auth_users || 0;
                document.getElementById('active-devices').textContent = stats.active_devices || 0;
                document.getElementById('total-users').textContent = stats.total_users || 0;
                document.getElementById('review-users').textContent = stats.review_users || 0;
                document.getElementById('server-uptime').textContent = stats.server_uptime || '-';

                // 加载在线用户统计图表
                setTimeout(() => {
                    loadOnlineStatsChart();
                }, 100); // 延迟100ms确保DOM元素已渲染
            } else {
                console.error('获取仪表板数据失败:', data.message);
            }
        })
        .catch(error => {
            console.error('加载仪表板数据失败:', error);
        });
}

// 加载在线用户统计图表
function loadOnlineStatsChart() {
    console.log('开始加载在线用户统计图表');

    const periodElement = document.getElementById('chart-period');
    const period = periodElement ? periodElement.value : 'hour';
    let limit;
    let actualPeriod;

    switch(period) {
        case 'hour':
            limit = 24;
            actualPeriod = 'hour';
            break;
        case 'day':
            limit = 7;
            actualPeriod = 'day';
            break;
        case 'day15':
            limit = 15;
            actualPeriod = 'day';
            break;
        case 'day30':
            limit = 30;
            actualPeriod = 'day';
            break;
        default:
            limit = 24;
            actualPeriod = 'hour';
    }

    console.log('图表参数:', { period, actualPeriod, limit });

    const url = `/admin/api/stats/history?period=${actualPeriod}&limit=${limit}`;
    console.log('请求URL:', url);

    fetch(url)
        .then(response => {
            console.log('收到响应:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('响应数据:', data);
            if (data.code == 200) {
                displayStatsChart(data.data, actualPeriod);
            } else {
                const chartElement = document.getElementById('online-stats-chart');
                if (chartElement) {
                    chartElement.innerHTML = '<div style="text-align: center; padding: 40px; color: #e53e3e;">加载统计数据失败: ' + data.message + '</div>';
                } else {
                    console.error('找不到 online-stats-chart 元素');
                }
            }
        })
        .catch(error => {
            console.error('加载统计数据失败:', error);
            const chartElement = document.getElementById('online-stats-chart');
            if (chartElement) {
                chartElement.innerHTML = '<div style="text-align: center; padding: 40px; color: #e53e3e;">加载统计数据失败</div>';
            } else {
                console.error('找不到 online-stats-chart 元素');
            }
        });
}

// 显示统计图表
function displayStatsChart(statsData, period) {
    const canvas = document.getElementById('stats-chart-canvas');
    if (!canvas) {
        console.error('找不到图表画布元素');
        return;
    }

    // 设置Canvas大小以适应容器
    const container = canvas.parentElement;
    const containerRect = container.getBoundingClientRect();
    const dpr = window.devicePixelRatio || 1;

    // 设置Canvas的实际大小（考虑设备像素比）
    canvas.width = (containerRect.width - 40) * dpr; // 减去padding
    canvas.height = (containerRect.height - 40) * dpr;

    // 设置Canvas的显示大小
    canvas.style.width = (containerRect.width - 40) + 'px';
    canvas.style.height = (containerRect.height - 40) + 'px';

    const ctx = canvas.getContext('2d');

    // 缩放绘图上下文以匹配设备像素比
    ctx.scale(dpr, dpr);

    // 清除画布
    ctx.clearRect(0, 0, canvas.width / dpr, canvas.height / dpr);

    console.log('图表数据:', statsData, '画布大小:', canvas.width / dpr, 'x', canvas.height / dpr);

    if (!statsData || statsData.length === 0) {
        const displayWidth = canvas.width / dpr;
        const displayHeight = canvas.height / dpr;
        ctx.fillStyle = '#718096';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('暂无统计数据，请等待系统收集数据', displayWidth / 2, displayHeight / 2);
        ctx.fillText('统计数据每小时更新一次', displayWidth / 2, displayHeight / 2 + 25);
        return;
    }

    // 准备数据
    const labels = [];
    const authData = [];
    const heartbeatData = [];
    const updateRequestData = [];

    statsData.forEach(stat => {
        const date = new Date(stat.timestamp * 1000);
        if (period === 'hour') {
            labels.push(date.getHours() + ':00');
        } else {
            labels.push((date.getMonth() + 1) + '/' + date.getDate());
        }
        authData.push(stat.auth_active_users || 0);
        heartbeatData.push(stat.heartbeat_active || 0);
        updateRequestData.push(stat.update_request_count || 0);
    });

    // 绘制图表
    drawLineChart(ctx, canvas, {
        labels: labels,
        datasets: [
            { label: '认证用户', data: authData, color: '#3182ce' },
            { label: '在线用户', data: heartbeatData, color: '#38a169' },
            { label: '请求量', data: updateRequestData, color: '#ecc94b' }
        ]
    });
}

// 绘制折线图
function drawLineChart(ctx, canvas, chartData) {
    const dpr = window.devicePixelRatio || 1;
    const displayWidth = canvas.width / dpr;
    const displayHeight = canvas.height / dpr;
    const padding = 60;
    const chartWidth = displayWidth - 2 * padding;
    const chartHeight = displayHeight - 2 * padding;

    // 找到最大值
    let maxValue = 0;
    chartData.datasets.forEach(dataset => {
        const max = Math.max(...dataset.data);
        if (max > maxValue) maxValue = max;
    });

    if (maxValue === 0) maxValue = 10; // 避免除零

    // 绘制坐标轴
    ctx.strokeStyle = '#e2e8f0';
    ctx.lineWidth = 1;

    // Y轴
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, padding + chartHeight);
    ctx.stroke();

    // X轴
    ctx.beginPath();
    ctx.moveTo(padding, padding + chartHeight);
    ctx.lineTo(padding + chartWidth, padding + chartHeight);
    ctx.stroke();

    // 绘制网格线和标签
    const stepX = chartWidth / (chartData.labels.length - 1);
    const stepY = chartHeight / 5;

    ctx.fillStyle = '#718096';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';

    // X轴标签
    chartData.labels.forEach((label, index) => {
        const x = padding + index * stepX;
        ctx.fillText(label, x, padding + chartHeight + 20);

        // 垂直网格线
        if (index > 0) {
            ctx.strokeStyle = '#f7fafc';
            ctx.beginPath();
            ctx.moveTo(x, padding);
            ctx.lineTo(x, padding + chartHeight);
            ctx.stroke();
        }
    });

    // Y轴标签和水平网格线
    ctx.textAlign = 'right';
    for (let i = 0; i <= 5; i++) {
        const y = padding + i * stepY;
        const value = Math.round(maxValue * (5 - i) / 5);
        ctx.fillText(value.toString(), padding - 10, y + 4);

        if (i > 0) {
            ctx.strokeStyle = '#f7fafc';
            ctx.beginPath();
            ctx.moveTo(padding, y);
            ctx.lineTo(padding + chartWidth, y);
            ctx.stroke();
        }
    }

    // 绘制数据线
    chartData.datasets.forEach(dataset => {
        ctx.strokeStyle = dataset.color;
        ctx.lineWidth = 2;
        ctx.beginPath();

        dataset.data.forEach((value, index) => {
            const x = padding + index * stepX;
            const y = padding + chartHeight - (value / maxValue) * chartHeight;

            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });

        ctx.stroke();

        // 绘制数据点
        ctx.fillStyle = dataset.color;
        dataset.data.forEach((value, index) => {
            const x = padding + index * stepX;
            const y = padding + chartHeight - (value / maxValue) * chartHeight;

            ctx.beginPath();
            ctx.arc(x, y, 3, 0, 2 * Math.PI);
            ctx.fill();
        });
    });

    // 绘制图例
    const legendY = 20;
    let legendX = padding;

    chartData.datasets.forEach(dataset => {
        ctx.fillStyle = dataset.color;
        ctx.fillRect(legendX, legendY, 12, 12);

        ctx.fillStyle = '#2d3748';
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';
        ctx.fillText(dataset.label, legendX + 20, legendY + 9);

        legendX += ctx.measureText(dataset.label).width + 50;
    });
}

// 加载公告数据
function loadAnnouncementsData() {
    fetch('/admin/api/announcements')
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                displayAnnouncements(data.data);
            } else {
                const tableBody = document.getElementById('announcements-table-body');
                if (tableBody) {
                    tableBody.innerHTML = '<tr><td colspan="5" style="padding: 40px; text-align: center; color: #e53e3e;">加载失败: ' + data.message + '</td></tr>';
                }
            }
        })
        .catch(error => {
            console.error('加载公告数据失败:', error);
            const tableBody = document.getElementById('announcements-table-body');
            if (tableBody) {
                tableBody.innerHTML = '<tr><td colspan="5" style="padding: 40px; text-align: center; color: #e53e3e;">加载失败</td></tr>';
            }
        });
}

// 显示公告列表
function displayAnnouncements(announcements) {
    const tableBody = document.getElementById('announcements-table-body');
    if (!tableBody) {
        console.error('公告表格tbody元素未找到');
        return;
    }

    let html = '';

    if (announcements && announcements.length > 0) {
        announcements.forEach(announcement => {
            // 状态映射
            let statusClass, statusText;
            switch(announcement.status) {
                case 0:
                    statusClass = 'status-draft';
                    statusText = '草稿';
                    break;
                case 1:
                    statusClass = 'status-published';
                    statusText = '已发布';
                    break;
                case 2:
                    statusClass = 'status-offline';
                    statusText = '已下线';
                    break;
                default:
                    statusClass = 'status-unknown';
                    statusText = '未知';
            }

            html += `
                <tr style="border-bottom: 1px solid #e2e8f0;">
                    <td style="padding: 12px; text-align: center; font-size: 12px;">${announcement.id}</td>
                    <td style="padding: 12px; text-align: center;">${announcement.title}</td>
                    <td style="padding: 12px; text-align: center;">
                        <span class="status-badge ${statusClass}">${statusText}</span>
                    </td>
                    <td style="padding: 12px; text-align: center; font-size: 12px;">${announcement.created_at}</td>
                    <td style="padding: 12px; text-align: center;">
                        <div class="action-buttons">
                            <button class="btn btn-secondary btn-small" onclick="editAnnouncement(${announcement.id})">编辑</button>
                            <button class="btn btn-danger btn-small" onclick="deleteAnnouncement(${announcement.id})">删除</button>
                        </div>
                    </td>
                </tr>`;
        });
    } else {
        html += `
            <tr>
                <td colspan="5" style="padding: 40px; text-align: center; color: #718096;">
                    暂无公告
                </td>
            </tr>`;
    }

    // 更新表格内容
    tableBody.innerHTML = html;
}

// 显示创建公告模态框
function showCreateAnnouncementModal() {
    const content = `
        <form id="announcement-form">
            <div class="form-group">
                <label for="title">标题</label>
                <input type="text" id="title" name="title" required>
            </div>
            <div class="form-group">
                <label for="content">内容</label>
                <textarea id="content" name="content" required></textarea>
            </div>
            <div class="form-group">
                <label for="type">公告类型</label>
                <select id="type" name="type" required>
                    <option value="0">信息</option>
                    <option value="1">警告</option>
                    <option value="2">更新</option>
                    <option value="3">维护</option>
                </select>
            </div>
            <div class="form-group">
                <label for="priority">优先级 (0-10，数字越大越重要)</label>
                <input type="number" id="priority" name="priority" value="1" min="0" max="10" step="1">
            </div>
            <div class="form-group">
                <label for="status">发布状态</label>
                <select id="status" name="status">
                    <option value="0">保存为草稿</option>
                    <option value="1">立即发布</option>
                    <option value="2">已下线</option>
                </select>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn">创建</button>
            </div>
        </form>
    `;

    const modal = createModal('announcement-modal', '新建公告', content, '600px');
    document.body.appendChild(modal);

    // 绑定表单提交事件
    document.getElementById('announcement-form').addEventListener('submit', function(e) {
        e.preventDefault();
        createAnnouncement();
    });
}

// 创建公告
function createAnnouncement() {
    const form = document.getElementById('announcement-form');
    if (!form) {
        showMessage('找不到公告表单', 'error');
        return;
    }

    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    console.log('原始表单数据:', data);

    // 转换数值类型并验证
    data.type = parseInt(data.type) || 0;
    data.status = parseInt(data.status);
    data.priority = parseInt(data.priority) || 1;

    // 验证必要字段
    if (!data.title || !data.content) {
        showMessage('标题和内容不能为空', 'error');
        return;
    }

    // 验证优先级范围
    if (data.priority < 0 || data.priority > 10) {
        showMessage('优先级必须在0-10之间', 'error');
        return;
    }

    // 验证状态值
    if (![0, 1, 2].includes(data.status)) {
        showMessage('无效的状态值', 'error');
        return;
    }

    console.log('处理后的创建公告数据:', data);

    fetch('/admin/api/announcements', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        console.log('创建公告响应:', data);
        if (data.code == 200) {
            closeModal('announcement-modal');
            loadAnnouncementsData();
            showMessage('公告创建成功', 'success');
        } else {
            showMessage('创建失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('创建公告失败:', error);
        showMessage('创建失败', 'error');
    });
}

// 编辑公告
function editAnnouncement(announcementId) {
    // 获取公告详情
    fetch(`/admin/api/announcements/${announcementId}`)
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                showEditAnnouncementModal(data.data);
            } else {
                showMessage('获取公告详情失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取公告详情失败:', error);
            showMessage('获取公告详情失败', 'error');
        });
}

// 显示编辑公告模态框
function showEditAnnouncementModal(announcement) {
    // 先填充数据
    const idField = document.getElementById('edit-announcement-id');
    const titleField = document.getElementById('edit-announcement-title');
    const contentField = document.getElementById('edit-announcement-content');
    const typeField = document.getElementById('edit-announcement-type');
    const priorityField = document.getElementById('edit-announcement-priority');
    const statusField = document.getElementById('edit-announcement-status');

    if (idField) idField.value = announcement.id;
    if (titleField) titleField.value = announcement.title;
    if (contentField) contentField.value = announcement.content;
    if (typeField) typeField.value = announcement.type || 0;
    if (priorityField) priorityField.value = announcement.priority || 1;
    if (statusField) statusField.value = announcement.status;

    // 绑定表单提交事件（如果还没有绑定）
    const form = document.getElementById('edit-announcement-form');
    if (form && !form.hasAttribute('data-bound')) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            updateAnnouncement();
        });
        form.setAttribute('data-bound', 'true');
    }

    // 最后显示模态框
    showModal('edit-announcement-modal');
}

// 更新公告
function updateAnnouncement() {
    const form = document.getElementById('edit-announcement-form');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // 转换数值类型
    data.type = parseInt(data.type) || 0;
    data.priority = parseInt(data.priority) || 1;
    data.status = parseInt(data.status);

    // 验证优先级范围
    if (data.priority < 0 || data.priority > 10) {
        showMessage('优先级必须在0-10之间', 'error');
        return;
    }

    // 验证状态值
    if (![0, 1, 2].includes(data.status)) {
        showMessage('无效的状态值', 'error');
        return;
    }

    const announcementId = data.id;
    delete data.id; // 移除ID，不需要在请求体中发送

    fetch(`/admin/api/announcements/${announcementId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 200) {
            closeModal('edit-announcement-modal');
            loadAnnouncementsData();
            showMessage('公告更新成功', 'success');
        } else {
            showMessage('更新失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('更新公告失败:', error);
        showMessage('更新失败', 'error');
    });
}

// 删除公告
function deleteAnnouncement(announcementId) {
    if (!confirm('确定要删除这个公告吗？')) {
        return;
    }

    fetch(`/admin/api/announcements/${announcementId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 200) {
            loadAnnouncementsData();
            showMessage('公告删除成功', 'success');
        } else {
            showMessage('删除失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('删除公告失败:', error);
        showMessage('删除失败', 'error');
    });
}

// 显示模态框
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'flex';
        modal.classList.add('show');

        // 强制重新计算样式，修复padding丢失问题
        setTimeout(() => {
            modal.style.display = 'none';
            modal.offsetHeight; // 触发重排
            modal.style.display = 'flex';
        }, 10);
    }
}

// 关闭模态框
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        // 如果是HTML中的模态框（有modal类），只隐藏
        if (modal.classList.contains('modal')) {
            modal.style.display = 'none';
            modal.classList.remove('show');
        } else {
            // 如果是动态创建的模态框，移除
            modal.remove();
        }
    }
}

// 创建带有右上角关闭按钮的模态框
function createModal(modalId, title, content, width = '500px') {
    // 先移除已存在的同ID模态框
    const existingModal = document.getElementById(modalId);
    if (existingModal) {
        existingModal.remove();
    }

    const modal = document.createElement('div');
    modal.id = modalId;
    modal.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background: rgba(0,0,0,0.5); display: flex; align-items: center;
        justify-content: center; z-index: 1000;
    `;

    modal.innerHTML = `
        <div style="background: white; border-radius: 8px; width: ${width}; max-width: 90vw; max-height: 90vh; display: flex; flex-direction: column; position: relative;">
            <button onclick="closeModal('${modalId}')" style="
                position: absolute; top: 12px; right: 12px; background: none; border: none;
                font-size: 24px; color: #718096; cursor: pointer; padding: 8px; line-height: 1;
                border-radius: 6px; transition: all 0.2s ease; width: 40px; height: 40px;
                display: flex; align-items: center; justify-content: center; z-index: 1;
            " onmouseover="this.style.background='#f7fafc'; this.style.color='#2d3748';"
               onmouseout="this.style.background='none'; this.style.color='#718096';">×</button>
            <div class="modal-header" style="padding: 30px; flex-shrink: 0;">
                ${title ? `<h3 style="margin: 0; color: #2d3748; font-size: 1.25rem; font-weight: 600;">${title}</h3>` : ''}
            </div>
            <div style="padding: 0 30px 30px 30px; overflow-y: auto; flex: 1; min-height: 0;">
                ${content}
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    return modal;
}

// 加载用户数据
function loadUsersData() {
    hasMoreUsers = true; // 重置分页状态
    loadUsersPage(1);
}

// 加载指定页的用户数据
function loadUsersPage(page) {
    currentUsersPage = page;
    const limit = 20; // 每页20条记录

    fetch(`/admin/api/users?page=${page}&limit=${limit}`)
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                // 检查是否还有更多数据
                hasMoreUsers = data.data && data.data.length === limit;
                displayUsersTable(data.data);
            } else {
                document.getElementById('users-content').innerHTML = '<p style="color: #e53e3e;">加载用户数据失败: ' + data.message + '</p>';
            }
        })
        .catch(error => {
            console.error('加载用户数据失败:', error);
            document.getElementById('users-content').innerHTML = '<p style="color: #e53e3e;">加载用户数据失败</p>';
        });
}

// 显示用户表格
function displayUsersTable(users) {
    // 获取状态文本和颜色
    function getStatusText(status) {
        const statuses = {
            0: '正常',
            1: '临时封禁',
            2: '永久封禁',
            3: '审核状态'
        };
        return statuses[status] || '未知';
    }

    function getStatusColor(status) {
        const colors = {
            0: '#38a169',  // 绿色 - 正常
            1: '#d69e2e',  // 橙色 - 临时封禁
            2: '#e53e3e',  // 红色 - 永久封禁
            3: '#3182ce'   // 蓝色 - 审核状态
        };
        return colors[status] || '#718096';
    }

    // 权限相关函数已移至全局作用域

    const tableBody = document.getElementById('users-table-body');
    if (!tableBody) {
        console.error('用户表格tbody元素未找到');
        return;
    }

    let html = '';

    if (users && users.length > 0) {
        users.forEach(user => {
            const statusText = getStatusText(user.status);
            const statusColor = getStatusColor(user.status);
            const permissionText = getPermissionText(user.permission);
            const permissionColor = getPermissionColor(user.permission);
            const onlineColor = user.is_online ? '#38a169' : '#718096';
            const onlineText = user.is_online ? '在线' : '离线';

            // 权限过期时间显示
            const expirationText = user.permission_expiration_text || '未知';
            let expirationColor = '#718096'; // 默认灰色
            if (expirationText.includes('已过期')) {
                expirationColor = '#e53e3e'; // 红色
            } else if (expirationText.includes('永久')) {
                expirationColor = '#38a169'; // 绿色
            } else if (expirationText.includes('剩余')) {
                expirationColor = '#d69e2e'; // 橙色
            }

            html += `
                <tr style="border-bottom: 1px solid #e2e8f0;">
                    <td style="padding: 12px; font-size: 12px;">${user.uid || '-'}</td>
                    <td style="padding: 12px; font-family: monospace;">${user.uin}</td>
                    <td style="padding: 12px; font-size: 12px;">${user.name || '未设置'}</td>
                    <td style="padding: 12px; font-size: 12px;">
                        <span style="color: ${statusColor}; font-weight: bold;">${statusText}</span>
                    </td>
                    <td style="padding: 12px; font-size: 12px; cursor: pointer;"
                        onclick="showUserCDKeyModal(${user.id || user.uid})"
                        title="点击查看口令码使用记录">
                        <span style="color: ${permissionColor}; font-weight: bold;">${permissionText}</span>
                    </td>
                    <td style="padding: 12px; font-size: 12px; cursor: pointer;"
                        onclick="showUserCDKeyModal(${user.id || user.uid})"
                        title="点击查看口令码使用记录">
                        <span style="color: ${expirationColor}; font-weight: bold;">${expirationText}</span>
                    </td>
                    <td style="padding: 12px; font-size: 12px;">
                        <span style="color: ${onlineColor}; font-weight: bold;">${onlineText}</span>
                    </td>
                    <td style="padding: 12px; font-size: 12px;">${user.login_time || '从未登录'}</td>
                    <td style="padding: 12px;">
                        <div class="action-buttons">
                            <button class="btn btn-small" onclick="viewUserDetails('${user.uin}')">
                                详情
                            </button>
                            ${user.is_online ? `
                                <button class="btn btn-danger btn-small" onclick="kickUser('${user.uin}')">
                                    踢出
                                </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>`;
        });
    } else {
        html += `
            <tr>
                <td colspan="9" style="padding: 40px; text-align: center; color: #718096;">
                    暂无用户数据
                </td>
            </tr>`;
    }

    // 更新表格内容
    tableBody.innerHTML = html;

    // 更新分页控件
    const paginationHtml = `
        <div style="display: flex; justify-content: center; align-items: center; margin-top: 20px; gap: 10px;">
            <button class="btn btn-secondary btn-small" onclick="loadUsersPage(currentUsersPage - 1)" ${currentUsersPage <= 1 ? 'disabled' : ''}>
                上一页
            </button>
            <span style="color: #4a5568; font-weight: 500; padding: 0 15px;">第 ${currentUsersPage} 页</span>
            <button class="btn btn-secondary btn-small" onclick="loadUsersPage(currentUsersPage + 1)" ${!hasMoreUsers ? 'disabled' : ''}>
                下一页
            </button>
        </div>`;

    // 查找或创建分页容器
    let paginationContainer = document.getElementById('users-pagination');
    if (!paginationContainer) {
        paginationContainer = document.createElement('div');
        paginationContainer.id = 'users-pagination';
        document.getElementById('users-content').appendChild(paginationContainer);
    }
    paginationContainer.innerHTML = paginationHtml;
}

// 搜索用户
function searchUsers() {
    const searchTerm = document.getElementById('user-search-input').value.trim();
    const statusFilter = document.getElementById('user-status-filter').value;
    const onlineFilter = document.getElementById('user-online-filter').value;
    const permissionFilter = document.getElementById('user-permission-filter').value;

    if (!searchTerm && !statusFilter && !onlineFilter && !permissionFilter) {
        loadUsersData();
        return;
    }

    let url = '/admin/api/users?';
    const params = [];

    if (searchTerm) {
        params.push(`search=${encodeURIComponent(searchTerm)}`);
    }

    if (statusFilter) {
        params.push(`status=${encodeURIComponent(statusFilter)}`);
    }

    if (onlineFilter) {
        params.push(`online=${encodeURIComponent(onlineFilter)}`);
    }

    if (permissionFilter) {
        params.push(`permission=${encodeURIComponent(permissionFilter)}`);
    }

    url += params.join('&');

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                displayUsersTable(data.data);
            } else {
                showMessage('搜索失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('搜索用户失败:', error);
            showMessage('搜索失败', 'error');
        });
}

// 筛选用户
function filterUsers() {
    const statusFilter = document.getElementById('user-status-filter').value;
    const onlineFilter = document.getElementById('user-online-filter').value;
    const permissionFilter = document.getElementById('user-permission-filter').value;
    const searchTerm = document.getElementById('user-search-input').value.trim();

    if (!statusFilter && !onlineFilter && !permissionFilter && !searchTerm) {
        loadUsersData();
        return;
    }

    searchUsers(); // 复用搜索功能
}

// 踢出用户
function kickUser(qqNumber) {
    if (!confirm(`确定要踢出用户 ${qqNumber} 吗？`)) {
        return;
    }

    fetch(`/admin/api/users/${qqNumber}/kick`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 200) {
            loadUsersData();
            showMessage('用户已被踢出', 'success');
        } else {
            showMessage('踢出失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('踢出用户失败:', error);
        showMessage('踢出失败', 'error');
    });
}

// 搜索口令码
function searchCDKeys() {
    currentCDKeysPage = 1; // 重置到第一页
    loadCDKeysPage(1);
}

// 查看用户详情
function viewUserDetails(qqNumber) {
    // 获取用户详细信息
    fetch(`/admin/api/users/${qqNumber}`)
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                showUserDetailsModal(data.data);
            } else {
                showMessage('获取用户详情失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取用户详情失败:', error);
            showMessage('获取用户详情失败', 'error');
        });
}

// 显示用户详情模态框
function showUserDetailsModal(userData) {
    const permissionText = getPermissionText(userData.permission);
    const onlineText = userData.is_online ? '在线' : '离线';
    const onlineColor = userData.is_online ? '#38a169' : '#718096';

    // 权限过期时间显示
    const expirationText = userData.permission_expiration_text || '未知';
    let expirationColor = '#718096'; // 默认灰色
    if (expirationText.includes('已过期')) {
        expirationColor = '#e53e3e'; // 红色
    } else if (expirationText.includes('永久')) {
        expirationColor = '#38a169'; // 绿色
    } else if (expirationText.includes('剩余')) {
        expirationColor = '#d69e2e'; // 橙色
    }

    const content = `
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
            <div>
                <label style="display: block; margin-bottom: 5px; color: #4a5568; font-weight: 500;">用户ID</label>
                <div style="padding: 8px; background: #f7fafc; border-radius: 4px; font-family: monospace;">${userData.id || userData.uid || '-'}</div>
            </div>
            <div>
                <label style="display: block; margin-bottom: 5px; color: #4a5568; font-weight: 500;">QQ号</label>
                <div style="padding: 8px; background: #f7fafc; border-radius: 4px; font-family: monospace;">${userData.qq_number}</div>
            </div>
            <div>
                <label style="display: block; margin-bottom: 5px; color: #4a5568; font-weight: 500;">用户名</label>
                <div style="padding: 8px; background: #f7fafc; border-radius: 4px;">${userData.name || '未设置'}</div>
            </div>
            <div>
                <label style="display: block; margin-bottom: 5px; color: #4a5568; font-weight: 500;">状态</label>
                <select id="user-status-select" style="width: 100%; padding: 8px; border: 1px solid #d1d5db; border-radius: 4px; background: #ffffff;">
                    <option value="0" ${userData.status === 0 ? 'selected' : ''}>正常</option>
                    <option value="1" ${userData.status === 1 ? 'selected' : ''}>临时封禁</option>
                    <option value="2" ${userData.status === 2 ? 'selected' : ''}>永久封禁</option>
                    <option value="3" ${userData.status === 3 ? 'selected' : ''}>审核状态</option>
                </select>
            </div>
            <div>
                <label style="display: block; margin-bottom: 5px; color: #4a5568; font-weight: 500;">权限</label>
                <div style="padding: 8px; background: #f7fafc; border-radius: 4px;">${permissionText}</div>
            </div>
            <div>
                <label style="display: block; margin-bottom: 5px; color: #4a5568; font-weight: 500;">权限过期</label>
                <div style="padding: 8px; background: #f7fafc; border-radius: 4px;">
                    <span style="color: ${expirationColor}; font-weight: bold;">${expirationText}</span>
                </div>
            </div>
            <div>
                <label style="display: block; margin-bottom: 5px; color: #4a5568; font-weight: 500;">在线状态</label>
                <div style="padding: 8px; background: #f7fafc; border-radius: 4px;">
                    <span style="color: ${onlineColor}; font-weight: bold;">${onlineText}</span>
                </div>
            </div>
            <div>
                <label style="display: block; margin-bottom: 5px; color: #4a5568; font-weight: 500;">最后登录</label>
                <div style="padding: 8px; background: #f7fafc; border-radius: 4px; font-size: 12px;">${userData.login_time || '从未登录'}</div>
            </div>
        </div>
    `;

    // 填充HTML模板
    document.getElementById('user-details-title').textContent = `用户详情 - ${userData.qq_number}`;
    document.getElementById('user-details-content').innerHTML = content;

    // 设置绑定口令码按钮的点击事件
    document.getElementById('bind-cdkey-btn').onclick = function() {
        showBindCDKeyModal(userData.uid || userData.id, userData.qq_number);
    };

    // 设置状态下拉框变化事件
    document.getElementById('user-status-select').onchange = function() {
        const newStatus = parseInt(this.value);
        const statusNames = {0: '正常', 1: '临时封禁', 2: '永久封禁', 3: '审核状态'};

        if (confirm(`确定要将用户状态修改为"${statusNames[newStatus]}"吗？`)) {
            updateUserStatus(userData.qq_number, newStatus);
        } else {
            // 恢复原状态
            this.value = userData.status;
        }
    };

    // 显示模态框
    const modal = document.getElementById('user-details-modal');
    modal.style.display = 'flex';
    modal.classList.add('show');
}

// 更新用户状态
function updateUserStatus(qqNumber, newStatus) {
    fetch(`/admin/api/users/${qqNumber}/status`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 200) {
            showMessage('用户状态更新成功', 'success');
            // 重新加载用户数据
            loadUsersData();
        } else {
            showMessage('更新用户状态失败: ' + data.message, 'error');
            // 恢复下拉框状态
            const select = document.getElementById('user-status-select');
            if (select) {
                select.value = select.getAttribute('data-original-status');
            }
        }
    })
    .catch(error => {
        console.error('更新用户状态失败:', error);
        showMessage('更新用户状态失败', 'error');
        // 恢复下拉框状态
        const select = document.getElementById('user-status-select');
        if (select) {
            select.value = select.getAttribute('data-original-status');
        }
    });
}

// 显示绑定口令码模态框
function showBindCDKeyModal(uid, qqNumber) {
    // 先移除已存在的模态框
    const existingModal = document.getElementById('bind-cdkey-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // 创建模态框
    const modal = document.createElement('div');
    modal.id = 'bind-cdkey-modal';
    modal.className = 'modal';
    modal.style.display = 'flex';

    modal.innerHTML = `
        <div class="modal-content" style="max-width: 500px; width: 90vw;">
            <div class="modal-header">
                <h3>绑定口令码</h3>
                <button type="button" class="modal-close" onclick="closeModal('bind-cdkey-modal')">&times;</button>
            </div>
            <div style="margin-bottom: 15px; padding: 12px; background: #f7fafc; border-radius: 6px; border-left: 4px solid #2b6cb0;">
                <strong style="color: #2b6cb0;">目标用户：</strong>
                <span style="font-family: monospace; color: #2d3748;">${qqNumber} (UID: ${uid})</span>
            </div>
            <form id="bind-cdkey-form">
                <div class="form-group">
                    <label for="bind-cdkey-input">口令码</label>
                    <input type="text" id="bind-cdkey-input" name="cdkey" required
                           placeholder="请输入要绑定的口令码"
                           style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                </div>
                <div class="form-group">
                    <label for="bind-reason-input">绑定原因</label>
                    <textarea id="bind-reason-input" name="reason"
                              placeholder="请输入绑定原因（可选）"
                              style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px; min-height: 80px; resize: vertical;"></textarea>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">确认绑定</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal('bind-cdkey-modal')">取消</button>
                </div>
            </form>
        </div>
    `;

    document.body.appendChild(modal);

    // 绑定表单提交事件
    document.getElementById('bind-cdkey-form').addEventListener('submit', function(e) {
        e.preventDefault();

        const cdkey = document.getElementById('bind-cdkey-input').value.trim();
        const reason = document.getElementById('bind-reason-input').value.trim();

        if (!cdkey) {
            showMessage('请输入口令码', 'error');
            return;
        }

        // 调用绑定API
        bindCDKeyToUser(uid, cdkey, reason);
    });
}

// 绑定口令码到用户
function bindCDKeyToUser(uid, cdkey, reason) {
    const data = {
        uid: uid,
        cdkey: cdkey,
        reason: reason || '管理员手动绑定'
    };

    fetch('/admin/api/users/bind-cdkey', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 200) {
            showMessage('口令码绑定成功', 'success');
            closeModal('bind-cdkey-modal');
            closeModal('user-details-modal');
        } else {
            showMessage('绑定失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('绑定口令码失败:', error);
        showMessage('绑定失败', 'error');
    });
}

// 显示用户口令码使用记录模态框
function showUserCDKeyModal(uid) {
    // 先移除已存在的模态框
    const existingModal = document.getElementById('user-cdkey-modal');
    if (existingModal) {
        existingModal.remove();
    }

    // 创建模态框
    const modal = document.createElement('div');
    modal.id = 'user-cdkey-modal';
    modal.className = 'modal';
    modal.style.display = 'flex';

    // 获取用户口令码使用记录
    fetch(`/admin/api/users/cdkeys?uid=${uid}`)
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                const records = data.data;
                let contentHtml = '';

                if (records && records.length > 0) {
                    // 有使用记录
                    const recordsHtml = records.map(record => `
                        <tr style="border-bottom: 1px solid #e2e8f0;">
                            <td style="padding: 12px; text-align: center; font-family: monospace; font-size: 12px;">${record.cdkey}</td>
                            <td style="padding: 12px; text-align: center;">${record.type || '未知'}</td>
                            <td style="padding: 12px; text-align: center;">${record.reason || '-'}</td>
                            <td style="padding: 12px; text-align: center; font-size: 12px;">${record.use_time}</td>
                        </tr>
                    `).join('');

                    contentHtml = `
                        <p style="color: #4a5568; margin-bottom: 15px;">该用户共使用了 ${records.length} 个口令码：</p>
                        <div class="modal-scroll-container">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead style="background: #f7fafc;">
                                    <tr>
                                        <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">口令码</th>
                                        <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">类型</th>
                                        <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">说明</th>
                                        <th style="padding: 12px; text-align: center; border-bottom: 1px solid #e2e8f0;">使用时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${recordsHtml}
                                </tbody>
                            </table>
                        </div>
                    `;
                } else {
                    // 没有使用记录
                    contentHtml = `
                        <div style="text-align: center; padding: 40px; color: #718096;">
                            <div style="font-size: 48px; margin-bottom: 16px;">🎫</div>
                            <h4 style="margin: 0 0 8px 0; color: #4a5568;">该用户没有使用口令码</h4>
                            <p style="margin: 0; color: #718096;">此用户尚未使用任何口令码</p>
                        </div>
                    `;
                }

                modal.innerHTML = `
                    <div class="modal-content" style="max-width: 800px; width: 90vw;">
                        <div class="modal-header">
                            <h3>用户口令码使用记录</h3>
                            <button type="button" class="modal-close" onclick="closeModal('user-cdkey-modal')">&times;</button>
                        </div>
                        <div class="modal-body">
                            ${contentHtml}
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
            } else {
                showMessage('获取用户口令码记录失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取用户口令码记录失败:', error);
            showMessage('获取口令码记录失败', 'error');
        });
}



// 加载口令码数据
function loadCDKeysData() {
    hasMoreCDKeys = true; // 重置分页状态
    loadCDKeysPage(1);
}

// 加载指定页的口令码数据
function loadCDKeysPage(page) {
    currentCDKeysPage = page;
    const limit = 20; // 每页20条记录

    // 获取搜索条件
    const searchTerm = document.getElementById('cdkey-search-input') ? document.getElementById('cdkey-search-input').value.trim() : '';
    const typeFilter = document.getElementById('cdkey-type-filter') ? document.getElementById('cdkey-type-filter').value : '';
    const statusFilter = document.getElementById('cdkey-status-filter') ? document.getElementById('cdkey-status-filter').value : '';

    // 构建查询参数
    let url = `/admin/api/cdkeys?page=${page}&limit=${limit}`;
    const params = [];

    if (searchTerm) {
        params.push(`search=${encodeURIComponent(searchTerm)}`);
    }
    if (typeFilter) {
        params.push(`type=${encodeURIComponent(typeFilter)}`);
    }
    if (statusFilter) {
        params.push(`status=${encodeURIComponent(statusFilter)}`);
    }

    if (params.length > 0) {
        url += '&' + params.join('&');
    }

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                // 检查是否还有更多数据
                hasMoreCDKeys = data.data && data.data.length === limit;
                displayCDKeysTable(data.data);
            } else {
                document.getElementById('cdkeys-content').innerHTML = '<p style="color: #e53e3e;">加载口令码数据失败: ' + data.message + '</p>';
            }
        })
        .catch(error => {
            console.error('加载口令码数据失败:', error);
            document.getElementById('cdkeys-content').innerHTML = '<p style="color: #e53e3e;">加载口令码数据失败</p>';
        });
}

// 显示口令码表格
function displayCDKeysTable(cdkeys) {
    const tableBody = document.getElementById('cdkeys-table-body');
    if (!tableBody) {
        console.error('口令码表格tbody元素未找到');
        return;
    }

    let html = '';

    if (cdkeys && cdkeys.length > 0) {
        cdkeys.forEach(cdkey => {
            // 生成状态文本和颜色
            let statusText, statusColor;
            if (cdkey.remain_num <= 0) {
                statusText = '已使用';
                statusColor = '#e53e3e'; // 红色
            } else if (cdkey.end_time && new Date(cdkey.end_time) < new Date()) {
                statusText = '已过期';
                statusColor = '#d69e2e'; // 橙色
            } else if (cdkey.start_time && new Date(cdkey.start_time) > new Date()) {
                statusText = '未开始';
                statusColor = '#718096'; // 灰色
            } else {
                statusText = `可用(剩余${cdkey.remain_num})`;
                statusColor = '#38a169'; // 绿色
            }

            html += `
                <tr style="border-bottom: 1px solid #e2e8f0;">
                    <td style="padding: 12px; font-family: monospace; font-size: 12px; cursor: pointer; color: #2b6cb0;"
                        onclick="showCDKeyUsageModal('${cdkey.cdkey}')"
                        title="点击查看使用记录">${cdkey.cdkey}</td>
                    <td style="padding: 12px;">${cdkey.type || '通用'}</td>
                    <td style="padding: 12px;">
                        <span style="color: ${statusColor}; font-weight: bold;">
                            ${statusText}
                        </span>
                    </td>
                    <td style="padding: 12px; font-size: 12px;">${cdkey.start_time || '-'}</td>
                    <td style="padding: 12px; font-size: 12px;">${cdkey.end_time || '-'}</td>
                    <td style="padding: 12px; font-size: 12px;">${cdkey.describe}</td>
                </tr>`;
        });
    } else {
        html += `
            <tr>
                <td colspan="6" style="padding: 40px; text-align: center; color: #718096;">
                    暂无口令码数据
                </td>
            </tr>`;
    }

    // 更新表格内容
    tableBody.innerHTML = html;

    // 更新分页控件
    const paginationHtml = `
        <div style="display: flex; justify-content: center; align-items: center; margin-top: 20px; gap: 10px;">
            <button class="btn btn-secondary btn-small" onclick="loadCDKeysPage(currentCDKeysPage - 1)" ${currentCDKeysPage <= 1 ? 'disabled' : ''}>
                上一页
            </button>
            <span style="color: #4a5568; font-weight: 500; padding: 0 15px;">第 ${currentCDKeysPage} 页</span>
            <button class="btn btn-secondary btn-small" onclick="loadCDKeysPage(currentCDKeysPage + 1)" ${!hasMoreCDKeys ? 'disabled' : ''}>
                下一页
            </button>
        </div>`;

    // 查找或创建分页容器
    let paginationContainer = document.getElementById('cdkeys-pagination');
    if (!paginationContainer) {
        paginationContainer = document.createElement('div');
        paginationContainer.id = 'cdkeys-pagination';
        document.getElementById('cdkeys-content').appendChild(paginationContainer);
    }
    paginationContainer.innerHTML = paginationHtml;
}

// 显示创建口令码模态框
function showCreateCDKeyModal() {
    // 显示HTML中的模态框
    const modal = document.getElementById('cdkey-modal');
    modal.style.display = 'flex';
    modal.classList.add('show');

    // 初始化验证码
    refreshCaptcha();

    // 绑定表单提交事件
    const form = document.getElementById('cdkey-form');
    form.removeEventListener('submit', handleCDKeyFormSubmit);
    form.addEventListener('submit', handleCDKeyFormSubmit);

    // 初始化前缀建议
    updatePrefixSuggestions();

    // 如果前缀为空，自动填充智能前缀
    if (document.getElementById('prefix-input').value === '') {
        document.getElementById('prefix-input').value = generateTimestampPrefix();
    }

    // 初始化可使用数量字段显示状态
    toggleUsageCountField();
}

// 处理CDKey表单提交
function handleCDKeyFormSubmit(e) {
    e.preventDefault();
    validateAndCreateCDKeys();
}

// 切换可使用数量字段显示
function toggleUsageCountField() {
    const countInput = document.querySelector('input[name="count"]');
    const usageCountField = document.getElementById('usage-count-field');

    if (countInput && usageCountField) {
        const count = parseInt(countInput.value);
        if (count === 1) {
            usageCountField.style.display = 'block';
        } else {
            usageCountField.style.display = 'none';
        }
    }
}

// 切换时间字段显示
function toggleTimeFields() {
    const timeType = document.getElementById('time-type-select').value;
    const durationFields = document.getElementById('duration-fields');
    const fixedTimeFields = document.getElementById('fixed-time-fields');

    if (timeType === 'fixed') {
        durationFields.style.display = 'none';
        fixedTimeFields.style.display = 'block';
    } else {
        durationFields.style.display = 'block';
        fixedTimeFields.style.display = 'none';
    }
}

// 设置识别前缀
function setPrefix(prefix) {
    document.getElementById('prefix-input').value = prefix;
    document.getElementById('prefix-input').focus();
}

// 智能前缀建议
function updatePrefixSuggestions() {
    const type = document.getElementById('cdkey-type-select').value;
    let suggestions = [];

    switch(type) {
        case 'customize':
            suggestions = ['VIP', 'CUSTOM', 'SPECIAL', 'PREMIUM', 'GOLD'];
            break;
        case 'client':
            suggestions = ['CLIENT', 'EQUIP', 'GEAR', 'TOOL', 'HELPER'];
            break;
        case 'drawtimes':
            suggestions = ['DRAW', 'LUCKY', 'LOTTERY', 'CHANCE', 'SPIN'];
            break;
        default:
            suggestions = ['VIP', 'GIFT', 'EVENT', 'PROMO', 'TEST'];
    }

    let html = '智能建议：';
    for (let i = 0; i < suggestions.length; i++) {
        html += '<a href="javascript:void(0)" onclick="setPrefix(\'' + suggestions[i] + '\')" class="prefix-suggestion">' + suggestions[i] + '</a>';
        if (i < suggestions.length - 1) html += ' | ';
    }

    document.getElementById('prefix-suggestions').innerHTML = html;
}

// 自动生成带时间戳的前缀
function generateTimestampPrefix() {
    const type = document.getElementById('cdkey-type-select').value;
    let typePrefix = '';

    switch(type) {
        case 'customize': typePrefix = 'CUSTOM'; break;
        case 'client': typePrefix = 'CLIENT'; break;
        case 'drawtimes': typePrefix = 'DRAW'; break;
        default: typePrefix = 'CDK';
    }

    const now = new Date();
    const timestamp = (now.getMonth() + 1).toString().padStart(2, '0') +
                    now.getDate().toString().padStart(2, '0') +
                    now.getHours().toString().padStart(2, '0') +
                    now.getMinutes().toString().padStart(2, '0');

    return typePrefix + timestamp + '_';
}

// 生成验证码
function generateCaptcha() {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = 0; i < 4; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// 刷新验证码
function refreshCaptcha() {
    const captcha = generateCaptcha();
    document.getElementById('captcha-display').textContent = captcha;
    document.getElementById('captcha-answer').value = captcha;
    document.getElementById('captcha-input').value = '';
}

// 验证并创建CDKEY
function validateAndCreateCDKeys() {
    // 验证验证码
    const inputCaptcha = document.getElementById('captcha-input').value.toUpperCase();
    const correctCaptcha = document.getElementById('captcha-answer').value;

    if (inputCaptcha !== correctCaptcha) {
        showMessage('验证码错误，请重新输入', 'error');
        refreshCaptcha();
        return;
    }

    // 验证码正确，继续创建CDKEY
    createCDKeys();
}

// 创建口令码
function createCDKeys() {
    const form = document.getElementById('cdkey-form');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // 转换数值类型
    data.count = parseInt(data.count);
    if (data.usage_count) {
        data.usage_count = parseInt(data.usage_count);
    }

    // 保存批次信息供下载时使用
    window.cdkeyBatchInfo = {...data};

    // 处理时间类型和持续时间
    if (data.time_type === 'duration') {
        data.duration = parseInt(data.duration);
        // 清空固定时间字段
        delete data.fixed_time;
    } else if (data.time_type === 'fixed') {
        // 清空持续时间字段
        delete data.duration;
        // 如果有固定时间，保留它
        if (data.fixed_time) {
            data.fixed_time = data.fixed_time + ' 23:59:59';
        }
    }

    // 处理激活时间范围
    if (data.start_time) {
        data.start_time = data.start_time + ' 00:00:00';
    }
    if (data.end_time) {
        data.end_time = data.end_time + ' 23:59:59';
    }

    fetch('/admin/api/cdkeys', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 200) {
            closeModal('cdkey-modal');
            loadCDKeysData();
            showMessage(`成功生成 ${data.data.count} 个口令码`, 'success');

            // 显示生成的口令码列表
            if (data.data.cdkeys && data.data.cdkeys.length > 0) {
                showGeneratedCDKeys(data.data.cdkeys);
            }
        } else {
            showMessage('生成失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('生成口令码失败:', error);
        showMessage('生成失败', 'error');
    });
}

// 显示口令码使用记录模态框
function showCDKeyUsageModal(cdkey) {
    // 显示模态框
    const modal = document.getElementById('cdkey-usage-records-modal');
    if (!modal) {
        showMessage('口令码使用记录模态框不存在', 'error');
        return;
    }

    showModal('cdkey-usage-records-modal');

    // 显示加载状态
    const contentDiv = document.getElementById('cdkey-usage-records-content');
    if (contentDiv) {
        contentDiv.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #718096;">
                <div style="margin-bottom: 10px;">加载中...</div>
                <div style="font-size: 12px;">正在获取口令码 ${cdkey} 的使用记录</div>
            </div>
        `;
    }

    // 获取使用记录
    fetch(`/admin/api/cdkeys/usage?cdkey=${encodeURIComponent(cdkey)}`)
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                const responseData = data.data;
                const records = responseData.records || [];
                const adminName = responseData.admin_name || '未知';
                const totalCount = responseData.total_count || 0;
                let contentHtml = '';

                if (records && records.length > 0) {
                    // 有使用记录
                    const recordsHtml = records.map((record, index) => `
                        <tr style="border-bottom: 1px solid #f1f5f9; background: ${index % 2 === 0 ? '#ffffff' : '#fafbfc'}; transition: background-color 0.2s ease;"
                            onmouseover="this.style.backgroundColor='#f0f9ff';"
                            onmouseout="this.style.backgroundColor='${index % 2 === 0 ? '#ffffff' : '#fafbfc'}';">
                            <td style="padding: 15px 12px; text-align: center; color: #374151;">${record.uid}</td>
                            <td style="padding: 15px 12px; text-align: center; font-family: monospace; color: #1f2937; font-weight: 500;">${record.qq || '未知'}</td>
                            <td style="padding: 15px 12px; text-align: center; color: #374151;">${record.username || '未知用户'}</td>
                            <td style="padding: 15px 12px; text-align: center; font-size: 13px; color: #6b7280;">${record.use_time}</td>
                        </tr>
                    `).join('');

                    contentHtml = `
                        <div style="margin-bottom: 20px;">
                            <p style="color: #4a5568; margin-bottom: 15px; font-size: 14px; text-align: left;">
                                此口令码共被使用 <strong style="color: #2b6cb0;">${totalCount}</strong> 次${totalCount >= 50 ? '（显示最近50条记录）' : ''}
                            </p>
                            <div style="border: 1px solid #e2e8f0; border-radius: 8px; overflow: hidden; max-height: 400px; overflow-y: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead style="background: #f8f9fa; position: sticky; top: 0;">
                                        <tr>
                                            <th style="padding: 15px 12px; text-align: center; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #374151;">用户ID</th>
                                            <th style="padding: 15px 12px; text-align: center; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #374151;">QQ号</th>
                                            <th style="padding: 15px 12px; text-align: center; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #374151;">用户名</th>
                                            <th style="padding: 15px 12px; text-align: center; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #374151;">使用时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${recordsHtml}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    `;
                } else {
                    // 没有使用记录
                    contentHtml = `
                        <div style="text-align: center; padding: 40px; color: #718096;">
                            <div style="font-size: 48px; margin-bottom: 16px;">📝</div>
                            <p style="margin: 0; color: #718096;">此口令码尚未被任何用户使用</p>
                        </div>
                    `;
                }

                // 更新模态框内容
                if (contentDiv) {
                    contentDiv.innerHTML = `
                        <div style="padding: 20px;">
                            <div style="margin-bottom: 20px; padding: 15px; background: #f7fafc; border-radius: 8px; border-left: 4px solid #2b6cb0;">
                                <strong style="color: #2b6cb0;">口令码：</strong>
                                <span style="font-family: monospace; color: #2d3748; font-size: 16px; font-weight: 600;">${cdkey}</span>
                                <span style="margin-left: 15px; color: #718096; font-size: 14px;">由 ${adminName} 生成</span>
                            </div>
                            ${contentHtml}
                        </div>
                    `;
                }
            } else {
                if (contentDiv) {
                    contentDiv.innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #e53e3e;">
                            <div style="font-size: 48px; margin-bottom: 16px;">❌</div>
                            <p style="margin: 0;">获取使用记录失败: ${data.message}</p>
                        </div>
                    `;
                }
                showMessage('获取使用记录失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取口令码使用记录失败:', error);
            if (contentDiv) {
                contentDiv.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #e53e3e;">
                        <div style="font-size: 48px; margin-bottom: 16px;">❌</div>
                        <p style="margin: 0;">网络错误，请稍后重试</p>
                    </div>
                `;
            }
            showMessage('获取使用记录失败', 'error');
        });
}

// 显示生成的口令码列表
function showGeneratedCDKeys(cdkeys) {
    const modal = document.createElement('div');
    modal.id = 'generated-cdkeys-modal';
    modal.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background: rgba(0,0,0,0.5); display: flex; align-items: center;
        justify-content: center; z-index: 1001;
    `;

    const cdkeyList = cdkeys.map(cdkey => `<div style="font-family: monospace; padding: 5px; background: #f7fafc; margin: 2px 0; border-radius: 3px;">${cdkey}</div>`).join('');

    modal.innerHTML = `
        <div style="background: white; padding: 30px; border-radius: 8px; width: 600px; max-width: 90vw; max-height: 80vh; overflow-y: auto; position: relative;">
            <button onclick="closeModal('generated-cdkeys-modal')" style="
                position: absolute; top: 12px; right: 12px; background: none; border: none;
                font-size: 24px; color: #718096; cursor: pointer; padding: 8px; line-height: 1;
                border-radius: 6px; transition: all 0.2s ease; width: 40px; height: 40px;
                display: flex; align-items: center; justify-content: center;
            " onmouseover="this.style.background='#f7fafc'; this.style.color='#2d3748';"
               onmouseout="this.style.background='none'; this.style.color='#718096';">×</button>
            <div class="modal-header">
                <h3>生成的口令码</h3>
            </div>
            <p style="color: #4a5568; margin-bottom: 15px;">共生成 ${cdkeys.length} 个口令码：</p>
            <div style="max-height: 400px; overflow-y: auto; border: 1px solid #e2e8f0; padding: 15px; border-radius: 6px;">
                ${cdkeyList}
            </div>
            <div class="form-actions" style="margin-top: 20px;">
                <button class="btn" onclick="copyCDKeys()">
                    复制全部
                </button>
                <button class="btn btn-success" onclick="downloadCDKeys()">
                    导出下载
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 存储口令码列表供复制使用
    window.generatedCDKeysList = cdkeys;

    // 自动导出下载
    setTimeout(() => {
        downloadCDKeys();
        showMessage('口令码已自动导出下载', 'success');
    }, 1000);
}

// 复制口令码
function copyCDKeys() {
    if (window.generatedCDKeysList) {
        const text = window.generatedCDKeysList.join('\n');
        navigator.clipboard.writeText(text).then(() => {
            showMessage('口令码已复制到剪贴板', 'success');
        }).catch(() => {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showMessage('口令码已复制到剪贴板', 'success');
        });
    }
}

// 下载口令码文件
function downloadCDKeys() {
    if (window.generatedCDKeysList && window.cdkeyBatchInfo) {
        // 构建批次信息头部
        const batchInfo = window.cdkeyBatchInfo;
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');

        let header = `# 口令码批次信息 - 生成时间: ${new Date().toLocaleString('zh-CN')}\n`;
        header += `# 生成数量: ${batchInfo.count}\n`;
        header += `# 口令码类型: ${batchInfo.type}\n`;
        header += `# 时间类型: ${batchInfo.time_type}\n`;
        if (batchInfo.duration) {
            header += `# 激活天数: ${batchInfo.duration}天\n`;
        }
        if (batchInfo.fixed_time) {
            header += `# 过期时间: ${batchInfo.fixed_time}\n`;
        }
        if (batchInfo.prefix) {
            header += `# 前缀: ${batchInfo.prefix}\n`;
        }
        if (batchInfo.description) {
            header += `# 描述: ${batchInfo.description}\n`;
        }
        if (batchInfo.start_time) {
            header += `# 激活开放时间: ${batchInfo.start_time}\n`;
        }
        if (batchInfo.end_time) {
            header += `# 激活结束时间: ${batchInfo.end_time}\n`;
        }
        if (batchInfo.usage_count) {
            header += `# 可使用数量: ${batchInfo.usage_count}\n`;
        }
        header += `# ==========================================\n\n`;

        const text = header + window.generatedCDKeysList.join('\n');
        const filename = `cdkeys_${timestamp}.txt`;

        // 创建下载链接
        const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';

        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        showMessage(`口令码已导出为 ${filename}`, 'success');
    }
}



// 加载管理员数据
async function loadAdminsData() {
    try {
        const response = await fetch('/admin/api/admins');
        const data = await response.json();
        if (data.code == 200) {
            await displayAdminsTable(data.data);
            // 检查当前用户权限并显示添加按钮
            checkAdminPermissionAndShowButton();
        } else {
            document.getElementById('admins-content').innerHTML = '<p style="color: #e53e3e;">加载管理员数据失败: ' + data.message + '</p>';
        }
    } catch (error) {
        console.error('加载管理员数据失败:', error);
        document.getElementById('admins-content').innerHTML = '<p style="color: #e53e3e;">加载管理员数据失败</p>';
    }
}

// 检查管理员权限并显示添加按钮
function checkAdminPermissionAndShowButton() {
    // 这里应该从服务器获取当前用户权限，暂时假设有权限
    // TODO: 实现真实的权限检查
    const addButton = document.getElementById('add-admin-btn');
    if (addButton) {
        addButton.style.display = 'inline-block';
    }
}

// 显示管理员表格
async function displayAdminsTable(admins) {
    // 确保获取到管理员信息
    await getCurrentAdminInfo();

    // 检查当前用户是否为超级管理员
    const currentUserPower = getCurrentUserPower();
    const currentUserID = getCurrentUserID();
    const isCurrentUserSuper = isCurrentUserSuperAdmin();

    // 调试信息
    console.log('当前用户权限信息:', {
        power: currentUserPower,
        uid: currentUserID,
        isSuper: isCurrentUserSuper,
        adminInfo: currentAdminInfo
    });

    const tableBody = document.getElementById('admins-table-body');
    if (!tableBody) {
        console.error('管理员表格tbody元素未找到');
        return;
    }

    let html = '';

    admins.forEach(admin => {
        const statusText = admin.is_action === 1 ? '启用' : '禁用';
        const statusColor = admin.is_action === 1 ? '#38a169' : '#e53e3e';
        const typeText = admin.is_super ? '超级管理员' : '普通管理员';
        const typeColor = admin.is_super ? '#d69e2e' : '#3182ce';

        // 获取权限显示文本
        const powerCount = admin.power ? admin.power.split(',').length : 0;

        html += `
            <tr style="border-bottom: 1px solid #e2e8f0;">
                <td style="padding: 12px;">${admin.uid}</td>
                <td style="padding: 12px;">${admin.username}</td>
                <td style="padding: 12px;">
                    <span style="color: #3182ce; font-weight: bold; cursor: pointer; text-decoration: underline;" onclick="showAdminPermissions(${admin.uid})" title="点击查看详细权限">${powerCount}个权限</span>
                </td>
                <td style="padding: 12px;">
                    <span style="background: ${typeColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                        ${typeText}
                    </span>
                </td>
                <td style="padding: 12px;">
                    <span style="color: ${statusColor}; font-weight: bold;">
                        ${statusText}
                    </span>
                </td>
                <td style="padding: 12px;">
                    ${isCurrentUserSuper ? `
                        <button class="btn btn-small" onclick="editAdmin(${admin.uid})">
                            编辑
                        </button>
                    ` : `
                        <span style="color: #718096; font-size: 12px;">仅超级管理员可操作</span>
                    `}
                </td>
            </tr>`;
    });

    // 更新表格内容
    tableBody.innerHTML = html;
}

// 当前管理员信息缓存
let currentAdminInfo = null;

// 获取当前管理员信息
async function getCurrentAdminInfo() {
    if (currentAdminInfo) {
        return currentAdminInfo;
    }

    try {
        const response = await fetch('/admin/api/current');
        const data = await response.json();
        if (data.code == 200) {
            currentAdminInfo = data.data;
            return currentAdminInfo;
        }
    } catch (error) {
        console.error('获取管理员信息失败:', error);
    }

    return null;
}

// 获取当前用户权限
function getCurrentUserPower() {
    return currentAdminInfo ? currentAdminInfo.power || '' : '';
}

// 获取当前用户ID
function getCurrentUserID() {
    return currentAdminInfo ? currentAdminInfo.uid || '' : '';
}

// 检查当前用户是否为超级管理员
function isCurrentUserSuperAdmin() {
    return currentAdminInfo ? currentAdminInfo.is_super === true : false;
}

// 获取当前用户名
function getCurrentUsername() {
    return currentAdminInfo ? currentAdminInfo.username || '' : '';
}

// 显示创建管理员模态框
function showCreateAdminModal() {
    const content = `
        <form id="admin-form">
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; color: #4a5568;">用户名</label>
                <input type="text" name="username" required style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; color: #4a5568;">密码</label>
                <input type="password" name="password" required style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; color: #4a5568;">权限</label>
                <input type="text" name="power" placeholder="例如: 1,2,3,4,5" style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 20px;">
                <label style="display: flex; align-items: center; color: #4a5568;">
                    <input type="checkbox" name="is_super" style="margin-right: 8px;">
                    超级管理员
                </label>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn">
                    创建
                </button>
            </div>
        </form>
    `;

    const modal = createModal('admin-modal', '添加管理员', content, '500px');
    document.body.appendChild(modal);

    // 绑定表单提交事件
    document.getElementById('admin-form').addEventListener('submit', function(e) {
        e.preventDefault();
        createAdmin();
    });
}

// 创建管理员
function createAdmin() {
    const form = document.getElementById('admin-form');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // 转换布尔值
    data.is_super = formData.has('is_super');

    fetch('/admin/api/admins', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 200) {
            closeModal('admin-modal');
            loadAdminsData();
            showMessage('管理员创建成功', 'success');
        } else {
            showMessage('创建失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('创建管理员失败:', error);
        showMessage('创建失败', 'error');
    });
}

// 编辑管理员
function editAdmin(adminId) {
    // 获取管理员详情
    fetch(`/admin/api/admins/${adminId}`)
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                showEditAdminModal(data.data);
            } else {
                showMessage('获取管理员详情失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取管理员详情失败:', error);
            showMessage('获取管理员详情失败', 'error');
        });
}

// 显示编辑管理员模态框
function showEditAdminModal(admin) {
    const currentUserID = getCurrentUserID();
    const isEditingSelf = admin.uid.toString() === currentUserID.toString();

    const content = `
        <form id="edit-admin-form">
            <input type="hidden" name="id" value="${admin.uid}">
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; color: #4a5568;">用户名</label>
                <input type="text" name="username" value="${admin.username}" required style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; color: #4a5568;">新密码（留空则不修改）</label>
                <input type="password" name="password" style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; color: #4a5568;">
                    <input type="checkbox" name="is_super" ${admin.is_super ? 'checked' : ''} style="margin-right: 8px;">
                    超级管理员
                </label>
            </div>
            <div style="margin-bottom: 20px;">
                <label style="display: block; margin-bottom: 5px; color: #4a5568;">状态</label>
                <select name="is_action" style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px; ${isEditingSelf ? 'background: #f5f5f5; color: #999;' : ''}" ${isEditingSelf ? 'disabled' : ''}>
                    <option value="1" ${admin.is_action === 1 ? 'selected' : ''}>启用</option>
                    <option value="0" ${admin.is_action === 0 ? 'selected' : ''}>禁用</option>
                </select>
                ${isEditingSelf ? `
                    <input type="hidden" name="is_action" value="${admin.is_action}">
                    <small style="color: #718096; font-size: 12px; display: block; margin-top: 5px;">为了安全考虑，不能修改自己的状态</small>
                ` : ''}
            </div>
            <div class="form-actions">
                <button type="submit" class="btn">
                    更新
                </button>
            </div>
        </form>
    `;

    const modal = createModal('edit-admin-modal', '编辑管理员', content, '500px');

    document.body.appendChild(modal);

    // 绑定表单提交事件
    document.getElementById('edit-admin-form').addEventListener('submit', function(e) {
        e.preventDefault();
        updateAdmin();
    });
}

// 更新管理员
function updateAdmin() {
    const form = document.getElementById('edit-admin-form');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // 转换布尔值和数值
    data.is_super = formData.has('is_super');
    data.is_action = parseInt(data.is_action);

    const adminId = data.id;
    delete data.id; // 移除ID，不需要在请求体中发送

    fetch(`/admin/api/admins/${adminId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 200) {
            closeModal('edit-admin-modal');
            loadAdminsData();
            showMessage('管理员更新成功', 'success');
        } else {
            showMessage('更新失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('更新管理员失败:', error);
        showMessage('更新失败', 'error');
    });
}

// 删除管理员
function deleteAdmin(adminId) {
    if (!confirm('确定要删除这个管理员吗？')) {
        return;
    }

    fetch(`/admin/api/admins/${adminId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 200) {
            loadAdminsData();
            showMessage('管理员删除成功', 'success');
        } else {
            showMessage('删除失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('删除管理员失败:', error);
        showMessage('删除失败', 'error');
    });
}

// 显示风控配置模态框
function showRiskConfigModal() {
    // 先获取当前配置
    fetch('/admin/api/risk/config')
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                displayRiskConfigModal(data.data);
            } else {
                showMessage('获取风控配置失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取风控配置失败:', error);
            showMessage('获取风控配置失败', 'error');
        });
}

// 显示风控配置模态框
function displayRiskConfigModal(config) {
    const content = `
        <form id="risk-config-form">
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; color: #4a5568;">同设备每日最大QQ数量</label>
                <input type="number" name="max_qq_per_device_per_day" value="${config.max_qq_per_device_per_day || 3}" min="1" max="50" required style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                <small style="color: #718096; font-size: 12px; display: block; margin-top: 5px;">当同一设备在一天内登录不同QQ号超过此数量时触发风控</small>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; color: #4a5568;">同IP每日最大QQ数量</label>
                <input type="number" name="max_qq_per_ip_per_day" value="${config.max_qq_per_ip_per_day || 3}" min="1" max="100" required style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                <small style="color: #718096; font-size: 12px; display: block; margin-top: 5px;">当同一IP在一天内登录不同QQ号超过此数量时触发风控</small>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; color: #4a5568;">单QQ每日最大IP数量</label>
                <input type="number" name="max_devices_per_qq_per_day" value="${config.max_devices_per_qq_per_day || 3}" min="1" max="20" required style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                <small style="color: #718096; font-size: 12px; display: block; margin-top: 5px;">当单个QQ号在一天内从不同IP登录超过此数量时，进行地理位置检查。3个或更多城市登录时触发风控，1-2个城市仅记录事件</small>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; color: #4a5568;">每小时最大登录次数</label>
                <input type="number" name="max_login_attempts_per_hour" value="${config.max_login_attempts_per_hour || 20}" min="1" max="100" required style="width: 100%; padding: 10px; border: 1px solid #e2e8f0; border-radius: 4px;">
                <small style="color: #718096; font-size: 12px; display: block; margin-top: 5px;">当同一QQ号在一小时内登录次数超过此数量时触发风控</small>
            </div>
            <div style="margin-bottom: 15px;">
                <label style="display: flex; align-items: center; color: #4a5568;">
                    <input type="checkbox" name="enable_new_device_check" ${config.enable_new_device_check ? 'checked' : ''} style="margin-right: 8px;">
                    启用新设备检查
                </label>
                <small style="color: #718096; font-size: 12px; display: block; margin-left: 24px; margin-top: 5px;">是否对新设备登录进行额外检查</small>
            </div>
            <div style="margin-bottom: 20px;">
                <label style="display: block; color: #4a5568; margin-bottom: 8px;">高德地图API密钥</label>
                <input type="text" name="amap_api_key" value="${config.amap_api_key || ''}" style="width: 100%; padding: 8px; border: 1px solid #e2e8f0; border-radius: 4px;">
                <small style="color: #718096; font-size: 12px; display: block; margin-top: 5px;">用于多设备检测的高德地图API密钥，城市相同时算作同一设备</small>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn">
                    保存配置
                </button>
            </div>
        </form>
    `;

    const modal = createModal('risk-config-modal', '风控配置', content, '600px');
    document.body.appendChild(modal);

    // 绑定表单提交事件
    document.getElementById('risk-config-form').addEventListener('submit', function(e) {
        e.preventDefault();
        updateRiskConfig();
    });
}

// 更新风控配置
function updateRiskConfig() {
    const form = document.getElementById('risk-config-form');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // 处理复选框
    data.enable_new_device_check = form.enable_new_device_check.checked;

    // 处理文本字段
    data.amap_api_key = form.amap_api_key.value;

    fetch('/admin/api/risk/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 200) {
            closeModal('risk-config-modal');
            showMessage('风控配置更新成功', 'success');
        } else {
            showMessage('更新失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('更新风控配置失败:', error);
        showMessage('更新失败', 'error');
    });
}

// 加载风控数据
function loadRiskData() {
    hasMoreRisk = true; // 重置分页状态
    loadRiskDataPage(1);
}

// 加载指定页的风控数据
function loadRiskDataPage(page) {
    if (page < 1) page = 1;
    currentRiskPage = page;

    fetch(`/admin/api/risk/events?page=${page}&limit=20`)
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                // 检查是否还有更多数据
                hasMoreRisk = data.data && data.data.length === 20;
                displayRiskTable(data.data);
            } else {
                document.getElementById('risk-content').innerHTML = '<p style="color: #e53e3e;">加载风控数据失败: ' + data.message + '</p>';
            }
        })
        .catch(error => {
            console.error('加载风控数据失败:', error);
            document.getElementById('risk-content').innerHTML = '<p style="color: #e53e3e;">加载风控数据失败</p>';
        });
}

// 显示风控表格
function displayRiskTable(events) {
    // 获取风险等级和状态的中文显示
    function getRiskLevelText(severity) {
        const levels = {
            'low': '低风险',
            'medium': '中风险',
            'high': '高风险',
            'critical': '严重风险'
        };
        return levels[severity] || severity || '未知';
    }

    function getRiskLevelColor(severity) {
        const colors = {
            'low': '#38a169',
            'medium': '#d69e2e',
            'high': '#e53e3e',
            'critical': '#9f1239'
        };
        return colors[severity] || '#718096';
    }

    function getStatusText(severity, status, processedBy) {
        if (severity === 'low') {
            return '低风险不触发封禁';
        }

        if (status === 'pending') {
            return null;
        }

        const statusTexts = {
            'approved': '已解封',
            'rejected': '已封禁',
        };

        return processedBy + statusTexts[status];
    }

    function getStatusColor(status) {
        const colors = {
            'pending': '#d69e2e',
            'approved': '#38a169',
            'rejected': '#e53e3e',
            'ignored': '#718096'
        };
        return colors[status] || '#718096';
    }

    // 直接填充现有表格的tbody
    const tableBody = document.getElementById('risk-table-body');
    if (!tableBody) {
        console.error('风控表格tbody元素未找到');
        return;
    }

    let html = '';

    if (events && events.length > 0) {
        // 过滤掉本地忽略的事件（仅对待处理状态的事件进行本地忽略过滤）
        const filteredEvents = events.filter(event => {
            if (event.status === 'pending') {
                return !isLocallyIgnored(event.id);
            }
            return true; // 已处理的事件始终显示
        });

        if (filteredEvents.length > 0) {
            filteredEvents.forEach(event => {
                const riskLevel = getRiskLevelText(event.severity);
                const riskColor = getRiskLevelColor(event.severity);
                const statusText = getStatusText(event.severity, event.status, event.processed_by);
                const statusColor = getStatusColor(event.status);

                html += `
                    <tr style="border-bottom: 1px solid #e2e8f0;">
                        <td style="padding: 12px; font-size: 12px; text-align: center;">${event.id}</td>
                        <td style="padding: 12px; font-size: 12px; text-align: center;">${event.qq_numbers || '未知'}</td>
                        <td style="padding: 12px; font-size: 12px; text-align: center;" title="${event.description || '无描述'}">${event.description || '无描述'}</td>
                        <td style="padding: 12px; font-size: 12px; text-align: center;">
                            <span style="color: ${riskColor}; font-weight: bold;">${riskLevel}</span>
                        </td>
                        <td style="padding: 12px; font-size: 12px; text-align: center;">${event.created_at}</td>
                        <td style="padding: 12px; text-align: center;">
                            ${event.status === 'pending' && event.severity !== 'low' ? `
                                <div class="action-buttons" style="justify-content: center;">
                                    <button class="btn btn-success btn-small" onclick="processRiskEvent(${event.id}, 'approve')">
                                        解封
                                    </button>
                                    <button class="btn btn-danger btn-small" onclick="processRiskEvent(${event.id}, 'reject')">
                                        封禁
                                    </button>
                                    <button class="btn btn-secondary btn-small" onclick="processRiskEvent(${event.id}, 'ignore')">
                                        忽略
                                    </button>
                                </div>
                            ` : `
                                <span style="color: ${statusColor}; font-size: 12px; font-weight: 500;">${statusText}</span>
                            `}
                        </td>
                    </tr>`;
            });
        } else {
            html += `
                <tr>
                    <td colspan="7" style="padding: 40px; text-align: center; color: #718096;">
                        ${events.length > 0 ? '所有事件已被忽略' : '暂无风控事件'}
                    </td>
                </tr>`;
        }
    } else {
        html += `
            <tr>
                <td colspan="7" style="padding: 40px; text-align: center; color: #718096;">
                    暂无风控事件
                </td>
            </tr>`;
    }

    // 填充表格内容
    tableBody.innerHTML = html;

    // 更新分页按钮
    const paginationDiv = document.getElementById('risk-pagination');
    if (paginationDiv) {
        paginationDiv.innerHTML = `
            <button class="btn btn-secondary btn-small" onclick="loadRiskDataPage(currentRiskPage - 1)" ${currentRiskPage <= 1 ? 'disabled' : ''}>
                上一页
            </button>
            <span style="color: #4a5568; font-weight: 500; padding: 0 15px;">第 ${currentRiskPage} 页</span>
            <button class="btn btn-secondary btn-small" onclick="loadRiskDataPage(currentRiskPage + 1)" ${!hasMoreRisk ? 'disabled' : ''}>
                下一页
            </button>
        `;
    }
}

// 本地忽略状态管理
const LOCAL_IGNORE_KEY = 'risk_events_ignored';

// 获取当前管理员的忽略列表
function getLocalIgnoredEvents() {
    try {
        const ignored = localStorage.getItem(LOCAL_IGNORE_KEY);
        return ignored ? JSON.parse(ignored) : [];
    } catch (e) {
        console.error('读取本地忽略状态失败:', e);
        return [];
    }
}

// 添加事件到本地忽略列表
function addToLocalIgnored(eventId) {
    try {
        const ignored = getLocalIgnoredEvents();
        if (!ignored.includes(eventId)) {
            ignored.push(eventId);
            localStorage.setItem(LOCAL_IGNORE_KEY, JSON.stringify(ignored));
        }
    } catch (e) {
        console.error('保存本地忽略状态失败:', e);
    }
}

// 从本地忽略列表移除事件
function removeFromLocalIgnored(eventId) {
    try {
        const ignored = getLocalIgnoredEvents();
        const index = ignored.indexOf(eventId);
        if (index > -1) {
            ignored.splice(index, 1);
            localStorage.setItem(LOCAL_IGNORE_KEY, JSON.stringify(ignored));
        }
    } catch (e) {
        console.error('更新本地忽略状态失败:', e);
    }
}

// 检查事件是否被本地忽略
function isLocallyIgnored(eventId) {
    const ignored = getLocalIgnoredEvents();
    return ignored.includes(eventId);
}

// 处理风控事件
function processRiskEvent(eventId, action) {
    const actionText = {
        'approve': '解封',
        'reject': '封禁',
        'ignore': '忽略'
    };

    // 如果是忽略操作，使用本地缓存
    if (action === 'ignore') {
        if (!confirm(`确定要${actionText[action]}这个风控事件吗？\n注意：这只会在您的界面中隐藏此事件，其他管理员仍可以看到。`)) {
            return;
        }

        addToLocalIgnored(eventId);
        showMessage('事件已忽略（仅对您生效）', 'success');
        loadRiskData(); // 重新加载数据
        return;
    }

    // 其他操作（approve/reject）仍然发送到服务器
    if (!confirm(`确定要${actionText[action]}这个风控事件吗？`)) {
        return;
    }

    fetch('/admin/api/risk/events/' + eventId, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: action,
            reason: `管理员${actionText[action]}处理`
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 200) {
            showMessage(`风控事件${actionText[action]}成功`, 'success');
            // 如果处理成功，从本地忽略列表中移除（如果存在）
            removeFromLocalIgnored(eventId);
            loadRiskData(); // 重新加载数据
        } else {
            showMessage(`处理失败: ${data.message}`, 'error');
        }
    })
    .catch(error => {
        console.error('处理风控事件失败:', error);
        showMessage('处理失败', 'error');
    });
}



// 加载更新数据
function loadUpdatesData() {
    fetch('/admin/api/updates')
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                displayUpdatesTable(data.data);
            } else {
                const tableBody = document.getElementById('updates-table-body');
                if (tableBody) {
                    tableBody.innerHTML = '<tr><td colspan="7" style="padding: 40px; text-align: center; color: #e53e3e;">加载更新数据失败: ' + data.message + '</td></tr>';
                }
            }
        })
        .catch(error => {
            console.error('加载更新数据失败:', error);
            const tableBody = document.getElementById('updates-table-body');
            if (tableBody) {
                tableBody.innerHTML = '<tr><td colspan="7" style="padding: 40px; text-align: center; color: #e53e3e;">加载更新数据失败</td></tr>';
            }
        });
}

// 显示更新表格
function displayUpdatesTable(updates) {
    const tableBody = document.getElementById('updates-table-body');
    if (!tableBody) {
        console.error('更新表格tbody元素未找到');
        return;
    }

    let html = '';

    if (updates && updates.length > 0) {
        updates.forEach(update => {
            const statusColor = update.is_active ? '#38a169' : '#e53e3e';
            const statusText = update.is_active ? '启用' : '禁用';

            html += `
                <tr style="border-bottom: 1px solid #e2e8f0;">
                    <td style="padding: 12px; text-align: center; font-family: monospace; font-size: 12px;">${update.id}</td>
                    <td style="padding: 12px; text-align: center;">${update.name}</td>
                    <td style="padding: 12px; text-align: center; font-family: monospace;">${update.version}</td>
                    <td style="padding: 12px; text-align: center; max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                        <a href="${update.url}" target="_blank" style="color: #3182ce; text-decoration: none; font-size: 12px;">
                            ${update.url}
                        </a>
                    </td>
                    <td style="padding: 12px; text-align: center;">
                        <span style="color: ${statusColor}; font-weight: bold;">
                            ${statusText}
                        </span>
                    </td>
                    <td style="padding: 12px; text-align: center; font-size: 12px;">${update.updated_at || update.created_at}</td>
                    <td style="padding: 12px; text-align: center;">
                        <div class="action-buttons">
                            <button class="btn btn-small" onclick="editUpdate(${update.id})">
                                编辑
                            </button>
                            <button class="btn ${update.is_active ? 'btn-danger' : 'btn-success'} btn-small" onclick="toggleUpdate(${update.id})">
                                ${update.is_active ? '禁用' : '启用'}
                            </button>
                        </div>
                    </td>
                </tr>`;
        });
    } else {
        html += `
            <tr>
                <td colspan="7" style="padding: 40px; text-align: center; color: #718096;">
                    暂无更新配置
                </td>
            </tr>`;
    }

    // 更新表格内容
    tableBody.innerHTML = html;
}

// 编辑更新配置
function editUpdate(updateId) {
    // 先获取当前配置
    fetch(`/admin/api/updates/${updateId}`)
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                showEditUpdateModal(data.data);
            } else {
                showMessage('获取更新配置失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取更新配置失败:', error);
            showMessage('获取更新配置失败', 'error');
        });
}

// 显示编辑更新配置模态框
function showEditUpdateModal(updateData) {
    // 显示模态框
    const modal = document.getElementById('edit-update-modal');
    if (!modal) {
        showMessage('编辑更新配置模态框不存在', 'error');
        return;
    }

    showModal('edit-update-modal');

    // 等待模态框显示后再填充数据
    setTimeout(() => {
        const idField = document.getElementById('edit-update-id');
        const nameField = document.getElementById('edit-update-name');
        const versionField = document.getElementById('edit-update-version');
        const urlField = document.getElementById('edit-update-url');
        const checksumField = document.getElementById('edit-update-checksum');
        const activeField = document.getElementById('edit-update-active');
        const forceField = document.getElementById('edit-update-force');

        if (idField) idField.value = updateData.id;
        if (nameField) nameField.value = updateData.name;
        if (versionField) versionField.value = updateData.version;
        if (urlField) urlField.value = updateData.url;
        if (checksumField) checksumField.value = updateData.checksum || '';
        if (activeField) activeField.checked = updateData.is_active;
        if (forceField) forceField.checked = updateData.force || false;
    }, 50);

    // 绑定表单提交事件（如果还没有绑定）
    const form = document.getElementById('edit-update-form');
    if (form && !form.hasAttribute('data-bound')) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            updateUpdateConfig();
        });
        form.setAttribute('data-bound', 'true');
    }
}

// 更新配置
function updateUpdateConfig() {
    const form = document.getElementById('edit-update-form');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // 处理复选框
    data.is_active = formData.has('is_active');
    data.force = formData.has('force');

    const updateId = data.id;
    delete data.id; // 移除ID，不需要在请求体中发送

    fetch(`/admin/api/updates/${updateId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 200) {
            closeModal('edit-update-modal');
            loadUpdatesData();
            showMessage('更新配置修改成功', 'success');
        } else {
            showMessage('修改失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('更新配置失败:', error);
        showMessage('修改失败', 'error');
    });
}


// 切换更新配置状态
function toggleUpdate(updateId) {
    if (!confirm('确定要切换此配置的状态吗？')) {
        return;
    }

    // 先获取当前配置，然后切换状态
    fetch(`/admin/api/updates/${updateId}`)
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                const currentConfig = data.data;
                // 切换状态
                const updatedConfig = {
                    name: currentConfig.name,
                    version: currentConfig.version,
                    url: currentConfig.url,
                    checksum: currentConfig.checksum || '',
                    is_active: !currentConfig.is_active  // 切换状态
                };

                // 发送更新请求
                return fetch(`/admin/api/updates/${updateId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updatedConfig)
                });
            } else {
                throw new Error(data.message || '获取配置失败');
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code == 200) {
                showMessage('状态切换成功', 'success');
                loadUpdatesData(); // 重新加载数据
            } else {
                showMessage('状态切换失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('切换状态失败:', error);
            showMessage('状态切换失败: ' + (error.message || '未知错误'), 'error');
        });
}

// 登出
function logout() {
    if (confirm('确定要退出登录吗？')) {
        fetch('/admin/api/logout', {
            method: 'POST'
        })
        .then(() => {
            window.location.href = '/admin/login';
        })
        .catch(error => {
            console.error('登出失败:', error);
            window.location.href = '/admin/login';
        });
    }
}

// 显示管理员权限详情
async function showAdminPermissions(adminId) {
    try {
        const response = await fetch(`/admin/api/admins/${adminId}/permissions`);
        const data = await response.json();

        if (data.code == 200) {
            showPermissionModal(adminId, data.data);
        } else {
            showMessage('获取权限信息失败: ' + data.message, 'error');
        }
    } catch (error) {
        showMessage('获取权限信息失败', 'error');
    }
}

// 显示权限管理模态框
function showPermissionModal(adminId, permissionData) {
    const { admin_permissions, all_admin_items } = permissionData;
    const currentUserIsSuper = isCurrentUserSuperAdmin();

    let content = '<div style="max-height: 500px; overflow-y: auto;">';
    content += '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 8px;">';

    // 显示所有权限项
    all_admin_items.forEach(item => {
        const isChecked = admin_permissions.includes(item.permission);
        const isDisabled = !currentUserIsSuper;

        content += `
            <label style="display: flex; align-items: center; padding: 8px; background: ${isChecked ? '#f0f8ff' : '#f9f9f9'}; border-radius: 4px; border: 1px solid ${isChecked ? '#3182ce' : '#e2e8f0'}; cursor: ${isDisabled ? 'not-allowed' : 'pointer'};">
                <input type="checkbox"
                       name="permissions"
                       value="${item.permission}"
                       data-item-id="${item.id}"
                       ${isChecked ? 'checked' : ''}
                       ${isDisabled ? 'disabled' : ''}
                       style="margin-right: 8px;">
                <div style="font-weight: bold; font-size: 12px; color: #2d3748;">${item.name}</div>
            </label>
        `;
    });

    content += '</div></div>';

    if (currentUserIsSuper) {
        content += `
            <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e2e8f0;">
                <div class="form-actions">
                    <button type="button" class="btn" onclick="updateAdminPermissions(${adminId})">
                        保存权限
                    </button>
                </div>
            </div>
        `;
    } else {
        content += `
            <div style="margin-top: 15px; padding: 10px; background: #fef5e7; border-radius: 4px; border-left: 4px solid #d69e2e;">
                <p style="margin: 0; color: #744210; font-size: 14px;">
                    <strong>提示：</strong>只有超级管理员才能修改权限设置
                </p>
            </div>
        `;
    }

    const modal = createModal('permission-modal', '权限管理', content, '800px');
    document.body.appendChild(modal);
}

// 更新管理员权限
async function updateAdminPermissions(adminId) {
    const checkboxes = document.querySelectorAll('#permission-modal input[name="permissions"]:checked');
    const permissions = Array.from(checkboxes).map(cb => cb.value);

    try {
        const response = await fetch(`/admin/api/admins/${adminId}/permissions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                permissions: permissions
            })
        });

        const data = await response.json();

        if (data.code == 200) {
            closeModal('permission-modal');
            showMessage('权限更新成功', 'success');
            // 刷新管理员列表
            loadAdminsData();
        } else {
            showMessage('权限更新失败: ' + data.message, 'error');
        }
    } catch (error) {
        console.error('权限更新失败:', error);
        showMessage('权限更新失败', 'error');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    // 首先获取管理员信息
    await getCurrentAdminInfo();

    // 恢复导航状态
    restoreSectionFromHash();

    // 监听hash变化
    window.addEventListener('hashchange', handleHashChange);
});

// 全局变量存储resize timeout
let resizeTimeout;

// 窗口大小改变时重新绘制图表
window.addEventListener('resize', function() {
    // 延迟重绘，避免频繁调用
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(function() {
        // 如果当前在仪表板页面，重新加载图表
        const dashboardSection = document.getElementById('dashboard');
        if (dashboardSection && dashboardSection.style.display !== 'none') {
            loadOnlineStatsChart();
        }
    }, 300);
});

// 显示更新配置模态框
function showUpdateConfigModal() {
    // 清空表单
    document.getElementById('create-update-form').reset();

    // 显示模态框
    const modal = document.getElementById('create-update-modal');
    modal.style.display = 'flex';
    modal.classList.add('show');

    // 绑定表单提交事件（移除之前的监听器）
    const form = document.getElementById('create-update-form');
    form.removeEventListener('submit', handleCreateUpdateSubmit);
    form.addEventListener('submit', handleCreateUpdateSubmit);
}

// 处理创建更新配置表单提交
function handleCreateUpdateSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const data = {
        name: formData.get('name'),
        version: formData.get('version'),
        url: formData.get('download_url'),
        checksum: formData.get('checksum') || '',
        is_active: formData.has('is_active'),
        force: formData.has('force')
    };

    fetch('/admin/api/updates', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code == 200) {
            showMessage('添加更新配置成功', 'success');
            closeModal('create-update-modal');
            loadUpdatesData();
        } else {
            showMessage('添加更新配置失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('添加更新配置失败:', error);
        showMessage('添加更新配置失败', 'error');
    });
}

// 显示忽略列表管理模态框
function showIgnoredEventsModal() {
    const ignoredIds = getLocalIgnoredEvents();

    if (ignoredIds.length === 0) {
        showMessage('当前没有被忽略的事件', 'info');
        return;
    }

    const content = `
        <div style="margin-bottom: 20px;">
            <p style="color: #4a5568; margin-bottom: 15px;">您已忽略 ${ignoredIds.length} 个风控事件：</p>
            <div style="max-height: 300px; overflow-y: auto; border: 1px solid #e2e8f0; border-radius: 6px;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead style="background: #f7fafc; position: sticky; top: 0;">
                        <tr>
                            <th style="padding: 10px; text-align: center; border-bottom: 1px solid #e2e8f0; font-size: 12px;">事件ID</th>
                            <th style="padding: 10px; text-align: center; border-bottom: 1px solid #e2e8f0; font-size: 12px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${ignoredIds.map(id => `
                            <tr>
                                <td style="padding: 8px; text-align: center; border-bottom: 1px solid #e2e8f0;">${id}</td>
                                <td style="padding: 8px; text-align: center; border-bottom: 1px solid #e2e8f0;">
                                    <button class="btn btn-success btn-small" onclick="restoreIgnoredEvent(${id})" style="padding: 2px 6px; font-size: 10px;">
                                        恢复显示
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            <div style="margin-top: 15px; text-align: center;">
                <button class="btn btn-danger" onclick="clearAllIgnored()" style="margin-right: 10px;">
                    清空所有忽略
                </button>
                <button class="btn btn-secondary" onclick="closeModal('ignored-events-modal')">
                    关闭
                </button>
            </div>
        </div>
    `;

    const modal = createModal('ignored-events-modal', '忽略事件管理', content, '500px');
    document.body.appendChild(modal);
}

// 恢复单个被忽略的事件
function restoreIgnoredEvent(eventId) {
    removeFromLocalIgnored(eventId);
    showMessage(`事件 ${eventId} 已恢复显示`, 'success');
    closeModal('ignored-events-modal');
    loadRiskData(); // 重新加载数据
}

// 清空所有忽略的事件
function clearAllIgnored() {
    if (!confirm('确定要清空所有忽略的事件吗？')) {
        return;
    }

    try {
        localStorage.removeItem(LOCAL_IGNORE_KEY);
        showMessage('已清空所有忽略的事件', 'success');
        closeModal('ignored-events-modal');
        loadRiskData(); // 重新加载数据
    } catch (e) {
        console.error('清空忽略列表失败:', e);
        showMessage('操作失败', 'error');
    }
}

// 定时刷新仪表板数据
setInterval(function() {
    if (currentSection === 'dashboard') {
        loadDashboardData();
    }
}, 30000); // 30秒刷新一次
