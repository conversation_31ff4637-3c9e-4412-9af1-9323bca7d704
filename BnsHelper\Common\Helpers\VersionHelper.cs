﻿using System.Reflection;
using Xylia.BnsHelper.Resources;

namespace Xylia.BnsHelper.Common.Helpers;
internal static class VersionHelper
{
    public static Version InternalVersion => Assembly.GetEntryAssembly()!.GetName().Version!;

    public static string Version => InternalVersion.ToString(3);

    public static string? ProductName => StringHelper.Get("ApplicationName");

    /// <summary>
    /// 检查是否有版本更新
    /// </summary>
    public static bool CheckVersion(string? client, string? server)
    {
        if (!System.Version.TryParse(client, out var version)) return false;
        if (!System.Version.TryParse(server, out var serverVersion)) return false;

        return serverVersion > version;
    }
}