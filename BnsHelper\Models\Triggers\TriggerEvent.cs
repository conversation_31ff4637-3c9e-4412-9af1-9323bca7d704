namespace Xylia.BnsHelper.Models.Triggers;

/// <summary>
/// 触发器事件接口
/// </summary>
public interface ITriggerEvent
{
    /// <summary>
    /// 事件时间
    /// </summary>
    DateTime Timestamp { get; }

    /// <summary>
    /// 事件类型
    /// </summary>
    string EventType { get; }

    /// <summary>
    /// 事件数据
    /// </summary>
    object? Data { get; }

    /// <summary>
    /// 原始消息
    /// </summary>
    string RawMessage { get; }

    /// <summary>
    /// 事件来源
    /// </summary>
    string Source { get; }
}

/// <summary>
/// 基础触发器事件实现
/// </summary>
public abstract class TriggerEvent : ITriggerEvent
{
    public DateTime Timestamp { get; set; } = DateTime.Now;
    public string EventType { get; set; } = "";
    public object? Data { get; set; }
    public string RawMessage { get; set; } = "";
    public string Source { get; set; } = "";

    public TriggerEvent(string eventType, string rawMessage, string source)
    {
        EventType = eventType;
        RawMessage = rawMessage;
        Source = source;
    }
}

/// <summary>
/// 游戏日志事件
/// </summary>
public class GameLogEvent : TriggerEvent
{
    public GameLogEvent(string message) : base("GameLog", message, "GameClient")
    {
        Data = message; // 消息内容作为数据
    }
}

/// <summary>
/// 时间触发事件
/// </summary>
public class TimeEvent : TriggerEvent
{
    public DateTime Time { get; }

    public TimeEvent(DateTime time) : base("Time", $"TIME_TRIGGER_{time:HH:mm}", "TimerService")
    {
        Time = time;
        Data = time; // 直接把时间对象作为数据
    }
}
