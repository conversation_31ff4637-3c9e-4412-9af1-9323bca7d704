using CommunityToolkit.Mvvm.ComponentModel;
using Newtonsoft.Json;
using System.Collections.ObjectModel;

namespace Xylia.BnsHelper.Models.Triggers;

/// <summary>
/// 触发器树节点
/// </summary>
public interface ITriggerNode
{
    bool IsEnabled { get; set; }

    TriggerNodeType NodeType { get; }

    ITriggerNode Clone();
}

/// <summary>
/// 触发器树节点类型
/// </summary>
public enum TriggerNodeType
{
    Folder,
    Trigger
}


public partial class TriggerFolder : ObservableObject, ITriggerNode
{
    #region Fields
    [ObservableProperty] private string _name = "";
    [ObservableProperty] private bool _isEnabled = true;
    [ObservableProperty] private bool _isExpanded = true;
    [ObservableProperty] private ObservableCollection<ITriggerNode> _nodes = [];

    // 运行时属性
    [JsonIgnore] public TriggerFolder? Parent { get; set; }
    [JsonIgnore] public int Level { get; set; } = 0;
    [JsonIgnore] public string NodeIcon => IsExpanded ? "📂" : "📁";
    [JsonIgnore] public TriggerNodeType NodeType => TriggerNodeType.Folder;

    /// <summary>
    /// 获取节点路径
    /// </summary>
    [JsonIgnore]
    public string NodePath
    {
        get
        {
            var path = new List<string>();
            var current = this;
            while (current != null)
            {
                path.Insert(0, current.Name);
                current = current.Parent;
            }
            return string.Join("/", path);
        }
    }
    #endregion

    #region Methods
    /// <summary>
    /// 同步启用状态变化
    /// </summary>
    /// <param name="value"></param>
    partial void OnIsEnabledChanged(bool value)
    {
        foreach (var node in Nodes)
        {
            node.IsEnabled = value;
        }
    }

    /// <summary>
    /// 增加节点
    /// </summary>
    public void AddNode(ITriggerNode node)
    {
        if (node == null) return;

        switch (node)
        {
            case TriggerFolder folder:
                folder.Parent = this;
                folder.Level = Level + 1;
                break;

        }

        Nodes.Add(node);
    }

    /// <summary>
    /// 移除节点
    /// </summary>
    /// <returns></returns>
    public bool RemoveNode(ITriggerNode node)
    {
        if (node == null) return false;

        return Nodes.Remove(node);
    }

    /// <summary>
    /// 移除全部节点
    /// </summary>
    /// <param name="node"></param>
    /// <returns></returns>
    public void RemoveNodes()
    {
        Nodes.Clear();
    }

    /// <summary>
    /// 移动触发器到指定位置
    /// </summary>
    public bool MoveTrigger(Trigger trigger, int newIndex)
    {
        if (trigger == null || !Nodes.Contains(trigger)) return false;

        var currentIndex = Nodes.IndexOf(trigger);
        if (currentIndex == newIndex) return true;

        Nodes.RemoveAt(currentIndex);

        if (newIndex > currentIndex) newIndex--;
        if (newIndex < 0) newIndex = 0;
        if (newIndex >= Nodes.Count) newIndex = Nodes.Count;

        Nodes.Insert(newIndex, trigger);
        return true;
    }

    /// <summary>
    /// 获取路径
    /// </summary>
    public string GetPath()
    {
        if (Parent == null) return Name;
        return $"{Parent.Name}/{Name}";
    }

    /// <summary>
    /// 克隆文件夹
    /// </summary>
    public ITriggerNode Clone()
    {
        var clone = new TriggerFolder
        {
            Name = $"{Name} (副本)",
            IsEnabled = IsEnabled,
            IsExpanded = IsExpanded
        };

        // 克隆节点
        foreach (var node in Nodes)
        {
            clone.AddNode(node.Clone());
        }

        return clone;
    }
    #endregion
}
