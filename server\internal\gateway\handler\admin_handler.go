package handler

import (
	"encoding/json"
	"net/http"
	"strconv"
	gatewayService "udp-server/server/internal/gateway/service"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"

	"github.com/gorilla/mux"
)

// AdminHandler 管理后台处理器（组合器）
type AdminHandler struct {
	// 分离的处理器
	ActivityHandler     *AdminActivityHandler
	AuthHandler         *AdminAuthHandler
	AnnouncementHandler *AdminAnnouncementHandler
	StatsHandler        *AdminStatsHandler
	UserHandler         *AdminUserHandler
	CdkeyHandler        *AdminCDKeyHandler
	UpdateHandler       *AdminUpdateHandler
	RiskHandler         *AdminRiskHandler

	// 服务
	authService  *service.AuthService
	adminService *gatewayService.AdminService
}

// NewAdminHandler 创建管理后台处理器实例
func NewAdminHandler(
	announcementService *service.AnnouncementService,
	authService *service.AuthService,
	updateService *service.UpdateService,
	updateAdminService *gatewayService.UpdateAdminService,
	statsService *service.StatsService,
	riskService *service.RiskControlService,
	luckyService *service.LuckyService,
	cdkeyService *service.CDKeyService,
	heartbeatService *service.HeartbeatService,
	permissionService *gatewayService.AdminPermissionService,
	adminService *gatewayService.AdminService,
	riskAdminService *gatewayService.RiskControlAdminService,
	activityService *service.ActivityService,
	cache cache.Cache,
) *AdminHandler {
	// 创建各个子处理器
	authHandler := NewAdminAuthHandler(adminService, authService)
	announcementHandler := NewAdminAnnouncementHandler(
		announcementService,
		gatewayService.NewAnnouncementAdminService(announcementService),
		authService,
	)
	statsHandler := NewAdminStatsHandler(statsService, authService)
	userHandler := NewAdminUserHandler(
		gatewayService.NewUserAdminService(),
		gatewayService.NewCDKeyAdminService(),
		authService,
	)
	cdkeyHandler := NewAdminCDKeyHandler(
		gatewayService.NewCDKeyAdminService(),
		authService,
	)
	updateHandler := NewAdminUpdateHandler(updateAdminService, authService)
	riskHandler := NewAdminRiskHandler(riskAdminService, authService)
	activityAdminService := gatewayService.NewActivityAdminService(activityService, cache)
	activityHandler := NewAdminActivityHandler(activityService, activityAdminService)

	return &AdminHandler{
		AuthHandler:         authHandler,
		AnnouncementHandler: announcementHandler,
		StatsHandler:        statsHandler,
		UserHandler:         userHandler,
		CdkeyHandler:        cdkeyHandler,
		UpdateHandler:       updateHandler,
		RiskHandler:         riskHandler,
		ActivityHandler:     activityHandler,
		authService:         authService,
		adminService:        adminService,
	}
}

// AdminResponse 管理后台通用响应结构
type AdminResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// 发送JSON响应
func SendJSONResponse(w http.ResponseWriter, statusCode int, message string, data interface{}) {
	response := AdminResponse{
		Code:    statusCode,
		Message: message,
		Data:    data,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

// ==================== 管理员信息 ==================== //

// 处理获取管理员列表请求
func (h *AdminHandler) HandleGetAdmins(w http.ResponseWriter, r *http.Request) {
	admins, err := h.adminService.GetAdminList()
	if err != nil {
		logger.Error("获取管理员列表失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取管理员列表失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取管理员列表成功", admins)
}

// 处理获取管理员详情请求
func (h *AdminHandler) HandleGetAdmin(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	adminID := vars["id"]

	admin, err := h.adminService.GetAdminByID(adminID)
	if err != nil {
		logger.Error("获取管理员详情失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取管理员详情失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取管理员详情成功", admin)
}

// 处理创建管理员请求
func (h *AdminHandler) HandleCreateAdmin(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	// 解析请求体
	var req struct {
		Username string `json:"username"`
		Password string `json:"password"`
		Power    string `json:"power"`
		IsSuper  bool   `json:"is_super"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析创建管理员请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 验证必填字段
	if req.Username == "" || req.Password == "" {
		SendJSONResponse(w, http.StatusBadRequest, "用户名和密码不能为空", nil)
		return
	}

	// 创建管理员
	adminID, err := h.adminService.CreateAdmin(req.Username, req.Password, req.Power, req.IsSuper)
	if err != nil {
		logger.Error("创建管理员失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "创建管理员失败", nil)
		return
	}

	logger.Info("管理员创建成功: ID=%d, 用户名=%s", adminID, req.Username)
	SendJSONResponse(w, http.StatusOK, "管理员创建成功", adminID)
}

// 处理更新管理员请求
func (h *AdminHandler) HandleUpdateAdmin(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPut && r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	vars := mux.Vars(r)
	adminID := vars["id"]

	// 解析请求体
	var req struct {
		Username string `json:"username"`
		Password string `json:"password"`
		Power    string `json:"power"`
		IsAction int    `json:"is_action"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析更新管理员请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 验证必填字段
	if req.Username == "" {
		SendJSONResponse(w, http.StatusBadRequest, "用户名不能为空", nil)
		return
	}

	// 更新管理员基本信息
	err := h.adminService.UpdateAdmin(adminID, req.Username, req.Password, req.IsAction)
	if err != nil {
		logger.Error("更新管理员基本信息失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "更新管理员失败", nil)
		return
	}

	// 如果提供了权限信息，更新权限
	if req.Power != "" {
		adminIDUint, err := strconv.ParseUint(adminID, 10, 64)
		if err != nil {
			SendJSONResponse(w, http.StatusBadRequest, "无效的管理员ID", nil)
			return
		}

		// 这里需要根据实际的权限更新逻辑来实现
		// 暂时跳过权限更新，因为需要AdminPermissionService
		logger.Info("管理员权限更新暂时跳过: ID=%d, 权限=%s", adminIDUint, req.Power)
	}

	logger.Info("管理员更新成功: ID=%s, 用户名=%s", adminID, req.Username)
	SendJSONResponse(w, http.StatusOK, "管理员更新成功", nil)
}

// 处理删除管理员请求
func (h *AdminHandler) HandleDeleteAdmin(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	vars := mux.Vars(r)
	adminID := vars["id"]

	// 删除管理员
	err := h.adminService.DeleteAdmin(adminID)
	if err != nil {
		logger.Error("删除管理员失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "删除管理员失败", nil)
		return
	}

	logger.Info("管理员删除成功: ID=%s", adminID)
	SendJSONResponse(w, http.StatusOK, "管理员删除成功", nil)
}

// 处理获取管理员权限请求
func (h *AdminHandler) HandleGetAdminPermissions(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	adminID := vars["id"]

	// 获取管理员详情（包含权限信息）
	admin, err := h.adminService.GetAdminByID(adminID)
	if err != nil {
		logger.Error("获取管理员权限失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取管理员权限失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取管理员权限成功", admin.Power)
}

// 处理更新管理员权限请求
func (h *AdminHandler) HandleUpdateAdminPermissions(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPut && r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	vars := mux.Vars(r)
	adminID := vars["id"]

	// 解析请求体
	var req struct {
		Permissions []string `json:"permissions"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析更新管理员权限请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 这里需要根据实际的权限更新逻辑来实现
	// 暂时返回成功，因为需要AdminPermissionService
	logger.Info("管理员权限更新: ID=%s, 权限=%v", adminID, req.Permissions)
	SendJSONResponse(w, http.StatusOK, "管理员权限更新成功", nil)
}
