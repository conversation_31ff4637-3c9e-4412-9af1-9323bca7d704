using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Newtonsoft.Json;
using Serilog;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using Xylia.BnsHelper.Common.Converters;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Services.Network.Plugin;
using Xylia.Preview.Data.Models.Sequence;
using static Xylia.Preview.Data.Models.BattleMessage;

namespace Xylia.BnsHelper.Models;

/// <summary>
/// 技能统计数据
/// </summary>
internal class SkillStats : ObservableObject
{
    #region Properties
    // 基本统计
    public CreatureStats? Owner { get; set; }
    public string Name { get; set; } = string.Empty;
    public string TargetName { get; set; } = string.Empty;
    public bool Summoned { get; set; } = false;  // 标识是否为召唤物的技能
    public DateTime? StartTime { get; set; }
    public DateTime EndTime { get; set; }

    // 伤害统计
    public long TotalDamage { get; set; }
    public int HitCount { get; set; }
    public int CriticalHitCount { get; set; }
    public int DodgeCount { get; set; }
    public int ParryCount { get; set; }
    public int MissCount { get; set; }

    // 计算属性
    public double CriticalRate => HitCount > 0 ? (double)CriticalHitCount / HitCount : 0;
    public long DamagePerHit => HitCount > 0 ? TotalDamage / HitCount : 0;
    public double TotalRate
    {
        get
        {
            if (Owner == null) return 0;

            // 根据当前统计类型确定总量基准
            var statisticsType = Owner.Owner?.StatisticsType ?? StatisticsType.Damage;
            long totalBase = Owner.GetTotalValueByType(statisticsType);

            // 如果目标名称是NULL，表示聚合统计，计算占对应总量的百分比
            if (string.IsNullOrEmpty(TargetName))
            {
                return totalBase > 0 ? (double)TotalDamage / totalBase : 0;
            }

            // 如果是特定目标，计算占该目标对应类型总量的百分比
            var targetTotal = statisticsType switch
            {
                StatisticsType.DamageTaken => Owner.Owner?.GetTotalDamageTakenForTarget(TargetName) ?? 0,
                StatisticsType.Healing => Owner.Owner?.GetTotalHealingForTarget(TargetName) ?? 0,
                _ => Owner.Owner?.GetTotalDamageForTarget(TargetName) ?? 0
            };
            return targetTotal > 0 ? (double)TotalDamage / targetTotal : 0;
        }
    }
    #endregion

    #region Methods
    public void AddHit(InstantEffectNotification2 item, long damage)
    {
        // 更新时间
        EndTime = item.Time;
        StartTime ??= item.Time;
        TotalDamage += damage;

        // 如果是物品技能，不记录伤害次数
        if (item.EffectAlias != null && item.EffectAlias.StartsWith("ItemSkill_", StringComparison.OrdinalIgnoreCase)) return;

        HitCount++;
        switch (item.SkillResultType)
        {
            case SkillResultTypeSeq.Cri:
                CriticalHitCount++;
                break;
            case SkillResultTypeSeq.Dodge:
                DodgeCount++;
                break;
            case SkillResultTypeSeq.Parry:
            case SkillResultTypeSeq.Pparry:
                ParryCount++;
                break;
            case SkillResultTypeSeq.Miss:
                MissCount++;
                break;
        }
    }

    public void Refresh()
    {
        // 批量刷新技能统计属性
        var propertiesToRefresh = new[]
        {
            nameof(TotalDamage),
            nameof(HitCount),
            nameof(CriticalHitCount),
            nameof(DodgeCount),
            nameof(ParryCount),
            nameof(MissCount),
            nameof(CriticalRate),
            nameof(DamagePerHit),
            nameof(TotalRate)
        };

        foreach (var property in propertiesToRefresh)
        {
            OnPropertyChanged(property);
        }
    }
    #endregion
}

/// <summary>
/// 玩家统计数据
/// </summary>
[DebuggerDisplay("{Name} ({PlayerId})")]
internal partial class CreatureStats : Collection<InstantEffectNotification2>, INotifyPropertyChanged
{
    #region Constructor
    readonly Creature Creature;
    public bool Self { get; init; }

    public CreatureStats(CombatCollection? owner, Creature creature, bool self = false, bool skipInitialUpdate = false)
    {
        Owner = owner;
        Creature = creature;
        Self = self;

        // 初始化技能显示（除非明确跳过）
        if (!skipInitialUpdate)
        {
            UpdateSkillsDisplay();
        }
    }
    #endregion

    #region Fields
    public CombatCollection? Owner { get; set; }
    public long PlayerId => Creature.Id;
    public string? Name => Creature.Name;
    public JobSeq Job => Creature.Job;
    public long Summoned => Creature.Summoned;

    // 时间统计
    public DateTime? StartTime { get; private set; }
    public DateTime EndTime { get; private set; }
    public double Seconds => Owner?.GetFilteredSeconds(this) ?? 0;

    // 伤害输出统计
    private readonly Dictionary<string, SkillStats> _damageSkills = [];
    public ObservableCollection<SkillStats> DamageSkills { get; } = [];

    // 聚合的伤害输出统计（按技能名称聚合，不区分目标）
    private readonly Dictionary<string, SkillStats> _aggregatedDamageSkills = [];
    public ObservableCollection<SkillStats> AggregatedDamageSkills { get; } = [];

    // 当前显示的技能集合（根据目标过滤器动态更新）
    public ObservableCollection<SkillStats> Skills { get; } = [];

    // 承伤统计
    private readonly Dictionary<string, SkillStats> _takenDamageSkills = [];
    public ObservableCollection<SkillStats> TakenDamageSkills { get; } = [];

    // 治疗统计
    private readonly Dictionary<string, SkillStats> _healSkills = [];
    public ObservableCollection<SkillStats> HealSkills { get; } = [];

    // 内力统计
    private readonly Dictionary<string, SkillStats> _spSkills = [];
    public ObservableCollection<SkillStats> SpSkills { get; } = [];

    // 状态统计
    public int ExhaustionCount { get; private set; }  // 濒死次数
    public int DeathCount { get; private set; }       // 死亡次数
    #endregion

    #region Properties
    // 显示模式
    public bool Mode => IsDamageOutputType && Owner?.Count > 1 && !SettingHelper.Default.DisplayMode;

    // 伤害输出统计
    public long TotalDamageDealt => GetFilteredDamage(DamageSkills);
    public long DamagePerSec => Seconds > 0 ? (long)(TotalDamageDealt / Seconds) : 0;
    public double DamageRate => Owner?.TotalDamage > 0 ? (double)TotalDamageDealt / Owner.TotalDamage : 0;
    public double DamageRate2 => Owner?.MaxDamage > 0 ? (double)TotalDamageDealt / Owner.MaxDamage : 1;

    // 承伤统计
    public long TotalDamageTaken => GetFilteredDamage(TakenDamageSkills);
    public long DamageTakenPerSec => Seconds > 0 ? (long)(TotalDamageTaken / Seconds) : 0;

    // 治疗统计
    public long TotalHealingDone => GetFilteredDamage(HealSkills); // 治疗使用 TotalDamage 属性存储
    public long HealingPerSec => Seconds > 0 ? (long)(TotalHealingDone / Seconds) : 0;

    // 内力统计
    public long TotalSpRestored => GetFilteredDamage(SpSkills); // 内力恢复使用 TotalDamage 属性存储
    public long SpPerSec => Seconds > 0 ? (long)(TotalSpRestored / Seconds) : 0;

    /// <summary>
    /// 根据当前统计类型获取右侧显示文本
    /// </summary>
    public string PerSecondDisplayText
    {
        get
        {
            var statisticsType = Owner?.StatisticsType ?? StatisticsType.Damage;
            return statisticsType switch
            {
                StatisticsType.DamageTaken => $"总承伤 {GetTotalValueByType(statisticsType)}",
                StatisticsType.Healing => $"总恢复 {GetTotalValueByType(statisticsType)}",
                _ => $"{GetPerSecondValueByType(statisticsType)}/秒"
            };
        }
    }

    /// <summary>
    /// 根据当前统计类型获取总量的显示文本
    /// </summary>
    public string TotalValueDisplayText
    {
        get
        {
            var statisticsType = Owner?.StatisticsType ?? StatisticsType.Damage;
            var totalValue = GetTotalValueByType(statisticsType);
            return statisticsType switch
            {
                StatisticsType.DamageTaken => $"总承伤值 {totalValue}",
                StatisticsType.Healing => $"总恢复值 {totalValue}",
                _ => $"累积伤害量 {totalValue}"
            };
        }
    }

    /// <summary>
    /// 根据当前目标过滤器计算过滤后的伤害总量
    /// </summary>
    private long GetFilteredDamage(ObservableCollection<SkillStats> skills)
    {
        return Owner?.GetFilteredDamage(skills) ?? 0;
    }

    // 防御统计（基于当前聚合技能计算）
    public int TotalHitEvents => Skills.Sum(s => s.HitCount);
    public double DodgeRate => TotalHitEvents > 0 ? (double)Skills.Sum(s => s.DodgeCount) / TotalHitEvents : 0;
    public double ParryRate => TotalHitEvents > 0 ? (double)Skills.Sum(s => s.ParryCount) / TotalHitEvents : 0;


    /// <summary>
    /// 是否显示每秒伤害
    /// </summary>
    public bool DisplayPerSecondValue => IsDamageOutputType && (Mode || Self);

    /// <summary>
    /// 根据当前统计类型获取每秒数值文本
    /// </summary>
    public string PerSecondValueText
    {
        get
        {
            var statisticsType = Owner?.StatisticsType ?? StatisticsType.Damage;
            return GetPerSecondValueByType(statisticsType).ToString();
        }
    }

    /// <summary>
    /// 检查当前是否为伤害输出统计类型
    /// </summary>
    public bool IsDamageOutputType => (Owner?.StatisticsType ?? StatisticsType.Damage) == StatisticsType.Damage;

    /// <summary>
    /// 检查当前是否为承伤统计类型
    /// </summary>
    public bool IsDamageTakenType => (Owner?.StatisticsType ?? StatisticsType.Damage) == StatisticsType.DamageTaken;

    /// <summary>
    /// 检查当前是否为治疗统计类型
    /// </summary>
    public bool IsHealingType => (Owner?.StatisticsType ?? StatisticsType.Damage) == StatisticsType.Healing;

    /// <summary>
    /// 根据统计类型获取对应的总量值
    /// </summary>
    /// <param name="statisticsType">统计类型</param>
    /// <returns>对应的总量值</returns>
    public long GetTotalValueByType(StatisticsType statisticsType)
    {
        return statisticsType switch
        {
            StatisticsType.DamageTaken => TotalDamageTaken,
            StatisticsType.Healing => TotalHealingDone,
            _ => TotalDamageDealt
        };
    }

    /// <summary>
    /// 根据统计类型获取对应的每秒数值
    /// </summary>
    /// <param name="statisticsType">统计类型</param>
    /// <returns>对应的每秒数值</returns>
    public long GetPerSecondValueByType(StatisticsType statisticsType)
    {
        return statisticsType switch
        {
            StatisticsType.DamageTaken => DamageTakenPerSec,
            StatisticsType.Healing => HealingPerSec,
            _ => DamagePerSec
        };
    }

    /// <summary>
    /// 更新Skills集合以反映当前的目标过滤器和统计类型
    /// </summary>
    public void UpdateSkillsDisplay(StatisticsType statisticsType = StatisticsType.Damage)
    {
        Skills.Clear();

        var targetFilter = Owner?.TargetFilter;

        // 根据统计类型选择对应的技能集合
        ObservableCollection<SkillStats> sourceSkills;
        ObservableCollection<SkillStats> aggregatedSkills;

        switch (statisticsType)
        {
            case StatisticsType.DamageTaken:
                sourceSkills = TakenDamageSkills;
                aggregatedSkills = new ObservableCollection<SkillStats>(); // 承伤没有预聚合集合，需要动态聚合
                break;
            case StatisticsType.Healing:
                sourceSkills = HealSkills;
                aggregatedSkills = new ObservableCollection<SkillStats>(); // 治疗没有预聚合集合，需要动态聚合
                break;
            default: // StatisticsType.Damage
                sourceSkills = DamageSkills;
                aggregatedSkills = AggregatedDamageSkills;
                break;
        }

        // 如果没有目标过滤器，显示聚合的技能统计
        if (string.IsNullOrEmpty(targetFilter))
        {
            if (statisticsType == StatisticsType.Damage && aggregatedSkills.Count > 0)
            {
                // 伤害输出有预聚合数据
                foreach (var skill in aggregatedSkills.OrderByDescending(s => s.TotalDamage))
                {
                    Skills.Add(skill);
                }
            }
            else
            {
                // 承伤和治疗需要动态聚合
                var dynamicAggregated = sourceSkills
                    .GroupBy(s => new { s.Name, s.Summoned })
                    .Select(g => CreateAggregatedSkill(g, string.Empty))
                    .OrderByDescending(s => s.TotalDamage);

                foreach (var skill in dynamicAggregated)
                {
                    Skills.Add(skill);
                }
            }
            return;
        }

        // 如果有特定目标过滤器，显示该目标的技能统计（按技能名称聚合）
        var targetNames = targetFilter.Split(',', StringSplitOptions.RemoveEmptyEntries);
        var targetSkills = sourceSkills.Where(s => targetNames.Contains(s.TargetName));

        var aggregatedTargetSkills = targetSkills
            .GroupBy(s => new { s.Name, s.Summoned })
            .Select(g => CreateAggregatedSkill(g, string.Join(",", targetNames)))
            .OrderByDescending(s => s.TotalDamage);

        foreach (var skill in aggregatedTargetSkills)
        {
            Skills.Add(skill);
        }

        // 通知防御统计属性变化
        OnPropertyChanged(nameof(DodgeRate));
        OnPropertyChanged(nameof(ParryRate));
        OnPropertyChanged(nameof(TotalHitEvents));
    }

    /// <summary>
    /// 创建聚合的技能统计
    /// </summary>
    private SkillStats CreateAggregatedSkill(IGrouping<dynamic, SkillStats> group, string targetName)
    {
        var first = group.First();
        var aggregated = new SkillStats
        {
            Name = first.Name,
            TargetName = targetName,
            Owner = this,
            Summoned = first.Summoned,
            TotalDamage = group.Sum(s => s.TotalDamage),
            HitCount = group.Sum(s => s.HitCount),
            CriticalHitCount = group.Sum(s => s.CriticalHitCount),
            DodgeCount = group.Sum(s => s.DodgeCount),
            ParryCount = group.Sum(s => s.ParryCount),
            MissCount = group.Sum(s => s.MissCount)
        };

        // 设置时间信息
        var startTimes = group.Where(s => s.StartTime.HasValue).Select(s => s.StartTime!.Value);
        var endTimes = group.Select(s => s.EndTime);

        if (startTimes.Any())
        {
            aggregated.StartTime = startTimes.Min();
            aggregated.EndTime = endTimes.Max();
        }

        return aggregated;
    }
    #endregion

    #region Methods
    protected override void InsertItem(int index, InstantEffectNotification2 item)
    {
        base.InsertItem(index, item);

        // 更新时间
        EndTime = item.Time;
        StartTime ??= item.Time;

        // 根据事件类型处理数据
        switch (item.ObjectType)
        {
            case ObjectTypeSeq.PlayerAttack:
            case ObjectTypeSeq.Other when item.CasterId == PlayerId || IsSummonedAttack(item.CasterId):
                ProcessCombatEvent(item, true);
                break;
            case ObjectTypeSeq.PlayerAttacked:
            case ObjectTypeSeq.Other when item.TargetId == PlayerId || IsSummonedTarget(item.TargetId):
                ProcessCombatEvent(item, false);
                break;
        }
    }

    /// <summary>
    /// 检查是否为当前玩家的召唤兽攻击
    /// </summary>
    private bool IsSummonedAttack(long casterId)
    {
        // 检查施法者是否为当前玩家的召唤兽
        bool isSummoned = Owner?.IsSummonedBy(casterId, PlayerId) == true;

        return isSummoned;
    }

    /// <summary>
    /// 检查是否为当前玩家的召唤兽被攻击
    /// </summary>
    private bool IsSummonedTarget(long targetId)
    {
        // 检查目标是否为当前玩家的召唤兽
        bool isSummoned = Owner?.IsSummonedBy(targetId, PlayerId) == true;

        return isSummoned;
    }

    /// <summary>
    /// 处理战斗事件（统一处理伤害输出和承受伤害）
    /// </summary>
    /// <param name="item">战斗事件</param>
    /// <param name="isOutputEvent">是否为输出事件（true=攻击方视角，false=被攻击方视角）</param>
    private void ProcessCombatEvent(InstantEffectNotification2 item, bool isOutputEvent)
    {
        // 处理治疗和内力恢复事件
        switch (item.EffectType)
        {
            case EffectTypeSeq.InstantHp:
            case EffectTypeSeq.IntervalHp:
                ProcessSkillStats(item, item.Value, item.TargetName, _healSkills, HealSkills);
                return;

            case EffectTypeSeq.InstantSp:
            case EffectTypeSeq.IntervalSp:
                ProcessSkillStats(item, item.Value, item.TargetName, _spSkills, SpSkills);
                return;

            // 跳过效果附加
            case EffectTypeSeq.Attach:
            case EffectTypeSeq.AttachFail:
            case EffectTypeSeq.Detach:
                return;
        }

        // 处理伤害事件
        if (isOutputEvent)
        {
            ProcessSkillStats(item, item.Value, item.TargetName, _damageSkills, DamageSkills);
            if (item.Value2 > 0) ProcessSkillStats(item, item.Value2, item.TargetName, _healSkills, HealSkills);
        }
        else
        {
            ProcessSkillStats(item, item.Value, item.CasterName, _takenDamageSkills, TakenDamageSkills);

            switch (item.EffectType)
            {
                case EffectTypeSeq.Exhaustion:
                    ExhaustionCount++;
                    OnPropertyChanged(nameof(ExhaustionCount));
                    break;
                case EffectTypeSeq.Dead:
                    DeathCount++;
                    OnPropertyChanged(nameof(DeathCount));
                    break;
            }
        }
    }

    /// <summary>
    /// 处理技能统计（通用方法）
    /// </summary>
    private void ProcessSkillStats(InstantEffectNotification2 item, long value, string? target, Dictionary<string, SkillStats> skillDict, ObservableCollection<SkillStats> skillCollection)
    {
        string skillKey = $"{item.Name}_{target}_{item.Summoned}";

        if (!skillDict.TryGetValue(skillKey, out var skill))
        {
            skill = new SkillStats
            {
                Owner = this,
                Name = item.Name,
                TargetName = target,
                Summoned = item.Summoned
            };
            skillDict[skillKey] = skill;
            skillCollection.Add(skill);
        }

        skill.AddHit(item, value);

        // 如果是伤害输出统计，同时更新聚合统计
        if (skillDict == _damageSkills)
        {
            UpdateAggregatedSkillStats(item, value, item.Name);
        }
    }

    /// <summary>
    /// 更新聚合的技能统计（按技能名称聚合，不区分目标）
    /// </summary>
    private void UpdateAggregatedSkillStats(InstantEffectNotification2 item, long value, string name)
    {
        // 构建聚合技能key，只包含技能名称，不包含目标信息
        string aggregatedSkillKey = $"{name}_{item.Summoned}";

        if (!_aggregatedDamageSkills.TryGetValue(aggregatedSkillKey, out var aggregatedSkill))
        {
            aggregatedSkill = new SkillStats
            {
                Name = name,
                TargetName = string.Empty, // 聚合统计的目标名称为空字符串
                Owner = this,
                Summoned = item.Summoned
            };
            _aggregatedDamageSkills[aggregatedSkillKey] = aggregatedSkill;
            AggregatedDamageSkills.Add(aggregatedSkill);
        }

        aggregatedSkill.AddHit(item, value);
    }

    /// <summary>
    /// 刷新所有统计数据
    /// </summary>
    public void Refresh()
    {
        if (!SettingHelper.Default.EncounterMode)
            EndTime = DateTime.Now;

        // 批量刷新属性统计
        var propertiesToRefresh = new[]
        {
            nameof(Mode), nameof(Seconds), nameof(TotalDamageDealt), nameof(TotalDamageTaken),
            nameof(TotalHealingDone), nameof(DamagePerSec), nameof(DamageTakenPerSec), nameof(HealingPerSec),
            nameof(PerSecondDisplayText), nameof(TotalValueDisplayText), nameof(PerSecondValueText),
            nameof(IsDamageOutputType), nameof(DisplayPerSecondValue), nameof(DamageRate),
            nameof(DamageRate2), nameof(DodgeRate), nameof(ParryRate)
        };

        foreach (var property in propertiesToRefresh)
        {
            OnPropertyChanged(property);
        }

        // 技能统计会在访问时自动计算，不需要手动刷新
        UpdateSkillsDisplay(Owner?.StatisticsType ?? StatisticsType.Damage);
    }

    [RelayCommand]
    void ToWorld()
    {
        if (Name is null) return;

        WindowHelper.SendMessage(WindowHelper.GetGameWindow(), $"<font name=\"00008130.UI.Hypertext_PC\"><link id='pc:{Name}'>{Name}</link></font>");
    }
    #endregion

    #region Interface
    public event PropertyChangedEventHandler? PropertyChanged;

    public void OnPropertyChanged(string name)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }

    /// <summary>
    /// 批量通知属性变化，减少PropertyChanged事件的触发次数
    /// </summary>
    public void BatchNotifyPropertiesChanged(params string[] propertyNames)
    {
        if (PropertyChanged != null)
        {
            foreach (var propertyName in propertyNames)
            {
                PropertyChanged.Invoke(this, new PropertyChangedEventArgs(propertyName));
            }
        }
    }
    #endregion
}

/// <summary>
/// 战斗记录系统的核心集合类 - 管理所有玩家的战斗数据
/// </summary>
[JsonConverter(typeof(CombatCollectionConverter))]
internal class CombatCollection : Collection<CreatureStats>, INotifyPropertyChanged
{
    #region Fields
    internal bool IsHistory;

    // 玩家对象缓存 - 按ID快速查找
    internal readonly Dictionary<long, CreatureStats> _playersByPlayerId = [];

    // 召唤物关系映射 - 召唤兽 -> 召唤师
    internal readonly ConcurrentDictionary<long, long> _summonedToSummoner = [];

    // 目标统计缓存 - 使用线程安全集合在Add方法中直接缓存
    private readonly ConcurrentDictionary<string, long> _targetDamageCache = [];
    private readonly ConcurrentDictionary<string, byte> _targetNamesCache = [];

    // 死亡目标跟踪 - 记录死亡目标和死亡时间，用于忽略延迟伤害
    private readonly HashSet<string> _deadTargets = [];

    // 时间统计
    public DateTime? StartTime { get; private set; }
    public DateTime EndTime { get; private set; }
    public DateTime LastCombatTime { get; private set; }
    public TimeSpan TimeSpan => StartTime.HasValue ? (EndTime - StartTime.Value) : TimeSpan.Zero;

    // 伤害统计
    public long TotalDamage => this.Sum(x => x?.TotalDamageDealt ?? 0);
    public long MaxDamage => Count == 0 ? 0 : this.Max(x => x?.TotalDamageDealt ?? 0);
    public IEnumerable? View
    {
        get
        {
            if (Count == 0) return null;

            var players = this.Where(x => x != null && x.Job != JobSeq.JobNone);

            // Apply target filtering if a specific target is selected
            if (TargetFilter != null && !string.IsNullOrEmpty(TargetFilter))
            {
                var targetNames = TargetFilter.Split(',', StringSplitOptions.RemoveEmptyEntries);

                // Filter players who have damage to any of the specified targets
                players = players.Where(p => targetNames.Any(targetName =>
                    GetTotalDamageForTarget(targetName) > 0 &&
                    p.DamageSkills.Any(s => s.TargetName == targetName && s.TotalDamage > 0)));
            }

            return players.OrderByDescending(x => x.TotalDamageDealt);
        }
    }

    // Target filter property
    private string? _targetFilter;
    public string? TargetFilter
    {
        get => _targetFilter;
        set
        {
            if (_targetFilter != value)
            {
                _targetFilter = value;
                OnPropertyChanged(nameof(TargetFilter));
            }
        }
    }

    // Statistics type property
    private StatisticsType _statisticsType = StatisticsType.Damage;
    public StatisticsType StatisticsType
    {
        get => _statisticsType;
        set
        {
            if (_statisticsType != value)
            {
                _statisticsType = value;
                OnPropertyChanged(nameof(StatisticsType));

                // 批量更新所有玩家的技能显示和统计数据
                foreach (var player in this)
                {
                    player.UpdateSkillsDisplay(value);

                    // 批量通知属性变化，减少单独的PropertyChanged调用
                    player.BatchNotifyPropertiesChanged(
                        nameof(player.PerSecondDisplayText),
                        nameof(player.TotalValueDisplayText),
                        nameof(player.PerSecondValueText),
                        nameof(player.DisplayPerSecondValue),
                        nameof(player.IsDamageOutputType),
                        nameof(player.IsDamageTakenType),
                        nameof(player.IsHealingType),
                        nameof(player.TotalSpRestored),
                        nameof(player.SpPerSec)
                    );
                }
            }
        }
    }

    /// <summary>
    /// 默认玩家
    /// </summary>
    public Creature? Player { get; set; }

    /// <summary>
    /// 所有目标死亡事件
    /// </summary>
    public event Action? AllTargetsDead;
    #endregion

    #region Methods
    protected override void InsertItem(int index, CreatureStats item)
    {
        // 添加到字典缓存
        if (item == null) return;
        if (!_playersByPlayerId.TryAdd(item.PlayerId, item)) return;

        base.InsertItem(index, item);
        Refresh();
    }

    protected override void RemoveItem(int index)
    {
        var player = this[index];

        // 从缓存中移除
        _playersByPlayerId.Remove(player.PlayerId);
        base.RemoveItem(index);
    }

    protected override void ClearItems()
    {
        IsHistory = false;
        StartTime = null;
        EndTime = default;
        LastCombatTime = default;
        _playersByPlayerId.Clear();
        _summonedToSummoner.Clear();
        _deadTargets.Clear();

        ClearTargetCache(); // 清理目标统计缓存
        base.ClearItems();

        if (Player != null) Add(new CreatureStats(this, Player, true));
    }

    /// <summary>
    /// 更新默认玩家信息
    /// </summary>
    public void UpdateDefaultPlayer(Creature player)
    {
        Player = player;
        Add(new CreatureStats(this, Player, true));
    }

    /// <summary>
    /// 添加战斗基本事件
    /// </summary>
    /// <param name="item"></param>
    public void Add(InstantEffectNotification item)
    {
        if (item.Player is null || item.Player.job == 0) return;

        // 检查是否已存在玩家对象
        if (_playersByPlayerId.TryGetValue(item.Player.Id, out var player)) return;

        // 如果是召唤物，检查其主人是否存在
        if (item.Player.Summoned != 0 && item.Player.Job > JobSeq.PcMax)
        {
            // 召唤师存在，建立映射关系
            if (_playersByPlayerId.ContainsKey(item.Player.Summoned))
            {
                _summonedToSummoner[item.Player.Id] = item.Player.Summoned;
            }
            return;
        }

        // 创建新的 CreatureStats 对象（只为真正的玩家创建）
        Add(new CreatureStats(this, item.Player, false, true));

        // 如果是召唤师，建立召唤物关系映射
        if (item.Player.Summoned != 0)
        {
            _summonedToSummoner[item.Player.Summoned] = item.Player.Id;
            Debug.WriteLine($"[召唤物映射] 召唤师: {item.Player.Name}({item.Player.Id}) -> 召唤兽: ({item.Player.Summoned})");
        }
    }

    /// <summary>
    /// 添加战斗事件
    /// </summary>
    public void Add(InstantEffectNotification2 instant)
    {
        // 检查死亡事件
        if (instant.EffectType == EffectTypeSeq.Dead)
        {
            CheckTargetDeath(instant.TargetName);
            return; // 死亡事件不需要进一步处理
        }

        // 更新全局时间
        EndTime = LastCombatTime = instant.Time;
        StartTime ??= instant.Time;

        // 根据事件类型分别处理
        switch (instant.ObjectType)
        {
            case ObjectTypeSeq.PlayerAttack:
                // 玩家攻击事件：处理攻击方数据
                ProcessCasterEvent(instant);
                // 如果目标是其他玩家，也需要处理被攻击方数据
                if (instant.TargetId != 0 && instant.TargetId != instant.CasterId)
                {
                    ProcessTargetEvent(instant);
                }
                break;

            case ObjectTypeSeq.PlayerAttacked:
                // 玩家被攻击事件：直接处理被攻击方数据，不需要转换
                ProcessPlayerAttackedEvent(instant);
                break;

            case ObjectTypeSeq.Other:
                // 第三人称事件：根据玩家ID判断处理方式
                ProcessCasterEvent(instant);
                ProcessTargetEvent(instant);
                break;
        }
    }

    /// <summary>
    /// 处理攻击方事件
    /// </summary>
    private void ProcessCasterEvent(InstantEffectNotification2 instant)
    {
        // 获取施法者（如果是召唤物则获取召唤师）
        var casterPlayer = GetPlayerOrSummoner(instant.CasterId, instant.CasterName, out bool isSummonedAttack);
        if (casterPlayer != null)
        {
            // 检查目标是否为已知玩家（队友），如果是则说明这是增益事件
            bool isTargetKnownPlayer = _playersByPlayerId.ContainsKey(instant.TargetId);
            if (!isTargetKnownPlayer)
            {
                // 这是对敌方目标的攻击，更新目标缓存
                if (!string.IsNullOrEmpty(instant.TargetName) && instant.Value > 0)
                {
                    _targetNamesCache.TryAdd(instant.TargetName, 0); // 添加目标名称到缓存
                    _targetDamageCache.AddOrUpdate(instant.TargetName, instant.Value, (key, oldValue) => oldValue + instant.Value);
                }

                // 设置召唤物标识
                instant.Summoned = isSummonedAttack;
                casterPlayer.Add(instant);
            }
        }

    }

    /// <summary>
    /// 处理玩家被攻击事件
    /// </summary>
    private void ProcessPlayerAttackedEvent(InstantEffectNotification2 instant)
    {
        // 对于PlayerAttacked事件，TargetId就是被攻击的玩家ID
        if (instant.TargetId == 0) return;

        // 获取被攻击的玩家（如果是召唤物则获取召唤师）
        var targetPlayer = GetPlayerOrSummoner(instant.TargetId, instant.TargetName, out bool isTargetSummoned);
        if (targetPlayer != null)
        {
            var summonerId = isTargetSummoned ? _summonedToSummoner[instant.TargetId] : instant.TargetId;
            // 根据效果类型判断是治疗还是承伤
            if (instant.EffectType == EffectTypeSeq.InstantHp || instant.EffectType == EffectTypeSeq.IntervalHp ||
                instant.EffectType == EffectTypeSeq.InstantSp || instant.EffectType == EffectTypeSeq.IntervalSp)
            {
                // 治疗/内力恢复事件，需要交换角色：召唤师作为施法者，目标保持不变
                var originalObjectType = instant.ObjectType;
                var originalCasterId = instant.CasterId;
                var originalCasterName = instant.CasterName;

                instant.ObjectType = ObjectTypeSeq.PlayerAttack; // 改为主动攻击类型
                instant.CasterId = summonerId; // 召唤师作为施法者
                instant.CasterName = targetPlayer.Name ?? instant.CasterName;
                instant.Summoned = true;
                targetPlayer.Add(instant);

                // 恢复所有原始值
                instant.ObjectType = originalObjectType;
                instant.CasterId = originalCasterId;
                instant.CasterName = originalCasterName;
                instant.Summoned = false;
            }
            else
            {
                // 承伤事件，直接处理
                instant.Summoned = true;
                targetPlayer.Add(instant);
            }
        }
    }

    /// <summary>
    /// 处理被攻击方事件（用于PlayerAttack和Other类型事件）
    /// </summary>
    private void ProcessTargetEvent(InstantEffectNotification2 instant)
    {
        // 只有当目标不是施法者本人时才处理被攻击数据
        if (instant.TargetId != 0 && instant.TargetId != instant.CasterId)
        {
            // 获取目标玩家（如果是召唤物则获取召唤师）
            var targetPlayer = GetPlayerOrSummoner(instant.TargetId, instant.TargetName, out bool isTargetSummoned);
            var summonerId = isTargetSummoned ? _summonedToSummoner[instant.TargetId] : instant.TargetId;

            if (targetPlayer != null)
            {
                // 直接处理被攻击事件，临时交换攻击者和被攻击者的角色
                var originalObjectType = instant.ObjectType;
                var originalCasterId = instant.CasterId;
                var originalCasterName = instant.CasterName;
                var originalTargetId = instant.TargetId;
                var originalTargetName = instant.TargetName;
                var originalEffectAlias = instant.EffectAlias;

                // 交换角色：被攻击方成为"施法者"，攻击方成为"目标"
                instant.ObjectType = ObjectTypeSeq.PlayerAttacked;
                instant.CasterId = isTargetSummoned ? summonerId : originalTargetId;
                instant.CasterName = isTargetSummoned ? (targetPlayer.Name ?? originalTargetName) : originalTargetName;
                instant.TargetId = originalCasterId;
                instant.TargetName = originalCasterName;

                // 如果目标是召唤物，标记为召唤物事件
                if (isTargetSummoned)
                {
                    instant.Summoned = true;
                }

                targetPlayer.Add(instant);

                // 恢复原始值
                instant.ObjectType = originalObjectType;
                instant.CasterId = originalCasterId;
                instant.CasterName = originalCasterName;
                instant.TargetId = originalTargetId;
                instant.TargetName = originalTargetName;
                instant.EffectAlias = originalEffectAlias;
            }
        }
    }

    /// <summary>
    /// 检查指定ID是否为某个玩家的召唤兽
    /// </summary>
    /// <param name="summonedId">可能的召唤兽ID</param>
    /// <param name="summonerId">召唤师ID</param>
    /// <returns>如果summonedId是summonerId的召唤兽返回true，否则返回false</returns>
    public bool IsSummonedBy(long summonedId, long summonerId)
    {
        return _summonedToSummoner.TryGetValue(summonedId, out var actualSummonerId) && actualSummonerId == summonerId;
    }

    /// <summary>
    /// 获取玩家对象，如果是召唤物则返回对应的召唤师
    /// </summary>
    /// <param name="entityId">实体ID（可能是玩家或召唤物）</param>
    /// <param name="entityName">实体名称</param>
    /// <param name="isSummoned">输出参数，表示是否为召唤物</param>
    /// <returns>玩家对象（如果是召唤物则返回召唤师对象）</returns>
    private CreatureStats? GetPlayerOrSummoner(long playerId, string? playerName, out bool isSummoned)
    {
        isSummoned = false;

        // 如果是召唤物，则应该返回召唤师的统计对象
        if (_summonedToSummoner.TryGetValue(playerId, out var summonerId))
        {
            isSummoned = true;
            playerId = summonerId;
        }

        // 通过对象ID查找
        return _playersByPlayerId.TryGetValue(playerId, out var player) ? player : null;
    }

    /// <summary>
    /// 刷新视图
    /// </summary>
    public void Refresh()
    {
        if (IsHistory) return;

        EndTime = DateTime.Now;
        OnPropertyChanged(nameof(View));
        OnPropertyChanged(nameof(TimeSpan));
    }

    /// <summary>
    /// Saves the current log to a JSON file.
    /// </summary>
    public void Save(int zone)
    {
        try
        {
            // 检查是否禁用战斗记录日志
            if (SettingHelper.Default.DisableBattleLog) return;
            if (IsHistory || !StartTime.HasValue || TimeSpan.TotalSeconds < 10) return;

            // 再次检查StartTime以确保安全
            if (!StartTime.HasValue) return;

            var logs = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
            Directory.CreateDirectory(logs);
            File.WriteAllText(Path.Combine(logs, $"act_{zone}_{StartTime.Value.Ticks}.json"), JsonConvert.SerializeObject(this));
        }
        catch (Exception e)
        {
            Log.Error(e, "Save act log failed.");
        }
    }
    #endregion

    #region Target Management
    /// <summary>
    /// 清空目标缓存，在重置时调用
    /// </summary>
    internal void ClearTargetCache()
    {
        _targetNamesCache.Clear();
        _targetDamageCache.Clear();
    }

    /// <summary>
    /// 获取所有目标名称列表（从缓存中直接获取）
    /// </summary>
    public IEnumerable<string> GetTargetNames()
    {
        return _targetNamesCache.Keys;
    }

    /// <summary>
    /// 获取对指定目标的总伤害（从缓存中直接获取）
    /// </summary>
    public long GetTotalDamageForTarget(string targetName)
    {
        return _targetDamageCache.GetValueOrDefault(targetName, 0);
    }

    /// <summary>
    /// 获取来自指定目标的总承受伤害
    /// </summary>
    public long GetTotalDamageTakenForTarget(string targetName)
    {
        // 计算所有玩家从指定目标承受的总伤害
        return this.Where(p => p.Job <= JobSeq.PcMax) // 只包含真正的玩家
            .SelectMany(p => p.TakenDamageSkills)
            .Where(s => s.TargetName == targetName)
            .Sum(s => s.TotalDamage);
    }

    /// <summary>
    /// 获取对指定目标的总治疗量
    /// </summary>
    public long GetTotalHealingForTarget(string targetName)
    {
        return this.Where(p => p.Job <= JobSeq.PcMax) // 只包含真正的玩家
            .SelectMany(p => p.HealSkills)
            .Where(s => s.TargetName == targetName)
            .Sum(s => s.TotalDamage);
    }

    /// <summary>
    /// 获取对指定目标的技能统计列表（所有队伍成员的技能）
    /// </summary>
    public IEnumerable<SkillStats> GetSkillsForTarget(string targetName)
    {
        // 返回所有队伍成员对该目标的技能统计
        return this.Where(p => p.Job <= JobSeq.PcMax) // 只包含真正的玩家
            .SelectMany(p => p.DamageSkills)
            .Where(s => s.TargetName == targetName);
    }

    /// <summary>
    /// 检查目标死亡，如果所有正在统计的目标都死亡，则触发事件
    /// </summary>
    /// <param name="deadTarget">死亡的目标名称</param>
    public void CheckTargetDeath(string? deadTarget)
    {
        if (string.IsNullOrWhiteSpace(deadTarget)) return;

        // 获取所有目标名称
        var allTargets = GetTargetNames().ToHashSet();
        if (allTargets.Count == 0) return;

        // 检查死亡的目标是否在我们的目标列表中
        if (!allTargets.Contains(deadTarget)) return;
        _deadTargets.Add(deadTarget);

        // 获取剩余存活目标
        var aliveTargets = allTargets.Except(_deadTargets);
        if (!aliveTargets.Any()) AllTargetsDead?.Invoke();
    }

    /// <summary>
    /// 根据当前目标过滤器计算过滤后的战斗时间
    /// </summary>
    /// <param name="player">玩家统计数据</param>
    /// <returns>过滤后的战斗时间（秒）</returns>
    public double GetFilteredSeconds(CreatureStats player)
    {
        // 如果没有目标过滤器，返回整体战斗时间
        if (string.IsNullOrEmpty(TargetFilter))
        {
            return player.StartTime.HasValue ? Math.Max(1.0, Math.Round((player.EndTime - player.StartTime.Value).TotalSeconds, 0)) : 0;
        }

        // 如果有目标过滤器，计算对该目标的战斗时间
        var targetNames = TargetFilter.Split(',', StringSplitOptions.RemoveEmptyEntries);
        var targetSkills = player.DamageSkills.Where(x => targetNames.Contains(x.TargetName));
        if (!targetSkills.Any()) return 0;

        var startTimes = targetSkills.Where(s => s.StartTime.HasValue).Select(s => s.StartTime!.Value);
        var endTimes = targetSkills.Select(s => s.EndTime);

        if (!startTimes.Any()) return 0;

        var minStart = startTimes.Min();
        var maxEnd = endTimes.Max();

        return Math.Max(1.0, Math.Round((maxEnd - minStart).TotalSeconds, 0));
    }

    /// <summary>
    /// 根据当前目标过滤器计算过滤后的伤害总量
    /// </summary>
    /// <param name="skills">技能统计集合</param>
    /// <returns>过滤后的伤害总量</returns>
    public long GetFilteredDamage(ObservableCollection<SkillStats> skills)
    {
        // 如果没有目标过滤器，返回所有技能的总伤害
        if (string.IsNullOrEmpty(TargetFilter)) return skills.Sum(x => x.TotalDamage);

        // 如果有目标过滤器，只计算对应目标的伤害
        var targetNames = TargetFilter.Split(',', StringSplitOptions.RemoveEmptyEntries);
        return skills.Where(x => targetNames.Contains(x.TargetName)).Sum(x => x.TotalDamage);
    }
    #endregion

    #region Interface
    public event PropertyChangedEventHandler? PropertyChanged;

    public void OnPropertyChanged(string name)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }
    #endregion
}

/// <summary>
/// 统计类型
/// </summary>
public enum StatisticsType
{
    Damage,      // 伤害输出
    DamageTaken, // 承受伤害
    Healing      // 治疗
}