using System.Text;
using Xylia.Preview.Data.Engine;
using Xylia.Updater;

namespace Xylia.BnsHelper.Services.Network.Service;
internal class UpdateConfigPacket : UpdateInfoEventArgs, IPacket
{
    #region Fields
    public byte AppType;        // Application type, 0x1 for BNS Helper
    public Version? Version;    // Application version

    // 响应字段
    public uint ErrorCode;
    public string? ErrorMessage;
    #endregion

    #region Methods
    public DataArchiveWriter Create()
    {
        var archive = new DataArchiveWriter();
        archive.Write(AppType);
        archive.WriteString(Version!.ToString(), Encoding.UTF8);
        return archive;
    }

    public void Read(DataArchive archive)
    {
        try
        {
            ErrorCode = archive.Read<uint>();
            if (ErrorCode != 0)
            {
                ErrorMessage = archive.ReadString();
                return;
            }

            // 读取更新配置字段（按服务端发送顺序）
            if (archive.Position < archive.Length)
            {
                DownloadURL = archive.ReadString();
                ExecutablePath = archive.ReadString();
                var sum = archive.ReadString();
                if (!string.IsNullOrEmpty(sum)) CheckSum = new CheckSum() { Value = sum, HashingAlgorithm = "SHA256" };
            }
        }
        catch (Exception ex)
        {
            ErrorCode = 500;
            ErrorMessage = $"解析响应失败: {ex.Message}";
        }
    }
    #endregion
}
