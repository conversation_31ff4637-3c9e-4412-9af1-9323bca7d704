package service

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strconv"
	"strings"
	"time"

	"udp-server/server/internal/gateway/model"
	gatewayModel "udp-server/server/internal/gateway/model"
	userModel "udp-server/server/internal/model"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// AdminService 管理后台专用服务
type AdminService struct {
	db    *gorm.DB
	cache cache.Cache
}

// 创建管理后台服务
func NewAdminService(db *gorm.DB, cache cache.Cache) *AdminService {
	return &AdminService{
		db:    db,
		cache: cache,
	}
}

// 格式化权限过期时间
func (s *AdminService) formatPermissionExpiration(expiration int64) string {
	switch expiration {
	case -1:
		return "永久"
	case 0:
		return "无权限"
	default:
		return time.Unix(expiration, 0).Format("2006-01-02 15:04:05")
	}
}

// 生成管理员令牌
func (s *AdminService) GenerateAdminToken(adminID uint64) (string, error) {
	// 生成32字节随机令牌
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	token := hex.EncodeToString(bytes)

	// 存储到缓存，有效期24小时
	cacheKey := fmt.Sprintf("admin_token:%s", token)
	if err := s.cache.Set(cacheKey, adminID, 24*time.Hour); err != nil {
		return "", err
	}

	return token, nil
}

// 验证管理员令牌
func (s *AdminService) ValidateAdminToken(token string) (uint64, error) {
	cacheKey := fmt.Sprintf("admin_token:%s", token)
	var adminID uint64
	if err := s.cache.Get(cacheKey, &adminID); err != nil {
		return 0, fmt.Errorf("令牌无效或已过期")
	}

	return adminID, nil
}

// 验证管理员账号
func (s *AdminService) ValidateAdminLogin(username, password string) (*gatewayModel.Admin, error) {
	var admin gatewayModel.Admin

	err := s.db.Table("bns_useradmin").
		Where("username = ? AND isAction = 1", username).
		First(&admin).Error

	if err == gorm.ErrRecordNotFound {
		logger.Warn("管理员用户不存在: username=%s", username)
		return nil, nil
	} else if err != nil {
		logger.Error("查询管理员失败: %v", err)
		return nil, fmt.Errorf("查询管理员失败: %w", err)
	}

	logger.Info("找到管理员: UID=%d, Username=%s, Power=%s, Super=%d", admin.UID, admin.Username, admin.Power, admin.Super)

	// 验证密码
	expectedHash := s.GenerateAdminPassword(password)
	if expectedHash != admin.Password {
		logger.Warn("管理员密码验证失败: username=%s", username)
		return nil, fmt.Errorf("密码验证失败")
	}

	return &admin, nil
}

// 为管理员生成新密码哈希
func (s *AdminService) GenerateAdminPassword(password string) string {
	// 使用固定盐值，实际生产环境应该使用随机盐值
	salt := "bnszs_salt_2024"

	// 组合密码和盐值
	combined := password + salt

	// 计算SHA256哈希
	hash := sha256.Sum256([]byte(combined))

	// 返回十六进制字符串
	return hex.EncodeToString(hash[:])
}

// 获取管理员列表
func (s *AdminService) GetAdminList() ([]map[string]interface{}, error) {
	var admins []struct {
		UID      uint64 `gorm:"column:uid"`
		Username string `gorm:"column:username"`
		Power    string `gorm:"column:power"`
		IsAction int    `gorm:"column:isAction"`
		Super    int    `gorm:"column:super"`
	}

	err := s.db.Table("bns_useradmin").
		Select("uid, username, power, isAction, super").
		Find(&admins).Error

	if err != nil {
		return nil, fmt.Errorf("查询管理员列表失败: %v", err)
	}

	result := make([]map[string]interface{}, len(admins))
	for i, admin := range admins {
		result[i] = map[string]interface{}{
			"uid":       admin.UID,
			"username":  admin.Username,
			"power":     admin.Power,
			"is_action": admin.IsAction,
			"is_super":  admin.Super == 1,
		}
	}

	return result, nil
}

// 创建管理员
func (s *AdminService) CreateAdmin(username, password, power string, isSuper bool) (uint64, error) {
	// 检查用户名是否已存在
	var count int64
	err := s.db.Table("bns_useradmin").
		Where("username = ?", username).
		Count(&count).Error

	if err != nil {
		return 0, fmt.Errorf("检查用户名失败: %v", err)
	}

	if count > 0 {
		return 0, fmt.Errorf("用户名已存在")
	}

	// 如果没有指定权限，设置默认权限
	if power == "" {
		if isSuper {
			power = "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20"
		} else {
			power = "1,2,3,4,5"
		}
	}

	// 加密密码
	hashedPassword := s.GenerateAdminPassword(password)

	// 插入新管理员
	admin := map[string]interface{}{
		"username": username,
		"password": hashedPassword,
		"power":    power,
		"isAction": 1,
	}

	result := s.db.Table("bns_useradmin").Create(&admin)
	if result.Error != nil {
		return 0, fmt.Errorf("创建管理员失败: %v", result.Error)
	}

	// 获取插入的ID
	var newAdmin struct {
		UID uint64 `gorm:"column:uid"`
	}
	err = s.db.Table("bns_useradmin").
		Where("username = ?", username).
		Select("uid").
		First(&newAdmin).Error

	if err != nil {
		return 0, fmt.Errorf("获取新管理员ID失败: %v", err)
	}

	return newAdmin.UID, nil
}

// 创建管理员操作日志
func (s *AdminService) CreateAdminLog(log *gatewayModel.AdminLog) error {
	return s.db.Create(log).Error
}

// 更新管理员信息
func (s *AdminService) UpdateAdmin(adminID string, username, password string, isAction int) error {
	updateData := map[string]interface{}{
		"username": username,
		"isAction": isAction,
	}

	// 如果提供了新密码，则更新密码
	if password != "" {
		updateData["password"] = s.GenerateAdminPassword(password)
	}

	err := s.db.Table("bns_useradmin").
		Where("uid = ?", adminID).
		Updates(updateData).Error

	if err != nil {
		return fmt.Errorf("更新管理员基本信息失败: %v", err)
	}

	return nil
}

// 删除管理员
func (s *AdminService) DeleteAdmin(adminID string) error {
	err := s.db.Table("bns_useradmin").
		Where("uid = ?", adminID).
		Update("isAction", 0).Error

	if err != nil {
		return fmt.Errorf("删除管理员失败: %v", err)
	}

	return nil
}

// 获取特定管理员详情
func (s *AdminService) GetAdminByID(adminID string) (*model.Admin, error) {
	var admin model.Admin
	err := s.db.Table("bns_useradmin").
		Select("uid, username, power, isAction, super").
		Where("uid = ?", adminID).
		First(&admin).Error

	if err != nil {
		return nil, fmt.Errorf("查询管理员详情失败: %v", err)
	}

	return &admin, nil
}

// 获取特定用户详情
func (s *AdminService) GetUserByQQ(qqNumber string, authService *service.AuthService) (map[string]interface{}, error) {
	var user userModel.User

	// 将字符串转换为uint64
	qqNum, err := strconv.ParseUint(qqNumber, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("无效的QQ号格式: %v", err)
	}

	err = s.db.Where("uin = ?", qqNum).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("查询用户详情失败: %v", err)
	}

	// 检查用户是否在线
	isOnline := authService.IsUserOnline(int64(user.Uin))

	result := map[string]interface{}{
		"id":         user.UID,
		"uid":        user.UID,
		"qq_number":  strconv.FormatUint(user.Uin, 10),
		"uin":        user.Uin,
		"name":       user.Name,
		"status":     user.Status,
		"is_online":  isOnline,
		"login_time": time.Unix(user.LoginTime, 0).Format("2006-01-02 15:04:05"),
		"created_at": time.Unix(user.LoginTime, 0).Format("2006-01-02 15:04:05"),
	}

	if user.LoginTime > 0 {
		result["login_time"] = time.Unix(user.LoginTime, 0).Format("2006-01-02 15:04:05")
	} else {
		result["login_time"] = "从未登录"
	}

	return result, nil
}

// 获取用户列表
func (s *AdminService) GetUsers(page, limit int, search string, statusFilter string, onlineFilter string, permissionFilter string, permissionService *service.PermissionService, authService *service.AuthService) ([]map[string]interface{}, error) {
	var users []userModel.User
	offset := (page - 1) * limit

	query := s.db.Model(&userModel.User{})

	// 搜索条件
	if search != "" {
		// 尝试将搜索词转换为数字（用于QQ号搜索）
		if qqNumber, err := strconv.ParseUint(search, 10, 64); err == nil {
			query = query.Where("uin = ? OR name LIKE ?", qqNumber, "%"+search+"%")
		} else {
			query = query.Where("name LIKE ?", "%"+search+"%")
		}
	}

	// 状态筛选
	if statusFilter != "" {
		if status, err := strconv.Atoi(statusFilter); err == nil {
			query = query.Where("status = ?", status)
		}
	}

	// 如果有在线状态筛选或权限筛选，需要获取更多数据进行内存筛选
	queryLimit := limit
	queryOffset := offset
	if onlineFilter != "" || permissionFilter != "" {
		// 获取更多数据以便筛选后能有足够的结果
		queryLimit = limit * 3 // 获取3倍的数据
		queryOffset = 0        // 从头开始获取
	}

	err := query.Order("uid DESC").Offset(queryOffset).Limit(queryLimit).Find(&users).Error
	if err != nil {
		return nil, err
	}

	result := make([]map[string]interface{}, 0, len(users))
	for _, user := range users {
		// 检查用户是否在线
		isOnline := authService.IsUserOnline(int64(user.Uin))

		// 应用在线状态筛选
		if onlineFilter != "" {
			if onlineFilter == "online" && !isOnline {
				continue // 跳过离线用户
			}
			if onlineFilter == "offline" && isOnline {
				continue // 跳过在线用户
			}
		}

		// 获取权限过期时间和权限等级
		var permissionExpiration int64
		var permissionExpirationText string
		var permissionLevel uint8 = 0

		if permissionService != nil {
			// 获取权限过期时间
			expiration, err := permissionService.GetPermissionExpiration(user.UID, "client")
			if err != nil {
				logger.Warn("获取用户权限过期时间失败: UID=%d, Error=%v", user.UID, err)
				permissionExpiration = 0
				permissionExpirationText = "获取失败"
			} else {
				permissionExpiration = expiration
				permissionExpirationText = s.formatPermissionExpiration(expiration)
			}

			// 获取基于CDKey的权限等级
			level, err := permissionService.GetUserPermissionLevel(user.UID, "client")
			if err != nil {
				logger.Warn("获取用户权限等级失败: UID=%d, Error=%v", user.UID, err)
				permissionLevel = 0
			} else {
				permissionLevel = level
			}
		} else {
			permissionExpiration = 0
			permissionExpirationText = "未知"
			permissionLevel = 0
		}

		// 应用权限筛选
		if permissionFilter != "" {
			if filterLevel, err := strconv.Atoi(permissionFilter); err == nil {
				if int(permissionLevel) != filterLevel {
					continue // 跳过不匹配权限等级的用户
				}
			}
		}

		userInfo := map[string]interface{}{
			"id":                         user.UID,
			"qq_number":                  user.Uin,
			"name":                       user.Name,       // 添加用户名字段
			"status":                     user.Status,     // 添加用户状态字段
			"permission":                 permissionLevel, // 使用基于CDKey计算的权限等级
			"permission_expiration":      permissionExpiration,
			"permission_expiration_text": permissionExpirationText,
			"is_online":                  isOnline,
			"login_time":                 user.GetLoginTime().Format("2006-01-02 15:04:05"),
			"token_time":                 user.GetTokenTime().Format("2006-01-02 15:04:05"),
		}

		// 用户名字段已经包含在userInfo中

		result = append(result, userInfo)

		// 如果是内存筛选（在线状态或权限筛选），检查是否已获得足够的结果
		if (onlineFilter != "" || permissionFilter != "") && len(result) >= limit {
			break
		}
	}

	return result, nil
}

// 获取用户列表（保留原方法，用于在线用户）
func (s *AdminService) GetUserList(search string, authService *service.AuthService) ([]map[string]interface{}, error) {
	// 获取在线用户列表
	onlineUsers := authService.GetOnlineUsers()
	result := make([]map[string]interface{}, 0)

	for _, qqNumber := range onlineUsers {
		// 如果有搜索条件，进行过滤
		if search != "" {
			qqStr := fmt.Sprintf("%d", qqNumber)
			if !strings.Contains(qqStr, search) {
				continue
			}
		}

		// 获取用户会话信息
		sessionInfo := authService.GetUserSessionInfo(qqNumber)

		userInfo := map[string]interface{}{
			"qq_number":   qqNumber,
			"token":       sessionInfo["token"],
			"is_active":   sessionInfo["online"],
			"last_active": sessionInfo["login_time"],
		}

		result = append(result, userInfo)
	}

	return result, nil
}

// 搜索用户（从数据库搜索）
func (s *AdminService) SearchUsers(search string, permissionService *service.PermissionService, authService *service.AuthService) ([]map[string]interface{}, error) {
	var users []userModel.User

	// 构建搜索查询
	query := s.db.Model(&userModel.User{})
	if search != "" {
		// 尝试将搜索词转换为数字（QQ号）
		if qqNumber, err := strconv.ParseInt(search, 10, 64); err == nil {
			query = query.Where("uin = ?", qqNumber)
		} else {
			// 如果不是数字，按用户名搜索
			query = query.Where("name LIKE ?", "%"+search+"%")
		}
	}

	err := query.Order("created_at DESC").Limit(50).Find(&users).Error
	if err != nil {
		return nil, err
	}

	result := make([]map[string]interface{}, len(users))
	for i, user := range users {
		// 检查用户是否在线
		isOnline := authService.IsUserOnline(int64(user.Uin))

		// 获取权限过期时间和权限等级
		var permissionExpiration int64
		var permissionExpirationText string
		var permissionLevel uint8 = 0

		if permissionService != nil {
			// 获取权限过期时间
			expiration, err := permissionService.GetPermissionExpiration(user.UID, "client")
			if err != nil {
				logger.Warn("获取用户权限过期时间失败: UID=%d, Error=%v", user.UID, err)
				permissionExpiration = 0
				permissionExpirationText = "获取失败"
			} else {
				permissionExpiration = expiration
				permissionExpirationText = s.formatPermissionExpiration(expiration)
			}

			// 获取基于CDKey的权限等级
			level, err := permissionService.GetUserPermissionLevel(user.UID, "client")
			if err != nil {
				logger.Warn("获取用户权限等级失败: UID=%d, Error=%v", user.UID, err)
				permissionLevel = 0
			} else {
				permissionLevel = level
			}
		} else {
			permissionExpiration = 0
			permissionExpirationText = "未知"
			permissionLevel = 0
		}

		result[i] = map[string]interface{}{
			"id":                         user.UID,
			"qq_number":                  user.Uin,
			"name":                       user.Name,
			"status":                     user.Status,
			"permission":                 permissionLevel, // 使用基于CDKey计算的权限等级
			"permission_expiration":      permissionExpiration,
			"permission_expiration_text": permissionExpirationText,
			"is_online":                  isOnline,
			"login_time":                 user.GetLoginTime().Format("2006-01-02 15:04:05"),
			"token_time":                 user.GetTokenTime().Format("2006-01-02 15:04:05"),
		}
	}

	return result, nil
}

// GetTotalUsers 获取用户总数
func (s *AdminService) GetTotalUsers() int64 {
	var count int64
	s.db.Model(&userModel.User{}).Count(&count)
	return count
}
