using Xylia.BnsHelper.Models.Triggers;
using Xylia.BnsHelper.ViewModels.Dialogs;

namespace Xylia.BnsHelper.Views.Dialogs;
public partial class TriggerActionSelectorDialog
{
    #region Constructor
    readonly TriggerActionSelectorViewModel _viewModel;

    public TriggerActionSelectorDialog()
    {
        InitializeComponent();

        DataContext = _viewModel = new TriggerActionSelectorViewModel();
        _viewModel.CloseRequested += OnCloseRequested;
    }
    #endregion

    #region Methods
    public TriggerAction? SelectedAction => _viewModel.CreateAction();

    private void OnCloseRequested(object? sender, EventArgs e)
    {
        DialogResult = _viewModel.DialogResult;
        Close();
    }

    protected override void OnClosed(EventArgs e)
    {
        _viewModel.CloseRequested -= OnCloseRequested;
        base.OnClosed(e);
    }
    #endregion
}
