package model

// 团队成员模型
type Team struct {
	ID        int    `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name      string `gorm:"column:name;not null" json:"name"`
	UIN       int64  `gorm:"column:uin;not null" json:"uin"`
	Slogan    string `gorm:"column:slogan" json:"slogan"`
	Strengths string `gorm:"column:strengths" json:"strengths"`
	URL       string `gorm:"column:url" json:"url"`
	Type      string `gorm:"column:type;type:enum('author','friends');not null" json:"type"`
}

// TableName 指定表名
func (Team) TableName() string {
	return "bns_team"
}
