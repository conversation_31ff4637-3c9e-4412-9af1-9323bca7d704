package model

import (
	"time"
)

// 管理员模型
type Admin struct {
	UID      uint64 `gorm:"column:uid;primaryKey" json:"id"`
	Username string `gorm:"column:username" json:"username"`
	Password string `gorm:"column:password" json:"-"` // 密码字段不序列化到JSON
	Power    string `gorm:"column:power" json:"power"`
	IsAction int    `gorm:"column:isAction" json:"-"`     // 不暴露给前端
	Super    bool   `gorm:"column:super" json:"is_super"` // 不直接暴露
	Token    string ` gorm:"-" json:"token"`              // 仅用于返回数据
}

// 指定表名
func (Admin) TableName() string {
	return "bns_useradmin"
}

// 管理员操作日志模型
type AdminLog struct {
	ID        uint      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Admin     uint64    `gorm:"column:admin;not null;index" json:"admin"`           // 管理员ID
	User      uint64    `gorm:"column:user;index" json:"user"`                      // 目标用户ID
	Action    string    `gorm:"column:action;size:50;not null;index" json:"action"` // 操作类型
	Details   string    `gorm:"column:details;size:500" json:"details"`             // 操作详情
	IPAddress string    `gorm:"column:ip_address;size:45" json:"ip_address"`        // IP地址
	Time      time.Time `gorm:"column:time;not null;index" json:"time"`             // 操作时间
}

// 指定表名
func (AdminLog) TableName() string {
	return "bns_useradminlog"
}
