using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Win32;
using Newtonsoft.Json;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Windows;
using Xylia.BnsHelper.Common.Converters;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models.Triggers;
using Xylia.BnsHelper.Services;
using Xylia.BnsHelper.Views.Dialogs;
using Trigger = Xylia.BnsHelper.Models.Triggers.Trigger;

namespace Xylia.BnsHelper.ViewModels.Pages;
internal partial class TriggerManagerViewModel : ObservableObject
{
    #region Constructor
    private readonly TriggerService _triggerService = TriggerService.Instance;

    public TriggerManagerViewModel()
    {
        _isEnabled = _triggerService.IsEnabled;

        // 订阅事件
        _triggerService.TriggerExecuted += OnTriggerExecuted;
        _triggerService.TriggerCollectionChanged += OnTriggerCollectionChanged;

        // 初始化根文件夹
        InitializeRootFolders();

        // 加载配置
        LoadConfiguration();

        // 更新统计信息
        UpdateStats();

        // 定时更新统计信息
        var timer = new System.Timers.Timer(1000);
        timer.Elapsed += (_, _) => UpdateStats();
        timer.Start();
    }
    #endregion

    #region Fields
    [ObservableProperty] ObservableCollection<TriggerFolder> _rootFolders = new();
    [ObservableProperty] object? _selectedItem;
    [ObservableProperty] bool _isEnabled = true;
    [ObservableProperty] string _statusMessage = "就绪";

    // 剪贴板
    private object? _clipboardItem;
    private bool _isCutOperation = false;

    // 统计信息
    [ObservableProperty] int _totalTriggers;
    [ObservableProperty] int _enabledTriggers;
    [ObservableProperty] int _totalExecutions;
    #endregion

    #region Methods
    /// <summary>
    /// 初始化根文件夹
    /// </summary>
    private void InitializeRootFolders()
    {
        RootFolders.Add(new TriggerFolder { Name = "本地触发器", IsExpanded = true });
        //RootFolders.Add(new TriggerFolder { Name = "远程触发器", IsExpanded = true, IsRemote = true });
    }

    /// <summary>
    /// 初始化示例触发器
    /// </summary>
    private void InitializeSampleTriggers()
    {
        try
        {
            // 示例触发器：获得物品
            var itemTrigger = new Trigger
            {
                Name = "获得物品提醒",
                IsEnabled = true,
                Priority = 3,
                Cooldown = 0
            };
            itemTrigger.Conditions.Add(new RegexCondition
            {
                Pattern = @"获得(?<count>\d+)个(?<name>.*)",
                IgnoreCase = true,
            });
            itemTrigger.Actions.Add(new SendGameMessageAction
            {
                Message = "获得了 ${count} 个 ${name}",
            });
            itemTrigger.Actions.Add(new AudioPlayAction
            {
                AudioPath = "E:\\Build\\DotNet\\Xylia\\BnsHelper\\BnsHelper\\Resources\\Musics\\alarm.mp3",
                Volume = 70
            });

            RootFolders.First().AddNode(itemTrigger);
            _triggerService.AddTrigger(itemTrigger);

            Debug.WriteLine("[TriggerManagerViewModel] 已添加示例触发器");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[TriggerManagerViewModel] 初始化示例触发器失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 切换管理器启用状态
    /// </summary>
    [RelayCommand]
    private void ToggleManager()
    {
        _triggerService.IsEnabled = !_triggerService.IsEnabled;
        IsEnabled = _triggerService.IsEnabled;
        StatusMessage = $"触发器管理器已{(IsEnabled ? "启用" : "禁用")}";
    }

    /// <summary>
    /// 切换项目启用状态
    /// </summary>
    [RelayCommand]
    private void ToggleItem(object item)
    {
        if (item == null) return;

        switch (item)
        {
            case Trigger trigger:
                trigger.IsEnabled = !trigger.IsEnabled;
                StatusMessage = $"触发器 '{trigger.Name}' 已{(trigger.IsEnabled ? "启用" : "禁用")}";
                break;
            case TriggerFolder folder:
                folder.IsEnabled = !folder.IsEnabled;
                StatusMessage = $"文件夹 '{folder.Name}' 已{(folder.IsEnabled ? "启用" : "禁用")}";
                break;
        }
    }

    /// <summary>
    /// 创建子文件夹
    /// </summary>
    [RelayCommand]
    private void CreateSubFolder(TriggerFolder parent)
    {
        if (parent == null) return;

        var folder = new TriggerFolder { Name = "新子文件夹" , IsEnabled = parent.IsEnabled };
        parent.AddNode(folder);
        StatusMessage = $"已在 '{parent.Name}' 中创建{folder.Name}";
    }

    /// <summary>
    /// 创建新触发器
    /// </summary>
    [RelayCommand]
    private void CreateTrigger(TriggerFolder folder)
    {
        if (folder == null) return;

        var dialog = new TriggerEditorDialog(null) { Owner = Application.Current.MainWindow };
        if (dialog.ShowDialog() == true)
        {
            dialog.Trigger.IsEnabled = folder.IsEnabled;
            folder.AddNode(dialog.Trigger);
            _triggerService.AddTrigger(dialog.Trigger);

            SaveConfiguration(); // 自动保存
            StatusMessage = $"已在 '{folder.Name}' 中创建新触发器";
        }
    }

    /// <summary>
    /// 编辑项目
    /// </summary>
    [RelayCommand]
    private void EditItem(object item)
    {
        if (item == null) return;

        StatusMessage = item switch
        {
            Trigger trigger => $"编辑触发器 '{trigger.Name}'",
            TriggerFolder folder => $"编辑文件夹 '{folder.Name}'",
            _ => "编辑项目"
        };
    }

    /// <summary>
    /// 编辑触发器
    /// </summary>
    [RelayCommand]
    private void EditTrigger(Trigger trigger)
    {
        if (trigger == null) return;

        var dialog = new TriggerEditorDialog(trigger) { Owner = Application.Current.MainWindow };
        if (dialog.ShowDialog() == true)
        {
            SaveConfiguration(); // 自动保存
            StatusMessage = $"已更新触发器 '{trigger.Name}' 的条件";
        }
    }

    /// <summary>
    /// 复制项目
    /// </summary>
    [RelayCommand]
    private void CopyItem(object item)
    {
        if (item == null) return;

        _clipboardItem = item;
        _isCutOperation = false;

        StatusMessage = item switch
        {
            Trigger trigger => $"已复制触发器 '{trigger.Name}'",
            TriggerFolder folder => $"已复制文件夹 '{folder.Name}'",
            _ => "已复制项目"
        };
    }

    /// <summary>
    /// 粘贴项目
    /// </summary>
    [RelayCommand]
    private void PasteItem(object targetItem)
    {
        if (_clipboardItem == null) return;

        try
        {
            switch (_clipboardItem)
            {
                case Trigger sourceTrigger:
                    PasteTrigger(sourceTrigger, targetItem);
                    break;
                case TriggerFolder sourceFolder:
                    PasteFolder(sourceFolder, targetItem);
                    break;
            }

            // 如果是剪切操作，清空剪贴板
            if (_isCutOperation)
            {
                _clipboardItem = null;
                _isCutOperation = false;
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"粘贴失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 粘贴触发器
    /// </summary>
    private void PasteTrigger(Trigger sourceTrigger, object targetItem)
    {
        var clonedTrigger = (Trigger)sourceTrigger.Clone();

        // 找到目标文件夹
        TriggerFolder? targetFolder = targetItem switch
        {
            TriggerFolder folder => folder,
            Trigger trigger => FindParentFolder(trigger),
            _ => RootFolders.FirstOrDefault()
        };

        if (targetFolder != null)
        {
            targetFolder.AddNode(clonedTrigger);
            _triggerService.AddTrigger(clonedTrigger);
            SaveConfiguration(); // 自动保存
            StatusMessage = $"已粘贴触发器 '{clonedTrigger.Name}' 到 '{targetFolder.Name}'";
        }
    }

    /// <summary>
    /// 粘贴文件夹
    /// </summary>
    private void PasteFolder(TriggerFolder sourceFolder, object targetItem)
    {
        var clonedFolder = (TriggerFolder)sourceFolder.Clone();

        // 找到目标文件夹
        TriggerFolder? targetFolder = targetItem switch
        {
            TriggerFolder folder => folder,
            Trigger trigger => FindParentFolder(trigger),
            _ => null
        };

        if (targetFolder != null)
        {
            targetFolder.AddNode(clonedFolder);
            StatusMessage = $"已粘贴文件夹 '{clonedFolder.Name}' 到 '{targetFolder.Name}'";
        }
        else
        {
            RootFolders.Add(clonedFolder);
            StatusMessage = $"已粘贴文件夹 '{clonedFolder.Name}' 到根目录";
        }
    }

    /// <summary>
    /// 删除项目
    /// </summary>
    [RelayCommand]
    private void DeleteItem(object item)
    {
        if (item == null) return;

        var itemName = item switch
        {
            Trigger trigger => trigger.Name,
            TriggerFolder folder => folder.Name,
            _ => "项目"
        };

        var result = MessageBox.Show($"确定要删除 '{itemName}' 吗？", "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);
        if (result == MessageBoxResult.Yes)
        {
            try
            {
                switch (item)
                {
                    case Trigger trigger:
                        DeleteTrigger(trigger);
                        break;
                    case TriggerFolder folder:
                        DeleteFolder(folder);
                        break;
                }

                SaveConfiguration();  //自动保存
                StatusMessage = $"已删除 '{itemName}'";
            }
            catch (Exception ex)
            {
                StatusMessage = $"删除失败: {ex.Message}";
            }
        }
    }

    /// <summary>
    /// 删除触发器
    /// </summary>
    private void DeleteTrigger(Trigger trigger)
    {
        // 从服务中移除
        _triggerService.RemoveTrigger(trigger);

        // 从文件夹中移除
        var parentFolder = FindParentFolder(trigger);
        parentFolder?.RemoveNode(trigger);
    }

    /// <summary>
    /// 删除文件夹
    /// </summary>
    private void DeleteFolder(TriggerFolder folder)
    {
        // 递归删除所有子触发器
        var triggersToDelete = new List<Trigger>();
        CollectTriggersFromFolder(folder, triggersToDelete);

        foreach (var trigger in triggersToDelete)
        {
            _triggerService.RemoveTrigger(trigger);
        }

        // 从父文件夹中移除
        if (folder.Parent is TriggerFolder parentFolder)
        {
            parentFolder.RemoveNode(folder);
        }
        // 如果没有父目录则说明是根目录
        else
        {
            folder.RemoveNodes();
        }
    }


    /// <summary>
    /// 强制执行触发器
    /// </summary>
    [RelayCommand]
    private async Task TestTrigger(Trigger trigger)
    {
        if (trigger == null) return;

        await Task.Run(() => trigger.ExecuteAsync(string.Empty));
    }

    /// <summary>
    /// 触发器执行事件处理
    /// </summary>
    private void OnTriggerExecuted(object? sender, TriggerExecutedEventArgs e)
    {
        UpdateStats();
        StatusMessage = $"触发器 '{e.Trigger.Name}' 已执行";
    }

    /// <summary>
    /// 触发器集合变更事件处理
    /// </summary>
    private void OnTriggerCollectionChanged(object? sender, TriggerCollectionChangedEventArgs e)
    {
        UpdateStats();
    }

    /// <summary>
    /// 更新统计信息
    /// </summary>
    private void UpdateStats()
    {
        var triggers = _triggerService.Triggers;
        TotalTriggers = triggers.Count();
        EnabledTriggers = triggers.Count(t => t.IsEnabled);
        TotalExecutions = triggers.Sum(t => t.ExecutionCount);
    }

    /// <summary>
    /// 查找触发器的父文件夹
    /// </summary>
    private TriggerFolder? FindParentFolder(Trigger trigger)
    {
        return FindFolderContainingTrigger(RootFolders, trigger);
    }

    /// <summary>
    /// 递归查找包含指定触发器的文件夹
    /// </summary>
    private TriggerFolder? FindFolderContainingTrigger(IEnumerable<TriggerFolder> folders, Trigger trigger)
    {
        foreach (var folder in folders)
        {
            if (folder.Nodes.Contains(trigger)) return folder;

            var subResult = FindFolderContainingTrigger(folder.Nodes.OfType<TriggerFolder>(), trigger);
            if (subResult != null) return subResult;
        }
        return null;
    }

    /// <summary>
    /// 从文件夹中收集所有触发器
    /// </summary>
    private void CollectTriggersFromFolder(TriggerFolder folder, List<Trigger> triggers)
    {
        foreach (var node in folder.Nodes)
        {
            switch (node)
            {
                case Trigger trigger:
                    triggers.Add(trigger);
                    break;
                case TriggerFolder subFolder:
                    CollectTriggersFromFolder(subFolder, triggers);
                    break;
            }
        }
    }
    #endregion

    #region Configuration
    private readonly string _configPath = Path.Combine(SettingHelper.CacheFolder, "triggers.json");

    /// <summary>
    /// 加载触发器配置
    /// </summary>
    public void LoadConfiguration()
    {
        try
        {
            if (!File.Exists(_configPath))
            {
                InitializeSampleTriggers();
                return;
            }

            var json = File.ReadAllText(_configPath);
            var folders = JsonConvert.DeserializeObject<ObservableCollection<TriggerFolder>>(json, new JsonSerializerSettings
            {
                TypeNameHandling = TypeNameHandling.Objects,
                Converters = { new TriggerConverter() }
            });

            if (folders != null)
            {
                RootFolders.Clear();
                foreach (var folder in folders)
                {
                    RootFolders.Add(folder);
                    AddTriggersFromFolderToService(folder);
                }
                Debug.WriteLine($"[TriggerManagerViewModel] 已加载 {folders.Count} 个文件夹");
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[TriggerManagerViewModel] 加载配置失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 保存触发器配置
    /// </summary>
    public void SaveConfiguration()
    {
        try
        {
            var json = JsonConvert.SerializeObject(RootFolders, Formatting.Indented, new JsonSerializerSettings
            {
                TypeNameHandling = TypeNameHandling.Objects,
                Converters = { new TriggerConverter() }
            });

            File.WriteAllText(_configPath, json);

            // 计算触发器总数用于日志
            var triggerCount = RootFolders.Sum(f => CountTriggersInFolder(f));
            Debug.WriteLine($"[TriggerManagerViewModel] 已保存 {RootFolders.Count} 个文件夹（{triggerCount} 个触发器）到配置文件");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[TriggerManagerViewModel] 保存配置失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 导入触发器
    /// </summary>
    [RelayCommand]
    private async Task ImportTriggers()
    {
        var dialog = new OpenFileDialog()
        {
            Filter = "JavaScript Object Notation|*.json",
        };
        if (dialog.ShowDialog() != true) return;

        try
        {
            var json = await File.ReadAllTextAsync(dialog.FileName);
            var importData = JsonConvert.DeserializeObject<object>(json, new JsonSerializerSettings
            {
                TypeNameHandling = TypeNameHandling.Objects,
                Converters = { new TriggerConverter() }
            });

            var importedCount = 0;

            switch (importData)
            {
                case TriggerFolder folder:
                    importedCount = ImportFolder(folder);
                    StatusMessage = $"导入文件夹 '{folder.Name}': {importedCount} 个触发器";
                    break;

                case List<TriggerFolder> folders:
                    foreach (var folder in folders)
                    {
                        importedCount += ImportFolder(folder);
                    }
                    StatusMessage = $"导入 {folders.Count} 个文件夹: {importedCount} 个触发器";
                    break;

                case List<Trigger> triggers:
                    var targetFolder = SelectedItem switch
                    {
                        TriggerFolder folder => folder,
                        Trigger trigger => FindParentFolder(trigger),
                        _ => RootFolders.FirstOrDefault()
                    };

                    foreach (var trigger in triggers)
                    {
                        _triggerService.AddTrigger(trigger);
                        targetFolder?.AddNode(trigger);
                        importedCount++;
                    }
                    StatusMessage = $"导入触发器: {importedCount} 个到 '{targetFolder?.Name ?? "根目录"}'";
                    break;

                default:
                    StatusMessage = "未找到有效的触发器数据";
                    return;
            }

            SaveConfiguration();
        }
        catch (Exception ex)
        {
            StatusMessage = $"导入失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 导出触发器
    /// </summary>
    [RelayCommand]
    private async Task ExportTriggers()
    {
        object exportData;
        string defaultFileName = "Triggers";

        // 根据选择的项目确定导出内容
        switch (SelectedItem)
        {
            case TriggerFolder folder:
                exportData = folder;
                StatusMessage = $"准备导出文件夹 '{folder.Name}'";
                break;

            case Trigger trigger:
                exportData = new List<Trigger> { trigger };
                StatusMessage = $"准备导出触发器 '{trigger.Name}'";
                break;

            default:
                exportData = RootFolders;
                StatusMessage = "准备导出所有文件夹结构";
                break;
        }

        var dialog = new SaveFileDialog()
        {
            FileName = defaultFileName,
            Filter = "JavaScript Object Notation|*.json",
        };
        if (dialog.ShowDialog() != true) return;

        try
        {
            var json = JsonConvert.SerializeObject(exportData, Formatting.Indented, new JsonSerializerSettings
            {
                TypeNameHandling = TypeNameHandling.Objects,
                Converters = { new TriggerConverter() }
            });
            await File.WriteAllTextAsync(dialog.FileName, json);
            StatusMessage = $"成功导出到 '{Path.GetFileName(dialog.FileName)}'";
        }
        catch (Exception ex)
        {
            StatusMessage = $"导出失败: {ex.Message}";
        }
    }


    /// <summary>
    /// 计算文件夹中的触发器数量
    /// </summary>
    private int CountTriggersInFolder(TriggerFolder folder)
    {
        var count = 0;
        foreach (var node in folder.Nodes)
        {
            switch (node)
            {
                case Trigger:
                    count++;
                    break;
                case TriggerFolder subFolder:
                    count += CountTriggersInFolder(subFolder);
                    break;
            }
        }
        return count;
    }

    /// <summary>
    /// 导入文件夹及其内容
    /// </summary>
    private int ImportFolder(TriggerFolder folder)
    {
        // 根据当前选择确定导入位置
        var targetParent = SelectedItem switch
        {
            TriggerFolder parentFolder => parentFolder,
            Trigger trigger => FindParentFolder(trigger),
            _ => null // 添加到根级别
        };

        // 添加到目标位置
        if (targetParent != null)
        {
            targetParent.AddNode(folder);
        }
        else
        {
            RootFolders.Add(folder);
        }

        // 将文件夹中的触发器添加到服务
        var importedCount = AddTriggersFromFolderToService(folder);
        return importedCount;
    }

    /// <summary>
    /// 将文件夹中的所有触发器添加到服务
    /// </summary>
    private int AddTriggersFromFolderToService(TriggerFolder folder)
    {
        var count = 0;
        foreach (var node in folder.Nodes)
        {
            switch (node)
            {
                case Trigger trigger:
                    _triggerService.AddTrigger(trigger);
                    count++;
                    break;
                case TriggerFolder subFolder:
                    subFolder.Parent = folder;
                    count += AddTriggersFromFolderToService(subFolder);
                    break;
            }
        }
        return count;
    }

    #endregion
}