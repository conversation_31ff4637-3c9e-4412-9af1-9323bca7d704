:lang(cn) {
    letter-spacing: 0px !important; 
}

.gem-wrap {
    position: relative;
}

.info-item {
    overflow-x: hidden;
}

.info-ability {
    position: relative;
}

.weapon-wrap .enchant .enchant-usable1,.enchant-usable2 {
    overflow: hidden !important; 
}

.info-ability {
    height: 612px !important;
}

.loading {
    width: 100% !important;
}

.hiddensearch .search-wrap {
    margin-left: -280px;
}

.hiddensearch .contents-wrap {
    width: 1199px;
}

.search-wrap{
    transition: margin-left .5s ease;
}
.contents-wrap{
    transition: width .5s ease;
}

.signature .desc{
    display: inline-block;
}
.contents-wrap{
    text-align: center;
    float: right;
}
.contents-wrap .info-ability{
    float: right;
}
.contents-wrap .info-item{
    display: inline-block;
    float: none;
    text-align: left;
}
.info-character,.info-ability{
    text-align: left;
}

.point_ability li{
    text-align: left;
}

.enchant img {
    vertical-align: inherit;
}

.point_ability {
    right: 0;
    left: auto;
}

.layer.warning {
    z-index: 10;
    display: none;
    bottom: 40%;
    left: 25%;
    right: 25%;
    background-color: #3b434b;
    border-radius: 10px;
    height: auto;
    padding: 0 0 0 30px;
    padding-bottom: 45px;
}

.warning .layer-title {
    margin-bottom: 18px;
    font-size: 22px;
    text-align: center;
}

.warning .layer-content {
    margin-right: 30px;
}

.warning .layer-tip {
    top: 30px;
    position: relative;
}

.layer-tip small {
    display: block;
}

.signature {
    width: auto;
}

/* 排名前端样式 */
.powerinfo:after {
    border: solid 0.1em #0bb6f7;
    border-radius: .2em;
    content: attr(data-content);
    white-space: pre;
    font-size: 20px;
    font-weight: bold;
    line-height: 1;
    opacity: 0;
    position: absolute;
    padding: .1em .5em;
    margin: 0 auto;
    bottom: 20px;
    right: 5px;
    text-transform: uppercase;
    transform-origin: 50% 50%;
    transform: rotate(-2deg) scale(4)  translate(-50%,-100%);
    transition: all 0.3s cubic-bezier(0.6, 0.04, 0.98, 0.335);
    background-image:-webkit-linear-gradient(bottom,rgb(255, 38, 0),#0b7ff7,#0bd4f7,rgb(234, 0, 255)); 
    -webkit-background-clip:text; 
    -webkit-text-fill-color:transparent; 
    text-align: center;
}
            
.powerinfo.show:after {
    display: block;
    opacity: 1;
    animation-duration: 10s;
    
    transform: rotate(-15deg) scale(1);
    z-index: 99999;
}

.recommend {  
    text-align:center;
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translate(-50%,-50%);
    width: 100%;
}  
		
.link ,.link a { 
    color:#ececec;font-size: 12px;font-family: "Microsoft Yahei UI","微软雅黑","Droid Sans",Sans-serif !important;
}

.accessory-wrap>div, .newItem-wrap>div {
    grid-template-columns: 33px 170px !important;
}

/* 按钮样式 */
.wrap_btLike_NP {
	position: relative;
	display: inline-block;
	vertical-align: middle
}

.wrap_btLike_NP span {
	background-image: url(https://down.qq.com/bns/static/ingameWeb/ingame/bns/character_v2/icSandbox.png);
	background-repeat: no-repeat
}

.wrap_btLike_NP .btCancelLike_NP,.wrap_btLike_NP .btLike_NP {
	width: 82px;
	padding: 8px 0 9px;
	color: #fff;
	font-size: 13px;
	text-align: center;
	border-radius: 4px;
	border: 1px solid #192231;
	-webkit-box-shadow: #2f4568 0 -1px 3px inset,#95cce5 0 1px 3px inset;
	box-shadow: #2f4568 0 -1px 3px inset,#95cce5 0 1px 3px inset;
	background-image: -webkit-gradient(linear,left top,left bottom,color-stop(5%,#5678b0),color-stop(60%,#2c4062),to(#2c4062));
	background-image: -webkit-linear-gradient(#5678b0 5%,#2c4062 60%,#2c4062 100%);
	background-image: -o-linear-gradient(#5678b0 5%,#2c4062 60%,#2c4062 100%);
	background-image: linear-gradient(#5678b0 5%,#2c4062 60%,#2c4062 100%);
	text-shadow: rgba(0,0,0,.8) 0 0 3px,rgba(0,0,0,.8) 1px 1px 1px;
	-webkit-background-clip: padding-box;
	overflow: hidden
}

:lang(en) .wrap_btLike_NP .btCancelLike_NP,:lang(en) .wrap_btLike_NP .btLike_NP {
	font-weight: 700;
	font-family: "Droid Serif"
}

.wrap_btLike_NP .btCancelLike_NP:hover,.wrap_btLike_NP .btLike_NP:hover {
	text-decoration: none;
	-webkit-box-shadow: #2f4568 0 -1px 3px inset,#95cce5 0 1px 3px inset;
	box-shadow: #2f4568 0 -1px 3px inset,#95cce5 0 1px 3px inset;
	background-image: -webkit-gradient(linear,left top,left bottom,color-stop(5%,#86bbdc),color-stop(60%,#2c4062),to(#2c4062));
	background-image: -webkit-linear-gradient(#86bbdc 5%,#2c4062 60%,#2c4062 100%);
	background-image: -o-linear-gradient(#86bbdc 5%,#2c4062 60%,#2c4062 100%);
	background-image: linear-gradient(#86bbdc 5%,#2c4062 60%,#2c4062 100%)
}

.wrap_btLike_NP .btCancelLike_NP span,.wrap_btLike_NP .btLike_NP span {
	padding: 0 0 0 18px
}

/* 计数器样式 */
.wrap_btLike_NP .btCount_NP {
	display: none;
	position: relative;
	float: left;
	margin: 6px 12px 0 0;
	padding: 4px 8px 3px;
	color: #aaa;
	font-size: 11px;
	white-space: nowrap;
	border: 1px solid #4f6988;
	background-color: #23344e;
	border-radius: 10px;
	-webkit-box-shadow: rgba(0,0,0,.5) 0 1px 1px;
	box-shadow: rgba(0,0,0,.5) 0 1px 1px;
	text-shadow: #000 1px 1px 0
}

.wrap_btLike_NP .btCount_NP span {
	background-image: none
}

.wrap_btLike_NP .btCount_NP:before {
	position: absolute;
	right: -6px;
	top: 6px;
	content: '';
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 4px 0 4px 6px;
	border-color: transparent transparent transparent #4f6988
}

.wrap_btLike_NP .btCount_NP:after {
	position: absolute;
	right: -4px;
	top: 6px;
	content: '';
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 4px 0 4px 6px;
	border-color: transparent transparent transparent #23344e
}

/* 按钮状态 */
.wrap_btLike_NP .btLike_NP {
	display: inline-block
}

.wrap_btLike_NP .btLike_NP span {
	background-position: 0 -8px
}

.wrap_btLike_NP .btCancelLike_NP {
	display: none
}

.wrap_btLike_NP .btCancelLike_NP span {
	background-position: 0 -22px
}

.wrap_btLike_NP.cancelLike_NP .btLike_NP {
	display: none
}

.wrap_btLike_NP.cancelLike_NP .btCancelLike_NP {
	display: inline-block
}

.wrap_btLike_NP.cancelLike_NP .btCount_NP {
	display: inline-block
}

.wrap_btLike_NP.haveLike_NP .btLike_NP {
	display: inline-block
}

.wrap_btLike_NP.haveLike_NP .btLike_NP span {
	background-position: 0 -22px
}

.wrap_btLike_NP.haveLike_NP .btCancelLike_NP {
	display: none
}

.wrap_btLike_NP.haveLike_NP .btCount_NP {
	display: inline-block
}
