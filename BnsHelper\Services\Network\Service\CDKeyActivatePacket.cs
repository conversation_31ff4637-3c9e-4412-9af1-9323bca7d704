using System.Text;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Service;
internal class CDKeyActivatePacket : BasePacket
{
    #region Request Fields
    public string? CDKey;
    #endregion

    #region Response Fields
    public byte? Permission;
    public long? PermissionExpiration;
    #endregion

    #region Methods
    public override DataArchiveWriter Create()
    {
        var writer = base.Create();
        writer.WriteString(CDKey?.Trim(), Encoding.UTF8);
        return writer;
    }

    protected override void ReadResponse(DataArchive reader)
    {
        Permission = reader.Read<byte>();
        PermissionExpiration = reader.Read<long>();
    }
    #endregion
}
