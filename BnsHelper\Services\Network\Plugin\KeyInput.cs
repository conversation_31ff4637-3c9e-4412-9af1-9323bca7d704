﻿using Vanara.PInvoke;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Plugin;
internal class KeyInput : IPacket
{
	public User32.VK Key { get; private set; }

	public bool IsUp { get; private set; }

	public DataArchiveWriter Create() => new();

	public void Read(DataArchive reader)
	{
		var value = reader.Read<int>();
		if (value < 0)
		{
			IsUp = true;
			value *= -1;
		}

		Key = (User32.VK)value;
	}
}

internal class SystemKeyInput : KeyInput
{

}
