﻿using System.Globalization;
using System.Windows.Data;

namespace Xylia.BnsHelper.Common.Converters;
internal class Enum2NumberConverter : IValueConverter
{
    /// <summary>
    /// Converts an enum value to its numeric representation
    /// </summary>
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value == null)
            return Binding.DoNothing;

        if (value.GetType().IsEnum)
        {
            return System.Convert.ChangeType(value, Enum.GetUnderlyingType(value.GetType()));
        }

        return Binding.DoNothing;
    }

    /// <summary>
    /// Converts a numeric value back to the enum type
    /// </summary>
    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value == null || !targetType.IsEnum)
            return Binding.DoNothing;

        try
        {
            // Handle nullable enums
            Type underlyingType = Nullable.GetUnderlyingType(targetType) ?? targetType;

            // Convert the value to the enum's underlying type first
            object numericValue = System.Convert.ChangeType(value, Enum.GetUnderlyingType(underlyingType));

            // Convert to enum
            return Enum.ToObject(underlyingType, numericValue);
        }
        catch
        {
            return Binding.DoNothing;
        }
    }
}