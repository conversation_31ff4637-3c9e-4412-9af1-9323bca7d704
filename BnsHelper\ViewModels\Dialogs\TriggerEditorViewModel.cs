using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Text.RegularExpressions;
using System.Windows;
using Xylia.BnsHelper.Models.Triggers;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.Views.Dialogs;
using Trigger = Xylia.BnsHelper.Models.Triggers.Trigger;
using TriggerAction = Xylia.BnsHelper.Models.Triggers.TriggerAction;

namespace Xylia.BnsHelper.ViewModels.Dialogs;

/// <summary>
/// 触发器编辑器视图模型
/// </summary>
public partial class TriggerEditorViewModel : ObservableObject
{
    #region Fields
    [ObservableProperty] string _title;
    [ObservableProperty] Trigger? _trigger;
    [ObservableProperty] TriggerAction? _selectedAction;
    [ObservableProperty] bool _canMoveActionUp = false;
    [ObservableProperty] bool _canMoveActionDown = false;

    // 触发条件类型
    [ObservableProperty] bool _isMessageCondition = true;
    [ObservableProperty] bool _isTimeCondition = false;

    [ObservableProperty] string? _expression;
    [ObservableProperty] int _triggerHour = 1;
    [ObservableProperty] int _triggerMinute = 30;
    [ObservableProperty] int _repeatMode = 1; // 0=仅一次, 1=每天, 2=每周

    // 每周具体日期选择
    [ObservableProperty] bool _isMonday = false;
    [ObservableProperty] bool _isTuesday = false;
    [ObservableProperty] bool _isWednesday = false;
    [ObservableProperty] bool _isThursday = false;
    [ObservableProperty] bool _isFriday = false;
    [ObservableProperty] bool _isSaturday = false;
    [ObservableProperty] bool _isSunday = false;

    public bool DialogResult { get; private set; } = false;
    #endregion

    #region Methods
    /// <summary>
    /// 设置要编辑的触发器
    /// </summary>
    public void SetTrigger(Trigger? trigger)
    {
        Trigger = trigger ?? new Trigger();
        Title = trigger is null ? "新建触发器" : $"编辑触发器 {trigger.Name}";

        // 根据触发器的条件类型设置界面状态
        if (trigger != null && trigger.Conditions.Count > 0)
        {
            var firstCondition = trigger.Conditions.First();
            switch (firstCondition)
            {
                case RegexCondition condition:
                    Expression = condition.Pattern;
                    IsMessageCondition = true;
                    IsTimeCondition = false;
                    break;
                case TimeCondition condition:
                    IsTimeCondition = true;
                    IsMessageCondition = false;
                    TriggerHour = condition.Time.Hours;
                    TriggerMinute = condition.Time.Minutes;
                    RepeatMode = condition.RepeatMode;

                    // 加载星期选择
                    IsMonday = condition.IsMonday;
                    IsTuesday = condition.IsTuesday;
                    IsWednesday = condition.IsWednesday;
                    IsThursday = condition.IsThursday;
                    IsFriday = condition.IsFriday;
                    IsSaturday = condition.IsSaturday;
                    IsSunday = condition.IsSunday;
                    break;

                default:
                    IsTimeCondition = IsMessageCondition = false;
                    MessageBox.Show("编辑器暂不支持当前触发器的条件", StringHelper.Get("ApplicationName"), icon: MessageBoxImage.Warning);
                    break;
            }
        }
    }

    /// <summary>
    /// 添加动作
    /// </summary>
    [RelayCommand]
    private void AddAction()
    {
        var selectorDialog = new TriggerActionSelectorDialog { Owner = Application.Current.MainWindow };
        if (selectorDialog.ShowDialog() == true)
        {
            var action = selectorDialog.SelectedAction;
            if (action != null)
            {
                // 显示动作编辑对话框
                var editorDialog = new TriggerActionEditorDialog(action) { Owner = Application.Current.MainWindow };
                if (editorDialog.ShowDialog() == true)
                {
                    Trigger.Actions.Add(action);
                    SelectedAction = action;
                }
            }
        }
    }

    /// <summary>
    /// 编辑动作
    /// </summary>
    [RelayCommand]
    private void EditAction()
    {
        if (SelectedAction == null) return;

        var editorDialog = new TriggerActionEditorDialog(SelectedAction) { Owner = Application.Current.MainWindow };
        editorDialog.ShowDialog();
    }

    /// <summary>
    /// 删除动作
    /// </summary>
    [RelayCommand]
    private void DeleteAction()
    {
        if (SelectedAction == null) return;

        var result = MessageBox.Show($"确定要删除该动作吗？", "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);
        if (result == MessageBoxResult.Yes)
        {
            var index = Trigger.Actions.IndexOf(SelectedAction);
            Trigger.Actions.Remove(SelectedAction);

            // 选择下一个动作
            if (Trigger.Actions.Count > 0)
            {
                SelectedAction = Trigger.Actions[Math.Min(index, Trigger.Actions.Count - 1)];
            }
            else
            {
                SelectedAction = null;
            }
        }
    }

    /// <summary>
    /// 执行动作 (测试用)
    /// </summary>
    [RelayCommand]
    private async Task ExecuteAction()
    {
        if (SelectedAction == null) return;

        await Task.Run(() => SelectedAction.ExecuteAsync(new TriggerExecutionContext()));
    }

    /// <summary>
    /// 上移动作
    /// </summary>
    [RelayCommand]
    private void MoveActionUp()
    {
        var action = SelectedAction;
        if (action == null) return;

        var index = Trigger.Actions.IndexOf(action);
        if (index > 0)
        {
            Trigger.Actions.RemoveAt(index);
            Trigger.Actions.Insert(index - 1, action);
        }
    }

    /// <summary>
    /// 下移动作
    /// </summary>
    [RelayCommand]
    private void MoveActionDown()
    {
        var action = SelectedAction;
        if (action == null) return;

        var index = Trigger.Actions.IndexOf(action);
        if (index < Trigger.Actions.Count - 1)
        {
            Trigger.Actions.RemoveAt(index);
            Trigger.Actions.Insert(index + 1, action);
        }
    }

    /// <summary>
    /// 在当前位置添加动作
    /// </summary>
    [RelayCommand]
    private void AddActionAt()
    {
        var selectorDialog = new TriggerActionSelectorDialog { Owner = Application.Current.MainWindow };
        if (selectorDialog.ShowDialog() == true)
        {
            var newAction = selectorDialog.SelectedAction;
            if (newAction != null)
            {
                // 如果有选中的动作，在其后面插入，否则添加到末尾
                if (SelectedAction != null)
                {
                    var index = Trigger.Actions.IndexOf(SelectedAction);
                    Trigger.Actions.Insert(index + 1, newAction);
                }
                else
                {
                    Trigger.Actions.Add(newAction);
                }

                SelectedAction = newAction;
            }
        }
    }

    /// <summary>
    /// 确定
    /// </summary>
    [RelayCommand]
    private void Ok()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(Trigger?.Name)) throw new ArgumentException("请输入触发器名称");

            // 清空现有条件
            Trigger.Conditions.Clear();

            // 根据选择的条件类型创建相应的条件
            if (IsMessageCondition)
            {
                #region 验证正则表达式
                if (string.IsNullOrWhiteSpace(Expression)) throw new ArgumentException("请输入正则表达式条件");

                try { var regex = new Regex(Expression, RegexOptions.IgnoreCase); }
                catch (Exception ex) { throw new ArgumentException("正则表达式错误: " + ex.Message, ex); }
                #endregion

                // 添加正则表达式条件
                Trigger.Conditions.Add(new RegexCondition
                {
                    Pattern = Expression,
                    IgnoreCase = true,
                });
            }
            else if (IsTimeCondition)
            {
                // 验证每周模式下至少选择一个星期
                if (RepeatMode == 2 && !IsMonday && !IsTuesday && !IsWednesday && !IsThursday && !IsFriday && !IsSaturday && !IsSunday)
                {
                    MessageBox.Show("每周模式下请至少选择一个星期", "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 添加定时触发条件
                Trigger.Conditions.Add(new TimeCondition
                {
                    Time = new TimeSpan(TriggerHour, TriggerMinute, 0),
                    RepeatMode = RepeatMode,
                    IsMonday = IsMonday,
                    IsTuesday = IsTuesday,
                    IsWednesday = IsWednesday,
                    IsThursday = IsThursday,
                    IsFriday = IsFriday,
                    IsSaturday = IsSaturday,
                    IsSunday = IsSunday,
                });
            }

            DialogResult = true;
            CloseRequested?.Invoke(this, EventArgs.Empty);
        }
        catch (Exception ex)
        {
            MessageBox.Show(ex.Message, "验证失败", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    /// <summary>
    /// 取消
    /// </summary>
    [RelayCommand]
    private void Cancel()
    {
        DialogResult = false;
        CloseRequested?.Invoke(this, EventArgs.Empty);
    }

    /// <summary>
    /// 关闭请求事件
    /// </summary>
    public event EventHandler? CloseRequested;

    /// <summary>
    /// 选择动作发生变化时更新按钮状态
    /// </summary>
    /// <param name="value"></param>
    partial void OnSelectedActionChanged(TriggerAction? value)
    {
        if (SelectedAction == null)
        {
            CanMoveActionUp = false;
            CanMoveActionDown = false;
            return;
        }

        var index = Trigger.Actions.IndexOf(SelectedAction);
        CanMoveActionUp = index > 0;
        CanMoveActionDown = index < Trigger.Actions.Count - 1;
    }
    #endregion
}
