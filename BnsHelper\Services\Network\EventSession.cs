﻿using System.Diagnostics;
using System.IO;
using System.Net;
using System.Net.Sockets;
using Xylia.BnsHelper.Services.Network.BinaryProtocol;
using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network;
internal abstract class EventSession : BaseSession
{
    #region Constructor
    private readonly string _host;
    private readonly int _port;
    private Thread? _receiveThread;
    private volatile bool _isReceiving = false;

    public EventSession(string host, int port)
    {
        _host = host;
        _port = port;
        Connect();
    }

    public EventSession(DnsEndPoint point) : this(point.Host, point.Port) { }
    #endregion

    #region Methods
    protected override void Connect()
    {
        _socket = new Socket(AddressFamily.InterNetwork, SocketType.Dgram, ProtocolType.Udp);
        _socket.Connect(_host, _port);

        _isReceiving = true;
        _receiveThread = new Thread(SocketClientReceive);
        _receiveThread.IsBackground = true;
        _receiveThread.Start();
    }

    private void SocketClientReceive()
    {
        var buffer = new byte[Constants.MaxMessageSize];
        int consecutiveErrors = 0;
        const int maxConsecutiveErrors = 5;

        try
        {
            while (_isReceiving && IsConnected)
            {
                try
                {
                    if (_socket == null || !_socket.Connected || !_isReceiving)
                    {
                        Debug.WriteLine("Socket已断开或接收已停止，退出接收循环");
                        break;
                    }

                    _socket.BeginReceive(buffer, 0, buffer.Length, SocketFlags.None, ReceiveCallback, buffer);
                    consecutiveErrors = 0; // 重置错误计数
                    Thread.Sleep(100);
                }
                catch (Exception ex) when (ex is SocketException or ObjectDisposedException or NullReferenceException)
                {
                    consecutiveErrors++;
                    Debug.WriteLine($"接收数据异常 (连续错误: {consecutiveErrors}/{maxConsecutiveErrors}): {ex.Message}");

                    if (consecutiveErrors >= maxConsecutiveErrors || !_isReceiving)
                    {
                        Debug.WriteLine("连续错误次数过多或接收已停止，关闭连接");
                        Close();
                        break;
                    }

                    // 连接相关异常，等待更长时间后重试
                    Thread.Sleep(1000);
                }
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 接收线程发生未处理异常: {ex.Message}");
        }
        finally
        {
            _isReceiving = false;
        }
    }

    private void ReceiveCallback(IAsyncResult ar)
    {
        try
        {
            if (_socket == null) return;

            var buffer = (byte[])ar.AsyncState!;
            var receivedBytes = _socket?.EndReceive(ar);
            if (receivedBytes is null || receivedBytes == 0) return;

            ProcessReceivedData(buffer, receivedBytes.Value);
            _exception = null;
        }
        catch (Exception ex) when (ex is ObjectDisposedException or SocketException)
        {
            // 连接相关异常，忽略
        }
        catch (Exception ex)
        {
            _exception = ex;
        }
    }

    private void ProcessReceivedData(byte[] buffer, int receivedBytes)
    {
        var data = new byte[receivedBytes];
        Array.Copy(buffer, data, receivedBytes);

        if (data.Length < Constants.HeaderSize)
        {
            throw new InvalidDataException($"Received data too short for header: {data.Length} bytes");
        }

        var message = Message.DecodeWithDecryption(data, XorKey);
        using var reader = new DataArchive(message.Body!);
        var packet = OnHandlePacket(message.MsgType, reader);

        EnqueuePacket(message.MsgType, packet);
    }

    private void EnqueuePacket(byte type, IPacket packet)
    {
        lock (_packetQueues)
        {
            if (!_packetQueues.TryGetValue(type, out Queue<IPacket>? value))
            {
                value = new Queue<IPacket>();
                _packetQueues[type] = value;
            }

            value.Enqueue(packet);
        }
    }

    /// <summary>
    /// Handles the received packet based on its message type.
    /// </summary>
    protected abstract IPacket OnHandlePacket(byte type, DataArchive reader);

    /// <summary>
    /// Sends a packet to the server with the specified message type.
    /// </summary>
    /// <param name="packet"></param>
    /// <param name="type"></param>
    /// <exception cref="InvalidOperationException"></exception>
    protected internal virtual void SendPacket(IPacket packet, byte type)
    {
        if (_socket == null || !_socket.Connected) throw new InvalidOperationException("Socket 未连接或已释放");

        try
        {
            var body = packet.Create()?.ToArray();
            var message = new Message(type, Constants.FlagNeedResponse | Constants.FlagEncrypted, body);
            var encodedData = message.EncodeWithEncryption(XorKey);
            _socket.Send(encodedData);
        }
        catch (Exception ex) when (ex is ObjectDisposedException or SocketException)
        {
            throw new InvalidOperationException($"Socket 发送失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 异步获取指定类型的响应，支持超时和取消
    /// </summary>
    public async Task<IPacket> GetResponseAsync(byte expectedType, int timeoutMs = 10000, CancellationToken cancellationToken = default)
    {
        var endTime = DateTime.UtcNow.AddMilliseconds(timeoutMs);

        while (DateTime.UtcNow < endTime)
        {
            cancellationToken.ThrowIfCancellationRequested();

            if (!IsConnected) throw new InvalidOperationException("无法连接到服务器，请稍后再试");

            if (_exception != null)
            {
                var ex = _exception;
                _exception = null;
                throw ex;
            }

            var packet = TryDequeuePacket(expectedType);
            if (packet != null) return packet;

            await Task.Delay(50, cancellationToken);
        }

        throw new TimeoutException($"网络连接超时，请稍后再试");
    }

    private IPacket? TryDequeuePacket(byte expectedType)
    {
        lock (_packetQueues)
        {
            if (_packetQueues.TryGetValue(expectedType, out var queue) && queue.Count > 0)
            {
                return queue.Dequeue();
            }
            return null;
        }
    }

    public async Task<IPacket> WaitForResponseWithRetry(byte expectedType, int maxRetries, int timeoutMs)
    {
        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                Debug.WriteLine($"  尝试 {attempt}/{maxRetries}，等待消息类型 0x{expectedType:X2}...");
                return await GetResponseAsync(expectedType, timeoutMs);
            }
            catch (TimeoutException) when (attempt < maxRetries && IsConnected)
            {
                Debug.WriteLine($"  尝试 {attempt} 超时，等待 1 秒后重试...");
                await Task.Delay(1000); // 减少重试间隔
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("连接已断开"))
            {
                Debug.WriteLine("连接已断开，停止重试");
                throw;
            }
        }

        throw new TimeoutException($"在 {maxRetries} 次尝试后仍然超时");
    }

    protected override void Close()
    {
        try
        {
            // 停止接收
            _isReceiving = false;

            // 等待接收线程结束，减少等待时间避免程序关闭时卡顿
            if (_receiveThread != null && _receiveThread.IsAlive)
            {
                if (!_receiveThread.Join(500)) // 减少到500毫秒，避免关闭时卡顿
                {
                    Debug.WriteLine("[WARNING] 接收线程未能在超时时间内结束，继续关闭");
                }
                _receiveThread = null;
            }

            // 清理数据包队列
            lock (_packetQueues)
            {
                _packetQueues.Clear();
            }

            base.Close();
            Debug.WriteLine("[INFO] EventSession连接关闭完成");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 关闭EventSession连接时发生异常: {ex.Message}");
        }
    }
    #endregion

    #region Fields
    private readonly Dictionary<byte, Queue<IPacket>> _packetQueues = new();
    private Exception? _exception;

    protected virtual byte[]? XorKey { get; }

    protected virtual byte[]? AesKey { get; }
    #endregion
}
