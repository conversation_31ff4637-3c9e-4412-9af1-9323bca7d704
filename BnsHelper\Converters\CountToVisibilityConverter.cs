using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace BnsHelper.Converters;

/// <summary>
/// 计数到可见性转换器
/// </summary>
public class CountToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is int count)
        {
            // 如果参数是 "0"，则当计数为0时显示，否则隐藏
            if (parameter?.ToString() == "0")
            {
                return count == 0 ? Visibility.Visible : Visibility.Collapsed;
            }
            
            // 默认行为：计数大于0时显示
            return count > 0 ? Visibility.Visible : Visibility.Collapsed;
        }

        return Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
