package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"udp-server/server/internal/database"
	"udp-server/server/internal/model"
	coreService "udp-server/server/internal/service"
	"udp-server/server/pkg/cache"
	"udp-server/server/pkg/logger"

	"gorm.io/gorm"
)

// 创建活动请求
type CreateActivityRequest struct {
	ActivityId   uint64 `json:"activity_id"`
	ActivityName string `json:"activity_name"`
	Status       int8   `json:"status"`
	Priority     int8   `json:"priority"`
	BeginTime    string `json:"begin_time"`
	EndTime      string `json:"end_time"`
}

// 更新流程请求
type UpdateFlowRequest struct {
	ActivityID  uint64 `json:"activity_id"`
	FlowID      uint64 `json:"flow_id"`
	FlowName    string `json:"flow_name"`
	Group       uint32 `json:"group"`
	IdeToken    string `json:"ide_token"`
	AccountType int    `json:"account_type"`
	FlowType    int    `json:"flow_type"`
	Custom      int    `json:"custom"`
	Parameters  string `json:"parameters"`
	Status      int8   `json:"status"`
	SortOrder   int    `json:"sort_order"`
}

// 活动管理后台服务
type ActivityAdminService struct {
	activityService *coreService.ActivityService
	cache           cache.Cache
	db              *gorm.DB
}

// 创建活动管理后台服务
func NewActivityAdminService(activityService *coreService.ActivityService, cache cache.Cache) *ActivityAdminService {
	return &ActivityAdminService{
		activityService: activityService,
		cache:           cache,
		db:              database.GetDB(),
	}
}

// ==================== 活动相关方法 ==================== //

// 活动列表请求
type ActivityListRequest struct {
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	Status   *int8  `json:"status"`
	Keyword  string `json:"keyword"`
}

// 活动列表响应
type ActivityListResponse struct {
	Activities []model.Activity `json:"activities"`
	Total      int64            `json:"total"`
	Page       int              `json:"page"`
	PageSize   int              `json:"page_size"`
}

// 活动修改请求
type UpdateActivityRequest struct {
	ActivityId   uint64 `json:"activity_id" binding:"required"`
	ActivityName string `json:"activity_name" binding:"required"`
	Status       int8   `json:"status"`
	Priority     int8   `json:"priority"`
	BeginTime    string `json:"begin_time" binding:"required"`
	EndTime      string `json:"end_time" binding:"required"`
}

// 创建活动
func (s *ActivityAdminService) CreateActivity(ctx context.Context, req *CreateActivityRequest) (*model.Activity, error) {
	// 解析时间
	timeFormats := []string{
		"2006-01-02T15:04:05.000Z", // ISO 8601 with milliseconds
		"2006-01-02T15:04:05Z",     // ISO 8601 without milliseconds
		"2006-01-02T15:04",         // 原有格式
		time.RFC3339,               // RFC3339 格式
	}

	var beginTime, endTime time.Time
	var err error

	// 解析开始时间
	for _, format := range timeFormats {
		beginTime, err = time.Parse(format, req.BeginTime)
		if err == nil {
			break
		}
	}
	if err != nil {
		logger.Error("解析开始时间失败: %s, 错误: %v", req.BeginTime, err)
		return nil, fmt.Errorf("开始时间格式错误: %s", req.BeginTime)
	}

	// 解析结束时间
	for _, format := range timeFormats {
		endTime, err = time.Parse(format, req.EndTime)
		if err == nil {
			break
		}
	}
	if err != nil {
		logger.Error("解析结束时间失败: %s, 错误: %v", req.EndTime, err)
		return nil, fmt.Errorf("结束时间格式错误: %s", req.EndTime)
	}

	// 直接创建活动到数据库
	activity := &model.Activity{
		ActivityId:   req.ActivityId,
		ActivityName: req.ActivityName,
		Status:       byte(req.Status),
		Priority:     byte(req.Priority),
		BeginTime:    beginTime,
		EndTime:      endTime,
	}

	err = s.db.WithContext(ctx).Create(activity).Error
	if err != nil {
		logger.Error("创建活动失败: %v", err)
		return nil, err
	}

	return activity, nil
}

// 获取活动列表
func (s *ActivityAdminService) GetActivityList(ctx context.Context, req *ActivityListRequest) (*ActivityListResponse, error) {
	// 构建查询
	query := s.db.WithContext(ctx).Model(&model.Activity{})

	// 添加状态过滤
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 添加关键词搜索
	if req.Keyword != "" {
		query = query.Where("activity_name LIKE ?", "%"+req.Keyword+"%")
	}

	// 计算总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Error("获取活动总数失败: %v", err)
		return nil, err
	}

	// 分页查询
	var activities []model.Activity
	offset := (req.Page - 1) * req.PageSize
	if err := query.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&activities).Error; err != nil {
		logger.Error("获取活动列表失败: %v", err)
		return nil, err
	}

	return &ActivityListResponse{
		Activities: activities,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
	}, nil
}

// 获取指定活动
func (s *ActivityAdminService) GetActivityById(ctx context.Context, activityId uint64) (*model.Activity, error) {
	var activity model.Activity
	err := s.db.WithContext(ctx).
		Preload("Flows", func(db *gorm.DB) *gorm.DB {
			return db.Order("sort_order ASC")
		}).
		First(&activity, activityId).Error
	if err != nil {
		logger.Error("获取活动失败: %v", err)
		return nil, err
	}

	return &activity, nil
}

// 更新活动
func (s *ActivityAdminService) UpdateActivity(ctx context.Context, activityId uint64, req *UpdateActivityRequest) (*model.Activity, error) {
	// 解析时间
	var beginTime, endTime time.Time
	var err error

	if req.BeginTime != "" {
		// 尝试多种时间格式
		timeFormats := []string{
			"2006-01-02T15:04:05.000Z", // ISO 8601 with milliseconds
			"2006-01-02T15:04:05Z",     // ISO 8601 without milliseconds
			"2006-01-02T15:04",         // 原有格式
			time.RFC3339,               // RFC3339 格式
		}

		for _, format := range timeFormats {
			beginTime, err = time.Parse(format, req.BeginTime)
			if err == nil {
				break
			}
		}
		if err != nil {
			logger.Error("解析开始时间失败: %s, 错误: %v", req.BeginTime, err)
			return nil, fmt.Errorf("开始时间格式错误: %s", req.BeginTime)
		}
	}

	if req.EndTime != "" {
		// 尝试多种时间格式
		timeFormats := []string{
			"2006-01-02T15:04:05.000Z", // ISO 8601 with milliseconds
			"2006-01-02T15:04:05Z",     // ISO 8601 without milliseconds
			"2006-01-02T15:04",         // 原有格式
			time.RFC3339,               // RFC3339 格式
		}

		for _, format := range timeFormats {
			endTime, err = time.Parse(format, req.EndTime)
			if err == nil {
				break
			}
		}
		if err != nil {
			logger.Error("解析结束时间失败: %s, 错误: %v", req.EndTime, err)
			return nil, fmt.Errorf("结束时间格式错误: %s", req.EndTime)
		}
	}

	// 先查询活动是否存在
	var activity model.Activity
	err = s.db.WithContext(ctx).First(&activity, activityId).Error
	if err != nil {
		logger.Error("活动不存在: %v", err)
		return nil, err
	}

	// 更新字段
	if req.ActivityName != "" {
		activity.ActivityName = req.ActivityName
	}
	activity.Status = byte(req.Status)
	activity.Priority = byte(req.Priority)

	if req.BeginTime != "" {
		activity.BeginTime = beginTime
	}
	if req.EndTime != "" {
		activity.EndTime = endTime
	}

	// 保存到数据库
	err = s.db.WithContext(ctx).Save(&activity).Error
	if err != nil {
		logger.Error("更新活动失败: %v", err)
		return nil, err
	}

	coreService.ActivityService.IncrementVersion()
	return &activity, nil
}

// 删除活动
func (s *ActivityAdminService) DeleteActivity(ctx context.Context, activityId uint64) error {
	// TODO: 实现删除活动逻辑
	return nil
}

// ==================== 流程相关方法 ==================== //

// 创建流程
func (s *ActivityAdminService) CreateFlow(ctx context.Context, flow *model.ActivityFlow) (*model.ActivityFlow, error) {
	err := s.db.WithContext(ctx).Create(flow).Error
	if err != nil {
		logger.Error("创建流程失败: %v", err)
		return nil, err
	}

	return flow, nil
}

// 获取特定的流程信息
func (s *ActivityAdminService) GetFlowByID(ctx context.Context, id uint64) (*model.ActivityFlow, error) {
	var flow model.ActivityFlow
	err := s.db.WithContext(ctx).First(&flow, id).Error
	if err != nil {
		logger.Error("获取流程失败: %v", err)
		return nil, err
	}

	return &flow, nil
}

// 更新流程
func (s *ActivityAdminService) UpdateFlow(ctx context.Context, flowId uint64, req *model.ActivityFlow) (*model.ActivityFlow, error) {
	// 先查询流程是否存在
	var flow model.ActivityFlow
	err := s.db.WithContext(ctx).First(&flow, flowId).Error
	if err != nil {
		logger.Error("流程不存在: %v", err)
		return nil, err
	}

	// 更新字段
	flow.FlowName = req.FlowName
	flow.Group = req.Group
	flow.IdeToken = req.IdeToken
	flow.AccountType = byte(req.AccountType)
	flow.FlowType = byte(req.FlowType)
	flow.Custom = byte(req.Custom)
	flow.Parameters = req.Parameters
	flow.Status = byte(req.Status)
	flow.SortOrder = req.SortOrder

	err = s.db.WithContext(ctx).Save(&flow).Error
	if err != nil {
		logger.Error("更新流程失败: %v", err)
		return nil, err
	}

	coreService.ActivityService.IncrementVersion()
	return &flow, nil
}

// 只更新流程状态
func (s *ActivityAdminService) UpdateFlowStatus(ctx context.Context, flowId uint64, status int8) (*model.ActivityFlow, error) {
	// 先查询流程是否存在
	var flow model.ActivityFlow
	err := s.db.WithContext(ctx).First(&flow, flowId).Error
	if err != nil {
		logger.Error("流程不存在: %v", err)
		return nil, err
	}

	// 只更新状态字段
	err = s.db.WithContext(ctx).Model(&flow).Update("status", status).Error
	if err != nil {
		logger.Error("更新流程状态失败: %v", err)
		return nil, err
	}

	// 重新查询更新后的流程信息
	err = s.db.WithContext(ctx).First(&flow, flowId).Error
	if err != nil {
		logger.Error("查询更新后的流程失败: %v", err)
		return nil, err
	}

	coreService.ActivityService.IncrementVersion()
	return &flow, nil
}

// 删除流程
func (s *ActivityAdminService) DeleteFlow(ctx context.Context, flowId uint64) error {
	err := s.db.WithContext(ctx).Delete(&model.ActivityFlow{}, flowId).Error
	if err != nil {
		logger.Error("删除流程失败: %v", err)
		return err
	}

	coreService.ActivityService.IncrementVersion()
	return nil
}

// 测试流程请求
func (s *ActivityAdminService) TestFlowWithParams(ctx context.Context, flowId uint64, accessToken, openID, appID, accType string, requiredParams, extraParams map[string]string) (interface{}, error) {
	// 获取流程信息
	flow, err := s.GetFlowByID(ctx, flowId)
	if err != nil {
		return nil, fmt.Errorf("获取流程信息失败: %w", err)
	}

	// 构建请求参数
	parameters := map[string]string{
		"iChartId":    strconv.FormatUint(flow.FlowId, 10),
		"iSubChartId": strconv.FormatUint(flow.FlowId, 10),
		"sIdeToken":   flow.IdeToken,
		"isPreengage": "1",
		"needGopenid": "1",
	}

	// 第二层：添加流程必需参数（用户输入的值）
	if requiredParams != nil {
		for key, value := range requiredParams {
			if key != "" && value != "" {
				parameters[key] = value
			}
		}
	} else {
		// 如果没有用户输入，使用流程配置的默认值
		if flow.Parameters != "" {
			var flowParams map[string]interface{}
			if err := json.Unmarshal([]byte(flow.Parameters), &flowParams); err == nil {
				for key, value := range flowParams {
					if paramObj, ok := value.(map[string]interface{}); ok {
						if defaultValue, exists := paramObj["default_value"]; exists {
							if strValue, ok := defaultValue.(string); ok && strValue != "" {
								parameters[key] = strValue
							}
						}
					}
				}
			}
		}
	}

	// 第三层：添加测试额外参数（覆盖前面的参数）
	if extraParams != nil {
		for key, value := range extraParams {
			if key != "" && value != "" {
				parameters[key] = value
			}
		}
	}

	// 构建cookies
	cookies := map[string]string{
		"acctype":      accType,
		"openid":       openID,
		"appid":        appID,
		"access_token": accessToken,
	}

	// 发送HTTP请求到AMS服务器
	amsResponse, err := s.SendAMSRequest(ctx, parameters, cookies)
	if err != nil {
		return map[string]interface{}{
			"message": "流程测试失败: " + err.Error(),
			"error":   err.Error(),
		}, nil
	}

	// 直接返回HTTP响应结果
	return amsResponse, nil
}

// 发送AMS请求
func (s *ActivityAdminService) SendAMSRequest(ctx context.Context, parameters map[string]string, cookies map[string]string) (map[string]interface{}, error) {
	// 构建表单数据
	formData := url.Values{}
	for key, value := range parameters {
		formData.Set(key, value)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", "https://comm.ams.game.qq.com/ide/", strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头，参考C#的RequestFlowAsync方法
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("accept", "application/json, text/plain, */*")
	req.Header.Set("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	// 设置cookies，使用分号分隔（与C#一致）
	var cookieStrs []string
	for key, value := range cookies {
		cookieStrs = append(cookieStrs, fmt.Sprintf("%s=%s", key, value))
	}
	if len(cookieStrs) > 0 {
		req.Header.Set("cookie", strings.Join(cookieStrs, ";"))
	}

	// 发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 解析JSON响应
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		// 如果不是JSON，返回原始文本
		return map[string]interface{}{
			"raw_response": string(body),
			"status_code":  resp.StatusCode,
		}, nil
	}

	result["status_code"] = resp.StatusCode
	return result, nil
}

// ==================== AMS解析方法 ==================== //

// AMS描述文件数据结构
type AMSData struct {
	ActivityID      string                    `json:"iActivityId"`
	ActivityName    string                    `json:"sActivityName"`
	ActivityStatus  string                    `json:"iActivityStatus"`
	ServiceType     string                    `json:"sServiceType"`
	AccountType     string                    `json:"sAccountType"`
	BeginTimeString string                    `json:"dtBeginTime"`
	EndTimeString   string                    `json:"dtEndTime"`
	IDE             *AMSIde                   `json:"ide"`
	Flows           map[string]*AMSFlowDetail `json:"flows"`
}

// IDE配置
type AMSIde struct {
	RetCode int                `json:"iRet"`
	Message string             `json:"sMsg"`
	IdeUrl  string             `json:"sIdeUrl"`
	Tokens  map[string]string  `json:"tokens"`
	Flows   map[int64]*AMSFlow `json:"flows"`
}

// IDE流程配置
type AMSFlow struct {
	Name        string                 `json:"sName"`        // 流程名称
	FlowName    string                 `json:"sFlowName"`    // 备用流程名称
	IdeToken    string                 `json:"sIdeToken"`    // IDE令牌
	AccountType string                 `json:"sAccountType"` // 账号类型
	Type        string                 `json:"iType"`        // 流程类型
	Custom      string                 `json:"iCustom"`      // 自定义标识
	Parameters  map[string]interface{} `json:"parameters"`   // 参数
	InputParams []AMSInputParam        `json:"inputParams"`  // 输入参数
}

// 输入参数
type AMSInputParam struct {
	Key  string `json:"key"`
	Desc string `json:"desc"`
}

// 流程详情
type AMSFlowDetail struct {
	FlowName    string `json:"sFlowName"`
	MapId       int    `json:"iMapId"`
	AccountType string `json:"sAccountType"`
}

// 获取AMS数据
func (s *ActivityAdminService) fetchAMSData(amsURL string) (*AMSData, error) {
	// 发起HTTP请求
	resp, err := http.Get(amsURL)
	if err != nil {
		return nil, fmt.Errorf("请求AMS数据失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("AMS服务返回错误状态: %d", resp.StatusCode)
	}

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应内容失败: %v", err)
	}

	// 解析JSON
	var amsData AMSData
	if err := json.Unmarshal(body, &amsData); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %v", err)
	}

	return &amsData, nil
}

// 创建活动和流程
func (s *ActivityAdminService) CreateActivityAndFlows(ctx context.Context, amsURL string) (*string, error) {
	// 获取AMS数据
	amsData, err := s.fetchAMSData(amsURL)
	if err != nil {
		return nil, fmt.Errorf("获取AMS数据失败: %v", err)
	}

	// 解析活动信息
	parsedActivity, err := s.parseActivity(amsData)
	if err != nil {
		return nil, fmt.Errorf("解析活动信息失败: %v", err)
	}

	// 获取活动ID
	activityID := parsedActivity.ActivityId

	// 检查活动是否已存在，如果存在则更新，否则创建
	var activity *model.Activity
	existingActivity, err := s.GetActivityById(ctx, activityID)
	if err == nil && existingActivity != nil {
		// 活动已存在，更新活动信息
		updateActivityReq := &UpdateActivityRequest{
			ActivityName: parsedActivity.ActivityName,
			Status:       int8(parsedActivity.Status),
			Priority:     int8(parsedActivity.Priority),
			BeginTime:    parsedActivity.BeginTime.Format("2006-01-02T15:04"),
			EndTime:      parsedActivity.EndTime.Format("2006-01-02T15:04"),
		}

		activity, err = s.UpdateActivity(ctx, activityID, updateActivityReq)
		if err != nil {
			return nil, fmt.Errorf("更新活动失败: %v", err)
		}
	} else {
		// 活动不存在，创建新活动
		createActivityReq := &CreateActivityRequest{
			ActivityId:   activityID,
			ActivityName: parsedActivity.ActivityName,
			Status:       int8(parsedActivity.Status),
			Priority:     int8(parsedActivity.Priority),
			BeginTime:    parsedActivity.BeginTime.Format("2006-01-02T15:04"),
			EndTime:      parsedActivity.EndTime.Format("2006-01-02T15:04"),
		}

		activity, err = s.CreateActivity(ctx, createActivityReq)
		if err != nil {
			return nil, fmt.Errorf("创建活动失败: %v", err)
		}
	}

	// 创建流程
	flowCount := 0
	if len(parsedActivity.Flows) > 0 && s.activityService != nil {
		flowCount, _ = s.CreateFlowsFromAMS(ctx, activity.ActivityId, parsedActivity.Flows)
	}

	// 根据是否为更新操作生成不同的消息
	var message string
	if existingActivity != nil {
		message = fmt.Sprintf("成功更新活动信息，新增 %d 个流程", flowCount)
	} else {
		message = fmt.Sprintf("成功创建活动草稿，包含 %d 个流程", flowCount)
	}

	return &message, nil
}

// 从AMS数据批量创建流程
func (s *ActivityAdminService) CreateFlowsFromAMS(ctx context.Context, activityId uint64, flows []model.ActivityFlow) (int, error) {
	successCount := 0
	var existingFlows = []model.ActivityFlow{}

	// 获取现有流程列表
	activity, err := s.GetActivityById(ctx, activityId)
	if err == nil {
		existingFlows = activity.Flows
	}

	// 创建现有流程ID的映射，用于快速查找
	existingFlowIds := make(map[uint64]bool)
	for _, existingFlow := range existingFlows {
		existingFlowIds[existingFlow.FlowId] = true
	}

	for _, flow := range flows {
		// 检查流程是否已存在
		if existingFlowIds[flow.FlowId] {
			logger.Info("流程 %d 已存在，跳过创建", flow.FlowId)
			continue
		}

		// 设置活动ID
		flow.ActivityID = activityId

		if _, err := s.CreateFlow(ctx, &flow); err == nil {
			successCount++
			logger.Info("成功创建新流程: %d - %s", flow.FlowId, flow.FlowName)
		} else {
			logger.Error("创建流程 %d 失败: %v", flow.FlowId, err)
		}
	}

	return successCount, nil
}

// 解析活动信息
func (s *ActivityAdminService) parseActivity(amsData *AMSData) (*model.Activity, error) {
	// 解析活动ID
	activityIdStr := amsData.ActivityID
	activityId, err := strconv.ParseUint(activityIdStr, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("无效的活动ID: %s", activityIdStr)
	}

	// 解码活动名称
	activityName := DecodeUnicodeString(amsData.ActivityName)
	if activityName == "" {
		activityName = fmt.Sprintf("导入的活动_%s", activityIdStr)
	}

	// 解析时间
	beginTime := time.Now().Add(24 * time.Hour)    // 默认明天
	endTime := time.Now().Add(30 * 24 * time.Hour) // 默认30天后
	if t, err := time.Parse("2006-01-02 15:04:05", amsData.BeginTimeString); err == nil {
		beginTime = t
	}

	if t, err := time.Parse("2006-01-02 15:04:05", amsData.EndTimeString); err == nil {
		endTime = t
	}

	return &model.Activity{
		ActivityId:   activityId,
		ActivityName: activityName,
		Status:       0, // 草稿状态
		Priority:     5,
		BeginTime:    beginTime,
		EndTime:      endTime,
		Flows:        s.parseFlows(amsData),
	}, nil
}

// 解析流程信息
func (s *ActivityAdminService) parseFlows(amsData *AMSData) []model.ActivityFlow {
	var flows []model.ActivityFlow
	sortOrder := 1

	// 优先从ide.flows解析
	if amsData.IDE != nil && amsData.IDE.Flows != nil {
		for flowID, flowData := range amsData.IDE.Flows {
			flowName := DecodeUnicodeString(flowData.FlowName)

			// 构建参数对象
			parameters := make(map[string]interface{})
			if flowData.InputParams != nil {
				for _, param := range flowData.InputParams {
					if param.Key != "" {
						parameters[param.Key] = map[string]interface{}{
							"name":     param.Desc,
							"required": false,
							"value":    "",
						}
					}
				}
			}

			// 序列化参数为JSON字符串
			parametersJSON, _ := json.Marshal(parameters)

			// 转换字符串类型到数值类型
			accountType, _ := strconv.ParseUint(flowData.AccountType, 10, 8)
			flowType, _ := strconv.ParseUint(flowData.Type, 10, 8)
			custom, _ := strconv.ParseUint(flowData.Custom, 10, 8)

			flows = append(flows, model.ActivityFlow{
				FlowId:      uint64(flowID),
				FlowName:    flowName,
				IdeToken:    flowData.IdeToken,
				AccountType: byte(accountType),
				FlowType:    byte(flowType),
				Custom:      byte(custom),
				Parameters:  string(parametersJSON),
				Status:      1,
				SortOrder:   sortOrder,
			})
			sortOrder++
		}
	}

	// 如果没有从ide.flows获取到，尝试从flows字段获取
	if len(flows) == 0 && amsData.Flows != nil {
		tokens := make(map[string]string)
		if amsData.IDE != nil && amsData.IDE.Tokens != nil {
			tokens = amsData.IDE.Tokens
		}

		for flowKey, flowDetail := range amsData.Flows {
			// 解码流程名称
			flowName := DecodeUnicodeString(flowDetail.FlowName)
			if flowName == "" {
				flowName = fmt.Sprintf("流程_%s", flowKey)
			}

			// 从mapid获取流程ID
			var flowId uint64
			if flowDetail.MapId != 0 {
				flowId = uint64(flowDetail.MapId)
			} else {
				// 尝试解析flowKey为数字
				if id, err := strconv.ParseUint(flowKey, 10, 64); err == nil {
					flowId = id
				} else {
					// 如果无法解析，跳过这个流程
					continue
				}
			}

			// 查找对应的token
			ideToken := "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
			flowIdStr := strconv.FormatUint(flowId, 10)
			for token, tokenFlowID := range tokens {
				if tokenFlowID == flowIdStr {
					ideToken = token
					break
				}
			}

			// 转换字符串类型到数值类型
			accountType, _ := strconv.ParseUint(flowDetail.AccountType, 10, 8)

			flows = append(flows, model.ActivityFlow{
				FlowId:      flowId,
				FlowName:    flowName,
				IdeToken:    ideToken,
				AccountType: byte(accountType),
				FlowType:    1,
				Custom:      1,
				Parameters:  "{}",
				Status:      1,
				SortOrder:   sortOrder,
			})
			sortOrder++
		}
	}

	return flows
}

// 辅助方法：解码Unicode字符串
func DecodeUnicodeString(str string) string {
	re := regexp.MustCompile(`\\u([0-9a-fA-F]{4})`)
	return re.ReplaceAllStringFunc(str, func(match string) string {
		codeStr := match[2:] // 去掉 \u
		if code, err := strconv.ParseInt(codeStr, 16, 32); err == nil {
			return string(rune(code))
		}
		return match
	})
}
