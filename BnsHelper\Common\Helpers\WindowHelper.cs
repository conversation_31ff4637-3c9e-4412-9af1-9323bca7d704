﻿using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Interop;
using Vanara.PInvoke;
using static Xylia.Preview.Data.Models.ChatChannelOption;

namespace Xylia.BnsHelper.Common.Helpers;
internal class WindowHelper
{
    public readonly HwndSource Source;
    private readonly Action RemoveHook;

    public WindowHelper(Window window, HwndSourceHook WndProc)
    {
        Source = HwndSource.FromHwnd(new WindowInteropHelper(window).Handle);
        Source.AddHook(WndProc);
        RemoveHook = () => Source.RemoveHook(WndProc);
        window.Closed += (sender, e) => RemoveHook();
    }

    public static nint GetGameWindow()
    {
        // 用户反馈这个句柄获取实际上是获取到最前台的游戏窗口
        return (nint)User32.FindWindow("UnrealWindow", null);

        //var process = Process.GetProcessesByName("BNSR");
        //if (process.Length == 0) return [];

        //return [.. process.Select(o => o.MainWindowHandle)];
    }

    public static void SendMessage(nint hwnd, string text, CategorySeq category = CategorySeq.Info)
    {
        if (!SettingHelper.Default.AllowMessage) return;

        var cds = new COPYDATASTRUCTSTR()
        {
            cbData = text.Length * 2 + 1,
            lpData = text,
            dwData = (int)category
        };

        User32.SendMessage(hwnd, User32.WindowMessage.WM_COPYDATA, 0x10, ref cds);
    }
}

internal struct COPYDATASTRUCTSTR
{
    public long dwData;
    public int cbData;
    [MarshalAs(UnmanagedType.LPWStr)]
    public string lpData;
}

public enum AppMessage
{
    None,
    Register,
    UnRegister,
}
