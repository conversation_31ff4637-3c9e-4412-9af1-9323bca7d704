﻿using System.IO;
using Xylia.Preview.Data.Engine.DatData;

namespace Xylia.BnsHelper.Models;
/// <summary>
/// 游戏目录信息模型
/// </summary>
/// <param name="fullPath"></param>
internal struct BnsGameInfo(string fullPath) : IComparable<BnsGameInfo>
{
    #region Common
    public readonly string FullPath => fullPath;

    public readonly bool IsEmpty => string.IsNullOrEmpty(FullPath) || !Directory.Exists(FullPath);

    private Locale? locale;
    public Locale Locale => locale ??= new Locale(new DirectoryInfo(FullPath));

    public readonly string? Drive => Path.GetPathRoot(fullPath)?.TrimEnd('\\');
    #endregion

    #region Extends
    public EPublisher Publisher => Locale.ClientPublisher;

    public string Name => Publisher switch
    {
        EPublisher.Tencent => "正式服",
        EPublisher.ZTX => "怀旧服",
        EPublisher.ZNCG when Locale.DataPublisher is EDataPublisher.TX => "巅峰服",
        _ => "不支持的客户端"
    };

    public readonly string Source =>
        fullPath.Contains("bns_ob_prod") ? "登录器" :
        fullPath.Contains("bns_ty_prod") ? "体验服" : "WeGame";

    public readonly string ClientPath => Path.Combine(FullPath, "Binaries\\Win64\\BNSR.exe");

    public readonly string PluginPath => Path.Combine(FullPath, "Binaries\\Win64\\libiconv2017_cl64.dll");
    #endregion

    #region IComparable
    public readonly bool Equals(BnsGameInfo other) => string.Equals(FullPath, other.FullPath, StringComparison.OrdinalIgnoreCase);
    public readonly override bool Equals(object obj) => obj is BnsGameInfo other && Equals(other);
    public readonly override int GetHashCode() => HashCode.Combine(FullPath);
    public readonly int CompareTo(BnsGameInfo other) => this.FullPath.CompareTo(other.FullPath);
    public readonly override string ToString() => FullPath;
    #endregion
}
