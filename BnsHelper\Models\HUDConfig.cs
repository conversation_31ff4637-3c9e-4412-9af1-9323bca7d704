﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.Preview.Common.Attributes;
using Xylia.Preview.Common.Extension;

namespace Xylia.BnsHelper.Models;

/// <summary>
/// HUD界面配置项
/// </summary>
public partial class HUDConfig(string name, int status = 1) : ObservableObject
{
	#region Properties
	[ObservableProperty] string name = name;
	[ObservableProperty] int status = status;
	[ObservableProperty] string description = "";

	/// <summary>
	/// HUD状态枚举
	/// </summary>
	public enum HUDStatus
	{
		[Text("Text_HUD_Hidden")] Hidden = 0,      // 隐藏
		[Text("Text_HUD_Visible")] Visible = 1,    // 显示
		[Text("Text_HUD_Transparent")] Transparent = 2  // 透明
	}

	/// <summary>
	/// 获取状态显示文本
	/// </summary>
	public string StatusText => Status switch
	{
		0 => "隐藏",
		1 => "显示",
		2 => "透明",
		_ => "未知"
	};

	/// <summary>
	/// 获取友好的名称显示
	/// </summary>
	public string DisplayName => GetFriendlyName(Name);

	/// <summary>
	/// 获取友好的界面名称
	/// </summary>
	private static string GetFriendlyName(string name) => name switch
	{
		"PlayerExpBarPanel" => "经验条",
		"PlayerStatusPanel" => "玩家状态面板",
		"Feedback_CombatSignal_Panel" => "战斗信号面板",
		"QuestQuickSlotPanel" => "任务快捷栏",
		"SystemMenuPanel" => "系统菜单",
		"NotificationMenuPanel" => "通知菜单",
		"ItemBarPanel" => "物品栏",
		"SprintPanel" => "冲刺面板",
		"ChatPanel" => "聊天面板",
		"MiniMapPanel" => "小地图",
		"SkillBarPanel" => "技能栏",
		"TargetPanel" => "目标面板",
		"BuffPanel" => "Buff面板",
		"InventoryPanel" => "背包面板",
		_ => name
	};

	public override bool Equals(object? obj) => obj is HUDConfig other && string.Equals(Name, other.Name, StringComparison.OrdinalIgnoreCase);
	public override int GetHashCode() => Name.GetHashCode(StringComparison.OrdinalIgnoreCase);
	#endregion

	#region ViewModel
	internal string LastKey = name;
	internal event EventHandler? Deleted;

	[RelayCommand] [property: Newtonsoft.Json.JsonIgnore]
	void Delete() => Deleted?.Invoke(this, EventArgs.Empty);

	/// <summary>
	/// 切换状态
	/// </summary>
	[RelayCommand] [property: Newtonsoft.Json.JsonIgnore]
	void ToggleStatus()
	{
		Status = Status switch
		{
			0 => 1, // 隐藏 -> 显示
			1 => 2, // 显示 -> 透明
			2 => 0, // 透明 -> 隐藏
			_ => 1
		};
	}

	internal void Save()
	{
		if (string.IsNullOrEmpty(Name)) return;
		SettingHelper.Default.SetValue(Status, LastKey = Name, HUDCollection.SECTION);
	}

	internal void Remove()
	{
		SettingHelper.Default.RemoveKey(LastKey, HUDCollection.SECTION);
	}
	#endregion
}

/// <summary>
/// HUD配置集合
/// </summary>
public class HUDCollection : ObservableCollection<HUDConfig>
{
	public const string SECTION = "HUD";

	protected override void InsertItem(int index, HUDConfig item)
	{
		item.Deleted += (s, e) => Remove(item);
		base.InsertItem(index, item);
	}

	protected override void RemoveItem(int index)
	{
		this[index].Remove();
		base.RemoveItem(index);
	}

	protected override void ClearItems()
	{
		foreach (var item in this)
		{
			item.Remove();
		}
		base.ClearItems();
	}

	protected override void SetItem(int index, HUDConfig item)
	{
		this[index].Remove();
		item.Deleted += (s, e) => Remove(item);
		base.SetItem(index, item);
	}

	/// <summary>
	/// 保存所有配置到设置文件
	/// </summary>
	public void Save()
	{
		foreach (var item in this)
		{
			item.Save();
		}
	}

	/// <summary>
	/// 从设置文件加载配置
	/// </summary>
	public static HUDCollection Load()
	{
		var collection = new HUDCollection();

		var keys = SettingHelper.Default[SECTION];
		if (keys.Count == 0)
		{
			// 添加默认的HUD配置
			collection.AddDefaultConfigs();
		}
		else
		{
			keys.ForEach(item => collection.Add(new HUDConfig(item.KeyName, int.Parse(item.Value))));
		}

		return collection;
	}

	/// <summary>
	/// 添加默认配置
	/// </summary>
	private void AddDefaultConfigs()
	{
		var defaultConfigs = new[]
		{
			new HUDConfig("PlayerExpBarPanel", 1) { Description = "玩家经验条显示" },
			new HUDConfig("PlayerStatusPanel", 1) { Description = "玩家状态面板（血量、内力等）" },
			new HUDConfig("Feedback_CombatSignal_Panel", 1) { Description = "战斗信号反馈面板" },
			new HUDConfig("QuestQuickSlotPanel", 1) { Description = "任务快捷操作栏" },
			new HUDConfig("SystemMenuPanel", 1) { Description = "系统菜单面板" },
			new HUDConfig("NotificationMenuPanel", 1) { Description = "通知消息菜单" },
			new HUDConfig("ItemBarPanel", 1) { Description = "物品快捷栏" },
			new HUDConfig("SprintPanel", 1) { Description = "冲刺状态面板" },
			new HUDConfig("ChatPanel", 1) { Description = "聊天窗口面板" },
			new HUDConfig("MiniMapPanel", 1) { Description = "小地图显示" },
			new HUDConfig("SkillBarPanel", 1) { Description = "技能快捷栏" },
			new HUDConfig("TargetPanel", 1) { Description = "目标信息面板" },
			new HUDConfig("BuffPanel", 1) { Description = "增益效果面板" },
			new HUDConfig("InventoryPanel", 0) { Description = "背包界面（默认隐藏）" }
		};

		foreach (var config in defaultConfigs)
		{
			Add(config);
		}
	}
}
