using System.IO;
using System.IO.Compression;

namespace Xylia.BnsHelper.Services.Network.BinaryProtocol;

/// <summary>
/// 消息压缩和解压缩工具类
/// </summary>
internal static class Compression
{
    /// <summary>
    /// 压缩阈值：只有当消息体大于此大小时才进行压缩
    /// </summary>
    private const int CompressionThreshold = 128; // 128字节

    /// <summary>
    /// 压缩数据
    /// </summary>
    /// <param name="data">要压缩的数据</param>
    /// <returns>压缩后的数据</returns>
    public static byte[] CompressData(byte[] data)
    {
        if (data == null || data.Length == 0)
            return data;

        using var output = new MemoryStream();
        using (var gzip = new GZipStream(output, CompressionMode.Compress))
        {
            gzip.Write(data, 0, data.Length);
        }
        return output.ToArray();
    }

    /// <summary>
    /// 解压缩数据
    /// </summary>
    /// <param name="compressedData">压缩的数据</param>
    /// <returns>解压缩后的数据</returns>
    public static byte[] DecompressData(byte[] compressedData)
    {
        if (compressedData == null || compressedData.Length == 0)
            return compressedData;

        using var input = new MemoryStream(compressedData);
        using var gzip = new GZipStream(input, CompressionMode.Decompress);
        using var output = new MemoryStream();
        
        gzip.CopyTo(output);
        return output.ToArray();
    }

    /// <summary>
    /// 压缩消息体（跳过消息头）
    /// </summary>
    /// <param name="data">完整的消息数据</param>
    /// <returns>压缩后的消息数据</returns>
    public static byte[] CompressMessageBody(byte[] data)
    {
        if (data.Length < Constants.HeaderSize)
            return data;

        // 如果已经压缩，直接返回
        if ((data[3] & Constants.FlagCompressed) != 0)
            return data;

        // 提取消息体
        var bodyData = new byte[data.Length - Constants.HeaderSize];
        Array.Copy(data, Constants.HeaderSize, bodyData, 0, bodyData.Length);

        // 如果消息体太小，不进行压缩
        if (bodyData.Length < CompressionThreshold)
            return data;

        // 压缩消息体
        var compressedBody = CompressData(bodyData);
        if (compressedBody.Length >= bodyData.Length) return data;

        // 创建新的消息数据
        var result = new byte[Constants.HeaderSize + compressedBody.Length];
        Array.Copy(data, 0, result, 0, Constants.HeaderSize);
        Array.Copy(compressedBody, 0, result, Constants.HeaderSize, compressedBody.Length);

        // 更新消息长度（使用大端序，与Message构造函数中的解码保持一致）
        var newLength = (uint)(Constants.HeaderSize + compressedBody.Length);
        result[4] = (byte)(newLength >> 24);
        result[5] = (byte)(newLength >> 16);
        result[6] = (byte)(newLength >> 8);
        result[7] = (byte)newLength;

        // 设置压缩标志位
        result[3] |= Constants.FlagCompressed;

        return result;
    }

    /// <summary>
    /// 解压缩消息体（跳过消息头）
    /// </summary>
    /// <param name="data">压缩的消息数据</param>
    /// <returns>解压缩后的消息数据</returns>
    public static byte[] DecompressMessageBody(byte[] data)
    {
        if (data.Length < Constants.HeaderSize)
            return data;

        // 检查压缩标志位
        if ((data[3] & Constants.FlagCompressed) == 0)
            return data; // 未压缩，直接返回

        // 提取压缩的消息体
        var compressedBody = new byte[data.Length - Constants.HeaderSize];
        Array.Copy(data, Constants.HeaderSize, compressedBody, 0, compressedBody.Length);

        // 解压缩消息体
        var decompressedBody = DecompressData(compressedBody);

        // 创建新的消息数据
        var result = new byte[Constants.HeaderSize + decompressedBody.Length];
        Array.Copy(data, 0, result, 0, Constants.HeaderSize);
        Array.Copy(decompressedBody, 0, result, Constants.HeaderSize, decompressedBody.Length);

        // 更新消息长度（使用大端序，与Message构造函数中的解码保持一致）
        var newLength = (uint)(Constants.HeaderSize + decompressedBody.Length);
        result[4] = (byte)(newLength >> 24);
        result[5] = (byte)(newLength >> 16);
        result[6] = (byte)(newLength >> 8);
        result[7] = (byte)newLength;

        // 清除压缩标志位
        result[3] &= unchecked((byte)~Constants.FlagCompressed);

        return result;
    }

    /// <summary>
    /// 检查数据是否已压缩
    /// </summary>
    /// <param name="data">消息数据</param>
    /// <returns>如果已压缩返回true</returns>
    public static bool IsCompressed(byte[] data)
    {
        if (data.Length < Constants.HeaderSize)
            return false;

        return (data[3] & Constants.FlagCompressed) != 0;
    }

    /// <summary>
    /// 计算压缩比
    /// </summary>
    /// <param name="originalSize">原始大小</param>
    /// <param name="compressedSize">压缩后大小</param>
    /// <returns>压缩比</returns>
    public static double GetCompressionRatio(int originalSize, int compressedSize)
    {
        if (originalSize == 0)
            return 0;
        return (double)compressedSize / originalSize;
    }
}
