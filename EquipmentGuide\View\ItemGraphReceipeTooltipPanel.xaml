﻿<BnsCustomWindowWidget x:Class="Xylia.Preview.UI.GameUI.Scene.Game_Tooltip2.ItemGraphReceipeTooltipPanel"
	xmlns="https://github.com/xyliaup/bns-preview-tools"
	xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
	xmlns:s="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
	Width="350">
	<BnsCustomWindowWidget.BaseImageProperty>
		<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_Window.BNSR_Window" ImageUV="7 7" ImageUVSize="49 49" EnableDrawImage="true" EnableSkinAlpha="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_9Frame" StaticPadding="-17 -17" Opacity="1" />
	</BnsCustomWindowWidget.BaseImageProperty>
	<BnsCustomWindowWidget.ExpansionComponentList>
		<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Line" MetaData="">
			<UBnsCustomExpansionComponent.ImageProperty>
				<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_Window_Outfocus.BNSR_Window_Outfocus" EnableBrushOnly="true" ImageUV="7 7" ImageUVSize="49 49" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" SperateImageType="BNS_SperateImageType_9Frame" TintColor="FFFFFF" StaticPadding="-13 -13" Opacity="1" />
			</UBnsCustomExpansionComponent.ImageProperty>
		</UBnsCustomExpansionComponent>
	</BnsCustomWindowWidget.ExpansionComponentList>
	<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_1" LayoutData.Anchors="0.5 0 0.5 0" LayoutData.Offsets="0 15 350 0">
		<BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
		</BnsCustomImageWidget.HorizontalResizeLink>
		<BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Offset1="15" />
		</BnsCustomImageWidget.VerticalResizeLink>
		<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_1_Title" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="10 10 10 35">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Offset1="10" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_16.Normal_16" LabelText="Item Name성장 재료Item Name성장 재료Item Name성장 재료" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
		<BnsCustomWindowWidget Name="ItemGraphReceipeTooltipPanel_1_ItemHolder" LayoutData.Offsets="12 55 300 53">
			<BnsCustomWindowWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Offset1="12" />
			</BnsCustomWindowWidget.HorizontalResizeLink>
			<BnsCustomWindowWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_1_Title" Offset1="10" />
			</BnsCustomWindowWidget.VerticalResizeLink>
			<BnsCustomWindowWidget.BaseImageProperty>
				<ImageProperty ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
			</BnsCustomWindowWidget.BaseImageProperty>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_1_ItemIcon" LayoutData.Offsets="3 0 50 50" Foreground="White">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Offset1="3" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" ImageScale="1.1" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="" WidgetState="BNSCustomWidgetState_Normal">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_12.Normal_Out_12" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="2.5 2.5" Offset="-5 -6.5" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="2.5 2.5" Offset="-5 -6.5" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_1_ItemIcon_Name" LayoutData.Offsets="65 0 235 16">
				<BnsCustomLabelWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" LinkWidgetName1="ItemGraphReceipeTooltipPanel_1_ItemIcon" Offset1="65" />
				</BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomLabelWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomLabelWidget.String>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_14.Normal_14" LabelText="Item Name" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" />
				</BnsCustomLabelWidget.String>
			</BnsCustomLabelWidget>
			<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_1_ItemIcon_Desc" LayoutData.Offsets="65 21 235 14">
				<BnsCustomLabelWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" LinkWidgetName1="ItemGraphReceipeTooltipPanel_1_ItemIcon" Offset1="65" />
				</BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomLabelWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_1_ItemIcon_Name" Offset1="5" />
				</BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomLabelWidget.String>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="획득처 1" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" />
				</BnsCustomLabelWidget.String>
			</BnsCustomLabelWidget>
		</BnsCustomWindowWidget>
		<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_1_MeterialItemHolder" LayoutData.Offsets="12 113 298 0" Foreground="White">
			<BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Offset1="12" />
			</BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_1_ItemHolder" Offset1="5" />
			</BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomImageWidget.BaseImageProperty>
				<ImageProperty EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
			</BnsCustomImageWidget.BaseImageProperty>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_1_MeterialItem_1" LayoutData.Offsets="0 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_1_MeterialItem_2" LayoutData.Offsets="38 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_1_MeterialItem_1" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_1_MeterialItem_3" LayoutData.Offsets="76 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_1_MeterialItem_2" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_1_MeterialItem_4" LayoutData.Offsets="114 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_1_MeterialItem_3" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_1_MeterialItem_5" LayoutData.Offsets="152 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_1_MeterialItem_4" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_1_MeterialItem_6" LayoutData.Offsets="190 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_1_MeterialItem_5" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_1_MeterialItem_7" LayoutData.Offsets="228 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_1_MeterialItem_6" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_1_MeterialItem_8" LayoutData.Offsets="266 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_1_MeterialItem_7" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
		</BnsCustomImageWidget>
		<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_1_Price" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="10 150 10 20">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_1_MeterialItemHolder" Offset1="5" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Price" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
			<BnsCustomLabelWidget.ExpansionComponentList>
				<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="CostLabel" MetaData="textref=UI.ItemGrowth.Common.Money.none">
					<UBnsCustomExpansionComponent.ImageProperty>
						<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
					</UBnsCustomExpansionComponent.ImageProperty>
					<UBnsCustomExpansionComponent.StringProperty>
						<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="기본비용#" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Opacity="1" TextScale="1" AnimScale="1" />
					</UBnsCustomExpansionComponent.StringProperty>
				</UBnsCustomExpansionComponent>
			</BnsCustomLabelWidget.ExpansionComponentList>
		</BnsCustomLabelWidget>
		<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_1_Guide" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="10 195 10 12">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_1_DiscountPrice" Offset1="5" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Guide" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
		<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_1_DiscountPrice" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="10 170 10 20">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_1_Price" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Price" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
			<BnsCustomLabelWidget.ExpansionComponentList>
				<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="DiscountCostLabel" MetaData="textref=UI.ItemGrowth.Common.Money.Guild">
					<UBnsCustomExpansionComponent.ImageProperty>
						<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
					</UBnsCustomExpansionComponent.ImageProperty>
					<UBnsCustomExpansionComponent.StringProperty>
						<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="할인비용" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Opacity="1" TextScale="1" AnimScale="1" />
					</UBnsCustomExpansionComponent.StringProperty>
				</UBnsCustomExpansionComponent>
			</BnsCustomLabelWidget.ExpansionComponentList>
		</BnsCustomLabelWidget>
		<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_1_HorizontalLine" LayoutData.Anchors="0 1 1 1" LayoutData.Offsets="0 0 0 1">
			<BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" />
			</BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" />
			</BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomImageWidget.BaseImageProperty>
				<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="199 61" ImageUVSize="5 1" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" Opacity="0.1" />
			</BnsCustomImageWidget.BaseImageProperty>
		</BnsCustomImageWidget>
	</BnsCustomImageWidget>
	<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_2" LayoutData.Anchors="0.5 0 0.5 0" LayoutData.Offsets="0 256 350 0">
		<BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
		</BnsCustomImageWidget.HorizontalResizeLink>
		<BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_1" />
		</BnsCustomImageWidget.VerticalResizeLink>
		<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_2_Title" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="10 10 10 35">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Offset1="10" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_16.Normal_16" LabelText="Item Name성장 재료Item Name성장 재료Item Name성장 재료" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
		<BnsCustomWindowWidget Name="ItemGraphReceipeTooltipPanel_2_ItemHolder" LayoutData.Offsets="12 55 300 53">
			<BnsCustomWindowWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Offset1="12" />
			</BnsCustomWindowWidget.HorizontalResizeLink>
			<BnsCustomWindowWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_2_Title" Offset1="10" />
			</BnsCustomWindowWidget.VerticalResizeLink>
			<BnsCustomWindowWidget.BaseImageProperty>
				<ImageProperty ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
			</BnsCustomWindowWidget.BaseImageProperty>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_2_ItemIcon" LayoutData.Offsets="3 0 50 49.99994" Foreground="White">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Offset1="3" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" ImageScale="1.1" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="" WidgetState="BNSCustomWidgetState_Normal">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_12.Normal_Out_12" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="2.5 2.5" Offset="-5 -6.5" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="2.5 2.5" Offset="-5 -6.5" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_2_ItemIcon_Name" LayoutData.Offsets="65 0 235 167">
				<BnsCustomLabelWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" LinkWidgetName1="ItemGraphReceipeTooltipPanel_2_ItemIcon" Offset1="65" />
				</BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomLabelWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomLabelWidget.String>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_14.Normal_14" LabelText="Item Name" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" />
				</BnsCustomLabelWidget.String>
			</BnsCustomLabelWidget>
			<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_2_ItemIcon_Desc" LayoutData.Offsets="65 217 235 14.002228">
				<BnsCustomLabelWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" LinkWidgetName1="ItemGraphReceipeTooltipPanel_2_ItemIcon" Offset1="65" />
				</BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomLabelWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_2_ItemIcon_Name" Offset1="5" />
				</BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomLabelWidget.String>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="획득처 1" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" />
				</BnsCustomLabelWidget.String>
			</BnsCustomLabelWidget>
		</BnsCustomWindowWidget>
		<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_2_MeterialItemHolder" LayoutData.Offsets="12 113 298 0">
			<BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Offset1="12" />
			</BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_2_ItemHolder" Offset1="5" />
			</BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomImageWidget.BaseImageProperty>
				<ImageProperty EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
			</BnsCustomImageWidget.BaseImageProperty>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_2_MeterialItem_1" LayoutData.Offsets="0 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_2_MeterialItem_2" LayoutData.Offsets="38 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_2_MeterialItem_1" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_2_MeterialItem_3" LayoutData.Offsets="76 0 31.999878 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_2_MeterialItem_2" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_2_MeterialItem_4" LayoutData.Offsets="114 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_2_MeterialItem_3" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_2_MeterialItem_5" LayoutData.Offsets="152 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_2_MeterialItem_4" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_2_MeterialItem_6" LayoutData.Offsets="190 0 31.999878 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_2_MeterialItem_5" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_2_MeterialItem_7" LayoutData.Offsets="228 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_2_MeterialItem_6" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_2_MeterialItem_8" LayoutData.Offsets="266 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_2_MeterialItem_7" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
		</BnsCustomImageWidget>
		<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_2_Price" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="10 150.001 10 20">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_2_MeterialItemHolder" Offset1="5" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Price" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
			<BnsCustomLabelWidget.ExpansionComponentList>
				<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="CostLabel" MetaData="textref=UI.ItemGrowth.Common.Money.none">
					<UBnsCustomExpansionComponent.ImageProperty>
						<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
					</UBnsCustomExpansionComponent.ImageProperty>
					<UBnsCustomExpansionComponent.StringProperty>
						<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="기본비용#" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Opacity="1" TextScale="1" AnimScale="1" />
					</UBnsCustomExpansionComponent.StringProperty>
				</UBnsCustomExpansionComponent>
			</BnsCustomLabelWidget.ExpansionComponentList>
		</BnsCustomLabelWidget>
		<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_2_Guide" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="10 1953 10 12">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_2_DiscountPrice" Offset1="5" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Guide" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
		<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_2_DiscountPrice" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="10 170 10 207">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_2_Price" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Price" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
			<BnsCustomLabelWidget.ExpansionComponentList>
				<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="DiscountCostLabel" MetaData="textref=UI.ItemGrowth.Common.Money.VIP">
					<UBnsCustomExpansionComponent.ImageProperty>
						<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
					</UBnsCustomExpansionComponent.ImageProperty>
					<UBnsCustomExpansionComponent.StringProperty>
						<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="할인비용" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Opacity="1" TextScale="1" AnimScale="1" />
					</UBnsCustomExpansionComponent.StringProperty>
				</UBnsCustomExpansionComponent>
			</BnsCustomLabelWidget.ExpansionComponentList>
		</BnsCustomLabelWidget>
		<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_2_HorizontalLine" LayoutData.Anchors="0 1 1 1" LayoutData.Offsets="0 0 0 1">
			<BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" />
			</BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" />
			</BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomImageWidget.BaseImageProperty>
				<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="199 61" ImageUVSize="5 1" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" Opacity="0.1" />
			</BnsCustomImageWidget.BaseImageProperty>
		</BnsCustomImageWidget>
	</BnsCustomImageWidget>
	<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_3" LayoutData.Anchors="0.5 0 0.5 0" LayoutData.Offsets="0 497 350 0">
		<BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
		</BnsCustomImageWidget.HorizontalResizeLink>
		<BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_2" />
		</BnsCustomImageWidget.VerticalResizeLink>
		<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_3_Title" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="10 10 10 35">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Offset1="10" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_16.Normal_16" LabelText="Item Name성장 재료Item Name성장 재료Item Name성장 재료" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
		<BnsCustomWindowWidget Name="ItemGraphReceipeTooltipPanel_3_ItemHolder" LayoutData.Offsets="12 55 300 53">
			<BnsCustomWindowWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Offset1="12" />
			</BnsCustomWindowWidget.HorizontalResizeLink>
			<BnsCustomWindowWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_3_Title" Offset1="10" />
			</BnsCustomWindowWidget.VerticalResizeLink>
			<BnsCustomWindowWidget.BaseImageProperty>
				<ImageProperty ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
			</BnsCustomWindowWidget.BaseImageProperty>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_3_ItemIcon" LayoutData.Offsets="3 0 50 50" Foreground="White">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Offset1="3" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" ImageScale="1.1" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="" WidgetState="BNSCustomWidgetState_Normal">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_12.Normal_Out_12" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="2.5 2.5" Offset="-5 -6.5" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="2.5 2.5" Offset="-5 -6.5" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_3_ItemIcon_Name" LayoutData.Offsets="65 0 235 16">
				<BnsCustomLabelWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" LinkWidgetName1="ItemGraphReceipeTooltipPanel_3_ItemIcon" Offset1="65" />
				</BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomLabelWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomLabelWidget.String>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_14.Normal_14" LabelText="Item Name" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" />
				</BnsCustomLabelWidget.String>
			</BnsCustomLabelWidget>
			<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_3_ItemIcon_Desc" LayoutData.Offsets="65 21 235 14">
				<BnsCustomLabelWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" LinkWidgetName1="ItemGraphReceipeTooltipPanel_3_ItemIcon" Offset1="65" />
				</BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomLabelWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_3_ItemIcon_Name" Offset1="5" />
				</BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomLabelWidget.String>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="획득처 1" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" />
				</BnsCustomLabelWidget.String>
			</BnsCustomLabelWidget>
		</BnsCustomWindowWidget>
		<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_3_MeterialItemHolder" LayoutData.Offsets="12 113 298 0">
			<BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Offset1="12" />
			</BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_3_ItemHolder" Offset1="5" />
			</BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomImageWidget.BaseImageProperty>
				<ImageProperty EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
			</BnsCustomImageWidget.BaseImageProperty>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_3_MeterialItem_1" LayoutData.Offsets="0 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_3_MeterialItem_2" LayoutData.Offsets="38 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_3_MeterialItem_1" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_3_MeterialItem_3" LayoutData.Offsets="76 0 31.999878 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_3_MeterialItem_2" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_3_MeterialItem_4" LayoutData.Offsets="114 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_3_MeterialItem_3" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_3_MeterialItem_5" LayoutData.Offsets="152 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_3_MeterialItem_4" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_3_MeterialItem_6" LayoutData.Offsets="190 0 31.999878 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_3_MeterialItem_5" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_3_MeterialItem_7" LayoutData.Offsets="228 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_3_MeterialItem_6" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_3_MeterialItem_8" LayoutData.Offsets="266 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_3_MeterialItem_7" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
		</BnsCustomImageWidget>
		<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_3_Price" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="10 150 10 20">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_3_MeterialItemHolder" Offset1="5" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Price" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
			<BnsCustomLabelWidget.ExpansionComponentList>
				<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="CostLabel" MetaData="textref=UI.ItemGrowth.Common.Money.none">
					<UBnsCustomExpansionComponent.ImageProperty>
						<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
					</UBnsCustomExpansionComponent.ImageProperty>
					<UBnsCustomExpansionComponent.StringProperty>
						<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="기본비용#" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Opacity="1" TextScale="1" AnimScale="1" />
					</UBnsCustomExpansionComponent.StringProperty>
				</UBnsCustomExpansionComponent>
			</BnsCustomLabelWidget.ExpansionComponentList>
		</BnsCustomLabelWidget>
		<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_3_Guide" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="10 195.00354 10 12">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_3_DiscountPrice" Offset1="5" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Guide" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
		<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_3_DiscountPrice" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="10 170.00195 10 20">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_3_Price" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Price" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
			<BnsCustomLabelWidget.ExpansionComponentList>
				<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="DiscountCostLabel" MetaData="textref=UI.ItemGrowth.Common.Money.VIP">
					<UBnsCustomExpansionComponent.ImageProperty>
						<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
					</UBnsCustomExpansionComponent.ImageProperty>
					<UBnsCustomExpansionComponent.StringProperty>
						<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="할인비용" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Opacity="1" TextScale="1" AnimScale="1" />
					</UBnsCustomExpansionComponent.StringProperty>
				</UBnsCustomExpansionComponent>
			</BnsCustomLabelWidget.ExpansionComponentList>
		</BnsCustomLabelWidget>
		<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_3_HorizontalLine" LayoutData.Anchors="0 1 1 1" LayoutData.Offsets="0 0 0 1">
			<BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" />
			</BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" />
			</BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomImageWidget.BaseImageProperty>
				<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="199 61" ImageUVSize="5 1" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" Opacity="0.1" />
			</BnsCustomImageWidget.BaseImageProperty>
		</BnsCustomImageWidget>
	</BnsCustomImageWidget>
	<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_4" LayoutData.Anchors="0.5 0 0.5 0" LayoutData.Offsets="0 738 350 0">
		<BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
		</BnsCustomImageWidget.HorizontalResizeLink>
		<BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_3" />
		</BnsCustomImageWidget.VerticalResizeLink>
		<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_4_Title" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="10 10 10 35">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Offset1="10" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_16.Normal_16" LabelText="Item Name성장 재료Item Name성장 재료Item Name성장 재료" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
		<BnsCustomWindowWidget Name="ItemGraphReceipeTooltipPanel_4_ItemHolder" LayoutData.Offsets="12 55 300 53">
			<BnsCustomWindowWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Offset1="12" />
			</BnsCustomWindowWidget.HorizontalResizeLink>
			<BnsCustomWindowWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_4_Title" Offset1="10" />
			</BnsCustomWindowWidget.VerticalResizeLink>
			<BnsCustomWindowWidget.BaseImageProperty>
				<ImageProperty ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
			</BnsCustomWindowWidget.BaseImageProperty>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_4_ItemIcon" LayoutData.Offsets="3 0 50 50" Foreground="White">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Offset1="3" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" ImageScale="1.1" SperateImageType="BNS_SperateImageType_9Frame" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="" WidgetState="BNSCustomWidgetState_Normal">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_12.Normal_Out_12" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="2.5 2.5" Offset="-5 -6.5" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="2.5 2.5" Offset="-5 -6.5" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_4_ItemIcon_Name" LayoutData.Offsets="65 0 235 16">
				<BnsCustomLabelWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" LinkWidgetName1="ItemGraphReceipeTooltipPanel_4_ItemIcon" Offset1="65" />
				</BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomLabelWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomLabelWidget.String>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_14.Normal_14" LabelText="Item Name" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" />
				</BnsCustomLabelWidget.String>
			</BnsCustomLabelWidget>
			<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_4_ItemIcon_Desc" LayoutData.Offsets="65 21 235 14">
				<BnsCustomLabelWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" LinkWidgetName1="ItemGraphReceipeTooltipPanel_4_ItemIcon" Offset1="65" />
				</BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomLabelWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_4_ItemIcon_Name" Offset1="5" />
				</BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomLabelWidget.String>
					<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="획득처 1" SpaceBetweenLines="3" VerticalAlignment="VAlign_Center" />
				</BnsCustomLabelWidget.String>
			</BnsCustomLabelWidget>
		</BnsCustomWindowWidget>
		<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_4_MeterialItemHolder" LayoutData.Offsets="12 113 298 0">
			<BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Offset1="12" />
			</BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_4_ItemHolder" Offset1="5" />
			</BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomImageWidget.BaseImageProperty>
				<ImageProperty EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" Opacity="1" />
			</BnsCustomImageWidget.BaseImageProperty>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_4_MeterialItem_1" LayoutData.Offsets="0 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_4_MeterialItem_2" LayoutData.Offsets="38 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_4_MeterialItem_1" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_4_MeterialItem_3" LayoutData.Offsets="76 0 31.999878 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_4_MeterialItem_2" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_4_MeterialItem_4" LayoutData.Offsets="114 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_4_MeterialItem_3" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_4_MeterialItem_5" LayoutData.Offsets="152 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_4_MeterialItem_4" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_4_MeterialItem_6" LayoutData.Offsets="190 0 31.999878 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_4_MeterialItem_5" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_4_MeterialItem_7" LayoutData.Offsets="228 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_4_MeterialItem_6" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
			<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_4_MeterialItem_8" LayoutData.Offsets="266 0 32 32">
				<BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_4_MeterialItem_7" Offset1="6" />
				</BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomImageWidget.VerticalResizeLink>
					<BnsCustomResizeLink bEnable="true" />
				</BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomImageWidget.BaseImageProperty>
					<ImageProperty ImageUV="868 275" ImageUVSize="44 44" ImageScale="1" Opacity="1" />
				</BnsCustomImageWidget.BaseImageProperty>
				<BnsCustomImageWidget.ExpansionComponentList>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="BackgroundFrameImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="133 1" ImageUVSize="46 46" EnableDrawImage="true" EnableResourceSize="true" EnableSkinColor="true" GrayWeightValue="0.15" ImageScale="0.83" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" StaticPadding="1 1" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="IconImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" EnableMultiImage="true" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="UnusableImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_00.GameUI_New_Scene_00" ImageUV="100 932" ImageUVSize="46 46" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="Grade_Image" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SearchedImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_New_Scene_15.GameUI_New_Scene_15" ImageUV="784 727" ImageUVSize="40 40" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="StackableLabel" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
						<UBnsCustomExpansionComponent.StringProperty>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_10.Normal_Out_10" LabelText="99" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Bottom" ClippingBoundFace_Vertical="WidgetFaceFace_Top" bWordWrap="true" Padding="1 1" Opacity="1" TextScale="1" AnimScale="1" />
						</UBnsCustomExpansionComponent.StringProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="CanSaleItem" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Icon/SlotItem_marketBusiness.SlotItem_marketBusiness" ImageUVSize="64 64" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage" MetaData="">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 448" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
					<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionName="SymbolImage_Chacked" MetaData="" WidgetSubState="Expansion_WidgetSubState_Checked">
						<UBnsCustomExpansionComponent.ImageProperty>
							<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window/GameUI_MenuIcon.GameUI_MenuIcon" ImageUV="96 416" ImageUVSize="32 32" EnableDrawImage="true" EnableResourceSize="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" TintColor="FFFFFF" StaticPadding="7 7" Offset="-8 -9" Opacity="1" />
						</UBnsCustomExpansionComponent.ImageProperty>
					</UBnsCustomExpansionComponent>
				</BnsCustomImageWidget.ExpansionComponentList>
			</BnsCustomImageWidget>
		</BnsCustomImageWidget>
		<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_4_Price" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="10 150 10 20">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_4_MeterialItemHolder" Offset1="5" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Price" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
			<BnsCustomLabelWidget.ExpansionComponentList>
				<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="CostLabel" MetaData="textref=UI.ItemGrowth.Common.Money.none">
					<UBnsCustomExpansionComponent.ImageProperty>
						<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
					</UBnsCustomExpansionComponent.ImageProperty>
					<UBnsCustomExpansionComponent.StringProperty>
						<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="기본비용#" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Opacity="1" TextScale="1" AnimScale="1" />
					</UBnsCustomExpansionComponent.StringProperty>
				</UBnsCustomExpansionComponent>
			</BnsCustomLabelWidget.ExpansionComponentList>
		</BnsCustomLabelWidget>
		<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_4_Guide" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="10 195 10 12">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_4_DiscountPrice" Offset1="5" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Guide" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" />
			</BnsCustomLabelWidget.String>
		</BnsCustomLabelWidget>
		<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_4_DiscountPrice" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="10 170 10 20">
			<BnsCustomLabelWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
			</BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomLabelWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_4_Price" />
			</BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomLabelWidget.String>
				<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Price" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Center" />
			</BnsCustomLabelWidget.String>
			<BnsCustomLabelWidget.ExpansionComponentList>
				<UBnsCustomExpansionComponent bPostExpansitonRender="true" bShow="true" ExpansionType="STRING" ExpansionName="DiscountCostLabel" MetaData="textref=UI.ItemGrowth.Common.Money.VIP">
					<UBnsCustomExpansionComponent.ImageProperty>
						<ImageProperty ImageUVSize="1 1" EnableDrawImage="true" GrayWeightValue="0.15" ImageScale="1" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" TintColor="FFFFFF" Opacity="1" />
					</UBnsCustomExpansionComponent.ImageProperty>
					<UBnsCustomExpansionComponent.StringProperty>
						<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="할인비용" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Center" ClippingBoundFace_Vertical="WidgetFaceFace_Top" Opacity="1" TextScale="1" AnimScale="1" />
					</UBnsCustomExpansionComponent.StringProperty>
				</UBnsCustomExpansionComponent>
			</BnsCustomLabelWidget.ExpansionComponentList>
		</BnsCustomLabelWidget>
		<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_4_HorizontalLine" LayoutData.Anchors="0 1 1 1" LayoutData.Offsets="0 0 0 1">
			<BnsCustomImageWidget.HorizontalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" />
			</BnsCustomImageWidget.HorizontalResizeLink>
			<BnsCustomImageWidget.VerticalResizeLink>
				<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT" />
			</BnsCustomImageWidget.VerticalResizeLink>
			<BnsCustomImageWidget.BaseImageProperty>
				<ImageProperty BaseImageTexture="BNSR/Content/Art/UI/GameUI/Resource/GameUI_Window_R/BNSR_UIElement.BNSR_UIElement" ImageUV="199 61" ImageUVSize="5 1" EnableDrawImage="true" ImageScale="1" HorizontalAlignment="HAlign_Left" VerticalAlignment="VAlign_Top" SperateImageType="BNS_SperateImageType_3Frame" Opacity="0.1" />
			</BnsCustomImageWidget.BaseImageProperty>
		</BnsCustomImageWidget>
	</BnsCustomImageWidget>
	<BnsCustomColumnListWidget Name="ItemGraphReceipeTooltipPanel_OptionList" LayoutData.Anchors="0.5 0 0.5 0" LayoutData.Offsets="0 979 350 0">
		<BnsCustomColumnListWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_CENTER" />
		</BnsCustomColumnListWidget.HorizontalResizeLink>
		<BnsCustomColumnListWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_4" />
		</BnsCustomColumnListWidget.VerticalResizeLink>
		<BnsCustomColumnListWidget.ItemTemplate>
			<WidgetTemplate>
				<BnsCustomImageWidget Name="ItemGraphReceipeTooltipPanel_OptionList_Column_1_1" LayoutData.Offsets="0 0 350 0">
					<BnsCustomImageWidget.HorizontalResizeLink>
						<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="10" Offset2="10" />
					</BnsCustomImageWidget.HorizontalResizeLink>
					<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_OptionList_Column_1_1_Label" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 0 60 0" Text="{Binding Path=Item1}" />
					<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_OptionList_Column_1_1_Desc" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="0 0 20 20" Text="{Binding Path=Item2,StringFormat=P2}" >
						<BnsCustomLabelWidget.String>
							<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_12.Normal_12" LabelText="Price" SpaceBetweenLines="3" HorizontalAlignment="HAlign_Right" VerticalAlignment="VAlign_Center" />
						</BnsCustomLabelWidget.String>
					</BnsCustomLabelWidget>
				</BnsCustomImageWidget>
			</WidgetTemplate>
		</BnsCustomColumnListWidget.ItemTemplate>
	</BnsCustomColumnListWidget>

	<BnsCustomLabelWidget Name="ItemGraphReceipeTooltipPanel_Guide" LayoutData.Anchors="0 0 1 0" LayoutData.Offsets="15 984 15 30">
		<BnsCustomLabelWidget.HorizontalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_BORDER_LINK_RIGHT_AND_LEFT" Offset1="15" Offset2="15" />
		</BnsCustomLabelWidget.HorizontalResizeLink>
		<BnsCustomLabelWidget.VerticalResizeLink>
			<BnsCustomResizeLink bEnable="true" Type="BNS_CUSTOM_WIDGET_LINK_RIGHT" LinkWidgetName1="ItemGraphReceipeTooltipPanel_OptionList" Offset1="5" />
		</BnsCustomLabelWidget.VerticalResizeLink>
		<BnsCustomLabelWidget.String>
			<StringProperty fontset="BNSR/Content/Art/UI/GameUI_BNSR/Resource/GameUI_FontSet_R/UI/Normal_Out_14.Normal_Out_14" LabelText="N개의 경로가 더 있습니다." SpaceBetweenLines="3" HorizontalAlignment="HAlign_Center" VerticalAlignment="VAlign_Center" />
		</BnsCustomLabelWidget.String>
	</BnsCustomLabelWidget>
</BnsCustomWindowWidget>