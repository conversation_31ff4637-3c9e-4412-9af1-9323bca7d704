using Xylia.BnsHelper.Models.Triggers;
using Xylia.BnsHelper.ViewModels.Dialogs;

namespace Xylia.BnsHelper.Views.Dialogs;

public partial class TriggerActionEditorDialog
{
    #region Constructor
     readonly TriggerActionEditorViewModel _viewModel;

    public TriggerActionEditorDialog(TriggerAction action)
    {
        InitializeComponent();

        DataContext = _viewModel = new TriggerActionEditorViewModel();
        _viewModel.SetAction(action);
        _viewModel.CloseRequested += OnCloseRequested;
    }
    #endregion

    #region Methods
    public TriggerAction Action => _viewModel.Action ?? throw new NotSupportedException();

    private void OnCloseRequested(object? sender, EventArgs e)
    {
        DialogResult = _viewModel.DialogResult;
        Close();
    }

    protected override void OnClosed(EventArgs e)
    {
        _viewModel.CloseRequested -= OnCloseRequested;
        base.OnClosed(e);
    }
    #endregion
}
