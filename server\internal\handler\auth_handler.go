package handler

import (
	"fmt"
	"net"
	"udp-server/server/internal/model"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/binary"
	"udp-server/server/pkg/logger"
)

// AuthHandler 处理认证相关的请求
type AuthHandler struct {
	authService       *service.AuthService
	permissionService *service.PermissionService
	riskService       *service.RiskControlService
	updateService     *service.UpdateService
}

// 创建新的认证处理器
func NewAuthHandler(authService *service.AuthService, permissionService *service.PermissionService, riskService *service.RiskControlService, updateService *service.UpdateService) *AuthHandler {
	return &AuthHandler{
		authService:       authService,
		permissionService: permissionService,
		riskService:       riskService,
		updateService:     updateService,
	}
}

// 处理登录请求
func (h *AuthHandler) HandleLoginRequest(msg *binary.Message, remoteAddr *net.UDPAddr) (*binary.BinaryWriter, error) {
	// 解析登录请求
	loginReq, err := DecodeLoginRequest(msg.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to parse login request: %w", err)
	}

	logger.Debug("收到登录请求: QQ号=%v, 设备指纹=%s, 客户端IP=%s", loginReq.QQNumber, loginReq.DeviceFingerprint, remoteAddr.IP.String())

	// 先检查用户封禁状态，如果已被封禁则无需进行风控检查
	if err := h.authService.CheckUserBanStatus(loginReq.QQNumber); err != nil {
		logger.Warn("用户封禁状态检查失败: QQ号=%d, 错误=%v", loginReq.QQNumber, err)
		return nil, err
	}

	// 用户状态正常，进行风控检查
	if h.riskService != nil {
		if err := h.riskService.CheckLoginRisk(loginReq.QQNumber, loginReq.DeviceFingerprint, remoteAddr.IP.String()); err != nil {
			logger.Warn("风控检查失败: QQ号=%d, 设备=%s, IP=%s, 错误=%v", loginReq.QQNumber, loginReq.DeviceFingerprint, remoteAddr.IP.String(), err)
			return nil, err
		}
	}

	// 调用认证服务
	user, err := h.authService.Login(loginReq.QQNumber, loginReq.DeviceFingerprint, loginReq, remoteAddr.IP.String())
	if err != nil {
		logger.Error("登录失败: QQ号=%v, 错误=%v", loginReq.QQNumber, err)
		return nil, err
	}

	logger.Info("登录成功: QQ号=%d, Token=%s", loginReq.QQNumber, user.Token)

	// 获取插件版本和下载URL
	pluginVersion, pluginUrl := getPluginConfig(h.updateService)

	// 通过权限服务获取权限过期时间
	permissionExpiration, err := h.permissionService.GetPermissionExpiration(user.UID, "client")
	if err != nil {
		logger.Warn("获取用户权限过期时间失败: UID=%d, Error=%v", user.UID, err)
		permissionExpiration = 0 // 无权限
	}

	// 通过权限服务计算权限等级
	finalPermission, err := h.permissionService.GetUserPermissionLevel(user.UID, "client")
	if err != nil {
		logger.Warn("获取用户权限等级失败: UID=%d, Error=%v", user.UID, err)
		finalPermission = 0 // 默认为普通用户
	}

	// 创建响应
	writer := binary.NewBinaryWriter()
	writer.WriteUint32(binary.ErrorCodeSuccess)
	writer.WriteString(user.Token)
	writer.WriteUint8(finalPermission)      // 用户权限
	writer.WriteInt64(permissionExpiration) // 权限过期时间
	writer.WriteString(pluginVersion)       // 插件版本
	writer.WriteString(pluginUrl)           // 插件下载URL
	return writer, nil
}

// 处理登出请求
func (h *AuthHandler) HandleLogoutRequest(msg *binary.Message, remoteAddr *net.UDPAddr) (*binary.BinaryWriter, error) {
	// 解析注销请求
	reader := binary.NewBinaryReader(msg.Body)
	token, err := reader.ReadString()
	if err != nil {
		return nil, fmt.Errorf("解析注销请求失败")
	}

	logger.Debug("收到注销请求: Token=%s, 客户端IP=%s", token, remoteAddr.IP.String())

	// 调用认证服务进行注销
	err = h.authService.Logout(token)
	if err != nil {
		return nil, err
	}

	// 构建响应
	writer := binary.NewBinaryWriter()
	writer.WriteUint32(binary.ErrorCodeSuccess)
	return writer, nil
}

// 获取插件配置
func getPluginConfig(updateService *service.UpdateService) (string, string) {
	// 获取插件更新配置
	config := updateService.GetLatestUpdateConfig("bns-helper")
	if config == nil || config.PluginURL == "" {
		logger.Warn("获取插件配置失败")
		return "", ""
	}

	return config.PluginVersion, config.PluginURL
}

// 解码登录请求
func DecodeLoginRequest(data []byte) (*model.LoginRequest, error) {
	reader := binary.NewBinaryReader(data)
	req := &model.LoginRequest{}

	var err error
	req.QQNumber, err = reader.ReadInt64()
	if err != nil {
		return nil, fmt.Errorf("failed to read QQNumber: %w", err)
	}

	req.DeviceFingerprint, err = reader.ReadString()
	if err != nil {
		return nil, fmt.Errorf("failed to read DeviceFingerprint: %w", err)
	}

	return req, nil
}
