using System.Collections.Concurrent;
using System.Diagnostics;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models.Triggers;
using Xylia.BnsHelper.Services.Network;
using Xylia.BnsHelper.Services.Network.Plugin;

namespace Xylia.BnsHelper.Services;

/// <summary>
/// 触发器服务
/// </summary>
public class TriggerService : IService, IDisposable
{
    #region Fields
    private static readonly Lazy<TriggerService> _instance = new(() => new TriggerService());
    public static TriggerService Instance => _instance.Value;

    private readonly ConcurrentBag<Trigger> _triggers = new();
    private readonly Timer _processingTimer;
    private readonly Timer _timeCheckTimer;
    private readonly ConcurrentQueue<string> _messageQueue = new();
    private PluginSession? Service;


    public event EventHandler<TriggerExecutedEventArgs>? TriggerExecuted;
    public event EventHandler<TriggerCollectionChangedEventArgs>? TriggerCollectionChanged;

    private TriggerService()
    {
        _processingTimer = new Timer(ProcessMessages, null, TimeSpan.FromMilliseconds(10), TimeSpan.FromMilliseconds(10));
        _timeCheckTimer = new Timer(CheckTimeTriggers, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));

        Register();
    }

    private bool _isInitialized = false;
    public bool IsInitialized => _isInitialized;

    private bool _isEnabled = SettingHelper.Default.EnableTrigger;
    public bool IsEnabled
    {
        get => _isEnabled;
        set
        {
            SettingHelper.Default.EnableTrigger = _isEnabled = value;
            Debug.WriteLine($"[TriggerService] 触发器服务已{(value ? "启用" : "禁用")}");
        }
    }

    /// <summary>
    /// 获取所有触发器
    /// </summary>
    public IEnumerable<Trigger> Triggers => _triggers;
    #endregion

    #region Service
    public void Register()
    {
        if (_isInitialized) return;

        Debug.WriteLine("[TriggerService] 正在初始化触发器服务...");

        // 连接到游戏插件（如果可用）
        var gameWindow = WindowHelper.GetGameWindow();
        Service = PluginSession.GetOrCreate(gameWindow);
        Service.PacketReceived += OnReceived;
        _isInitialized = true;
    }

    private void OnReceived(object? sender, IPacket packet)
    {
        switch (packet)
        {
            case InstantNotification i:
                ProcessMessage(i.TrimedText);
                break;
        }
    }

    public void Dispose()
    {
        try
        {
            if (_isInitialized)
            {
                // 禁用触发器系统
                IsEnabled = false;

                if (Service != null) Service.PacketReceived -= OnReceived;

                _isInitialized = false;
                Debug.WriteLine("[TriggerService] 触发器服务已关闭");
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[AdvancedTriggerService] 关闭服务失败: {ex.Message}");
        }
        finally
        {
            GC.SuppressFinalize(this);
        }
    }
    #endregion

    #region Trigger Management
    /// <summary>
    /// 添加触发器
    /// </summary>
    public void AddTrigger(Trigger trigger)
    {
        if (trigger == null) return;

        _triggers.Add(trigger);
        Debug.WriteLine($"[TriggerService] 添加触发器: {trigger.Name}");
        TriggerCollectionChanged?.Invoke(this, new TriggerCollectionChangedEventArgs(trigger, TriggerCollectionChangeType.Added));
    }

    /// <summary>
    /// 移除触发器
    /// </summary>
    public void RemoveTrigger(Trigger trigger)
    {
        if (trigger == null) return;

        // Concurrent集合无法删除特定对象，因此使用软删除策略
        trigger.IsEnabled = false;
        Debug.WriteLine($"[TriggerService] 移除触发器: {trigger.Name}");
        TriggerCollectionChanged?.Invoke(this, new TriggerCollectionChangedEventArgs(trigger, TriggerCollectionChangeType.Removed));
    }

    /// <summary>
    /// 处理消息
    /// </summary>
    public void ProcessMessage(string? message)
    {
        if (!_isEnabled || string.IsNullOrEmpty(message)) return;

        _messageQueue.Enqueue(message);
    }

    /// <summary>
    /// 处理消息队列
    /// </summary>
    private void ProcessMessages(object? state)
    {
        if (!_isEnabled) return;

        var processedCount = 0;
        const int maxProcessPerCycle = 20;

        while (_messageQueue.TryDequeue(out var message) && processedCount < maxProcessPerCycle)
        {
            try
            {
                ProcessSingleMessage(message);
                processedCount++;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[TriggerService] 处理消息失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 处理单个消息
    /// </summary>
    private void ProcessSingleMessage(string message)
    {
        foreach (var trigger in _triggers.Where(t => t.IsMatch(message)).OrderBy(t => t.Priority))
        {
            try
            {
                _ = Task.Run(async () =>
                {
                    await trigger.ExecuteAsync(message);
                    TriggerExecuted?.Invoke(this, new TriggerExecutedEventArgs(trigger, message));
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[TriggerService] 触发器执行失败: {trigger.Name} - {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 检查时间触发器
    /// </summary>
    private void CheckTimeTriggers(object? state)
    {
        if (!_isEnabled) return;

        var currentTime = DateTime.Now;
        var timeEvent = new TimeEvent(currentTime);
        var variables = new Dictionary<string, object>();

        foreach (var trigger in _triggers.Where(t => t.IsEnabled && t.Conditions.Any(c => c is TimeCondition)))
        {
            try
            {
                // 检查所有时间条件
                var shouldExecute = trigger.Conditions.OfType<TimeCondition>().Any(condition => condition.Evaluate(timeEvent, variables));
                if (shouldExecute && trigger.CanExecute())
                {
                    _ = Task.Run(async () =>
                    {
                        await trigger.ExecuteAsync($"定时触发: {currentTime:HH:mm}");
                        TriggerExecuted?.Invoke(this, new TriggerExecutedEventArgs(trigger, $"定时触发: {currentTime:HH:mm}"));
                    });
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[TriggerService] 时间触发器执行失败: {trigger.Name} - {ex.Message}");
            }
        }
    }
    #endregion
}

/// <summary>
/// 触发器执行事件参数
/// </summary>
public class TriggerExecutedEventArgs : EventArgs
{
    public Trigger Trigger { get; }
    public string Message { get; }
    public DateTime ExecutionTime { get; }

    public TriggerExecutedEventArgs(Trigger trigger, string message)
    {
        Trigger = trigger;
        Message = message;
        ExecutionTime = DateTime.Now;
    }
}

/// <summary>
/// 触发器集合变更事件参数
/// </summary>
public class TriggerCollectionChangedEventArgs : EventArgs
{
    public Trigger Trigger { get; }
    public TriggerCollectionChangeType ChangeType { get; }

    public TriggerCollectionChangedEventArgs(Trigger trigger, TriggerCollectionChangeType changeType)
    {
        Trigger = trigger;
        ChangeType = changeType;
    }
}

/// <summary>
/// 触发器集合变更类型
/// </summary>
public enum TriggerCollectionChangeType
{
    Added,
    Removed,
    Modified
}