using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace BnsHelper.Common.Helpers;
public static class EncryptionHelper
{
    #region Constants
    /// <summary>
    /// 默认加密密钥
    /// </summary>
    private static readonly byte[] DefaultKey = Encoding.UTF8.GetBytes("Xylia@2025Key#");

    /// <summary>
    /// AES块大小
    /// </summary>
    private const int AesBlockSize = 16;
    #endregion

    #region Public Methods
    /// <summary>
    /// 加密文件内容并保存
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="content">要加密的内容</param>
    /// <param name="key">加密密钥（可选）</param>
    public static async Task EncryptToFileAsync(string filePath, string content, byte[]? key = null)
    {
        var encryptedContent = EncryptString(content, key);
        await File.WriteAllTextAsync(filePath, encryptedContent);
    }

    /// <summary>
    /// 智能读取文件内容（自动检测并解密）
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="key">解密密钥（可选）</param>
    /// <returns>文件内容（如果是加密文件则自动解密）</returns>
    public static string ReadFile(string filePath, byte[]? key = null)
    {
        if (!File.Exists(filePath)) return string.Empty;

        try
        {
            var content = File.ReadAllText(filePath);
            
            // 尝试检测是否为加密格式
            if (IsEncryptedContent(content))
            {
                // 是加密内容，尝试解密
                return DecryptString(content, key);
            }
            else
            {
                // 不是加密内容，直接返回
                return content;
            }
        }
        catch
        {
            return string.Empty;
        }
    }
    #endregion

    #region Private Helper Methods
    /// <summary>
    /// 使用AES加密字符串
    /// </summary>
    private static string EncryptString(string plainText, byte[]? key = null)
    {
        if (string.IsNullOrEmpty(plainText))
            return string.Empty;

        key ??= DefaultKey;
        var plainBytes = Encoding.UTF8.GetBytes(plainText);
        var encryptedBytes = EncryptBytes(plainBytes, key);
        return Convert.ToBase64String(encryptedBytes);
    }

    /// <summary>
    /// 使用AES解密字符串
    /// </summary>
    private static string DecryptString(string encryptedText, byte[]? key = null)
    {
        if (string.IsNullOrEmpty(encryptedText))
            return string.Empty;

        try
        {
            key ??= DefaultKey;
            var encryptedBytes = Convert.FromBase64String(encryptedText);
            var decryptedBytes = DecryptBytes(encryptedBytes, key);
            return Encoding.UTF8.GetString(decryptedBytes);
        }
        catch
        {
            return string.Empty;
        }
    }

    /// <summary>
    /// 使用AES加密字节数组
    /// </summary>
    private static byte[] EncryptBytes(byte[] data, byte[] key)
    {
        using var aes = Aes.Create();
        aes.Key = DeriveKey(key, 32); // AES-256
        aes.GenerateIV();

        using var encryptor = aes.CreateEncryptor();
        using var msEncrypt = new MemoryStream();
        
        // 先写入IV
        msEncrypt.Write(aes.IV, 0, aes.IV.Length);
        
        using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
        {
            csEncrypt.Write(data, 0, data.Length);
        }

        return msEncrypt.ToArray();
    }

    /// <summary>
    /// 使用AES解密字节数组
    /// </summary>
    private static byte[] DecryptBytes(byte[] encryptedData, byte[] key)
    {
        using var aes = Aes.Create();
        aes.Key = DeriveKey(key, 32); // AES-256

        // 提取IV
        var iv = new byte[AesBlockSize];
        Array.Copy(encryptedData, 0, iv, 0, AesBlockSize);
        aes.IV = iv;

        using var decryptor = aes.CreateDecryptor();
        using var msDecrypt = new MemoryStream(encryptedData, AesBlockSize, encryptedData.Length - AesBlockSize);
        using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
        using var msResult = new MemoryStream();
        
        csDecrypt.CopyTo(msResult);
        return msResult.ToArray();
    }

    /// <summary>
    /// 检查内容是否为加密格式
    /// </summary>
    private static bool IsEncryptedContent(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
            return false;

        try
        {
            // 尝试Base64解码
            var bytes = Convert.FromBase64String(content.Trim());
            
            // 检查长度是否足够（至少包含IV）
            if (bytes.Length <= AesBlockSize) return false;

            // 检查是否不是有效的JSON（简单检测）
            var trimmed = content.Trim();
            return !trimmed.StartsWith("{") && !trimmed.StartsWith("[");
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 从密钥派生指定长度的密钥
    /// </summary>
    private static byte[] DeriveKey(byte[] key, int keySize)
    {
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(key);
        
        if (hash.Length >= keySize)
        {
            var result = new byte[keySize];
            Array.Copy(hash, result, keySize);
            return result;
        }
        
        // 如果哈希长度不够，重复哈希
        var derivedKey = new byte[keySize];
        var offset = 0;
        while (offset < keySize)
        {
            var remaining = keySize - offset;
            var copyLength = Math.Min(hash.Length, remaining);
            Array.Copy(hash, 0, derivedKey, offset, copyLength);
            offset += copyLength;
            
            if (offset < keySize)
            {
                hash = sha256.ComputeHash(hash);
            }
        }
        
        return derivedKey;
    }
    #endregion
}