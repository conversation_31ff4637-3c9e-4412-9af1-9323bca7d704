using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using Xylia.BnsHelper.ViewModels;

namespace Xylia.BnsHelper.Views;
public partial class AnnouncementWindow
{
    public AnnouncementWindow()
    {
        InitializeComponent();
        DataContext = new AnnouncementViewModel();

        // 监听窗口状态变化，更新最大化/还原按钮图标
        StateChanged += OnWindowStateChanged;
        UpdateMaximizeRestoreIcon();
    }

    #region 标题栏事件处理
    /// <summary>
    /// 标题栏拖拽移动窗口
    /// </summary>
    private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (e.ClickCount == 2)
        {
            // 双击切换最大化/还原
            ToggleMaximizeRestore();
        }
        else
        {
            // 单击拖拽移动
            DragMove();
        }
    }
    #endregion

    #region 窗口控制按钮事件
    /// <summary>
    /// 最小化按钮
    /// </summary>
    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState.Minimized;
    }

    /// <summary>
    /// 最大化/还原按钮
    /// </summary>
    private void MaximizeRestoreButton_Click(object sender, RoutedEventArgs e)
    {
        ToggleMaximizeRestore();
    }

    /// <summary>
    /// 关闭按钮
    /// </summary>
    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }
    #endregion

    #region 辅助方法
    /// <summary>
    /// 切换最大化/还原状态
    /// </summary>
    private void ToggleMaximizeRestore()
    {
        WindowState = WindowState == WindowState.Maximized ? WindowState.Normal : WindowState.Maximized;
    }

    /// <summary>
    /// 窗口状态变化时更新图标
    /// </summary>
    private void OnWindowStateChanged(object? sender, EventArgs e)
    {
        UpdateMaximizeRestoreIcon();
    }

    /// <summary>
    /// 更新最大化/还原按钮图标
    /// </summary>
    private void UpdateMaximizeRestoreIcon()
    {
        if (MaximizeRestoreIcon != null)
        {
            // 根据窗口状态切换图标，使用HandyControl的内置资源
            var iconResource = WindowState == WindowState.Maximized ? "WindowRestoreGeometry" : "WindowMaxGeometry";

            if (Application.Current.TryFindResource(iconResource) is Geometry geometry)
            {
                MaximizeRestoreIcon.Data = geometry;
            }

            // 更新工具提示
            MaximizeRestoreButton.ToolTip = WindowState == WindowState.Maximized ? "还原" : "最大化";
        }
    }
    #endregion
}