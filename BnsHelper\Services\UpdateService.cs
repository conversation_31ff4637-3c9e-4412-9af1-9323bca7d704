﻿using Serilog;
using System.Windows;
using Xylia.BnsHelper.Common;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.Services.Network.BinaryProtocol;
using Xylia.BnsHelper.Services.Network.Service;
using Xylia.Updater;

namespace Xylia.BnsHelper.Services;
/// <summary>
/// Provides functionality to manage and execute application updates using the AutoUpdater.NET library.
/// </summary>
/// <remarks>This service is responsible for configuring and initiating the update process for the application. 
/// It ensures that updates are downloaded and applied automatically when a newer version is available.  The service
/// uses forced download mode and handles update information parsing and error reporting.</remarks>
internal class UpdateService : IService
{
    #region Constuctor
    private static Action? _updateCheckCompleteCallback;
    private static UpdateService? _instance;
    public static UpdateService Instance => _instance ??= new UpdateService();

    static UpdateService()
    {
        AutoUpdater.ClearAppDirectory = true;
        AutoUpdater.RemindLaterTimeSpan = 0;
        AutoUpdater.ReportErrors = true;
        AutoUpdater.UpdateMode = Mode.ForcedDownload;
        AutoUpdater.RunUpdateAsAdmin = true;
    }

    private UpdateService() { }
    #endregion

    #region Methods
    public async void Register()
    {
        const int maxRetries = 2; // 最多重试2次（包括初始尝试）
        using var helper = new ServerConfigHelper();

        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                // 通信Gateway获取程序配置，注意此会话不是持久化的
                using var session = new BnszsGateSession(helper.GetServerConfig());
                session.SendPacket(new UpdateConfigPacket { AppType = 0x1, Version = VersionHelper.InternalVersion }, MessageTypes.UpdateConfig);

                var responsePacket = await session.WaitForResponseWithRetry(MessageTypes.UpdateConfigResponse, 5, 3000);
                if (responsePacket is UpdateConfigPacket response)
                {
                    if (response.ErrorCode != 0)
                    {
                        Log.Warning($"服务器返回错误: {response.ErrorCode}: {response.ErrorMessage}");
                        throw new AppException(response.ErrorMessage);
                    }

                    // 检查是否有更新可用
                    if (!string.IsNullOrEmpty(response.DownloadURL))
                    {
                        Log.Information("发现新版本，开始下载更新");
                        AutoUpdater.DownloadUpdate(response);
                        Environment.Exit(0);
                    }
                    else
                    {
                        Log.Information("当前版本已是最新版本");
                    }

                    // 成功获取配置，跳出重试循环
                    break;
                }
                else
                {
                    throw new InvalidOperationException("未收到有效的更新配置响应");
                }
            }
            catch (Exception ex) when (attempt < maxRetries)
            {
                // 等待一段时间后重试
                helper.OnConnectionFailed();
                await Task.Delay(1000);
            }
            catch (Exception ex)
            {
                Log.Error("获取更新配置失败: {ErrorMessage}", ex.Message);
                MessageBox.Show(ex.Message, StringHelper.Get("Application_ErrorMessage"), MessageBoxButton.OK, MessageBoxImage.Warning);
                Environment.Exit(500);
                return;
            }
        }

        // 调用回调函数启动MainWindow
        _updateCheckCompleteCallback?.Invoke();
    }

    /// <summary>
    /// 设置更新检查完成的回调函数
    /// </summary>
    /// <param name="callback">当确认没有更新时调用的回调函数</param>
    public static void SetUpdateCheckCompleteCallback(Action callback)
    {
        _updateCheckCompleteCallback = callback;
    }
    #endregion
}
