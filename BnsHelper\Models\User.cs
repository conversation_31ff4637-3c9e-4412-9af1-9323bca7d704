﻿using CommunityToolkit.Mvvm.ComponentModel;
using System.Diagnostics;
using System.Windows.Media.Imaging;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models.Api;
using Xylia.BnsHelper.Services;
using Xylia.BnsHelper.Services.Network.BinaryProtocol;
using Xylia.BnsHelper.Services.Network.Service;

namespace Xylia.BnsHelper.Models;
internal class User : ObservableObject, IDisposable
{
    #region Constructor
    public User(BnszsSession session, long uin)
    {
        Uin = uin;
        SetSession(session);
    }
    #endregion

    #region Properties
    public long Uin { get; init; }

    private byte _permission = 0;

    /// <summary>
    /// 用户当前权限级别
    /// </summary>
    public byte Permission
    {
        get
        {
            // 这是为了能正确判断是否已经过期，不存在逻辑问题
            if (PermissionExpiration < DateTime.Now) return _permission = 0;
            return _permission;
        }
        set
        {
            if (_permission != value)
            {
                _permission = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(HeadImg)); // 通知头像属性更新
                OnPropertyChanged(nameof(PermissionText)); // 通知权限文本更新
                OnPropertyChanged(nameof(ShowActivityNotice)); // 通知活动提示显示状态更新
            }

            // 时间每次都要刷新
            OnPropertyChanged(nameof(PermissionExpiration));
            OnPropertyChanged(nameof(ExpirationText));
        }
    }

    /// <summary>
    /// 用户权限过期时间
    /// </summary>
    public DateTime? PermissionExpiration { get; private set; }

    /// <summary>
    /// 权限级别文本
    /// </summary>
    public string PermissionText => Permission switch
    {
        0 => "普通用户",
        1 => "高级用户",
        2 => "超级用户",
        3 => "特级用户",
        _ => "未知权限"
    };

    /// <summary>
    /// 权限过期时间文本
    /// </summary>
    public string? ExpirationText
    {
        get
        {
            // 首先检查权限过期时间（服务端发送的时间戳）
            if (PermissionExpiration == null) return null;
            else if (PermissionExpiration == DateTime.MaxValue) return "永久权限";

            // 检查是否在活动期间（根据权限过期时间判断是否为活动权限）
            var activity = ZSActivityInfo.Instance;
            if (activity.IsActive && PermissionExpiration.HasValue)
            {
                // 如果权限过期时间接近活动结束时间（误差在1小时内），认为是活动权限
                if (Math.Abs((PermissionExpiration.Value - activity.EndTime).TotalHours) < 1)
                {
                    var remaining = activity.EndTime - DateTime.Now;
                    if (remaining.TotalDays >= 1) return $"免费体验剩余 {(int)remaining.TotalDays} 天";
                    else if (remaining.TotalHours >= 1) return $"免费体验剩余 {(int)remaining.TotalHours} 小时";
                    else if (remaining.TotalMinutes >= 1) return $"免费体验剩余 {(int)remaining.TotalMinutes} 分钟";
                    else return "免费体验即将结束";
                }
            }

            // 显示剩余时间
            var timeSpan = PermissionExpiration.Value - DateTime.Now;
            if (timeSpan.TotalSeconds < 0) return "已过期";
            else if (timeSpan.TotalDays >= 1) return $"剩余 {(int)timeSpan.TotalDays} 天";
            else if (timeSpan.TotalHours >= 1) return $"剩余 {(int)timeSpan.TotalHours} 小时";
            else return $"剩余 {(int)timeSpan.TotalMinutes} 分钟";
        }
    }

    /// <summary>
    /// 是否显示活动提示
    /// </summary>
    public bool ShowActivityNotice => ZSActivityInfo.Instance.IsActive && Permission <= 1;

    /// <summary>
    /// 用户头像
    /// </summary>
    public BitmapFrame? HeadImg
    {
        get
        {
            return BitmapFrame.Create(
                new Uri($"http://thirdqq.qlogo.cn/g?b=qq&nk={Uin}&s=100"),
                BitmapCreateOptions.IgnoreColorProfile,
                BitmapCacheOption.OnDemand);
        }
    }

    /// <summary>
    /// 更新权限信息
    /// </summary>
    /// <param name="permission">新的权限级别</param>
    /// <param name="permissionExpiration">新的权限过期时间</param>
    internal void UpdatePermissionInfo(byte? permission, long? permissionExpiration)
    {
        // 两个值都必须存在
        if (permission is null || permissionExpiration is null) return;

        // 先更新权限过期时间，避免在更新权限等级时触发过期检查
        if (permissionExpiration == -1)
        {
            // 永久权限
            PermissionExpiration = DateTime.MaxValue;
        }
        else if (permissionExpiration > 0)
        {
            PermissionExpiration = DateTimeOffset.FromUnixTimeSeconds(permissionExpiration.Value).DateTime.ToLocalTime();
        }
        else
        {
            PermissionExpiration = null;
        }

        // 然后更新权限级别（此时过期时间已经是最新的）
        Permission = permission.Value;
        Debug.WriteLine($"[INFO] 权限信息已更新: Permission={Permission}, PermissionExpiration={PermissionExpiration}");
    }
    #endregion

    #region Session Management
    private BnszsSession? _session;

    /// <summary>
    /// 是否已登录（有有效会话）
    /// </summary>
    public bool IsLoggedIn => _session != null;

    /// <summary>
    /// 会话状态变化事件
    /// </summary>
    public event EventHandler<bool>? SessionStateChanged;

    /// <summary>
    /// 心跳失败事件
    /// </summary>
    public event EventHandler? HeartbeatFailed;

    /// <summary>
    /// 设置用户会话
    /// </summary>
    /// <param name="session">会话实例</param>
    public void SetSession(BnszsSession session)
    {
        // 清理旧会话
        ClearSession();

        // 设置新会话
        _session = session;
        _session.HeartbeatFailed += OnHeartbeatFailed;
        _session.AnnouncementVersionChanged += OnAnnouncementVersionChanged;

        Debug.WriteLine($"[INFO] 用户会话已设置: {Uin}");

        // 触发会话状态变化事件
        SessionStateChanged?.Invoke(this, true);

        // 自动刷新签到状态（登录时不显示错误消息，避免干扰登录流程）
        _ = RefreshSignInStatusAsync();
    }

    /// <summary>
    /// 清理用户会话
    /// </summary>
    public void ClearSession()
    {
        if (_session != null)
        {
            try
            {
                Debug.WriteLine("[INFO] 开始清理用户会话");

                // 取消事件订阅
                _session.HeartbeatFailed -= OnHeartbeatFailed;
                _session.AnnouncementVersionChanged -= OnAnnouncementVersionChanged;

                // 释放会话资源
                _session.Dispose();
                _session = null;

                Debug.WriteLine("[INFO] 用户会话已清理");

                // 清空签到状态
                ClearSignInStatus();

                // 触发会话状态变化事件
                SessionStateChanged?.Invoke(this, false);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 清理用户会话时发生异常: {ex.Message}");
                // 即使发生异常，也要确保会话被设置为null
                _session = null;
            }
        }
    }

    /// <summary>
    /// 异步注销用户会话
    /// </summary>
    public async Task LogoutAsync()
    {
        // 管理会话
        if (_session is null) return;
        _session.HeartbeatFailed -= OnHeartbeatFailed;
        _session.AnnouncementVersionChanged -= OnAnnouncementVersionChanged;

        try
        {
            // 等待注销响应，设置更短的超时时间，避免程序关闭时卡顿
            _session.SendPacket(new LogoutPacket(), MessageTypes.Logout);
            await _session.WaitForResponseWithRetry(MessageTypes.LogoutResponse, 1, 2 * 1000);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 注销请求失败: {ex.Message}");
        }

        _session.Dispose();
        _session = null;

        Debug.WriteLine("[INFO] 用户会话已注销并清理");
        SessionStateChanged?.Invoke(this, false);
    }

    /// <summary>
    /// 心跳失败处理
    /// </summary>
    private void OnHeartbeatFailed(object? sender, EventArgs e)
    {
        Debug.WriteLine("[WARNING] 心跳失败，触发心跳失败事件");

        // 清理会话
        ClearSession();

        // 触发心跳失败事件，让UI层处理
        HeartbeatFailed?.Invoke(this, EventArgs.Empty);
    }

    /// <summary>
    /// 公告版本变化处理
    /// </summary>
    private async void OnAnnouncementVersionChanged(object? sender, uint newVersion)
    {
        try
        {
            await AnnouncementService.Instance.PerformIncrementalUpdateAsync(newVersion);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 处理公告版本变化时发生异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        try
        {
            Debug.WriteLine("[INFO] 开始释放User资源");

            // 清理会话
            ClearSession();

            // 清理事件订阅
            SessionStateChanged = null;
            HeartbeatFailed = null;

            Debug.WriteLine("[INFO] User资源释放完成");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] 释放User资源时发生异常: {ex.Message}");
        }
    }
    #endregion

    #region SignIn
    private bool _isSignedToday;
    private uint _totalSignCount;
    private bool _canSign;
    private string? _lastSignInError;
    private DateTime? _lastSignInCheckDate; // 上次检查签到状态的日期

    /// <summary>
    /// 今日是否已签到
    /// </summary>
    public bool IsSignedToday
    {
        get => _isSignedToday;
        private set
        {
            if (_isSignedToday != value)
            {
                _isSignedToday = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(SignInStatusText)); // 通知签到状态文本更新
            }
        }
    }

    /// <summary>
    /// 连签天数
    /// </summary>
    public uint TotalSignCount
    {
        get => _totalSignCount;
        private set
        {
            if (_totalSignCount != value)
            {
                _totalSignCount = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// 是否可以签到
    /// </summary>
    public bool CanSign
    {
        get => _canSign;
        private set
        {
            if (_canSign != value)
            {
                _canSign = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(SignInStatusText)); // 通知签到状态文本更新
            }
        }
    }

    /// <summary>
    /// 签到状态文本
    /// </summary>
    public string SignInStatusText
    {
        get
        {
            if (IsSignedToday) return "今日已签到";
            else if (CanSign) return "立即签到";
            else return _lastSignInError ?? "获取签到信息失败，请重新登录后再试";
        }
    }

    /// <summary>
    /// 检查天数是否更新，如果更新则自动刷新签到状态
    /// </summary>
    public void CheckAndRefreshSignInStatusIfNeeded()
    {
        // 如果没有会话，无法刷新
        if (_session == null) return;

        // 如果从未检查过，或者日期已经变化，则需要刷新签到状态
        var today = DateTime.Today;
        if (_lastSignInCheckDate == null || _lastSignInCheckDate.Value.Date != today)
        {
            Debug.WriteLine($"[INFO] 检测到天数更新，自动刷新签到状态。上次检查日期: {_lastSignInCheckDate?.ToString("yyyy-MM-dd") ?? "从未检查"}, 当前日期: {today:yyyy-MM-dd}");

            // 立即更新检查日期，防止重复触发
            _lastSignInCheckDate = today;

            // 异步刷新签到状态，不等待结果以避免阻塞UI
            _ = Task.Run(async () =>
            {
                try
                {
                    await RefreshSignInStatusAsync();
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"[ERROR] 自动刷新签到状态失败: {ex.Message}");
                }
            });
        }
    }

    /// <summary>
    /// 刷新签到状态
    /// </summary>
    public async Task<(bool success, string? errorMessage)> RefreshSignInStatusAsync()
    {
        ArgumentNullException.ThrowIfNull(_session);

        try
        {
            _session.SendPacket(new LuckyStatusPacket(), MessageTypes.LuckyStatus);

            var response = await _session.WaitForResponseWithRetry(MessageTypes.LuckyStatusResponse, 3, 5 * 1000);
            if (response is LuckyStatusPacket packet)
            {
                switch (packet.ErrorCode)
                {
                    case 0:
                        UpdateSignInStatus(packet);
                        _lastSignInError = null; // 清空错误消息
                        return (true, null);

                    // 已经签到
                    case 1:
                        CanSign = false;
                        IsSignedToday = true;
                        _lastSignInError = null; // 已签到状态不需要显示错误消息
                        return (false, packet.ErrorMessage);

                    // 其他错误
                    default:
                        ClearSignInStatus();
                        _lastSignInError = packet.ErrorMessage;
                        return (false, packet.ErrorMessage);
                }
            }

            Debug.WriteLine("获取签到状态失败: 响应为空");
            return (false, "服务器无响应");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"刷新签到状态异常: {ex.Message}");
            return (false, $"网络异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 执行签到
    /// </summary>
    public async Task<(bool Success, string? Message)> SignInAsync()
    {
        ArgumentNullException.ThrowIfNull(_session);
        if (!CanSign) return (false, "今日已签到或不满足签到条件");

        try
        {
            _session.SendPacket(new LuckyDrawPacket(), MessageTypes.LuckyDraw);

            var response = await _session.WaitForResponseWithRetry(MessageTypes.LuckyDrawResponse, 3, 5 * 1000);
            if (response is LuckyDrawPacket packet)
            {
                switch (packet.ErrorCode)
                {
                    // 签到成功，检查是否包含权限更新信息
                    case 0:
                        UpdatePermissionInfo(packet.Permission, packet.PermissionExpiration);
                        _ = await RefreshSignInStatusAsync();
                        return (true, packet.Message);

                    // 已经签到
                    case 1:
                        CanSign = false;
                        IsSignedToday = true;
                        break;

                    default: return (false, packet.ErrorMessage ?? "签到失败");
                }
            }

            return (false, "签到失败: 服务器无响应");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"签到异常: {ex.Message}");
            return (false, $"签到异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 更新签到状态
    /// </summary>
    private void UpdateSignInStatus(LuckyStatusPacket response)
    {
        TotalSignCount = response.Point;  // 使用连签天数
        CanSign = response.AvailableCount > 0; // 根据可用次数判断是否可以签到
        IsSignedToday = response.AvailableCount == 0; // 如果可用次数为0，说明今日已签到
    }

    /// <summary>
    /// 清空签到状态
    /// </summary>
    private void ClearSignInStatus()
    {
        IsSignedToday = false;
        TotalSignCount = 0;
        CanSign = false;
        _lastSignInCheckDate = null; // 清空检查日期
    }

    /// <summary>
    /// 强制刷新显示文本属性
    /// </summary>
    public void RefreshDisplayTexts()
    {
        OnPropertyChanged(nameof(SignInStatusText));
        OnPropertyChanged(nameof(ExpirationText));
        OnPropertyChanged(nameof(Permission));
        OnPropertyChanged(nameof(PermissionText));
    }
    #endregion

    #region Cdkey
    /// <summary>
    /// CDKey请求时间缓存，用于减少服务端通信压力
    /// </summary>
    private static DateTime lastRequestTime = new();

    /// <summary>
    /// 激活CDKEY
    /// </summary>
    /// <param name="cdkey">要激活的CDKEY</param>
    /// <returns>激活结果</returns>
    public async Task<(bool success, string? message)> ActivateCDKeyAsync(string cdkey)
    {
        try
        {
            // 本地类型口令码处理
            if (cdkey == "我是秒伤狗！出警！")
            {
                RegistryHelper.Default.UseDisplayMode = !RegistryHelper.Default.UseDisplayMode;
                return (true, "口令码激活成功");
            }

            // 正常口令码
            var response = await ActivateCDKeyRequestAsync(cdkey);
            if (response == null) return (false, "口令码激活失败: 服务器无响应");
            if (response.ErrorCode != 0) return (false, response.ErrorMessage);

            // 激活成功，检查是否包含权限更新信息
            UpdatePermissionInfo(response.Permission, response.PermissionExpiration);
            return (true, "口令码激活成功");
        }
        catch (Exception ex)
        {
            return (false, $"口令码激活失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 发送CDKEY激活请求
    /// </summary>
    /// <param name="cdkey">要激活的CDKEY</param>
    /// <returns>激活响应</returns>
    private async Task<CDKeyActivatePacket?> ActivateCDKeyRequestAsync(string cdkey)
    {
        ArgumentNullException.ThrowIfNull(_session);
        ArgumentException.ThrowIfNullOrWhiteSpace(cdkey);

        // 检查本地CDKey请求时间缓存
        var timeSinceLastRequest = DateTime.Now - lastRequestTime;
        if (timeSinceLastRequest.TotalSeconds < 30)
        {
            var remainingSeconds = 30 - (int)timeSinceLastRequest.TotalSeconds;
            Debug.WriteLine($"[WARN] CDKey请求过于频繁，请等待 {remainingSeconds} 秒后再试");

            // 返回一个错误响应，模拟服务端的频率限制响应
            return new CDKeyActivatePacket
            {
                ErrorCode = 429, // Too Many Requests
                ErrorMessage = $"请求过于频繁，请等待 {remainingSeconds} 秒后再试"
            };
        }

        lastRequestTime = DateTime.Now;

        try
        {
            Debug.WriteLine($"[INFO] 发送CDKEY激活请求: {cdkey}");
            _session.SendPacket(new CDKeyActivatePacket { CDKey = cdkey }, MessageTypes.CDKeyActivate);

            // 等待CDKEY激活响应
            var response = await _session.WaitForResponseWithRetry(MessageTypes.CDKeyActivateResponse, 3, 5 * 1000);
            if (response is CDKeyActivatePacket cdkeyResponse) return cdkeyResponse;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"[ERROR] CDKEY激活请求异常: {ex.Message}");
        }

        return null;
    }
    #endregion

    #region Activity
    /// <summary>
    /// 获取活动总版本
    /// </summary>
    public async Task<uint?> GetActivityTotalVersionAsync()
    {
        ArgumentNullException.ThrowIfNull(_session);

        try
        {
            _session.SendPacket(new ActivityVersionPacket(), MessageTypes.ActivityVersion);

            var response = await _session.WaitForResponseWithRetry(MessageTypes.ActivityVersionResponse, 3, 5 * 1000);
            if (response is ActivityVersionPacket packet && packet.ErrorCode == 0) return packet.TotalVersion;
        }
        catch(Exception ex)
        {
            Debug.WriteLine($"获取活动总版本失败:" + ex.Message);
        }

        return null;
    }

    /// <summary>
    /// 获取各个活动的版本
    /// </summary>
    public async Task<KeyValuePair<ulong, ushort>[]> GetActivityListItemsAsync()
    {
        ArgumentNullException.ThrowIfNull(_session);

        try
        {
            _session.SendPacket(new ActivityListPacket(), MessageTypes.ActivityList);

            var response = await _session.WaitForResponseWithRetry(MessageTypes.ActivityListResponse, 3, 5 * 1000);
            if (response is ActivityListPacket listResponse && listResponse.ErrorCode == 0) return listResponse.Items;
        }
        catch(Exception ex)
        {
            Debug.WriteLine($"获取活动列表失败: " + ex.Message);
        }


        return null;
    }

    /// <summary>
    /// 获取活动详情
    /// </summary>
    public async Task<ActivityInfo?> GetActivityDetailAsync(ulong activityId)
    {
        ArgumentNullException.ThrowIfNull(_session);

        try
        {
            _session.SendPacket(new ActivityPacket { ActivityId = activityId }, MessageTypes.ActivityDetail);

            var response = await _session.WaitForResponseWithRetry(MessageTypes.ActivityDetailResponse, 3, 5 * 1000);
            if (response is ActivityPacket detailResponse && detailResponse.ErrorCode == 0) return detailResponse.Activity;
        }
        catch(Exception ex)
        {
            Debug.WriteLine($"获取活动详情失败: " + ex.Message);
        }

        return null;
    }
    #endregion
}
