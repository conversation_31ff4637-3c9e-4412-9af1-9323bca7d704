﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows;
using Xylia.Updater.Resources;

namespace Xylia.Updater;
public partial class App : Application
{
  private const int MaxRetries = 5; // 增加重试次数
  private const int RetryDelayMs = 1000; // 重试延迟时间
  private BackgroundWorker _backgroundWorker;

  protected override void OnStartup(StartupEventArgs e)
  {
    var args = e.Args;
    if (args.Length == 0) Environment.Exit(0);

    string zipPath = null;
    string extractionPath = null;
    string currentExe = null;
    string updatedExe = null;
    bool clearAppDirectory = false;
    bool clearApp = false;
    string commandLineArgs = null;

    for (var index = 0; index < args.Length; index++)
    {
      string arg = args[index].ToLower();
      switch (arg)
      {
        case "--input":
          zipPath = args[index + 1];
          break;
        case "--output":
          extractionPath = args[index + 1];
          break;
        case "--current-exe":
          currentExe = args[index + 1];
          break;
        case "--updated-exe":
          updatedExe = args[index + 1];
          break;
        case "--clear":
          clearApp = true;
          break;
        case "--args":
          commandLineArgs = args[index + 1];
          break;
      }
    }

    if (string.IsNullOrEmpty(zipPath) || string.IsNullOrEmpty(extractionPath) || string.IsNullOrEmpty(currentExe))
    {
      return;
    }

    // Extract all the files.
    _backgroundWorker = new BackgroundWorker
    {
      WorkerReportsProgress = true,
      WorkerSupportsCancellation = true
    };

    _backgroundWorker.DoWork += (_, eventArgs) =>
    {
      // Ensures that the last character on the extraction path
      // is the directory separator char.
      // Without this, a malicious zip file could try to traverse outside of the expected
      // extraction path.
      if (!extractionPath.EndsWith(Path.DirectorySeparatorChar.ToString(), StringComparison.Ordinal))
      {
        extractionPath += Path.DirectorySeparatorChar;
      }

      var archive = ZipFile.OpenRead(zipPath);
      var entries = archive.Entries;

      try
      {
        var progress = 0;

        if (clearAppDirectory)
        {
          var directoryInfo = new DirectoryInfo(extractionPath);

          foreach (FileInfo file in directoryInfo.GetFiles())
          {
            _backgroundWorker.ReportProgress(0, string.Format(Resource.Removing, file.FullName));
            file.Delete();
          }

          foreach (DirectoryInfo directory in directoryInfo.GetDirectories())
          {
            _backgroundWorker.ReportProgress(0, string.Format(Resource.Removing, directory.FullName));
            directory.Delete(true);
          }
        }

        if (clearApp)
        {
          if (!TryDeleteFileWithRetry(currentExe, 3, 300))
          {
            // 如果删除失败，标记为延迟删除
            _backgroundWorker.ReportProgress(0, $"警告：无法立即删除旧程序文件，将在更新完成后重试");

            // 尝试重命名文件，以便新文件可以正常安装
            try
            {
              string backupPath = currentExe + ".old";
              if (File.Exists(backupPath))
              {
                File.Delete(backupPath);
              }
              File.Move(currentExe, backupPath);
              _backgroundWorker.ReportProgress(0, $"已将旧程序重命名为 {Path.GetFileName(backupPath)}");
            }
            catch (Exception)
            {
              _backgroundWorker.ReportProgress(0, "警告：无法重命名旧程序文件，更新可能失败");
            }
          }
          else
          {
            _backgroundWorker.ReportProgress(0, string.Format(Resource.Removing, currentExe));
          }
        }


        for (var index = 0; index < entries.Count; index++)
        {
          if (_backgroundWorker.CancellationPending)
          {
            eventArgs.Cancel = true;
            break;
          }

          ZipArchiveEntry entry = entries[index];

          string currentFile = string.Format(Resource.CurrentFileExtracting, entry.FullName);
          _backgroundWorker.ReportProgress(progress, currentFile);
          var retries = 0;
          var notCopied = true;
          while (notCopied)
          {
            var filePath = string.Empty;
            try
            {
              filePath = Path.Combine(extractionPath, entry.FullName);
              if (!entry.IsDirectory())
              {
                string parentDirectory = Path.GetDirectoryName(filePath);
                if (parentDirectory != null)
                {
                  if (!Directory.Exists(parentDirectory))
                  {
                    Directory.CreateDirectory(parentDirectory);
                  }
                }
                else
                {
                  throw new ArgumentNullException($"parentDirectory is null for \"{filePath}\"!");
                }

                using (Stream destination = File.Open(filePath, FileMode.OpenOrCreate, FileAccess.Write, FileShare.None))
                {
                  using Stream stream = entry.Open();
                  stream.CopyTo(destination);
                  destination.SetLength(destination.Position);
                }

                File.SetLastWriteTime(filePath, entry.LastWriteTime.DateTime);
              }

              notCopied = false;
            }
            catch (IOException exception)
            {
              const int errorSharingViolation = 0x20;
              const int errorLockViolation = 0x21;
              int errorCode = Marshal.GetHRForException(exception) & 0x0000FFFF;
              if (errorCode is not (errorSharingViolation or errorLockViolation)) throw;

              retries++;
              if (retries > MaxRetries) throw;

              List<Process> lockingProcesses = null;
              if (Environment.OSVersion.Version.Major >= 6 && retries >= 2)
              {
                try
                {
                  lockingProcesses = FileUtil.WhoIsLocking(filePath);
                }
                catch (Exception)
                {
                  // ignored
                }
              }

              if (lockingProcesses == null)
              {
                Thread.Sleep(RetryDelayMs);
                continue;
              }

              foreach (Process lockingProcess in lockingProcesses)
              {
                var result = MessageBoxResult.No;

                if (entry.FullName == Path.GetFileName(currentExe)) result = MessageBoxResult.OK;
                else Dispatcher.Invoke(new(() => result = MessageBox.Show(
                  string.Format(Resource.FileStillInUseMessage, lockingProcess.ProcessName, filePath),
                  string.Format(Resource.FileStillInUseCaption),
                  MessageBoxButton.OKCancel, MessageBoxImage.Information)));

                if (result == MessageBoxResult.OK)
                {
                  lockingProcess.Kill();
                  Thread.Sleep(RetryDelayMs);
                }
                else throw;
              }
            }
            catch (UnauthorizedAccessException exception)
            {
              retries++;
              if (retries > MaxRetries)
              {
                // 即使以管理员权限运行仍然权限不足，记录详细错误信息
                Dispatcher.Invoke(new(() => MessageBox.Show(
                  $"更新文件时遇到权限问题：{exception.Message}\n文件路径：{filePath}\n\n请检查文件是否被其他程序占用或具有特殊权限设置。",
                  "权限不足", MessageBoxButton.OK, MessageBoxImage.Error)));
                throw;
              }

              Thread.Sleep(RetryDelayMs);
              continue;
            }
          }

          progress = (index + 1) * 100 / entries.Count;
          _backgroundWorker.ReportProgress(progress, currentFile);
        }
      }
      finally
      {
        archive.Dispose();
        File.Delete(zipPath);
      }
    };

    _backgroundWorker.ProgressChanged += (_, eventArgs) =>
    {
      Debug.WriteLine(eventArgs.UserState?.ToString());
    };

    _backgroundWorker.RunWorkerCompleted += (_, eventArgs) =>
    {
      try
      {
        if (eventArgs.Error != null) throw eventArgs.Error;
        if (eventArgs.Cancelled) return;

        try
        {
          string executablePath = string.IsNullOrWhiteSpace(updatedExe)
            ? currentExe
            : Path.Combine(extractionPath, updatedExe);

          // 确保可执行文件存在
          if (!File.Exists(executablePath))
          {
            throw new FileNotFoundException($"更新后的可执行文件不存在：{executablePath}");
          }

          var processStartInfo = new ProcessStartInfo(executablePath)
          {
            UseShellExecute = true, // 使用Shell执行，有助于权限处理
            WorkingDirectory = Path.GetDirectoryName(executablePath)
          };

          if (!string.IsNullOrEmpty(commandLineArgs))
          {
            processStartInfo.Arguments = commandLineArgs;
          }

          Process.Start(processStartInfo);

          // 启动成功后，尝试清理旧文件
          CleanupOldFiles(extractionPath);
        }
        catch (Win32Exception exception)
        {
          if (exception.NativeErrorCode == 1223)
          {
            // 用户取消了UAC提示，这是正常情况
            return;
          }
          else
          {
            // 记录详细的错误信息
            MessageBox.Show($"启动更新后的程序失败：{exception.Message}\n错误代码：{exception.NativeErrorCode}", "启动失败", MessageBoxButton.OK, MessageBoxImage.Error);
            throw;
          }
        }
        catch (Exception exception)
        {
          MessageBox.Show($"启动更新后的程序时发生未知错误：{exception.Message}", "启动失败", MessageBoxButton.OK, MessageBoxImage.Error);
          throw;
        }
      }
      catch (Exception exception)
      {
        MessageBox.Show(exception.Message, exception.GetType().ToString(), MessageBoxButton.OK, MessageBoxImage.Error);
      }
      finally
      {
        Environment.Exit(0);
      }
    };

    _backgroundWorker.RunWorkerAsync();
  }

  protected override void OnExit(ExitEventArgs e)
  {
    _backgroundWorker?.CancelAsync();
  }

  /// <summary>
  /// 尝试删除文件，带重试机制
  /// </summary>
  /// <param name="filePath">要删除的文件路径</param>
  /// <param name="maxRetries">最大重试次数</param>
  /// <param name="retryDelayMs">重试间隔（毫秒）</param>
  /// <returns>是否成功删除</returns>
  private static bool TryDeleteFileWithRetry(string filePath, int maxRetries = 5, int retryDelayMs = 500)
  {
    if (!File.Exists(filePath))
    {
      return true; // 文件不存在，视为删除成功
    }

    for (int attempt = 0; attempt <= maxRetries; attempt++)
    {
      try
      {
        // 第一次尝试前，先处理锁定进程
        if (attempt == 0)
        {
          var lockingProcesses = FileUtil.WhoIsLocking(filePath);
          foreach (Process lockingProcess in lockingProcesses)
          {
            try
            {
              lockingProcess.Kill();
              // 等待进程真正退出
              if (!lockingProcess.WaitForExit(2000)) // 等待2秒
              {
                lockingProcess.Kill(); // 强制终止
              }
            }
            catch (Exception)
            {
              // 忽略进程终止错误
            }
          }
        }

        // 尝试删除文件
        File.Delete(filePath);

        // 验证文件是否真的被删除
        if (!File.Exists(filePath))
        {
          return true;
        }
      }
      catch (UnauthorizedAccessException)
      {
        // 权限不足，尝试修改文件属性
        try
        {
          File.SetAttributes(filePath, FileAttributes.Normal);
        }
        catch (Exception)
        {
          // 忽略属性修改错误
        }
      }
      catch (IOException)
      {
        // 文件被占用，继续重试
      }
      catch (Exception)
      {
        // 其他错误，继续重试
      }

      // 如果不是最后一次尝试，等待后重试
      if (attempt < maxRetries)
      {
        Thread.Sleep(retryDelayMs);
      }
    }

    return false; // 所有尝试都失败
  }

  /// <summary>
  /// 清理旧文件
  /// </summary>
  /// <param name="directory">目录路径</param>
  private static void CleanupOldFiles(string directory)
  {
    try
    {
      // 查找所有 .old 文件并尝试删除
      var oldFiles = Directory.GetFiles(directory, "*.old", SearchOption.TopDirectoryOnly);
      foreach (var oldFile in oldFiles)
      {
        try
        {
          File.Delete(oldFile);
        }
        catch (Exception)
        {
          // 忽略删除失败，这些文件可能在下次更新时被清理
        }
      }
    }
    catch (Exception)
    {
      // 忽略清理错误
    }
  }
}
