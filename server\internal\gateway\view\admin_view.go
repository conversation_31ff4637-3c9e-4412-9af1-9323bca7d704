package view

import (
	"html/template"
	"net/http"
	"os"
	"path/filepath"
	"runtime"
	"strings"

	"udp-server/server/internal/gateway"
	"udp-server/server/pkg/logger"
)

var adminTemplateContent string = ""
var loginTemplateContent string = ""

// AdminView 管理后台视图处理器
type AdminView struct {
	templates map[string]*template.Template
	basePath  string
}

// getBasePath 获取项目基础路径
func getBasePath() string {
	// 获取当前文件的路径
	_, filename, _, ok := runtime.Caller(0)
	if !ok {
		logger.Warn("无法获取当前文件路径，使用当前工作目录")
		if wd, err := os.Getwd(); err == nil {
			return wd
		}
		return "."
	}

	// 从当前文件路径推导项目根目录
	// 当前文件: server/internal/gateway/view/admin_view.go
	// 需要回到: server/
	dir := filepath.Dir(filename) // .../server/internal/gateway/view
	dir = filepath.Dir(dir)       // .../server/internal/gateway
	dir = filepath.Dir(dir)       // .../server/internal
	dir = filepath.Dir(dir)       // .../server

	return dir
}

// loadEmbeddedTemplates 从嵌入的文件系统加载模板
func (v *AdminView) loadEmbeddedTemplates() bool {
	// 尝试加载基础模板
	baseData, err := gateway.TemplateFS.ReadFile("templates/base.html")
	if err != nil {
		logger.Debug("嵌入的基础模板不存在: %v", err)
		return false
	}

	// 尝试加载管理后台布局模板
	layoutData, err := gateway.TemplateFS.ReadFile("templates/admin_layout.html")
	if err != nil {
		logger.Debug("嵌入的管理后台布局模板不存在: %v", err)
		return false
	}

	// 尝试加载管理后台模板
	adminData, err := gateway.TemplateFS.ReadFile("templates/admin.html")
	if err != nil {
		logger.Debug("嵌入的管理后台模板不存在: %v", err)
		return false
	}

	// 解析管理后台模板（支持模板继承）
	adminTemplate, err := template.New("base.html").Parse(string(baseData))
	if err != nil {
		logger.Error("解析嵌入的基础模板失败: %v", err)
		return false
	}

	adminTemplate, err = adminTemplate.Parse(string(layoutData))
	if err != nil {
		logger.Error("解析嵌入的管理后台布局模板失败: %v", err)
		return false
	}

	adminTemplate, err = adminTemplate.Parse(string(adminData))
	if err != nil {
		logger.Error("解析嵌入的管理后台模板失败: %v", err)
		return false
	}

	v.templates["admin"] = adminTemplate
	logger.Info("嵌入的管理后台模板加载成功")

	// 尝试加载登录页面模板
	loginData, err := gateway.TemplateFS.ReadFile("templates/login.html")
	if err != nil {
		logger.Debug("嵌入的登录页面模板不存在: %v", err)
		return false
	}

	// 解析登录页面模板（支持模板继承）
	loginTemplate, err := template.New("base.html").Parse(string(baseData))
	if err != nil {
		logger.Error("解析嵌入的基础模板失败: %v", err)
		return false
	}

	loginTemplate, err = loginTemplate.Parse(string(loginData))
	if err != nil {
		logger.Error("解析嵌入的登录模板失败: %v", err)
		return false
	}

	v.templates["login"] = loginTemplate
	logger.Info("嵌入的登录页面模板加载成功")

	return true
}

// checkAuth 检查管理员认证
func (v *AdminView) checkAuth(w http.ResponseWriter, r *http.Request) bool {
	// 从请求头或Cookie中获取token
	token := r.Header.Get("Authorization")
	if token == "" {
		// 尝试从Cookie获取
		if cookie, err := r.Cookie("admin_token"); err == nil {
			token = cookie.Value
		}
	}

	if token == "" {
		// 重定向到登录页面
		http.Redirect(w, r, "/admin/login", http.StatusFound)
		return false
	}

	// 验证token（这里简化处理，实际应该验证token的有效性）
	// 暂时允许所有请求通过，因为我们还没有实现完整的session管理
	return true
}

// NewAdminView 创建管理后台视图处理器实例
func NewAdminView() *AdminView {
	basePath := getBasePath()
	view := &AdminView{
		templates: make(map[string]*template.Template),
		basePath:  basePath,
	}

	logger.Info("管理后台视图初始化，基础路径: %s", basePath)

	// 加载模板文件
	view.loadTemplates()

	return view
}

// loadTemplates 加载HTML模板文件
func (v *AdminView) loadTemplates() {
	// 首先尝试从嵌入的文件系统加载模板
	if v.loadEmbeddedTemplates() {
		logger.Info("使用嵌入的模板文件")
		return
	}

	// 如果嵌入模板不可用，尝试从文件系统加载
	logger.Info("嵌入模板不可用，尝试从文件系统加载")

	templatePaths := []string{
		filepath.Join(v.basePath, "internal/gateway/templates"), // 使用绝对路径
		"internal/gateway/templates",                            // 相对于项目根目录
		"server/internal/gateway/templates",                     // 相对于上级目录
		"../internal/gateway/templates",                         // 相对于bin目录
		"./internal/gateway/templates",                          // 当前目录
	}

	var templateDir string
	var adminTemplatePath, loginTemplatePath string

	// 尝试找到正确的模板目录
	for _, path := range templatePaths {
		adminPath := filepath.Join(path, "admin.html")
		loginPath := filepath.Join(path, "login.html")

		// 检查文件是否存在
		if _, err := os.Stat(adminPath); err == nil {
			if _, err := os.Stat(loginPath); err == nil {
				templateDir = path
				adminTemplatePath = adminPath
				loginTemplatePath = loginPath
				logger.Info("找到模板文件: %s", path)
				break
			}
		}
	}

	if templateDir == "" {
		logger.Error("未找到模板文件目录，使用备用模板")
		v.templates["admin"] = template.Must(template.New("admin").Parse(fallbackAdminTemplate))
		v.templates["login"] = template.Must(template.New("login").Parse(fallbackLoginTemplate))
		return
	}

	logger.Info("使用模板目录: %s", templateDir)

	// 加载管理后台主页模板（支持模板继承）
	basePath := filepath.Join(templateDir, "base.html")
	layoutPath := filepath.Join(templateDir, "admin_layout.html")

	adminTemplate, err := template.ParseFiles(basePath, layoutPath, adminTemplatePath)
	if err != nil {
		logger.Error("加载管理后台模板失败: %v", err)
		v.templates["admin"] = template.Must(template.New("admin").Parse(fallbackAdminTemplate))
	} else {
		v.templates["admin"] = adminTemplate
		logger.Info("管理后台模板加载成功")
	}

	// 加载登录页面模板（支持模板继承）
	loginTemplate, err := template.ParseFiles(basePath, loginTemplatePath)
	if err != nil {
		logger.Error("加载登录模板失败: %v", err)
		v.templates["login"] = template.Must(template.New("login").Parse(fallbackLoginTemplate))
	} else {
		v.templates["login"] = loginTemplate
		logger.Info("登录页面模板加载成功")
	}
}

// HandleAdminPage 处理管理后台主页
func (v *AdminView) HandleAdminPage(w http.ResponseWriter, r *http.Request) {
	// 检查用户认证
	if !v.checkAuth(w, r) {
		return
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")

	data := map[string]interface{}{
		"Title":   "剑灵小助手",
		"Version": "1.0.0",
	}

	if err := v.templates["admin"].ExecuteTemplate(w, "base.html", data); err != nil {
		logger.Error("渲染管理后台页面失败: %v", err)
		http.Error(w, "内部服务器错误", http.StatusInternalServerError)
		return
	}
}

// HandleLoginPage 处理登录页面
func (v *AdminView) HandleLoginPage(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/html; charset=utf-8")

	data := map[string]interface{}{
		"Title": "剑灵小助手",
	}

	if err := v.templates["login"].ExecuteTemplate(w, "base.html", data); err != nil {
		logger.Error("渲染登录页面失败: %v", err)
		http.Error(w, "内部服务器错误", http.StatusInternalServerError)
		return
	}
}

// HandleActivityPage 处理活动管理页面
func (v *AdminView) HandleActivityPage(w http.ResponseWriter, r *http.Request) {
	// 检查用户认证
	if !v.checkAuth(w, r) {
		return
	}

	// 读取活动管理页面模板
	templatePath := filepath.Join(v.basePath, "web", "templates", "activity_list.html")

	// 尝试读取模板文件
	if data, err := os.ReadFile(templatePath); err == nil {
		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		w.Write(data)
		return
	}

	// 如果文件不存在，返回简单的HTML页面
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(`渲染管理后台页面失败`))
}

// HandleStaticFiles 处理静态文件请求
func (v *AdminView) HandleStaticFiles(w http.ResponseWriter, r *http.Request) {
	path := r.URL.Path

	// 安全检查，防止路径遍历攻击
	if strings.Contains(path, "..") {
		http.Error(w, "Forbidden", http.StatusForbidden)
		return
	}

	// 移除 /static/ 前缀
	staticPath := strings.TrimPrefix(path, "/static/")

	// 首先尝试从嵌入的文件系统中读取
	embedPath := "static/" + staticPath
	if data, err := gateway.StaticFS.ReadFile(embedPath); err == nil {
		logger.Debug("使用嵌入的静态文件: %s", embedPath)

		// 根据文件扩展名设置Content-Type
		ext := filepath.Ext(path)
		switch ext {
		case ".css":
			w.Header().Set("Content-Type", "text/css")
		case ".js":
			w.Header().Set("Content-Type", "application/javascript")
		case ".png":
			w.Header().Set("Content-Type", "image/png")
		case ".jpg", ".jpeg":
			w.Header().Set("Content-Type", "image/jpeg")
		case ".gif":
			w.Header().Set("Content-Type", "image/gif")
		case ".ico":
			w.Header().Set("Content-Type", "image/x-icon")
		default:
			w.Header().Set("Content-Type", "text/plain")
		}

		w.Write(data)
		return
	}

	// 如果嵌入文件不存在，尝试从文件系统读取
	logger.Debug("嵌入文件不存在，尝试从文件系统读取: %s", staticPath)

	// 尝试多个可能的静态文件路径
	possiblePaths := []string{
		filepath.Join(v.basePath, "internal/gateway/static", staticPath), // 使用绝对路径
		filepath.Join("internal/gateway/static", staticPath),             // 相对路径
		filepath.Join("server/internal/gateway/static", staticPath),      // 上级目录
		filepath.Join("../internal/gateway/static", staticPath),          // bin目录
	}

	var filePath string
	var fileExists bool

	// 找到第一个存在的文件路径
	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			filePath = path
			fileExists = true
			logger.Debug("找到静态文件: %s", path)
			break
		}
	}

	if !fileExists {
		logger.Warn("静态文件不存在: %s", staticPath)
		http.Error(w, "文件不存在", http.StatusNotFound)
		return
	}

	// 根据文件扩展名设置Content-Type
	ext := filepath.Ext(path)
	switch ext {
	case ".css":
		w.Header().Set("Content-Type", "text/css")
	case ".js":
		w.Header().Set("Content-Type", "application/javascript")
	case ".png":
		w.Header().Set("Content-Type", "image/png")
	case ".jpg", ".jpeg":
		w.Header().Set("Content-Type", "image/jpeg")
	case ".gif":
		w.Header().Set("Content-Type", "image/gif")
	case ".ico":
		w.Header().Set("Content-Type", "image/x-icon")
	default:
		w.Header().Set("Content-Type", "text/plain")
	}

	// 提供静态文件服务
	http.ServeFile(w, r, filePath)
}

// 备用模板（当外部文件加载失败时使用）
const fallbackAdminTemplate = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .error { color: red; text-align: center; }
    </style>
</head>
<body>
    <div class="error">
        <h1>{{.Title}}</h1>
        <p>模板文件加载失败，请检查模板文件是否存在。</p>
    </div>
</body>
</html>`

const fallbackLoginTemplate = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; text-align: center; }
        .error { color: red; }
    </style>
</head>
<body>
    <div class="error">
        <h1>{{.Title}}</h1>
        <p>模板文件加载失败，请检查模板文件是否存在。</p>
    </div>
</body>
</html>`
