using Xylia.Preview.Data.Engine;

namespace Xylia.BnsHelper.Services.Network.Service;

/// <summary>
/// 签到状态包，包含状态查询请求和响应的处理逻辑
/// </summary>
internal class LuckyStatusPacket : BasePacket
{
    #region Response Fields
    /// <summary>
    /// 连签天数
    /// </summary>
    public uint Point { get; set; }

    /// <summary>
    /// 今日可用次数（今日次数+额外次数）
    /// </summary>
    public uint AvailableCount { get; set; }
    #endregion

    #region Methods
    protected override void ReadResponse(DataArchive reader)
    {
        Point = reader.Read<uint>();
        AvailableCount = reader.Read<uint>();
    }
    #endregion
}
