using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Windows;
using Xylia.BnsHelper.Models.Triggers;
using TriggerAction = Xylia.BnsHelper.Models.Triggers.TriggerAction;

namespace Xylia.BnsHelper.ViewModels.Dialogs;
public partial class TriggerActionSelectorViewModel : ObservableObject
{
    #region Fields
    [ObservableProperty] ActionTypeInfo? _selectedActionType;
    [ObservableProperty] ObservableCollection<ActionTypeInfo> _actionTypes = new();

    public bool DialogResult { get; private set; } = false;

    /// <summary>
    /// 关闭请求事件
    /// </summary>
    public event EventHandler? CloseRequested;
    #endregion

    #region Constructor
    public TriggerActionSelectorViewModel()
    {
        // 初始化动作类型列表
        ActionTypes.Add(new ActionTypeInfo(TriggerActionType.AudioPlay, "音频播放", "播放音频文件", "🔊"));
        ActionTypes.Add(new ActionTypeInfo(TriggerActionType.TTS, "TTS语音播报", "使用系统TTS播报文本", "🗣️"));
        ActionTypes.Add(new ActionTypeInfo(TriggerActionType.Notification, "系统通知", "发送系统托盘通知", "🔔"));
        ActionTypes.Add(new ActionTypeInfo(TriggerActionType.TextDisplay, "文本显示", "在屏幕上显示文本", "📝"));
        ActionTypes.Add(new ActionTypeInfo(TriggerActionType.Log, "日志记录", "记录日志信息", "📋"));
        ActionTypes.Add(new ActionTypeInfo(TriggerActionType.SendGameMessage, "游戏内消息", "发送游戏内通知消息", "💬"));
        ActionTypes.Add(new ActionTypeInfo(TriggerActionType.KeyboardInput, "键盘输入", "模拟键盘按键", "⌨️"));
        ActionTypes.Add(new ActionTypeInfo(TriggerActionType.Mouse, "鼠标操作", "模拟鼠标操作", "🖱️"));
        ActionTypes.Add(new ActionTypeInfo(TriggerActionType.Counter, "计数器", "计数器操作", "🔢"));
    }
    #endregion

    #region Methods
    /// <summary>
    /// 创建选中的动作实例
    /// </summary>
    public TriggerAction? CreateAction()
    {
        if (SelectedActionType == null) return null;

        return SelectedActionType.ActionType switch
        {
            TriggerActionType.AudioPlay => new AudioPlayAction(),
            TriggerActionType.TTS => new TTSAction(),
            TriggerActionType.Notification => new NotificationAction(),
            TriggerActionType.TextDisplay => new TextDisplayAction(),
            TriggerActionType.Log => new LogAction(),
            TriggerActionType.SendGameMessage => new SendGameMessageAction(),
            TriggerActionType.KeyboardInput => new KeyboardInputAction(),
            TriggerActionType.Mouse => new MouseAction(),
            TriggerActionType.Counter => new CounterAction(),
            _ => null
        };
    }

    /// <summary>
    /// 确定
    /// </summary>
    [RelayCommand]
    private void Ok()
    {
        if (SelectedActionType == null)
        {
            MessageBox.Show("请选择一个动作类型", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        DialogResult = true;
        CloseRequested?.Invoke(this, EventArgs.Empty);
    }

    /// <summary>
    /// 取消
    /// </summary>
    [RelayCommand]
    private void Cancel()
    {
        DialogResult = false;
        CloseRequested?.Invoke(this, EventArgs.Empty);
    }
    #endregion
}

/// <summary>
/// 动作类型信息
/// </summary>
public class ActionTypeInfo(TriggerActionType type, string name, string description, string icon)
{
    public TriggerActionType ActionType { get; } = type;
    public string Name { get; } = name;
    public string Description { get; } = description;
    public string Icon { get; } = icon;
}