﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Services.Network.Plugin;
using Xylia.Preview.Data.Models.Sequence;
using static Xylia.Preview.Data.Models.BattleMessage;

namespace Xylia.BnsHelper.Common.Converters;
internal class InstantEffectNotification2Converter : JsonConverter<InstantEffectNotification2>
{
    public override void Write<PERSON>son(JsonWriter writer, InstantEffectNotification2? value, JsonSerializer serializer)
    {
        ArgumentNullException.ThrowIfNull(value);

        writer.WriteStartObject();

        // 保存所有字段，不做任何优化
        writer.WritePropertyName("t");
        writer.WriteValue(value.Time.Ticks);

        writer.WritePropertyName("o");
        writer.WriteValue((int)value.ObjectType);

        writer.WritePropertyName("r");
        writer.WriteValue((int)value.SkillResultType);

        writer.WritePropertyName("e");
        writer.WriteValue((int)value.EffectType);

        // 保存所有ID和名称信息
        writer.WritePropertyName("ci");
        writer.WriteValue(value.CasterId);

        if (!string.IsNullOrEmpty(value.CasterName))
        {
            writer.WritePropertyName("cn");
            writer.WriteValue(value.CasterName);
        }

        writer.WritePropertyName("ti");
        writer.WriteValue(value.TargetId);

        if (!string.IsNullOrEmpty(value.TargetName))
        {
            writer.WritePropertyName("tn");
            writer.WriteValue(value.TargetName);
        }

        // 技能和效果信息
        if (!string.IsNullOrEmpty(value.SkillName))
        {
            writer.WritePropertyName("sn");
            writer.WriteValue(value.SkillName);
        }

        if (!string.IsNullOrEmpty(value.EffectAlias))
        {
            writer.WritePropertyName("ea");
            writer.WriteValue(value.EffectAlias);
        }

        if (!string.IsNullOrEmpty(value.EffectName))
        {
            writer.WritePropertyName("en");
            writer.WriteValue(value.EffectName);
        }
        // 数值字段
        if (value.Value != 0)
        {
            writer.WritePropertyName("v1");
            writer.WriteValue(value.Value);
        }

        if (value.Value2 != 0)
        {
            writer.WritePropertyName("v2");
            writer.WriteValue(value.Value2);
        }

        if (value.Value3 != 0)
        {
            writer.WritePropertyName("v3");
            writer.WriteValue(value.Value3);
        }

        if (value.Value4 != 0)
        {
            writer.WritePropertyName("v4");
            writer.WriteValue(value.Value4);
        }

        writer.WriteEndObject();
    }

    public override InstantEffectNotification2 ReadJson(JsonReader reader, Type objectType, InstantEffectNotification2? existingValue, bool hasExistingValue, JsonSerializer serializer)
    {
        var obj = serializer.Deserialize<JObject>(reader)!;

        return new InstantEffectNotification2
        {
            Time = new DateTime(obj.Value<long>("t")),
            ObjectType = (ObjectTypeSeq)obj.Value<int>("o"),
            SkillResultType = (SkillResultTypeSeq)obj.Value<int>("r"),
            EffectType = (EffectTypeSeq)obj.Value<int>("e"),
            CasterId = obj.Value<long>("ci"),
            CasterName = obj.Value<string>("cn"),
            TargetId = obj.Value<long>("ti"),
            TargetName = obj.Value<string>("tn"),
            SkillName = obj.Value<string>("sn"),
            EffectAlias = obj.Value<string>("ea"),
            EffectName = obj.Value<string>("en"),
            Value = obj.Value<long>("v1"),
            Value2 = obj.Value<long>("v2"),
            Value3 = obj.Value<long>("v3"),
            Value4 = obj.Value<long>("v4"),
        };
    }
}

internal class CombatCollectionConverter : JsonConverter<CombatCollection>
{
    public override void WriteJson(JsonWriter writer, CombatCollection? value, JsonSerializer serializer)
    {
        ArgumentNullException.ThrowIfNull(value);

        // basic section
        writer.WriteStartObject();
        writer.WritePropertyName("version");
        writer.WriteValue(2); // 新版本：统一保存所有事件

        // 保存玩家信息缓存（包含self字段）
        writer.WritePropertyName("creature");
        writer.WriteStartArray();
        foreach (var creature in value._playersByPlayerId.Values)
        {
            writer.WriteStartObject();
            writer.WritePropertyName("id");
            writer.WriteValue(creature.PlayerId);
            writer.WritePropertyName("name");
            writer.WriteValue(creature.Name);
            writer.WritePropertyName("job");
            writer.WriteValue((int)creature.Job);
            writer.WritePropertyName("summoned");
            writer.WriteValue(creature.Summoned);
            writer.WritePropertyName("self");
            writer.WriteValue(creature.Self);
            writer.WriteEndObject();
        }
        writer.WriteEndArray();

        // 保存所有事件（不按玩家分组）
        writer.WritePropertyName("events");
        writer.WriteStartArray();
        foreach (var player in value)
        {
            foreach (var eventData in player)
            {
                serializer.Serialize(writer, eventData);
            }
        }
        writer.WriteEndArray();

        writer.WriteEndObject();
    }

    public override CombatCollection ReadJson(JsonReader reader, Type objectType, CombatCollection? existingValue, bool hasExistingValue, JsonSerializer serializer)
    {
        var obj = serializer.Deserialize<JObject>(reader)!;
        var version = obj.Value<int>("version");

        // Create instance of collection
        var collection = new CombatCollection() { IsHistory = true };
        if (version == 1) return collection;

        // 先恢复creature缓存和创建玩家对象
        var creaturesArray = obj.Value<JArray>("creature");
        if (creaturesArray != null)
        {
            foreach (var creatureToken in creaturesArray)
            {
                if (creatureToken is JObject creatureObj)
                {
                    var creatureId = creatureObj.Value<long>("id");
                    var creatureName = creatureObj.Value<string>("name") ?? "";
                    var creatureJob = (JobSeq)creatureObj.Value<int>("job");
                    var summoned = creatureObj.Value<long>("summoned");
                    var self = creatureObj.Value<bool>("self");

                    // 创建Creature对象并添加到缓存
                    var creature = new Creature
                    {
                        Id = creatureId,
                        Name = creatureName,
                        job = (byte)creatureJob,
                        Summoned = summoned
                    };

                    // 重建召唤物关系映射
                    if (creature.Summoned != 0)
                    {
                        if (creature.Job > JobSeq.PcMax)
                        {
                            // 这是召唤兽，Summoned字段指向召唤师
                            collection._summonedToSummoner[creature.Id] = creature.Summoned;
                        }
                        else
                        {
                            // 这是召唤师，Summoned字段指向召唤兽
                            collection._summonedToSummoner[creature.Summoned] = creature.Id;
                        }
                    }

                    // 如果这是玩家（不是召唤兽），创建CreatureStats对象
                    if (creatureJob <= JobSeq.PcMax)
                    {
                        var player = new CreatureStats(collection, creature, self, true);
                        collection.Add(player);

                        // 手动添加到玩家缓存，避免GetOrCreatePlayer重复创建
                        collection._playersByPlayerId[creatureId] = player;
                    }
                }
            }
        }

        // 加载所有事件，让系统自动分配和创建缓存
        var eventsArray = obj.Value<JArray>("events");
        if (eventsArray != null)
        {
            foreach (var eventToken in eventsArray)
            {
                var eventData = eventToken.ToObject<InstantEffectNotification2>();
                if (eventData != null) collection.Add(eventData);
            }
        }

        // 更新所有玩家的技能显示
        foreach (var player in collection)
        {
            player.UpdateSkillsDisplay();
        }

        return collection;
    }
}
