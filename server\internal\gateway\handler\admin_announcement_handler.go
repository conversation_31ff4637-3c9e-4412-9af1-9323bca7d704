package handler

import (
	"encoding/json"
	"net/http"
	"strconv"

	gatewayService "udp-server/server/internal/gateway/service"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/logger"

	"github.com/gorilla/mux"
)

// AdminAnnouncementHandler 管理后台公告处理器
type AdminAnnouncementHandler struct {
	announcementService      *service.AnnouncementService
	announcementAdminService *gatewayService.AnnouncementAdminService
	authService              *service.AuthService
}

// NewAdminAnnouncementHandler 创建管理后台公告处理器
func NewAdminAnnouncementHandler(
	announcementService *service.AnnouncementService,
	announcementAdminService *gatewayService.AnnouncementAdminService,
	authService *service.AuthService,
) *AdminAnnouncementHandler {
	return &AdminAnnouncementHandler{
		announcementService:      announcementService,
		announcementAdminService: announcementAdminService,
		authService:              authService,
	}
}

// 处理获取公告列表请求
func (h *AdminAnnouncementHandler) HandleGetAnnouncements(w http.ResponseWriter, r *http.Request) {
	announcements, err := h.announcementAdminService.GetAllAnnouncements()
	if err != nil {
		logger.Error("获取公告列表失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取公告列表失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取公告列表成功", announcements)
}

// 获取单个公告详情
func (h *AdminAnnouncementHandler) HandleGetAnnouncement(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idStr := vars["id"]

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的公告ID", nil)
		return
	}

	announcement, err := h.announcementAdminService.GetAnnouncementByID(id)
	if err != nil {
		logger.Error("获取公告详情失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "获取公告详情失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "获取公告详情成功", announcement)
}

// 处理创建公告请求
func (h *AdminAnnouncementHandler) HandleCreateAnnouncement(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	// 解析请求体
	var req struct {
		Title    string `json:"title"`
		Content  string `json:"content"`
		Type     uint8  `json:"type"`
		Priority uint8  `json:"priority"`
		Status   uint8  `json:"status"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析创建公告请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 验证必填字段
	if req.Title == "" || req.Content == "" {
		SendJSONResponse(w, http.StatusBadRequest, "标题和内容不能为空", nil)
		return
	}

	// 创建公告
	announcementID, err := h.announcementAdminService.CreateAnnouncement(
		req.Title,
		req.Content,
		req.Type,
		req.Priority,
		req.Status,
		0, // adminUID - 暂时使用0，后续可以从认证信息中获取
	)
	if err != nil {
		logger.Error("创建公告失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "创建公告失败", nil)
		return
	}

	logger.Info("公告创建成功: ID=%d", announcementID)
	SendJSONResponse(w, http.StatusOK, "公告创建成功", map[string]interface{}{
		"announcement_id": announcementID,
	})
}

// 处理更新公告请求
func (h *AdminAnnouncementHandler) HandleUpdateAnnouncement(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPut && r.Method != http.MethodPost {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	vars := mux.Vars(r)
	idStr := vars["id"]

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的公告ID", nil)
		return
	}

	// 解析请求体
	var req struct {
		Title    string `json:"title"`
		Content  string `json:"content"`
		Type     uint8  `json:"type"`
		Priority uint8  `json:"priority"`
		Status   uint8  `json:"status"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.Error("解析更新公告请求失败: %v", err)
		SendJSONResponse(w, http.StatusBadRequest, "请求格式错误", nil)
		return
	}

	// 验证必填字段
	if req.Title == "" || req.Content == "" {
		SendJSONResponse(w, http.StatusBadRequest, "标题和内容不能为空", nil)
		return
	}

	// 更新公告
	err = h.announcementAdminService.UpdateAnnouncement(
		uint32(id),
		req.Title,
		req.Content,
		req.Type,
		req.Priority,
		req.Status,
		0, // adminUID - 暂时使用0，后续可以从认证信息中获取
	)
	if err != nil {
		logger.Error("更新公告失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "更新公告失败", nil)
		return
	}

	SendJSONResponse(w, http.StatusOK, "公告更新成功", nil)
}

// 处理删除公告请求
func (h *AdminAnnouncementHandler) HandleDeleteAnnouncement(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		SendJSONResponse(w, http.StatusMethodNotAllowed, "方法不允许", nil)
		return
	}

	vars := mux.Vars(r)
	id, err := strconv.ParseUint(vars["id"], 10, 64)
	if err != nil {
		SendJSONResponse(w, http.StatusBadRequest, "无效的公告ID", nil)
		return
	}

	// 删除公告
	err = h.announcementAdminService.DeleteAnnouncement(uint32(id))
	if err != nil {
		logger.Error("删除公告失败: %v", err)
		SendJSONResponse(w, http.StatusInternalServerError, "删除公告失败", nil)
		return
	}

	logger.Info("公告删除成功: ID=%d", id)
	SendJSONResponse(w, http.StatusOK, "公告删除成功", nil)
}
