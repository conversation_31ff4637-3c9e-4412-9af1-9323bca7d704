﻿using CommunityToolkit.Mvvm.ComponentModel;
using System.Windows;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.ViewModels;

namespace Xylia.BnsHelper.Views.Pages;
internal interface IPageController
{
    object? Content { get; set; }

    string? Name { get; }

    bool CheckLogin { get; }

    int RequiredPermission { get; }

    Task Initialize();
}

internal class PageController<T>(string? icon = null, int requiredPermission = 0, Action? OnInitializing = null) : ObservableObject, IPageController
{
    public string? Name => StringHelper.Get("MainWindow_Navigation_" + typeof(T).Name);
    public object? Icon => Application.Current.FindResource(icon ?? "FatalGeometry");
    public bool IsNew { get; set; } = false;

    /// <summary>
    /// 获取页面所需的权限级别
    /// 权限级别：0=不需要登录，1=需要登录，2=需要高级权限
    /// </summary>
    public int RequiredPermission => requiredPermission;

    /// <summary>
    /// 检查是否满足访问条件
    /// </summary>
    public bool CheckLogin
    {
        get
        {
            var user = MainWindowViewModel.Instance.User;

            // 权限级别 0：不需要登录，直接允许访问
            if (requiredPermission == 0) return true;

            // 权限级别 1+：需要登录，检查用户是否已登录
            if (user == null || !user.IsLoggedIn) return false;

            // 权限级别 2+：需要高级权限，检查用户权限是否满足要求
            if (requiredPermission >= 2) return user.Permission >= 1; // 用户权限1表示有CDKey或会员权限

            // 权限级别 1：只需要登录即可
            return true;
        }
    }

    private object? _content;
    public object? Content
    {
        set => _content = value;
        get
        {
            if (!CheckLogin) throw new UnauthorizedAccessException();

            if (typeof(T) == typeof(DamageMeterPanel)) return _content ??= DamageMeterPanel.Instance;
            else return _content ??= Activator.CreateInstance(typeof(T));
        }
    }

    public async Task Initialize() => await Task.Run(() => OnInitializing?.Invoke());
}
