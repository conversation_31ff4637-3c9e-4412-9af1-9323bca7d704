﻿using System.Globalization;
using System.Windows.Data;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models;

namespace Xylia.BnsHelper.Common.Converters;
internal class DamageMeterHitFormat : IValueConverter
{
	public object? Convert(object value, Type targetType, object parameter, CultureInfo culture)
	{
		if (value is not SkillStats data) throw new NotImplementedException();

		return SettingHelper.Default.HitFormat
			.Replace("暴击率", data.CriticalRate.ToString("P0"))
			.Replace("命中", data.HitCount.ToString())
			.Replace("暴击", data.CriticalHitCount.ToString())
			.Replace("闪避", data.DodgeCount.ToString())
			.Replace("格挡", data.ParryCount.ToString());
	}

	public object? ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) => throw new NotImplementedException();
}
