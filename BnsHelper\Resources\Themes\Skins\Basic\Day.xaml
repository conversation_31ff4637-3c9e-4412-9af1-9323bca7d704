﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

	<Color x:Key="LightPrimaryColor">#f8fafe</Color>

	<Color x:Key="LightDangerColor">#fef7f7</Color>
	<Color x:Key="DangerColor">#e74c3c</Color>
	<Color x:Key="DarkDangerColor">#e74c3c</Color>

	<Color x:Key="LightWarningColor">#fefdf8</Color>
	<Color x:Key="WarningColor">#f39c12</Color>
	<Color x:Key="DarkWarningColor">#f39c12</Color>

	<Color x:Key="LightInfoColor">#f4f8fb</Color>
	<Color x:Key="InfoColor">#3498db</Color>
	<Color x:Key="DarkInfoColor">#3498db</Color>

	<Color x:Key="LightSuccessColor">#f7fbf8</Color>
	<Color x:Key="SuccessColor">#27ae60</Color>
	<Color x:Key="DarkSuccessColor">#27ae60</Color>

	<Color x:Key="PrimaryTextColor">#2c3e50</Color>
	<Color x:Key="SecondaryTextColor">#7f8c8d</Color>
	<Color x:Key="ThirdlyTextColor">#bdc3c7</Color>
	<Color x:Key="ReverseTextColor">#2c3e50</Color>
	<Color x:Key="TextIconColor">White</Color>

    <Color x:Key="BorderColor">#e0e0e0</Color>
    <Color x:Key="SecondaryBorderColor">#bdc3c7</Color>
	<Color x:Key="BackgroundColor">#f8fafe</Color>
	<Color x:Key="RegionColor">#ffffff</Color>
    <Color x:Key="SecondaryRegionColor">#eeeeee</Color>
    <Color x:Key="ThirdlyRegionColor">White</Color>
    <Color x:Key="TitleColor">#5dade2</Color>
	<Color x:Key="SecondaryTitleColor">#85c1e9</Color>

	<Color x:Key="DefaultColor">#ffffff</Color>
	<Color x:Key="DarkDefaultColor">#f8fafe</Color>

	<Color x:Key="AccentColor">#5dade2</Color>
	<Color x:Key="DarkAccentColor">#5dade2</Color>

	<Color x:Key="DarkMaskColor">#15000000</Color>
	<Color x:Key="DarkOpacityColor">#25000000</Color>
	<system:UInt32 x:Key="BlurGradientValue">0x99FFFFFF</system:UInt32>

	<!-- Additional Brush Resources -->
	<SolidColorBrush x:Key="LightDangerBrush" Color="{StaticResource LightDangerColor}"/>
	<SolidColorBrush x:Key="DangerBrush" Color="{StaticResource DangerColor}"/>
	<SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
	<SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}"/>
	<SolidColorBrush x:Key="TextIconBrush" Color="{StaticResource TextIconColor}"/>
	<SolidColorBrush x:Key="DarkPrimaryBrush" Color="{StaticResource DarkPrimaryColor}"/>
	<SolidColorBrush x:Key="SecondaryBorderBrush" Color="{StaticResource SecondaryBorderColor}"/>
	<SolidColorBrush x:Key="ThirdlyRegionBrush" Color="{StaticResource ThirdlyRegionColor}"/>
	<SolidColorBrush x:Key="DarkOpacityBrush" Color="{StaticResource DarkOpacityColor}"/>


	<!-- AvalonEdit -->
	<Color x:Key="ControlAccentColorKey">#5dade2</Color>
	<Color x:Key="EditorBackgroundColor">#ffffff</Color>
	<Color x:Key="EditorForegroundColor">#2c3e50</Color>
	<Color x:Key="EditorLineNumbersForegroundColor">#7f8c8d</Color>
	<Color x:Key="EditorNonPrintableCharacterColor">#3F5dade2</Color>
	<Color x:Key="EditorLinkTextForegroundColor">#5dade2</Color>
	<Color x:Key="EditorLinkTextBackgroundColor">#00000000</Color>

	<Color x:Key="XML_XmlDeclaration">#5dade2</Color>
	<Color x:Key="XML_XmlTag">#85c1e9</Color>
	<Color x:Key="XML_AttributeName">#e74c3c</Color>
	<Color x:Key="XML_AttributeValue">#5dade2</Color>
	<!-- AvalonEdit -->

</ResourceDictionary>