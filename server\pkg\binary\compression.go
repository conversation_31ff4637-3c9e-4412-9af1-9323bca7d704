package binary

import (
	"bytes"
	"compress/gzip"
	"fmt"
	"io"
	"udp-server/server/pkg/logger"
)

// 压缩阈值：只有当消息体大于此大小时才进行压缩
const CompressionThreshold = 128 // 128字节

// CompressMessage 压缩消息体（如果有益）
func CompressMessage(msg *Message) (*Message, error) {
	if msg == nil {
		return nil, fmt.Errorf("message cannot be nil")
	}

	// 如果已经压缩，直接返回
	if msg.Header.HasFlag(FlagCompressed) {
		return msg, nil
	}

	// 如果消息体太小，不进行压缩
	if len(msg.Body) < CompressionThreshold {
		return msg, nil
	}

	// 克隆消息以避免修改原始消息
	compressedMsg := msg.Clone()

	// 压缩消息体
	compressedBody, err := compressData(compressedMsg.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to compress message body: %w", err)
	}

	// 只有在压缩后确实减小了大小时才使用压缩版本
	if len(compressedBody) < len(msg.Body) {
		compressedMsg.Body = compressedBody
		compressedMsg.Header.SetFlag(FlagCompressed)
	}

	return compressedMsg, nil
}

// DecompressMessage 解压缩消息体
func DecompressMessage(msg *Message) (*Message, error) {
	if msg == nil {
		return nil, fmt.Errorf("message cannot be nil")
	}

	// 检查是否设置了压缩标志
	if !msg.Header.HasFlag(FlagCompressed) {
		return msg, nil // 消息未压缩，直接返回
	}

	// 克隆消息
	decompressedMsg := msg.Clone()

	// 解压缩消息体
	decompressedBody, err := decompressData(decompressedMsg.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress message body: %w", err)
	}

	decompressedMsg.Body = decompressedBody
	decompressedMsg.Header.ClearFlag(FlagCompressed)

	return decompressedMsg, nil
}

// compressData 使用gzip压缩数据
func compressData(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return data, nil
	}

	var buf bytes.Buffer
	writer := gzip.NewWriter(&buf)

	_, err := writer.Write(data)
	if err != nil {
		writer.Close()
		return nil, err
	}

	err = writer.Close()
	if err != nil {
		return nil, err
	}

	return buf.Bytes(), nil
}

// decompressData 使用gzip解压缩数据
func decompressData(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return data, nil
	}

	reader, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return nil, err
	}
	defer reader.Close()

	decompressed, err := io.ReadAll(reader)
	if err != nil {
		return nil, err
	}

	return decompressed, nil
}

// 压缩消息体（跳过消息头）
func CompressMessageBody(data []byte) []byte {
	// 检查消息头长度
	if len(data) < HeaderSize {
		logger.Error("data too short for message header: %d bytes", len(data))
		return data
	}

	// 如果已经压缩，直接返回
	if (data[3] & FlagCompressed) != 0 {
		return data
	}

	// 提取消息体
	bodyData := data[HeaderSize:]

	// 如果消息体太小，不进行压缩
	if len(bodyData) < CompressionThreshold {
		return data
	}

	// 压缩消息体
	compressedBody, err := compressData(bodyData)
	if err != nil {
		logger.Error("failed to compress body: %w", err)
		return data
	}

	// 只有在压缩后确实减小了大小时才使用压缩版本
	if len(compressedBody) >= len(bodyData) {
		return data
	}

	// 创建新的消息数据
	result := make([]byte, HeaderSize+len(compressedBody))
	copy(result[:HeaderSize], data[:HeaderSize])
	copy(result[HeaderSize:], compressedBody)

	// 更新消息长度
	newLength := uint32(HeaderSize + len(compressedBody))
	result[4] = byte(newLength)
	result[5] = byte(newLength >> 8)
	result[6] = byte(newLength >> 16)
	result[7] = byte(newLength >> 24)

	// 设置压缩标志位
	result[3] |= FlagCompressed
	return result
}

// 解压缩消息体（跳过消息头）
func DecompressMessageBody(data []byte) ([]byte, error) {
	if len(data) < HeaderSize {
		return nil, fmt.Errorf("data too short for message header: %d bytes", len(data))
	}

	// 检查压缩标志位
	if (data[3] & FlagCompressed) == 0 {
		return data, nil // 未压缩，直接返回
	}

	// 提取压缩的消息体
	compressedBody := data[HeaderSize:]

	// 解压缩消息体
	decompressedBody, err := decompressData(compressedBody)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress body: %w", err)
	}

	// 创建新的消息数据
	result := make([]byte, HeaderSize+len(decompressedBody))
	copy(result[:HeaderSize], data[:HeaderSize])
	copy(result[HeaderSize:], decompressedBody)

	// 更新消息长度
	newLength := uint32(HeaderSize + len(decompressedBody))
	result[4] = byte(newLength)
	result[5] = byte(newLength >> 8)
	result[6] = byte(newLength >> 16)
	result[7] = byte(newLength >> 24)

	// 清除压缩标志位
	result[3] &= ^uint8(FlagCompressed)

	return result, nil
}

// IsCompressed 检查数据是否已压缩
func IsCompressed(data []byte) bool {
	if len(data) < HeaderSize {
		return false
	}

	return (data[3] & FlagCompressed) != 0
}

// GetCompressionRatio 计算压缩比
func GetCompressionRatio(originalSize, compressedSize int) float64 {
	if originalSize == 0 {
		return 0
	}
	return float64(compressedSize) / float64(originalSize)
}
