package service

import (
	"sync"
	"time"
	"udp-server/server/pkg/logger"
)

// UpdateMetrics 更新服务性能指标
type UpdateMetrics struct {
	mu sync.RWMutex

	// 请求统计
	TotalRequests     int64 `json:"total_requests"`
	SuccessRequests   int64 `json:"success_requests"`
	ErrorRequests     int64 `json:"error_requests"`
	
	// 缓存统计
	CacheHits         int64 `json:"cache_hits"`
	CacheMisses       int64 `json:"cache_misses"`
	CacheErrors       int64 `json:"cache_errors"`
	
	// 版本比较统计
	VersionCompareOK  int64 `json:"version_compare_ok"`
	VersionCompareErr int64 `json:"version_compare_err"`
	UpdatesNeeded     int64 `json:"updates_needed"`
	NoUpdatesNeeded   int64 `json:"no_updates_needed"`
	
	// 性能统计
	TotalResponseTime time.Duration `json:"total_response_time_ns"`
	MaxResponseTime   time.Duration `json:"max_response_time_ns"`
	MinResponseTime   time.Duration `json:"min_response_time_ns"`
	
	// 错误详情
	DatabaseErrors    int64 `json:"database_errors"`
	CacheSetErrors    int64 `json:"cache_set_errors"`
	CacheGetErrors    int64 `json:"cache_get_errors"`
	
	// 应用统计
	AppRequests       map[string]int64 `json:"app_requests"`
	
	// 启动时间
	StartTime         time.Time `json:"start_time"`
}

// NewUpdateMetrics 创建新的指标实例
func NewUpdateMetrics() *UpdateMetrics {
	return &UpdateMetrics{
		AppRequests: make(map[string]int64),
		StartTime:   time.Now(),
		MinResponseTime: time.Duration(^uint64(0) >> 1), // 最大值作为初始最小值
	}
}

// RecordRequest 记录请求
func (m *UpdateMetrics) RecordRequest(appName string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.TotalRequests++
	m.AppRequests[appName]++
}

// RecordSuccess 记录成功请求
func (m *UpdateMetrics) RecordSuccess() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.SuccessRequests++
}

// RecordError 记录错误请求
func (m *UpdateMetrics) RecordError() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.ErrorRequests++
}

// RecordCacheHit 记录缓存命中
func (m *UpdateMetrics) RecordCacheHit() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.CacheHits++
}

// RecordCacheMiss 记录缓存未命中
func (m *UpdateMetrics) RecordCacheMiss() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.CacheMisses++
}

// RecordCacheError 记录缓存错误
func (m *UpdateMetrics) RecordCacheError() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.CacheErrors++
}

// RecordVersionCompareOK 记录版本比较成功
func (m *UpdateMetrics) RecordVersionCompareOK() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.VersionCompareOK++
}

// RecordVersionCompareError 记录版本比较错误
func (m *UpdateMetrics) RecordVersionCompareError() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.VersionCompareErr++
}

// RecordUpdateNeeded 记录需要更新
func (m *UpdateMetrics) RecordUpdateNeeded() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.UpdatesNeeded++
}

// RecordNoUpdateNeeded 记录不需要更新
func (m *UpdateMetrics) RecordNoUpdateNeeded() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.NoUpdatesNeeded++
}

// RecordResponseTime 记录响应时间
func (m *UpdateMetrics) RecordResponseTime(duration time.Duration) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.TotalResponseTime += duration
	
	if duration > m.MaxResponseTime {
		m.MaxResponseTime = duration
	}
	
	if duration < m.MinResponseTime {
		m.MinResponseTime = duration
	}
}

// RecordDatabaseError 记录数据库错误
func (m *UpdateMetrics) RecordDatabaseError() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.DatabaseErrors++
}

// RecordCacheSetError 记录缓存设置错误
func (m *UpdateMetrics) RecordCacheSetError() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.CacheSetErrors++
}

// RecordCacheGetError 记录缓存获取错误
func (m *UpdateMetrics) RecordCacheGetError() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.CacheGetErrors++
}

// GetStats 获取统计信息
func (m *UpdateMetrics) GetStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	var avgResponseTime time.Duration
	if m.SuccessRequests > 0 {
		avgResponseTime = m.TotalResponseTime / time.Duration(m.SuccessRequests)
	}
	
	var cacheHitRate float64
	totalCacheOps := m.CacheHits + m.CacheMisses
	if totalCacheOps > 0 {
		cacheHitRate = float64(m.CacheHits) / float64(totalCacheOps) * 100
	}
	
	var successRate float64
	if m.TotalRequests > 0 {
		successRate = float64(m.SuccessRequests) / float64(m.TotalRequests) * 100
	}
	
	uptime := time.Since(m.StartTime)
	
	return map[string]interface{}{
		"total_requests":       m.TotalRequests,
		"success_requests":     m.SuccessRequests,
		"error_requests":       m.ErrorRequests,
		"success_rate":         successRate,
		
		"cache_hits":           m.CacheHits,
		"cache_misses":         m.CacheMisses,
		"cache_errors":         m.CacheErrors,
		"cache_hit_rate":       cacheHitRate,
		
		"version_compare_ok":   m.VersionCompareOK,
		"version_compare_err":  m.VersionCompareErr,
		"updates_needed":       m.UpdatesNeeded,
		"no_updates_needed":    m.NoUpdatesNeeded,
		
		"avg_response_time_ms": avgResponseTime.Milliseconds(),
		"max_response_time_ms": m.MaxResponseTime.Milliseconds(),
		"min_response_time_ms": m.MinResponseTime.Milliseconds(),
		
		"database_errors":      m.DatabaseErrors,
		"cache_set_errors":     m.CacheSetErrors,
		"cache_get_errors":     m.CacheGetErrors,
		
		"app_requests":         m.AppRequests,
		"uptime_seconds":       uptime.Seconds(),
		"start_time":           m.StartTime.Unix(),
	}
}

// LogStats 定期记录统计信息
func (m *UpdateMetrics) LogStats() {
	stats := m.GetStats()
	
	logger.Info("UpdateService Stats: requests=%d, success_rate=%.2f%%, cache_hit_rate=%.2f%%, avg_response=%dms",
		stats["total_requests"],
		stats["success_rate"],
		stats["cache_hit_rate"],
		stats["avg_response_time_ms"])
	
	if stats["error_requests"].(int64) > 0 {
		logger.Warn("UpdateService Errors: total=%d, db=%d, cache_set=%d, cache_get=%d, version_compare=%d",
			stats["error_requests"],
			stats["database_errors"],
			stats["cache_set_errors"],
			stats["cache_get_errors"],
			stats["version_compare_err"])
	}
}

// Reset 重置统计信息
func (m *UpdateMetrics) Reset() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.TotalRequests = 0
	m.SuccessRequests = 0
	m.ErrorRequests = 0
	m.CacheHits = 0
	m.CacheMisses = 0
	m.CacheErrors = 0
	m.VersionCompareOK = 0
	m.VersionCompareErr = 0
	m.UpdatesNeeded = 0
	m.NoUpdatesNeeded = 0
	m.TotalResponseTime = 0
	m.MaxResponseTime = 0
	m.MinResponseTime = time.Duration(^uint64(0) >> 1)
	m.DatabaseErrors = 0
	m.CacheSetErrors = 0
	m.CacheGetErrors = 0
	m.AppRequests = make(map[string]int64)
	m.StartTime = time.Now()
}
