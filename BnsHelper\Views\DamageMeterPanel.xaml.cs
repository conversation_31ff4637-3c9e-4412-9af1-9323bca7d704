﻿using HandyControl.Data;
using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Interop;
using System.Windows.Media;
using Vanara.PInvoke;
using Xylia.BnsHelper.Common.Helpers;
using Xylia.BnsHelper.Models;
using Xylia.BnsHelper.Resources;
using Xylia.BnsHelper.ViewModels;
using static Xylia.BnsHelper.ViewModels.DamageMeterViewModel;

namespace Xylia.BnsHelper.Views;
public partial class DamageMeterPanel
{
    #region Constructor
    readonly DamageMeterViewModel _viewModel;

    private DamageMeterPanel()
    {
        _instance = this;
        DataContext = _viewModel = new DamageMeterViewModel();

        InitializeComponent();
        Loaded += OnLoaded;
        DamageMeterViewModel.OnRefresh += OnRefresh;
    }
    #endregion

    #region Properties	 
    static DamageMeterPanel? _instance;
    internal static DamageMeterPanel Instance
    {
        get
        {
            // 检查权限：需要登录且有高级权限
            var user = MainWindowViewModel.Instance.User;
            if (user == null || !user.IsLoggedIn || user.Permission < 1) throw new UnauthorizedAccessException("战斗统计功能需要登录并拥有高级权限");

            return _instance ??= new DamageMeterPanel();
        }
    }
    #endregion

    #region Methods
    private static nint _hwnd;
    private static User32.SafeHHOOK? _hookId;
    private static DamageMeterPanel? _currentInstance;
    private bool _isTransparent = false;
    private bool _lastHitTestVisible = true;
    private static double _cachedDpi = 1.0;
    private static DateTime _lastDpiUpdate = DateTime.MinValue;
    private bool _isDisposed = false;

    protected override void OnSourceInitialized(EventArgs e)
    {
        base.OnSourceInitialized(e);
        _hwnd = new WindowInteropHelper(this).EnsureHandle();
        _currentInstance = this;

        // 应用初始的OBS capture设置
        UpdateCaptureSettings();

        var source = HwndSource.FromHwnd(_hwnd);
        source.AddHook(WndProc);

        // 监听IsHitTestVisible属性变化
        _viewModel.PropertyChanged += OnViewModelPropertyChanged;

        // 监听AllowCapture设置变化
        SettingHelper.Default.PropertyChanged += OnSettingChanged;
    }

    protected override void OnClosed(EventArgs e)
    {
        _isDisposed = true;
        DamageMeterViewModel.OnRefresh -= OnRefresh;
        _viewModel.PropertyChanged -= OnViewModelPropertyChanged;
        SettingHelper.Default.PropertyChanged -= OnSettingChanged;

        UninstallHook();
        _currentInstance = null;
        _viewModel.Dispose();
        _instance = null;

        // 保存窗口位置
        SaveWindowPosition();

        base.OnClosed(e);
    }

    private nint WndProc(nint hwnd, int msg, nint wParam, nint lParam, ref bool handled)
    {
        switch ((User32.WindowMessage)msg)
        {
            case User32.WindowMessage.WM_WINDOWPOSCHANGING when !_viewModel.IsHitTestVisible:
                var wp = Marshal.PtrToStructure<User32.WINDOWPOS>(lParam);
                wp.flags |= User32.SetWindowPosFlags.SWP_NOMOVE;
                Marshal.StructureToPtr(wp, lParam, false);
                handled = true;
                break;

            case User32.WindowMessage.WM_STYLECHANGING when wParam == (long)User32.WindowLongFlags.GWL_EXSTYLE:
                var style = Marshal.PtrToStructure<User32.STYLESTRUCT>(lParam);
                style.styleNew |= (uint)User32.WindowStylesEx.WS_EX_LAYERED;
                Marshal.StructureToPtr(style, lParam, false);
                handled = true;
                break;
        }

        return nint.Zero;
    }

    private static nint Hook(int nCode, nint wParam, nint lParam)
    {
        try
        {
            // 检查窗口实例是否存在
            if (_currentInstance == null || _currentInstance._isDisposed || _hookId == null)
            {
                return User32.CallNextHookEx(_hookId, nCode, wParam, lParam);
            }

            // Hook只在锁定状态下运行，所以不需要检查IsHitTestVisible
            if (nCode >= 0)
            {
                // 只在鼠标移动时检查位置，减少性能开销
                if (wParam == (nint)User32.WindowMessage.WM_MOUSEMOVE)
                {
                    // check position
                    var info = Marshal.PtrToStructure<User32.MSLLHOOKSTRUCT>(lParam);

                    // 使用缓存的DPI值，每秒最多更新一次
                    var now = DateTime.Now;
                    if ((now - _lastDpiUpdate).TotalSeconds > 1.0)
                    {
                        _lastDpiUpdate = now;
                        // 异步更新DPI，避免阻塞Hook线程
                        try
                        {
                            var instance = _currentInstance;
                            if (instance != null && !instance._isDisposed)
                            {
                                var dispatcher = instance.Dispatcher;
                                if (dispatcher != null)
                                {
                                    dispatcher.BeginInvoke(() =>
                                    {
                                        try
                                        {
                                            if (instance != null && !instance._isDisposed)
                                            {
                                                _cachedDpi = WpfScreenHelper.Screen.FromWindow(instance).ScaleFactor;
                                            }
                                        }
                                        catch
                                        {
                                            _cachedDpi = 1.0;
                                        }
                                    });
                                }
                            }
                        }
                        catch
                        {
                            _cachedDpi = 1.0;
                        }
                    }

                    // 异步处理HitTest，避免阻塞Hook线程
                    try
                    {
                        var instance = _currentInstance;
                        if (instance != null && !instance._isDisposed)
                        {
                            var dispatcher = instance.Dispatcher;
                            if (dispatcher != null)
                            {
                                dispatcher.BeginInvoke(() =>
                                {
                                    try
                                    {
                                        if (instance._isDisposed) return;

                                        var point = new Point(info.pt.X / _cachedDpi - instance.Left, info.pt.Y / _cachedDpi - instance.Top);

                                        // 检查鼠标是否在窗口范围内
                                        if (point.X >= 0 && point.Y >= 0 && point.X <= instance.ActualWidth && point.Y <= instance.ActualHeight)
                                        {
                                            var result = VisualTreeHelper.HitTest(instance, point)?.VisualHit as FrameworkElement;
                                            bool shouldBeTransparent = result?.Tag is null;
                                            instance.SetWindowTransparency(shouldBeTransparent);
                                        }
                                        else
                                        {
                                            // 鼠标在窗口外，设置为透明
                                            instance.SetWindowTransparency(true);
                                        }
                                    }
                                    catch
                                    {
                                        // 如果HitTest失败，设置为透明
                                        if (!instance._isDisposed)
                                        {
                                            instance.SetWindowTransparency(true);
                                        }
                                    }
                                });
                            }
                        }
                    }
                    catch
                    {
                        // 如果Dispatcher调用失败，设置为透明
                        _currentInstance?.SetWindowTransparency(true);
                    }
                }
            }
        }
        catch
        {
            // 如果Hook处理出现异常，确保不会崩溃
            // 设置为透明状态作为安全回退
            try
            {
                var instance = _currentInstance;
                if (instance != null && !instance._isDisposed)
                {
                    instance.SetWindowTransparency(true);
                }
            }
            catch
            {
                // 忽略设置透明状态的异常，防止级联崩溃
            }
        }

        // 安全地调用下一个Hook
        try
        {
            return User32.CallNextHookEx(_hookId, nCode, wParam, lParam);
        }
        catch
        {
            // 如果CallNextHookEx失败，返回0
            return nint.Zero;
        }
    }

    private void SetWindowTransparency(bool transparent)
    {
        // 检查窗口是否已经被销毁
        if (_isDisposed || _hwnd == nint.Zero)
        {
            return;
        }

        // 只在状态改变时修改窗口样式
        if (transparent != _isTransparent)
        {
            _isTransparent = transparent;

            try
            {
                if (_isTransparent)
                {
                    var style = User32.GetWindowLongPtr(_hwnd, User32.WindowLongFlags.GWL_EXSTYLE) | (nint)User32.WindowStylesEx.WS_EX_TRANSPARENT;
                    User32.SetWindowLong(_hwnd, User32.WindowLongFlags.GWL_EXSTYLE, style);
                }
                else
                {
                    var style = User32.GetWindowLongPtr(_hwnd, User32.WindowLongFlags.GWL_EXSTYLE) & ~(nint)User32.WindowStylesEx.WS_EX_TRANSPARENT;
                    User32.SetWindowLong(_hwnd, User32.WindowLongFlags.GWL_EXSTYLE, style);
                }
            }
            catch
            {
                // 如果设置窗口样式失败，记录但不抛出异常
                System.Diagnostics.Debug.WriteLine("Failed to set window transparency");
            }
        }
    }

    private static void InstallHook()
    {
        // 如果Hook已经安装，先卸载
        if (_hookId != null && !_hookId.IsInvalid)
        {
            UninstallHook();
        }

        try
        {
            var moduleHandle = Kernel32.GetModuleHandle();

            _hookId = User32.SetWindowsHookEx(User32.HookType.WH_MOUSE_LL, Hook, moduleHandle, 0);
            if (_hookId == nint.Zero) throw new Win32Exception(Marshal.GetLastWin32Error());

            Debug.WriteLine("Mouse hook installed successfully");
        }
        catch (Exception ex)
        {
            // 如果Hook安装失败，记录错误但不阻止程序运行
            Debug.WriteLine($"Failed to install mouse hook: {ex.Message}");
        }
    }

    private static void UninstallHook()
    {
        if (_hookId != null && !_hookId.IsInvalid)
        {
            try
            {
                _hookId.Close();
                _hookId = null;
                Debug.WriteLine("Mouse hook uninstalled successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Failed to uninstall mouse hook: {ex.Message}");
            }
        }
    }

    private void OnViewModelPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (_isDisposed) return;

        if (e.PropertyName == nameof(DamageMeterViewModel.IsHitTestVisible))
        {
            // 当IsHitTestVisible改变时，立即更新窗口透明状态和Hook状态
            if (_viewModel.IsHitTestVisible != _lastHitTestVisible)
            {
                _lastHitTestVisible = _viewModel.IsHitTestVisible;

                try
                {
                    if (!_viewModel.IsHitTestVisible)
                    {
                        // 锁定状态：安装Hook并设置为透明
                        InstallHook();
                        SetWindowTransparency(true);
                    }
                    else
                    {
                        // 解锁状态：卸载Hook并移除透明
                        UninstallHook();
                        SetWindowTransparency(false);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error in OnViewModelPropertyChanged: {ex.Message}");
                }
            }
        }
    }

    private void OnSettingChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (_isDisposed) return;

        if (e.PropertyName == nameof(SettingHelper.AllowCapture))
        {
            // 当AllowCapture设置改变时，更新窗口样式
            UpdateCaptureSettings();
        }
    }

    private void UpdateCaptureSettings()
    {
        if (_hwnd == nint.Zero) return;

        try
        {
            var currentExStyle = User32.GetWindowLong(_hwnd, User32.WindowLongFlags.GWL_EXSTYLE);

            if (!SettingHelper.Default.AllowCapture)
            {
                // 不允许捕获：添加WS_EX_TOOLWINDOW标志，隐藏任务栏图标
                var newExStyle = currentExStyle | (nint)User32.WindowStylesEx.WS_EX_TOOLWINDOW;
                User32.SetWindowLong(_hwnd, User32.WindowLongFlags.GWL_EXSTYLE, newExStyle);
            }
            else
            {
                // 允许捕获：移除WS_EX_TOOLWINDOW标志，显示任务栏图标
                var newExStyle = currentExStyle & ~(nint)User32.WindowStylesEx.WS_EX_TOOLWINDOW;
                User32.SetWindowLong(_hwnd, User32.WindowLongFlags.GWL_EXSTYLE, newExStyle);
            }

            System.Diagnostics.Debug.WriteLine($"Updated capture settings: AllowCapture={SettingHelper.Default.AllowCapture}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to update capture settings: {ex.Message}");
        }
    }


    private void OnLoaded(object? sender, EventArgs e)
    {
        // 初始化
        RestoreWindowPosition();
        CheckAndShowActivityNotice();

        Init:
        try
        {
            _viewModel.Initialize();
        }
        catch (Exception ex)
        {
            if (MessageBox.Show(ex.Message, StringHelper.Get("ApplicationName"), MessageBoxButton.OKCancel, MessageBoxImage.Error) == MessageBoxResult.OK) goto Init;

            // 安全关闭窗口：使用Dispatcher.BeginInvoke延迟执行，避免在当前调用栈中关闭窗口
            Dispatcher.BeginInvoke(() =>
            {
                try
                {
                    Close();
                }
                catch (Exception closeEx)
                {
                    Debug.WriteLine($"Error closing window: {closeEx.Message}");
                }
            });
        }
    }

    private void OnExit(object sender, RoutedEventArgs e)
    {
        Close();
    }

    private void OnTargetButtonClick(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.ContextMenu != null)
        {
            button.ContextMenu.PlacementTarget = button;
            button.ContextMenu.Placement = PlacementMode.Bottom;
            button.ContextMenu.IsOpen = true;
        }
    }

    private void OnRefresh(object? sender, EventArgs e)
    {
        // 检查用户权限，如果权限不足则关闭界面
        var user = MainWindowViewModel.Instance.User;
        if (user == null || !user.IsLoggedIn || user.Permission < 1)
        {
            Debug.WriteLine("[DamageMeter] 权限不足，关闭战斗统计界面");
            Application.Current.Dispatcher.BeginInvoke(() =>
            {
                try
                {
                    if (IsVisible) this.Close();
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"[DamageMeter] 关闭界面时发生异常: {ex.Message}");
                }
            });

            return; // 权限不足时直接返回，不继续刷新
        }

        // Tooltip cannot be opened in refresh
        if (!PlayerHolder.IsMouseOver) _viewModel.Players.Refresh();
    }

    private void HistoryHolder_MouseDoubleClick(object sender, MouseButtonEventArgs e)
    {
        if (HistoryHolder.SelectedItem is HistoryData history)
        {
            _viewModel.Status = StatusType.Pause;
            _viewModel.Players = history.Data;
            _viewModel.Page = 0; // 切换到Player集合分页
        }
    }


    /// <summary>
    /// 显示活动信息
    /// </summary>
    private void CheckAndShowActivityNotice()
    {
        var user = MainWindowViewModel.Instance.User;
        if (user?.ShowActivityNotice == true)
        {
            var activity = ZSActivityInfo.Instance;
            HandyControl.Controls.Growl.Info(new GrowlInfo
            {
                Message = activity.Description,
                WaitTime = 5,
                Token = "DamageMeterGrowl",
                ShowDateTime = false
            });
        }
    }

    /// <summary>
    /// 恢复窗口位置
    /// </summary>
    private void RestoreWindowPosition()
    {
        try
        {
            var settings = SettingHelper.Default;

            // 恢复窗口位置和大小
            Left = settings.DamageMeterLeft;
            Top = settings.DamageMeterTop;

            // 确保窗口在屏幕范围内
            EnsureWindowInScreen();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error restoring window position: {ex.Message}");
            // 如果恢复失败，使用默认位置
            Left = 100;
            Top = 100;
            Width = 400;
            Height = 300;
        }
    }

    /// <summary>
    /// 保存窗口位置
    /// </summary>
    private void SaveWindowPosition()
    {
        try
        {
            if (WindowState == WindowState.Normal)
            {
                var settings = SettingHelper.Default;
                settings.DamageMeterLeft = (int)Left;
                settings.DamageMeterTop = (int)Top;
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error saving window position: {ex.Message}");
        }
    }

    /// <summary>
    /// 确保窗口在屏幕范围内
    /// </summary>
    private void EnsureWindowInScreen()
    {
        var screenWidth = SystemParameters.PrimaryScreenWidth;
        var screenHeight = SystemParameters.PrimaryScreenHeight;

        // 确保窗口不会超出屏幕边界
        if (Left < 0) Left = 0;
        if (Top < 0) Top = 0;
        if (Left + Width > screenWidth) Left = screenWidth - Width;
        if (Top + Height > screenHeight) Top = screenHeight - Height;

        // 确保窗口大小合理
        if (Width < MinWidth) Width = MinWidth;
        if (Height < MinHeight) Height = MinHeight;
    }
    #endregion
}
