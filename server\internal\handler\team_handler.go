package handler

import (
	"net"
	"udp-server/server/internal/service"
	"udp-server/server/pkg/binary"
	"udp-server/server/pkg/logger"
)

// TeamHandler 团队信息处理器
type TeamHandler struct {
	teamService *service.TeamService
}

// NewTeamHandler 创建团队信息处理器
func NewTeamHandler(teamService *service.TeamService) *TeamHandler {
	return &TeamHandler{
		teamService: teamService,
	}
}

// 处理获取团队信息请求
func (h *TeamHandler) HandleGetTeamInfoRequest(remoteAddr *net.UDPAddr) (*binary.BinaryWriter, error) {
	logger.Debug("处理获取团队信息请求，来自: %s", remoteAddr.IP.String())

	// 获取团队信息
	members, err := h.teamService.GetTeamInfo()
	if err != nil {
		logger.Error("获取团队信息失败: %v", err)
		return nil, err
	}

	// 创建成功响应
	writer := binary.NewBinaryWriter()
	writer.WriteUint32(binary.ErrorCodeSuccess)
	writer.WriteUint16(uint16(len(members))) // 成员信息
	for _, member := range members {
		writer.WriteString(member.Name)
		writer.WriteInt64(member.UIN)
		writer.WriteString(member.Slogan)
		writer.WriteString(member.Strengths)
		writer.WriteString(member.URL)

		switch member.Type {
		case "author":
			writer.WriteInt8(0)
		case "friends":
			writer.WriteInt8(1)
		default:
			writer.WriteInt8(2)
		}
	}

	return writer, nil
}
